#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
字幕音频播放器
支持播放字幕对应的配音音频文件
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import os
import srt
import pygame
import threading
import time
from pathlib import Path

class SubtitleAudioPlayer:
    def __init__(self, root):
        self.root = root
        self.root.title("字幕音频播放器")
        self.root.geometry("800x600")
        
        # 初始化pygame音频
        pygame.mixer.init()
        
        # 数据存储
        self.subtitles = []
        self.audio_files = {}  # 存储字幕索引到音频文件的映射
        self.current_playing = None
        self.current_sound = None
        
        self.setup_ui()
        
    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(2, weight=1)
        
        # 文件选择区域
        file_frame = ttk.LabelFrame(main_frame, text="文件选择", padding="5")
        file_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        file_frame.columnconfigure(1, weight=1)
        
        # SRT文件选择
        ttk.Label(file_frame, text="字幕文件:").grid(row=0, column=0, sticky=tk.W, padx=(0, 5))
        self.srt_path_var = tk.StringVar()
        ttk.Entry(file_frame, textvariable=self.srt_path_var, state="readonly").grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 5))
        ttk.Button(file_frame, text="浏览", command=self.select_srt_file).grid(row=0, column=2)
        
        # 音频目录选择
        ttk.Label(file_frame, text="音频目录:").grid(row=1, column=0, sticky=tk.W, padx=(0, 5), pady=(5, 0))
        self.audio_dir_var = tk.StringVar()
        ttk.Entry(file_frame, textvariable=self.audio_dir_var, state="readonly").grid(row=1, column=1, sticky=(tk.W, tk.E), padx=(0, 5), pady=(5, 0))
        ttk.Button(file_frame, text="浏览", command=self.select_audio_dir).grid(row=1, column=2, pady=(5, 0))
        
        # 加载按钮
        ttk.Button(file_frame, text="加载字幕", command=self.load_subtitles).grid(row=2, column=0, columnspan=3, pady=(10, 0))
        
        # 控制区域
        control_frame = ttk.Frame(main_frame)
        control_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        self.stop_button = ttk.Button(control_frame, text="停止播放", command=self.stop_audio, state="disabled")
        self.stop_button.pack(side=tk.LEFT, padx=(0, 10))
        
        self.status_label = ttk.Label(control_frame, text="请选择字幕文件和音频目录")
        self.status_label.pack(side=tk.LEFT)
        
        # 字幕列表
        list_frame = ttk.LabelFrame(main_frame, text="字幕列表", padding="5")
        list_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S))
        list_frame.columnconfigure(0, weight=1)
        list_frame.rowconfigure(0, weight=1)
        
        # 创建Treeview
        columns = ("序号", "开始时间", "结束时间", "字幕内容", "音频状态")
        self.tree = ttk.Treeview(list_frame, columns=columns, show="headings", height=15)
        
        # 设置列标题和宽度
        self.tree.heading("序号", text="序号")
        self.tree.heading("开始时间", text="开始时间")
        self.tree.heading("结束时间", text="结束时间")
        self.tree.heading("字幕内容", text="字幕内容")
        self.tree.heading("音频状态", text="音频状态")
        
        self.tree.column("序号", width=60, anchor=tk.CENTER)
        self.tree.column("开始时间", width=100, anchor=tk.CENTER)
        self.tree.column("结束时间", width=100, anchor=tk.CENTER)
        self.tree.column("字幕内容", width=300, anchor=tk.W)
        self.tree.column("音频状态", width=100, anchor=tk.CENTER)
        
        # 添加滚动条
        scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)
        
        # 布局
        self.tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        # 绑定双击事件
        self.tree.bind("<Double-1>", self.on_item_double_click)
        
        # 右键菜单
        self.context_menu = tk.Menu(self.root, tearoff=0)
        self.context_menu.add_command(label="播放音频", command=self.play_selected_audio)
        self.context_menu.add_command(label="停止播放", command=self.stop_audio)
        
        self.tree.bind("<Button-3>", self.show_context_menu)
        
    def select_srt_file(self):
        """选择SRT字幕文件"""
        file_path = filedialog.askopenfilename(
            title="选择字幕文件",
            filetypes=[("SRT文件", "*.srt"), ("所有文件", "*.*")]
        )
        if file_path:
            self.srt_path_var.set(file_path)
            
    def select_audio_dir(self):
        """选择音频文件目录"""
        dir_path = filedialog.askdirectory(title="选择音频文件目录")
        if dir_path:
            self.audio_dir_var.set(dir_path)
            
    def load_subtitles(self):
        """加载字幕文件"""
        srt_path = self.srt_path_var.get()
        audio_dir = self.audio_dir_var.get()
        
        if not srt_path or not audio_dir:
            messagebox.showerror("错误", "请先选择字幕文件和音频目录！")
            return
            
        if not os.path.exists(srt_path):
            messagebox.showerror("错误", "字幕文件不存在！")
            return
            
        if not os.path.exists(audio_dir):
            messagebox.showerror("错误", "音频目录不存在！")
            return
            
        try:
            # 读取SRT文件
            with open(srt_path, 'r', encoding='utf-8') as f:
                srt_content = f.read()
                
            self.subtitles = list(srt.parse(srt_content))
            
            # 清空现有列表
            for item in self.tree.get_children():
                self.tree.delete(item)
                
            self.audio_files.clear()
            
            # 扫描音频文件
            audio_dir_path = Path(audio_dir)
            
            # 填充字幕列表
            for i, subtitle in enumerate(self.subtitles):
                # 查找对应的音频文件
                audio_status = "无音频"
                possible_audio_files = [
                    f"{i+1}.wav",
                    f"{i+1}.mp3",
                    f"subtitle_{i+1:03d}.wav",
                    f"subtitle_{i+1:04d}.wav",
                    f"subtitle_{i+1:03d}.mp3",
                    f"subtitle_{i+1:04d}.mp3"
                ]
                
                for audio_file in possible_audio_files:
                    audio_path = audio_dir_path / audio_file
                    if audio_path.exists():
                        self.audio_files[i] = str(audio_path)
                        audio_status = "有音频"
                        break
                
                # 添加到列表
                self.tree.insert("", "end", values=(
                    subtitle.index,
                    str(subtitle.start).split('.')[0],  # 去掉毫秒部分
                    str(subtitle.end).split('.')[0],
                    subtitle.content.replace('\n', ' '),
                    audio_status
                ))
                
            self.status_label.config(text=f"已加载 {len(self.subtitles)} 条字幕，其中 {len(self.audio_files)} 条有音频")
            
        except Exception as e:
            messagebox.showerror("错误", f"加载字幕文件失败：\n{str(e)}")
            
    def on_item_double_click(self, event):
        """双击列表项事件"""
        self.play_selected_audio()
        
    def show_context_menu(self, event):
        """显示右键菜单"""
        item = self.tree.identify_row(event.y)
        if item:
            self.tree.selection_set(item)
            self.context_menu.post(event.x_root, event.y_root)
            
    def play_selected_audio(self):
        """播放选中的音频"""
        selection = self.tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请先选择一条字幕！")
            return
            
        item = selection[0]
        values = self.tree.item(item, "values")
        subtitle_index = int(values[0]) - 1  # 转换为0基索引
        
        if subtitle_index not in self.audio_files:
            messagebox.showwarning("警告", "该字幕没有对应的音频文件！")
            return
            
        audio_path = self.audio_files[subtitle_index]
        
        try:
            # 停止当前播放
            self.stop_audio()
            
            # 加载并播放音频
            self.current_sound = pygame.mixer.Sound(audio_path)
            self.current_playing = subtitle_index
            self.current_sound.play()
            
            # 更新状态
            subtitle_content = values[3][:30] + "..." if len(values[3]) > 30 else values[3]
            self.status_label.config(text=f"正在播放: {subtitle_content}")
            self.stop_button.config(state="normal")
            
            # 启动监控线程
            threading.Thread(target=self.monitor_playback, daemon=True).start()
            
        except Exception as e:
            messagebox.showerror("错误", f"播放音频失败：\n{str(e)}")
            
    def stop_audio(self):
        """停止音频播放"""
        if self.current_sound:
            self.current_sound.stop()
            self.current_sound = None
            
        self.current_playing = None
        self.status_label.config(text="播放已停止")
        self.stop_button.config(state="disabled")
        
    def monitor_playback(self):
        """监控播放状态"""
        while self.current_sound and pygame.mixer.get_busy():
            time.sleep(0.1)
            
        # 播放结束
        if self.current_playing is not None:
            self.root.after(0, lambda: self.status_label.config(text="播放完成"))
            self.root.after(0, lambda: self.stop_button.config(state="disabled"))
            self.current_playing = None
            self.current_sound = None

def main():
    """主函数"""
    root = tk.Tk()
    app = SubtitleAudioPlayer(root)
    
    try:
        root.mainloop()
    except KeyboardInterrupt:
        pass
    finally:
        pygame.mixer.quit()

if __name__ == "__main__":
    main()
