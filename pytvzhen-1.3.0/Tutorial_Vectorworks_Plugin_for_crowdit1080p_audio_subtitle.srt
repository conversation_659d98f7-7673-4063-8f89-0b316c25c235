1
00:00:00,000 --> 00:00:04,560
Hello and welcome to the short tutorial on how to use VectorWorks in Croutet.

2
00:00:07,020 --> 00:00:10,200
Croutet is a simulation software where you can do crowd simulations,

3
00:00:10,620 --> 00:00:14,320
meaning that you can simulate how people move through a building or for example your planned

4
00:00:14,320 --> 00:00:20,040
festival area. Therefore, we have developed a plugin especially for VectorWorks in order to

5
00:00:20,040 --> 00:00:29,980
make sure that all the cut files can be imported in the simulation software and changes can be

6
00:00:29,980 --> 00:00:36,360
down to install partner products. And there you find Croutet ready to be installed.

7
00:00:37,300 --> 00:00:43,580
Let's have a look at how to use this plugin. You have an example here. It's a two-story building.

8
00:00:44,380 --> 00:00:47,900
And here we want to create simulation objects that we can later use.

9
00:00:48,740 --> 00:00:54,180
The workflow would be first we have to prepare the plan. The next step is we export the plan

10
00:00:54,180 --> 00:01:00,080
in the right format to use in Croutet. And the third step is we import it into our simulation software

11
00:01:00,080 --> 00:01:05,960
and you can work from there to create our simulation. Let's do the first steps in VectorWorks.

12
00:01:07,220 --> 00:01:11,160
First, how does the software understand that things are put in for our simulation?

13
00:01:13,560 --> 00:01:19,800
We have to create a class called Croutet. The first step here is to say create a new class and just name it

14
00:01:19,800 --> 00:01:31,520
about it. We see our class here now. And then the next thing we want to do is to create simulation

15
00:01:31,520 --> 00:01:37,460
objects. Simulation objects are the geometric reference that we can later use for our simulation

16
00:01:37,460 --> 00:01:43,080
for so-called origins or destinations. In origins the agents are born and destinations will be the

17
00:01:43,080 --> 00:01:49,700
areas where they move towards. We have to define the areas where people can interact. It's quite easy.

18
00:01:49,800 --> 00:01:54,060
We use polygons or rectangles and just mark the areas in here.

19
00:01:56,760 --> 00:02:03,920
You have to make sure that it's on the crowd at class. And that's pretty much it. We define in these

20
00:02:03,920 --> 00:02:10,300
rooms agents will be born. Now what we also can do is to create a future destination outside of the building.

21
00:02:10,860 --> 00:02:16,220
The polygons we create will later become simulation objects. And that's it for the ground floor.

22
00:02:18,460 --> 00:02:23,940
Now let's have a look at the second floor. We can also use a different way to insert the simulation

23
00:02:23,940 --> 00:02:29,840
objects. As before I insert my rectangles in rooms where I want my agents to be born.

24
00:02:49,620 --> 00:02:53,780
Now you can mark all of them and everything that has to be put in the crowd at layer.

25
00:02:56,940 --> 00:03:04,080
And you can go to tools, partner products, crowd it, and tag simulation objects. Here we go.

26
00:03:05,120 --> 00:03:10,120
Now we have defined all of our simulation objects, meaning that we have defined all the areas where

27
00:03:10,120 --> 00:03:15,220
agents can be spawned or leave the building. And the next step would be to check if we have

28
00:03:15,220 --> 00:03:23,380
everything visible. Which is the case here. Now we have to export our created plans in the right

29
00:03:23,380 --> 00:03:31,280
format for the simulation software. Once again we go to tools, partner products, crowd it, and then

30
00:03:31,280 --> 00:03:36,800
choose export to crowd it floor because now we want to export the prepared geometry to put it into a simulator.

31
00:03:37,940 --> 00:03:42,500
And as you can see here it says that we export all the classes that contain the name crowd it.

32
00:03:43,230 --> 00:03:47,950
We can also use the color code but I would recommend to use the layer name or the class name.

33
00:03:49,390 --> 00:03:53,770
And we say okay, now you can save your file wherever you want it to save.

34
00:03:54,650 --> 00:03:59,430
And as you can see here the ending is dot floor, which will be the crowded ending that we need.

35
00:04:00,570 --> 00:04:05,010
If you go to the folder now, as you can see, you have now two files in your in the folder.

36
00:04:06,050 --> 00:04:12,270
The first floor and the second floor. If you plan and 3D automatically, both floors get exported in a

37
00:04:12,270 --> 00:04:16,290
separate file. So now we can start working in crowded.

38
00:04:18,770 --> 00:04:23,490
We choose we have a new project. First, you have to save a new crowded project.

39
00:04:24,170 --> 00:04:26,190
I will just name it project one.

40
00:04:28,250 --> 00:04:35,650
We start now to insert our floor plans. Either I can open the plans from here or I can just drag and drop them in.

41
00:04:36,510 --> 00:04:40,010
I put in our first floor and I want to add the second floor as well.

42
00:04:40,650 --> 00:04:44,490
And here you can choose do I want to replace the floor or do I want to insert it?

43
00:04:45,710 --> 00:04:51,550
In this case we want to insert it. But if you have to change your geometry, you can also choose replace.

44
00:04:52,670 --> 00:04:57,450
Then everything that you worked on in crowded so far is preserved. No work is lost.

45
00:04:57,990 --> 00:05:04,270
That's a big advantage. But for now, we say apply. And now we have both floors in our project.

46
00:05:04,910 --> 00:05:09,330
We are ready to start modeling our new project. Let's start at the ground floor.

47
00:05:09,330 --> 00:05:14,430
You can see all the orange objects are the rectangles we just created in vector works.

48
00:05:15,510 --> 00:05:20,690
Now we want to assign the properties for these objects. I already know that all these objects are

49
00:05:20,690 --> 00:05:28,250
origins so I select all of them. Then right click manage create and then choose origin.

50
00:05:28,990 --> 00:05:35,430
Now you can choose your settings for all origins. I want 10 agents in every origin so I type 10 here.

51
00:05:36,070 --> 00:05:42,450
And click apply. You can see now that the color of the objects change to red as they are now assigned objects.

52
00:05:43,550 --> 00:05:48,610
Then we also need a destination. Let's say I forgot to model the destination in vector works.

53
00:05:49,550 --> 00:05:54,750
Then I can easily choose draw destination in our toolbar and create one outside of the building.

54
00:05:55,730 --> 00:06:00,270
And again apply to be able to go to our second floor we also need a stair.

55
00:06:00,810 --> 00:06:07,190
For that we select this object and say create and then choose stair. Now you can choose the

56
00:06:07,190 --> 00:06:12,890
direction for the stair. We also want this stair to connect to the first floor so we have to go to

57
00:06:12,890 --> 00:06:17,390
connect to floor and select our upper floor with that we are done with the ground floor.

58
00:06:18,350 --> 00:06:23,650
Now we have to create the origins for the upper floor. It is the same workflow as for the others.

59
00:06:32,890 --> 00:06:40,390
So we have all our simulation objects ready now. For this example I show you the easiest and quickest way to get to your simulation.

60
00:06:41,530 --> 00:06:48,390
Press control and F then the quick select window opens. Now you can search for a signed simulation objects.

61
00:06:49,150 --> 00:06:56,050
Is one of these types and choose origins. We want to add them to a set. Therefore we create a new set

62
00:06:56,050 --> 00:07:01,670
name it all origins and click add. We only have to create an easy path starting with the

63
00:07:01,670 --> 00:07:08,170
origin set now. For that we click right on any origin and choose start path with set all origins.

64
00:07:09,330 --> 00:07:13,550
Then we go to our ground floor click on our destination and choose end with destination.

65
00:07:14,770 --> 00:07:22,370
We leave the name path and apply. It's time to start the simulation. Therefore go to simulate run simulation.

66
00:07:23,010 --> 00:07:28,870
In our case five minutes should be enough. Now extend the advanced menu and tick automatically

67
00:07:28,870 --> 00:07:34,910
find floor connectors. With this setting the agents from the upper floor find the stair automatically

68
00:07:34,910 --> 00:07:40,970
to get to their designated destination. After a short time you can see your first simulation running.

69
00:07:41,690 --> 00:07:45,330
There are a lot of possibilities to analyze and visualize the simulation now.

70
00:07:47,910 --> 00:07:55,730
For more information on that feel free to visit our website at q-rate.de. Thank you and have a nice day.

