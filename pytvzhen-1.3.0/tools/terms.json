{"agent": "智能体", "environment": "环境", "state": "状态", "action": "动作", "reward": "奖励", "policy": "策略", "value": "价值", "model": "模型", "exploration": "探索", "exploitation": "利用", "model-free": "无模型", "model-based": "有模型", "value-based": "基于价值", "policy-based": "基于策略", "transition": "转移元组", "buffer": "经验池", "replay": "回放", "experience": "经验", "episode": "回合", "epoch": "轮次", "transformer": "transformer", "actor-critic": "actor-critic", "off-policy": "off-policy", "on-policy": "on-policy"}