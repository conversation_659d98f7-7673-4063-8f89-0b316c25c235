#!/usr/bin/env python
# -*- coding: utf-8 -*-

# 正确的show_home_page方法实现
new_method = '''    def show_home_page(self):
        """显示首页"""
        try:
            # 清空右侧区域布局中的所有组件
            for i in reversed(range(self.right_area_layout.count())): 
                widget = self.right_area_layout.itemAt(i).widget()
                if widget:
                    widget.hide()
                    self.right_area_layout.removeWidget(widget)
            
            # 添加主内容区域到右侧布局（包含了左侧容器和字幕编辑区域）
            if hasattr(self, 'main_content') and self.main_content:
                self.right_area_layout.addWidget(self.main_content)
                self.main_content.show()
                
                # 确保内部组件也显示
                if hasattr(self, 'upload_area') and self.upload_area:
                    self.upload_area.show()
                if hasattr(self, 'parameter_panel') and self.parameter_panel:
                    self.parameter_panel.show()
                if hasattr(self, 'subtitle_area') and self.subtitle_area:
                    self.subtitle_area.show()
                
                print("首页组件已添加并显示")
            else:
                print("错误：主内容区域不存在")
        except Exception as e:
            print(f"显示首页出错: {e}")
'''

# 读取原文件内容
with open('ui/fliptalk_ui.py', 'r', encoding='utf-8') as f:
    content = f.read()

# 查找方法的起始位置
start_idx = content.find('def show_home_page(self):')
if start_idx == -1:
    print("没有找到show_home_page方法！")
    exit(1)

# 查找方法的结束位置（下一个def的位置）
end_idx = content.find('def ', start_idx + 10)
if end_idx == -1:
    print("无法确定方法的结束位置！")
    exit(1)

# 替换方法
new_content = content[:start_idx] + new_method + content[end_idx:]

# 写入修改后的内容
with open('ui/fliptalk_ui.py', 'w', encoding='utf-8') as f:
    f.write(new_content)

print("show_home_page方法替换完成！") 