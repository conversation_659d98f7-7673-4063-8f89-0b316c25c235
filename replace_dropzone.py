#!/usr/bin/env python
# -*- coding: utf-8 -*-

# 读取新的DropZone类实现
with open('ui/dropzone_fixed.py', 'r', encoding='utf-8') as f:
    new_dropzone = f.read()

# 读取原文件内容
with open('ui/fliptalk_ui.py', 'r', encoding='utf-8') as f:
    content = f.read()

# 查找DropZone类的起始位置
start_idx = content.find('class DropZone(QWidget):')
if start_idx == -1:
    print("没有找到DropZone类！")
    exit(1)

# 查找类的结束位置（下一个class的位置）
end_idx = content.find('class ', start_idx + 10)
if end_idx == -1:
    print("无法确定类的结束位置！")
    exit(1)

# 替换类
new_content = content[:start_idx] + new_dropzone + content[end_idx:]

# 写入修改后的内容
with open('ui/fliptalk_ui.py', 'w', encoding='utf-8') as f:
    f.write(new_content)

print("DropZone类替换完成！") 