#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
使用VLC后端的视频播放测试程序
"""

import sys
import os
import platform
import vlc
from PySide6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QPushButton, QLabel, QFileDialog, QMessageBox, QSlider
)
from PySide6.QtCore import Qt, QTimer

class VlcVideoPlayer(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("VLC视频播放测试")
        self.setMinimumSize(800, 600)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        
        # 创建状态标签
        self.status_label = QLabel("请选择视频文件测试播放")
        self.status_label.setAlignment(Qt.AlignCenter)
        self.status_label.setStyleSheet("font-size: 16px; color: white;")
        main_layout.addWidget(self.status_label)
        
        # 创建视频显示区域
        self.video_frame = QWidget()
        self.video_frame.setMinimumHeight(360)
        self.video_frame.setStyleSheet("background-color: black;")
        main_layout.addWidget(self.video_frame)
        
        # 创建播放控制区域
        control_layout = QHBoxLayout()
        
        # 创建播放/暂停按钮
        self.play_pause_button = QPushButton("播放")
        self.play_pause_button.clicked.connect(self.toggle_play)
        control_layout.addWidget(self.play_pause_button)
        
        # 创建停止按钮
        self.stop_button = QPushButton("停止")
        self.stop_button.clicked.connect(self.stop_video)
        control_layout.addWidget(self.stop_button)
        
        # 创建选择文件按钮
        self.select_button = QPushButton("选择视频文件")
        self.select_button.clicked.connect(self.select_video)
        control_layout.addWidget(self.select_button)
        
        main_layout.addLayout(control_layout)
        
        # 创建进度条
        self.position_slider = QSlider(Qt.Horizontal)
        self.position_slider.setRange(0, 1000)
        self.position_slider.sliderMoved.connect(self.set_position)
        main_layout.addWidget(self.position_slider)
        
        # 初始化VLC实例和播放器
        self.instance = vlc.Instance()
        self.player = self.instance.media_player_new()
        
        # 初始化VLC播放器设置
        self.setup_player()
        
        # 创建更新定时器
        self.timer = QTimer(self)
        self.timer.setInterval(100)
        self.timer.timeout.connect(self.update_ui)
        
        # 设置样式
        self.setup_style()
        
        # 当前媒体
        self.media = None
        self.is_playing = False
    
    def setup_player(self):
        """配置VLC播放器与窗口的连接"""
        if platform.system() == "Windows":
            self.player.set_hwnd(int(self.video_frame.winId()))
        elif platform.system() == "Darwin":  # macOS
            self.player.set_nsobject(int(self.video_frame.winId()))
        else:  # Linux
            self.player.set_xwindow(int(self.video_frame.winId()))
    
    def setup_style(self):
        """设置界面样式"""
        self.setStyleSheet("""
            QMainWindow {
                background-color: #1B1E24;
            }
            QLabel {
                color: #FFFFFF;
            }
            QPushButton {
                background-color: #2B9D7C;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #33B490;
            }
            QSlider::groove:horizontal {
                border: 1px solid #999999;
                height: 8px;
                background: #4A4A4A;
                margin: 2px 0;
                border-radius: 4px;
            }
            QSlider::handle:horizontal {
                background: #2B9D7C;
                border: 1px solid #5c5c5c;
                width: 18px;
                margin: -2px 0;
                border-radius: 9px;
            }
        """)
    
    def select_video(self):
        """选择视频文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择视频文件", "",
            "视频文件 (*.mp4 *.avi *.mov *.mkv *.wmv)"
        )
        
        if file_path:
            self.load_video(file_path)
    
    def load_video(self, file_path):
        """加载视频文件"""
        if not os.path.isfile(file_path):
            QMessageBox.warning(self, "文件错误", f"找不到视频文件:\n{file_path}")
            return
        
        # 停止当前播放
        self.stop_video()
        
        # 创建新的媒体
        self.media = self.instance.media_new(file_path)
        self.player.set_media(self.media)
        
        # 更新状态
        self.status_label.setText(f"已加载: {os.path.basename(file_path)}")
        
        # 开始播放
        self.play_video()
        
        # 启动更新计时器
        self.timer.start()
    
    def play_video(self):
        """播放视频"""
        if self.media:
            self.player.play()
            self.play_pause_button.setText("暂停")
            self.is_playing = True
    
    def pause_video(self):
        """暂停视频"""
        if self.is_playing:
            self.player.pause()
            self.play_pause_button.setText("播放")
            self.is_playing = False
    
    def stop_video(self):
        """停止视频"""
        self.timer.stop()
        if self.player:
            self.player.stop()
            self.play_pause_button.setText("播放")
            self.is_playing = False
            self.position_slider.setValue(0)
    
    def toggle_play(self):
        """切换播放/暂停状态"""
        if not self.media:
            self.select_video()
            return
        
        if self.is_playing:
            self.pause_video()
        else:
            self.play_video()
    
    def set_position(self, position):
        """设置播放位置"""
        if self.player:
            # 将位置值从0-1000转换为0.0-1.0
            self.player.set_position(position / 1000.0)
    
    def update_ui(self):
        """更新UI，包括播放位置和状态"""
        if not self.player:
            return
        
        # 更新滑块位置
        media_pos = int(self.player.get_position() * 1000)
        self.position_slider.setValue(media_pos)
        
        # 更新状态，如果视频已结束
        if self.is_playing and not self.player.is_playing():
            self.timer.stop()
            self.play_pause_button.setText("播放")
            self.is_playing = False
            self.position_slider.setValue(0)
    
    def closeEvent(self, event):
        """窗口关闭事件"""
        self.timer.stop()
        if self.player:
            self.player.stop()
        event.accept()

def main():
    app = QApplication(sys.argv)
    window = VlcVideoPlayer()
    window.show()
    sys.exit(app.exec())

if __name__ == "__main__":
    main() 