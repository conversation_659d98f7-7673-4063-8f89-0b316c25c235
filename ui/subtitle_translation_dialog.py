#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
字幕翻译对话框
功能：支持多种翻译渠道的字幕翻译功能
"""

import sys
import os
import re
import time
from datetime import datetime
from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, 
    QTextEdit, QLineEdit, QScrollArea, QWidget, QFrame,
    QFileDialog, QMessageBox, QSplitter, QTableWidget, 
    QTableWidgetItem, QHeaderView, QComboBox, QSpinBox,
    QCheckBox, QGroupBox, QGridLayout, QProgressBar,
    QTabWidget, QFormLayout, QListWidget, QListWidgetItem,
    QStackedWidget
)
from PySide6.QtCore import Qt, Signal, QTimer, QThread, QUrl
from PySide6.QtGui import QFont, QTextCursor, QColor, QPalette, QDragEnterEvent, QDragMoveEvent, QDropEvent


class TranslationWorker(QThread):
    """翻译工作线程 - 基于插件系统，支持并发和序列翻译处理"""
    progress_updated = Signal(int)
    translation_completed = Signal(int, str, str)  # 索引, 原文, 译文
    error_occurred = Signal(str)
    finished_all = Signal()
    
    def __init__(self, plugin, texts, source_lang, target_lang, translator_type, api_key="", mode="smart"):
        super().__init__()
        self.plugin = plugin
        self.texts = texts
        self.source_lang = source_lang
        self.target_lang = target_lang
        self.translator_type = translator_type
        self.api_key = api_key
        self.mode = mode
        self.is_running = True

        # 重试配置
        self.max_retries = 3  # 最大重试次数
        self.retry_delay = 1.0  # 重试延迟（秒）
        self.failed_items = []  # 记录失败的项目用于重试
    
    def run(self):
        """执行翻译任务"""
        try:
            # 设置翻译器
            if not self.plugin.set_translator(self.translator_type, self.api_key):
                self.error_occurred.emit("设置翻译器失败")
                return
            
            total = len(self.texts)
            
            # 根据模式选择翻译策略
            if self.mode == "smart":
                # 智能模式：根据翻译器类型自动选择最佳策略
                if self.translator_type == "deepl":
                    # DeepL在智能模式下优先使用序列模式，更稳定
                    self.translate_sequential_mode(total)
                else:
                    self.translate_concurrent_mode(total)
            elif self.mode == "concurrent":
                # 强制使用并发模式
                self.translate_concurrent_mode(total)
            elif self.mode == "sequential":
                # 使用逐个翻译模式
                self.translate_sequential_mode(total)
            else:
                # 默认使用智能模式
                if self.translator_type == "deepl":
                    self.translate_sequential_mode(total)
                else:
                    self.translate_concurrent_mode(total)
            
            self.finished_all.emit()
        except Exception as e:
            self.error_occurred.emit(f"翻译过程出错: {str(e)}")
    

    
    def translate_concurrent_mode(self, total):
        """并发翻译模式（适用于Google等免费API）"""
        import concurrent.futures
        import threading
        
        processed = 0
        progress_lock = threading.Lock()
        
        def translate_and_update(index, text):
            nonlocal processed
            if not self.is_running:
                return None
            
            try:
                if text.strip():
                    translated = self.translate_text_with_retry(text, index)
                    if translated:
                        self.translation_completed.emit(index, text, translated)
                        result = translated
                    else:
                        self.error_occurred.emit(f"翻译失败（已重试{self.max_retries}次）: {text[:50]}...")
                        # 记录失败的项目
                        self.failed_items.append((index, text))
                        result = None
                else:
                    # 空文本直接返回，但仍需要发送信号以保持索引一致
                    self.translation_completed.emit(index, text, "")
                    result = ""
                    
                with progress_lock:
                    processed += 1
                    progress = int(processed / total * 100)
                    self.progress_updated.emit(progress)
                
                return result
                
            except Exception as e:
                self.error_occurred.emit(f"翻译第{index+1}条失败: {str(e)}")
                with progress_lock:
                    processed += 1
                    progress = int(processed / total * 100)
                    self.progress_updated.emit(progress)
                return None
        
        # 根据翻译器类型调整并发数
        if self.translator_type == "google":
            max_workers = 5
        elif self.translator_type == "deepl":
            max_workers = 2  # DeepL对并发更敏感，减少并发数
        else:
            max_workers = 3
        
        # 使用线程池进行并发翻译
        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有翻译任务
            future_to_index = {}
            
            for i, text in enumerate(self.texts):
                if not self.is_running:
                    break
                
                # 为每个文本创建任务，包括空文本
                future = executor.submit(translate_and_update, i, text)
                future_to_index[future] = i
                
                # 控制提交速度，特别是DeepL
                if self.translator_type == "deepl":
                    time.sleep(0.1)  # DeepL需要更长延迟
                else:
                    time.sleep(0.02)
            
            # 等待所有任务完成
            completed_count = 0
            for future in concurrent.futures.as_completed(future_to_index):
                if not self.is_running:
                    break
                
                try:
                    result = future.result(timeout=30)  # 设置超时
                    completed_count += 1
                except concurrent.futures.TimeoutError:
                    index = future_to_index[future]
                    self.error_occurred.emit(f"翻译第{index+1}条超时")
                    completed_count += 1
                except Exception as e:
                    index = future_to_index[future]
                    self.error_occurred.emit(f"翻译第{index+1}条异常: {str(e)}")
                    completed_count += 1
            
            print(f"并发翻译完成，处理了 {completed_count}/{len(self.texts)} 条")
    
    def translate_single_text(self, text):
        """翻译单个文本（回退方法，不带索引）"""
        translated = self.translate_text(text)
        if translated:
            # 注意：这里没有索引信息，可能导致匹配问题
            self.translation_completed.emit(-1, text, translated)
        else:
            self.error_occurred.emit(f"翻译失败: {text[:50]}...")
    
    def translate_single_text_with_index(self, index, text):
        """翻译单个文本（带索引）"""
        translated = self.translate_text(text)
        if translated:
            self.translation_completed.emit(index, text, translated)
        else:
            self.error_occurred.emit(f"翻译失败: {text[:50]}...")
    
    def translate_text_with_retry(self, text, index=None):
        """带重试机制的翻译方法"""
        for attempt in range(self.max_retries + 1):  # +1 因为第一次不算重试
            try:
                result = self.translate_text(text)
                if result:
                    if attempt > 0:
                        print(f"✅ 第{index+1 if index is not None else '?'}条字幕重试第{attempt}次成功")
                    return result
                else:
                    if attempt < self.max_retries:
                        print(f"⚠️ 第{index+1 if index is not None else '?'}条字幕翻译失败，{self.retry_delay}秒后重试第{attempt+1}次...")
                        time.sleep(self.retry_delay)
                    else:
                        print(f"❌ 第{index+1 if index is not None else '?'}条字幕翻译失败，已达到最大重试次数")

            except Exception as e:
                if attempt < self.max_retries:
                    print(f"⚠️ 第{index+1 if index is not None else '?'}条字幕翻译异常: {e}，{self.retry_delay}秒后重试第{attempt+1}次...")
                    time.sleep(self.retry_delay)
                else:
                    print(f"❌ 第{index+1 if index is not None else '?'}条字幕翻译异常: {e}，已达到最大重试次数")

        return None

    def translate_text(self, text):
        """使用插件翻译单个文本"""
        try:
            if self.plugin and self.plugin.current_translator:
                # 使用插件的翻译功能
                return self.plugin.current_translator.translate_text(
                    text, self.source_lang, self.target_lang
                )
            else:
                # 回退到基本翻译功能
                return self.fallback_translate(text)
        except Exception as e:
            print(f"插件翻译错误: {e}")
            return None
    
    def fallback_translate(self, text):
        """回退翻译功能（插件不可用时）"""
        try:
            if self.translator_type == "google":
                return self.translate_google_fallback(text)
            # 其他翻译服务需要API Key，在没有插件时暂不支持
            return None
        except Exception as e:
            print(f"回退翻译错误: {e}")
            return None
    
    def translate_google_fallback(self, text):
        """谷歌翻译回退实现"""
        try:
            import requests
            url = "https://translate.googleapis.com/translate_a/single"
            params = {
                'client': 'gtx',
                'sl': self.source_lang,
                'tl': self.target_lang,
                'dt': 't',
                'q': text
            }
            
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }
            
            response = requests.get(url, params=params, headers=headers, timeout=10)
            
            if response.status_code == 200:
                result = response.json()
                if result and len(result) > 0 and len(result[0]) > 0:
                    translated_text = ""
                    for item in result[0]:
                        if item[0]:
                            translated_text += item[0]
                    return translated_text
            
            return None
        except Exception as e:
            print(f"谷歌翻译回退错误: {e}")
            return None
    
    def translate_sequential_mode(self, total):
        """逐个翻译模式（最稳定）"""
        processed = 0
        
        for i, text in enumerate(self.texts):
            if not self.is_running:
                break
            
            try:
                if text.strip():
                    translated = self.translate_text_with_retry(text, i)
                    if translated:
                        self.translation_completed.emit(i, text, translated)
                    else:
                        self.error_occurred.emit(f"翻译第{i+1}条失败（已重试{self.max_retries}次）: {text[:50]}...")
                        # 记录失败的项目
                        self.failed_items.append((i, text))
                else:
                    # 空文本跳过，但仍需要发送信号以保持索引一致
                    self.translation_completed.emit(i, text, "")
                
                processed += 1
                progress = int(processed / total * 100)
                self.progress_updated.emit(progress)
                
                # 根据翻译器类型设置适当的延迟
                if self.translator_type == "deepl":
                    time.sleep(0.3)  # DeepL需要更长延迟避免超限
                elif self.translator_type == "google":
                    time.sleep(0.1)
                else:
                    time.sleep(0.2)
                    
            except Exception as e:
                self.error_occurred.emit(f"翻译第{i+1}条异常: {str(e)}")
                processed += 1
                progress = int(processed / total * 100)
                self.progress_updated.emit(progress)
        
        print(f"序列翻译完成，处理了 {processed}/{len(self.texts)} 条")
    
    def stop(self):
        """停止翻译"""
        self.is_running = False


class SubtitleTranslationDialog(QDialog):
    """字幕翻译对话框 - 基于插件系统"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("字幕翻译")
        self.setFixedSize(1400, 1000)
        
        # 强制设置调色板确保样式一致性
        self.force_dark_palette()
        
        # 初始化属性
        self.subtitle_items = []
        self.translated_items = []
        self.translation_worker = None
        self.file_list = []
        self.current_file_index = -1
        self.current_edit_index = -1
        self.is_editing = False
        self.original_content = None
        self.original_start_time = None
        self.original_end_time = None
        self.plugin = None  # 添加插件属性初始化
        
        # 设置样式
        self.setup_style()
        
        # 设置UI（必须在加载插件之前）
        self.setup_ui()
        
        # 设置拖拽
        self.setup_drag_drop()
        
        # 加载翻译插件（在UI创建之后）
        self.load_plugin()
        
        # 最后设置信号连接（确保所有控件都已创建）
        self.setup_connections()
    
    def force_dark_palette(self):
        """强制设置暗色调色板，确保样式一致性"""
        from PySide6.QtGui import QPalette, QColor
        from PySide6.QtWidgets import QApplication
        
        # 为当前对话框设置暗色调色板
        palette = QPalette()
        palette.setColor(QPalette.Window, QColor(27, 30, 36))
        palette.setColor(QPalette.WindowText, QColor(255, 255, 255))
        palette.setColor(QPalette.Base, QColor(20, 22, 26))
        palette.setColor(QPalette.AlternateBase, QColor(27, 30, 36))
        palette.setColor(QPalette.ToolTipBase, QColor(255, 255, 255))
        palette.setColor(QPalette.ToolTipText, QColor(255, 255, 255))
        palette.setColor(QPalette.Text, QColor(255, 255, 255))
        palette.setColor(QPalette.Button, QColor(27, 30, 36))
        palette.setColor(QPalette.ButtonText, QColor(255, 255, 255))
        palette.setColor(QPalette.BrightText, QColor(255, 0, 0))
        palette.setColor(QPalette.Link, QColor(43, 157, 124))
        palette.setColor(QPalette.Highlight, QColor(43, 157, 124))
        palette.setColor(QPalette.HighlightedText, QColor(255, 255, 255))
        
        # 应用调色板到对话框
        self.setPalette(palette)
        
        # 如果应用程序还没有设置过样式，也为应用程序设置
        app = QApplication.instance()
        if app and not hasattr(app, '_dark_palette_set'):
            app.setStyle('Fusion')
            app.setPalette(palette)
            app._dark_palette_set = True
    
    def setup_style(self):
        """设置对话框的整体样式"""
        self.setStyleSheet("""
            QDialog {
                background-color: #1B1E24;
                color: #FFFFFF;
                font-family: "Microsoft YaHei UI", "Microsoft YaHei", sans-serif;
            }
            QFrame {
                background-color: #14161A;
                border-radius: 8px;
            }
            QGroupBox {
                border: 2px solid #444444;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 5px;
                color: #FFFFFF;
                font-weight: bold;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                color: #2B9D7C;
            }
            /* 强制设置所有QComboBox的文本颜色 */
            QComboBox {
                color: #FFFFFF !important;
                background-color: #14161A !important;
            }
            QComboBox QAbstractItemView {
                color: #FFFFFF !important;
                background-color: #14161A !important;
                selection-color: #FFFFFF !important;
            }
            QComboBox QAbstractItemView::item {
                color: #FFFFFF !important;
            }
            /* 强制设置所有QLineEdit的文本颜色 */
            QLineEdit {
                color: #FFFFFF !important;
                background-color: #14161A !important;
            }
            /* 强制设置所有QLabel的文本颜色 */
            QLabel {
                color: #FFFFFF !important;
            }
        """)
    
    def load_plugin(self):
        """加载字幕翻译插件"""
        try:
            # 更新状态指示器为加载中
            if hasattr(self, 'status_indicator'):
                self.status_indicator.setStyleSheet("""
                    QLabel {
                        font-size: 12px;
                        color: #F39C12;
                        background: transparent;
                        border: none;
                        padding: 0px;
                        margin: 0px;
                    }
                """)
                self.status_indicator.setToolTip("正在加载插件...")
            
            # 尝试不同的导入路径
            try:
                from plugins.subtitle_translator import get_plugin
            except ImportError:
                import sys
                import os
                # 添加父目录到路径
                parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
                if parent_dir not in sys.path:
                    sys.path.insert(0, parent_dir)
                from plugins.subtitle_translator import get_plugin
            
            self.plugin = get_plugin()
            self.update_translator_options()
            print("字幕翻译插件加载成功")
            
            # 更新状态指示器为成功
            if hasattr(self, 'status_indicator'):
                self.status_indicator.setStyleSheet("""
                    QLabel {
                        font-size: 12px;
                        color: #2ECC71;
                        background: transparent;
                        border: none;
                        padding: 0px;
                        margin: 0px;
                    }
                """)
                self.status_indicator.setToolTip("插件加载成功")
                
        except ImportError as e:
            print(f"插件加载失败: {e}")
            # 插件加载失败时，添加基本的回退选项
            self.add_fallback_options()
            
            # 更新状态指示器为警告
            if hasattr(self, 'status_indicator'):
                self.status_indicator.setStyleSheet("""
                    QLabel {
                        font-size: 12px;
                        color: #E74C3C;
                        background: transparent;
                        border: none;
                        padding: 0px;
                        margin: 0px;
                    }
                """)
                self.status_indicator.setToolTip("插件加载失败，使用回退模式")
    
    def add_fallback_options(self):
        """添加回退选项（插件加载失败时）"""
        # 添加基本翻译器选项
        self.translator_combo.clear()
        self.translator_combo.addItem("谷歌翻译 (免费)", "google")
        self.translator_combo.addItem("微软翻译 (需要API Key)", "microsoft")
        self.translator_combo.addItem("DeepL Pro (需要API Key)", "deepl")
        
        # 添加基本语言选项
        languages = [
            ("自动检测", "auto"),
            ("中文", "zh"),
            ("英语", "en"),
            ("日语", "ja"),
            ("韩语", "ko"),
            ("法语", "fr"),
            ("德语", "de"),
            ("西班牙语", "es"),
            ("俄语", "ru"),
            ("阿拉伯语", "ar"),
            ("泰语", "th")
        ]
        
        self.source_lang_combo.clear()
        self.target_lang_combo.clear()
        
        for name, code in languages:
            self.source_lang_combo.addItem(f"{name} ({code})", code)
            self.target_lang_combo.addItem(f"{name} ({code})", code)
        
        # 设置默认值
        self.source_lang_combo.setCurrentText("自动检测 (auto)")
        self.target_lang_combo.setCurrentText("中文 (zh)")
    
    def update_translator_options(self):
        """更新翻译器选项"""
        if not self.plugin:
            return
        
        # 获取插件支持的翻译器
        translators = self.plugin.get_available_translators()
        
        # 清空现有选项并添加新选项
        self.translator_combo.clear()
        for key, name in translators.items():
            self.translator_combo.addItem(name, key)
        
        # 更新语言选项
        self.update_language_options()
    
    def update_language_options(self):
        """更新语言选项"""
        if not self.plugin:
            return
        
        # 设置默认翻译器获取语言列表
        self.plugin.set_translator("google")
        languages = self.plugin.get_supported_languages()
        
        # 保存当前选择
        current_source = self.source_lang_combo.currentText()
        current_target = self.target_lang_combo.currentText()
        
        # 清空并重新填充
        self.source_lang_combo.clear()
        self.target_lang_combo.clear()
        
        for code, name in languages.items():
            self.source_lang_combo.addItem(f"{name} ({code})", code)
            self.target_lang_combo.addItem(f"{name} ({code})", code)
        
        # 尝试恢复之前的选择，如果没有就设置默认值
        if current_source:
            index = self.source_lang_combo.findText(current_source)
            if index >= 0:
                self.source_lang_combo.setCurrentIndex(index)
            else:
                self.source_lang_combo.setCurrentText("自动检测 (auto)")
        else:
            self.source_lang_combo.setCurrentText("自动检测 (auto)")
        
        if current_target:
            index = self.target_lang_combo.findText(current_target)
            if index >= 0:
                self.target_lang_combo.setCurrentIndex(index)
            else:
                self.target_lang_combo.setCurrentText("中文 (zh)")
        else:
            self.target_lang_combo.setCurrentText("中文 (zh)")
    
    def setup_ui(self):
        """设置UI界面"""
        self.setWindowTitle("字幕翻译器 - FlipTalk AI")
        self.setFixedSize(1400, 1000)  # 增加高度以适应编辑区域
        self.setModal(True)
        
        # 启用拖拽功能
        self.setAcceptDrops(True)
        
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(15, 10, 15, 15)
        main_layout.setSpacing(8)
        
        # 创建标题区域（最小空间）
        title_area = self.create_title_area()
        main_layout.addWidget(title_area)
        
        # 创建三栏内容区域（占用绝大部分空间）
        content_area = self.create_three_column_content_area()
        main_layout.addWidget(content_area, 1)  # 设置拉伸因子为1，让内容区域占用剩余空间
        
        # 创建底部控制区域（最小空间）
        control_area = self.create_control_area()
        main_layout.addWidget(control_area)
        
        # 确保默认显示上传区域（当没有文件时）
        if hasattr(self, 'file_stack') and not self.file_list:
            self.switch_to_upload_area()
    
    def create_title_area(self):
        """创建标题区域"""
        # 创建外层容器
        container = QFrame()
        container.setFixedHeight(80)
        container.setStyleSheet("QFrame { background: transparent; border: none; }")
        
        container_layout = QVBoxLayout(container)
        container_layout.setContentsMargins(0, 8, 0, 8)
        container_layout.setSpacing(0)
        
        # 创建卡片式标题框架
        title_frame = QFrame()
        title_frame.setMinimumHeight(55)  # 设置最小高度，让它能够填充容器空间
        title_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(52, 152, 219, 0.08),
                    stop:0.5 rgba(52, 152, 219, 0.05),
                    stop:1 rgba(52, 152, 219, 0.08));
                border: 1px solid rgba(52, 152, 219, 0.25);
                border-radius: 12px;
                margin: 0px;
                padding: 0px;
            }
        """)
        
        title_layout = QHBoxLayout(title_frame)
        title_layout.setContentsMargins(20, 12, 20, 12)
        title_layout.setSpacing(15)
        
        # 装饰性左侧图标容器
        icon_container = QFrame()
        icon_container.setFixedSize(30, 30)
        icon_container.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(52, 152, 219, 0.2),
                    stop:1 rgba(46, 204, 113, 0.2));
                border: 1px solid rgba(52, 152, 219, 0.3);
                border-radius: 15px;
            }
        """)
        
        icon_layout = QHBoxLayout(icon_container)
        icon_layout.setContentsMargins(0, 0, 0, 0)
        
        # 精美的图标
        icon_label = QLabel("🌐")
        icon_label.setStyleSheet("""
            QLabel {
                font-size: 18px; 
                color: #3498DB;
                background: transparent;
                border: none;
                padding: 0px;
                margin: 0px;
            }
        """)
        icon_label.setAlignment(Qt.AlignCenter)
        icon_layout.addWidget(icon_label)
        
        title_layout.addWidget(icon_container)
        
        # 主标题
        title_label = QLabel("字幕翻译器")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 16px; 
                font-weight: bold; 
                color: #FFFFFF;
                background: transparent;
                border: none;
                padding: 0px;
                margin: 0px;
                font-family: 'Microsoft YaHei UI', 'Microsoft YaHei', 'SimHei', sans-serif;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
            }
        """)
        title_label.setAlignment(Qt.AlignLeft | Qt.AlignVCenter)
        title_layout.addWidget(title_label)
        
        # 装饰性分隔线
        separator_line = QFrame()
        separator_line.setFixedSize(1, 16)
        separator_line.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 transparent,
                    stop:0.3 rgba(52, 152, 219, 0.4),
                    stop:0.7 rgba(52, 152, 219, 0.4),
                    stop:1 transparent);
                border: none;
                border-radius: 0px;
            }
        """)
        title_layout.addWidget(separator_line)
        
        # 副标题
        subtitle_label = QLabel("智能翻译，支持拖拽")
        subtitle_label.setStyleSheet("""
            QLabel {
                font-size: 11px; 
                color: #8FA8B2;
                background: transparent;
                border: none;
                padding: 0px;
                margin: 0px;
                font-family: 'Microsoft YaHei UI', 'Microsoft YaHei', 'SimHei', sans-serif;
                font-weight: normal;
            }
        """)
        subtitle_label.setAlignment(Qt.AlignLeft | Qt.AlignVCenter)
        title_layout.addWidget(subtitle_label)
        
        title_layout.addStretch()
        
        # 装饰性星星图标
        star_label = QLabel("✨")
        star_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                color: #F39C12;
                background: transparent;
                border: none;
                padding: 0px;
                margin: 0px;
            }
        """)
        star_label.setAlignment(Qt.AlignCenter)
        title_layout.addWidget(star_label)
        
        # 状态指示器容器
        status_container = QFrame()
        status_container.setFixedSize(18, 18)
        status_container.setStyleSheet("""
            QFrame {
                background: rgba(46, 204, 113, 0.15);
                border: 1px solid rgba(46, 204, 113, 0.3);
                border-radius: 9px;
            }
        """)
        
        status_layout = QHBoxLayout(status_container)
        status_layout.setContentsMargins(0, 0, 0, 0)
        
        # 添加一个状态指示器
        self.status_indicator = QLabel("●")
        self.status_indicator.setStyleSheet("""
            QLabel {
                font-size: 8px;
                color: #2ECC71;
                background: transparent;
                border: none;
                padding: 0px;
                margin: 0px;
            }
        """)
        self.status_indicator.setAlignment(Qt.AlignCenter)
        self.status_indicator.setToolTip("系统就绪")
        status_layout.addWidget(self.status_indicator)
        
        title_layout.addWidget(status_container)
        
        container_layout.addWidget(title_frame, 1)  # 添加拉伸因子，让title_frame填充剩余空间
        
        return container
    
    def create_three_column_content_area(self):
        """创建三栏内容区域"""
        # 创建水平分割器，用于三栏布局
        content_splitter = QSplitter(Qt.Horizontal)
        content_splitter.setChildrenCollapsible(False)
        
        # 左栏：翻译配置（占1/4空间）
        config_area = self.create_config_area()
        content_splitter.addWidget(config_area)
        
        # 右侧区域：包含字幕展示和编辑区域
        right_area = self.create_subtitle_display_and_edit_area()
        content_splitter.addWidget(right_area)
        
        # 设置分割比例：1:3 (配置:字幕区域)
        # 假设总宽度为1400，那么比例为：350:1050
        content_splitter.setSizes([350, 1050])
        
        return content_splitter
    
    def create_subtitle_display_and_edit_area(self):
        """创建字幕展示和编辑区域"""
        # 创建主容器
        main_widget = QWidget()
        main_layout = QVBoxLayout(main_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(8)
        
        # 上部分：原文字幕和翻译结果的水平布局
        subtitle_splitter = QSplitter(Qt.Horizontal)
        subtitle_splitter.setChildrenCollapsible(False)
        
        # 中栏：原文字幕（占1/2空间）
        original_area = self.create_original_area()
        subtitle_splitter.addWidget(original_area)
        
        # 右栏：翻译结果（占1/2空间）
        translated_area = self.create_translated_area()
        subtitle_splitter.addWidget(translated_area)
        
        # 设置分割比例：1:1
        subtitle_splitter.setSizes([525, 525])
        
        # 添加到主布局
        main_layout.addWidget(subtitle_splitter, 2)  # 字幕表格占主要空间
        
        # 下部分：字幕编辑区域
        edit_area = self.create_subtitle_edit_area()
        main_layout.addWidget(edit_area, 1)  # 编辑区域占次要空间
        
        return main_widget
    
    def create_subtitle_edit_area(self):
        """创建字幕编辑区域"""
        edit_frame = QGroupBox("字幕内容编辑")
        edit_frame.setStyleSheet("""
            QGroupBox {
                border: 2px solid #444444;
                border-radius: 17px;
                margin-top: 10px;
                padding-top: 8px;
                color: #FFFFFF;
                font-weight: bold;
                background-color: #1B1E24;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 8px 0 8px;
                color: #2B9D7C;
                font-size: 14px;
            }
        """)
        edit_layout = QVBoxLayout(edit_frame)
        edit_layout.setContentsMargins(15, 15, 15, 15)
        edit_layout.setSpacing(10)
        
        # 编辑工具栏
        toolbar_layout = QHBoxLayout()
        toolbar_layout.setSpacing(10)
        
        # 当前编辑的字幕信息
        self.edit_info_label = QLabel("未选择字幕")
        self.edit_info_label.setStyleSheet("""
            QLabel {
                font-size: 12px;
                color: #FFFFFF;
                font-weight: 500;
                background-color: rgba(43, 157, 124, 0.2);
                padding: 4px 8px;
                border-radius: 4px;
            }
        """)
        toolbar_layout.addWidget(self.edit_info_label)
        
        toolbar_layout.addStretch()
        
        # 编辑模式切换
        mode_label = QLabel("编辑模式:")
        mode_label.setStyleSheet("""
            QLabel {
                font-size: 12px;
                color: #FFFFFF;
                font-weight: bold;
            }
        """)
        toolbar_layout.addWidget(mode_label)
        
        self.edit_mode_combo = QComboBox()
        self.edit_mode_combo.addItem("原文编辑", "original")
        self.edit_mode_combo.addItem("翻译编辑", "translated")
        self.edit_mode_combo.setFixedWidth(120)
        self.edit_mode_combo.setStyleSheet("""
            QComboBox {
                background-color: #14161A !important;
                color: #FFFFFF !important;
                font-size: 12px;
                border: 2px solid #444444;
                border-radius: 8px;
                padding: 4px 8px;
            }
            QComboBox:hover {
                border-color: #2B9D7C;
                color: #FFFFFF !important;
            }
            QComboBox:focus {
                border-color: #00CC55;
                color: #FFFFFF !important;
            }
            QComboBox::drop-down {
                border: none;
                background: #2B9D7C;
                border-radius: 4px;
            }
            QComboBox::down-arrow {
                image: none;
                border: 2px solid #FFFFFF;
                width: 6px;
                height: 6px;
                border-top: none;
                border-right: none;
            }
            QComboBox QAbstractItemView {
                background-color: #14161A !important;
                color: #FFFFFF !important;
                border: 1px solid #2B9D7C;
                selection-background-color: #2B9D7C !important;
                selection-color: #FFFFFF !important;
            }
            QComboBox QAbstractItemView::item {
                color: #FFFFFF !important;
                background-color: transparent;
                padding: 4px 8px;
            }
            QComboBox QAbstractItemView::item:selected {
                background-color: #2B9D7C !important;
                color: #FFFFFF !important;
            }
            QComboBox QAbstractItemView::item:hover {
                background-color: rgba(43, 157, 124, 0.15) !important;
                color: #FFFFFF !important;
            }
        """)
        toolbar_layout.addWidget(self.edit_mode_combo)
        
        edit_layout.addLayout(toolbar_layout)
        
        # 时间编辑区域 - 简化为水平布局
        time_layout = QHBoxLayout()
        
        # 开始时间
        start_time_label = QLabel("开始时间:")
        start_time_label.setStyleSheet("""
            QLabel {
                font-weight: bold; 
                color: #FFFFFF;
                font-size: 13px;
                background-color: rgba(43, 157, 124, 0.3);
                padding: 2px 6px;
                border-radius: 4px;
            }
        """)
        time_layout.addWidget(start_time_label)
        
        self.start_time_edit = QLineEdit()
        self.start_time_edit.setPlaceholderText("00:00:00,000")
        self.start_time_edit.setFixedWidth(120)
        self.start_time_edit.setStyleSheet("""
            QLineEdit {
                background-color: #14161A;
                color: #FFFFFF;
                font-size: 12px;
                border: 2px solid #444444;
                border-radius: 8px;
                padding: 6px 8px;
            }
            QLineEdit:hover {
                border-color: #2B9D7C;
            }
            QLineEdit:focus {
                border-color: #00CC55;
                background-color: #1B1E24;
            }
            QLineEdit::placeholder {
                color: rgba(255, 255, 255, 0.4);
            }
        """)
        time_layout.addWidget(self.start_time_edit)
        
        # 结束时间
        end_time_label = QLabel("结束时间:")
        end_time_label.setStyleSheet("""
            QLabel {
                font-weight: bold; 
                color: #FFFFFF;
                font-size: 13px;
                background-color: rgba(43, 157, 124, 0.3);
                padding: 2px 6px;
                border-radius: 4px;
            }
        """)
        time_layout.addWidget(end_time_label)
        
        self.end_time_edit = QLineEdit()
        self.end_time_edit.setPlaceholderText("00:00:00,000")
        self.end_time_edit.setFixedWidth(120)
        self.end_time_edit.setStyleSheet("""
            QLineEdit {
                background-color: #14161A;
                color: #FFFFFF;
                font-size: 12px;
                border: 2px solid #444444;
                border-radius: 8px;
                padding: 6px 8px;
            }
            QLineEdit:hover {
                border-color: #2B9D7C;
            }
            QLineEdit:focus {
                border-color: #00CC55;
                background-color: #1B1E24;
            }
            QLineEdit::placeholder {
                color: rgba(255, 255, 255, 0.4);
            }
        """)
        time_layout.addWidget(self.end_time_edit)
        
        time_layout.addStretch()
        
        edit_layout.addLayout(time_layout)
        
        # 内容编辑区域
        content_layout = QHBoxLayout()
        
        # 内容编辑框
        self.content_edit = QTextEdit()
        self.content_edit.setMaximumHeight(100)
        self.content_edit.setStyleSheet("""
            QTextEdit {
                background-color: #14161A;
                color: #FFFFFF;
                font-size: 13px;
                border: 2px solid #444444;
                border-radius: 8px;
                padding: 8px;
                selection-background-color: rgba(43, 157, 124, 0.3);
            }
            QTextEdit:focus {
                border-color: #2B9D7C;
                background-color: #1B1E24;
            }
        """)
        content_layout.addWidget(self.content_edit)
        
        # 右侧控制按钮
        button_layout = QVBoxLayout()
        
        self.char_count_label = QLabel("0 字符")
        self.char_count_label.setStyleSheet("""
            QLabel {
                background-color: rgba(43, 157, 124, 0.8);
                color: #FFFFFF;
                padding: 4px 8px;
                border-radius: 10px;
                font-size: 10px;
                font-weight: bold;
            }
        """)
        button_layout.addWidget(self.char_count_label)
        
        self.apply_edit_btn = QPushButton("应用修改")
        self.apply_edit_btn.setEnabled(False)
        self.apply_edit_btn.setFixedSize(80, 32)
        self.apply_edit_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #2B9D7C, stop:1 #00CC55);
                color: #FFFFFF;
                border: none;
                border-radius: 8px;
                font-weight: bold;
                font-size: 11px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #00CC55, stop:1 #2B9D7C);
            }
            QPushButton:pressed {
                background-color: #1E8B6B;
            }
            QPushButton:disabled {
                background-color: #444444;
                color: rgba(255, 255, 255, 0.4);
            }
        """)
        button_layout.addWidget(self.apply_edit_btn)
        
        self.reset_edit_btn = QPushButton("重置")
        self.reset_edit_btn.setEnabled(False)
        self.reset_edit_btn.setFixedSize(80, 32)
        self.reset_edit_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #95A5A6, stop:1 #7F8C8D);
                color: #FFFFFF;
                border: none;
                border-radius: 8px;
                font-weight: bold;
                font-size: 11px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #7F8C8D, stop:1 #95A5A6);
            }
            QPushButton:pressed {
                background-color: #6C7B7D;
            }
            QPushButton:disabled {
                background-color: #444444;
                color: rgba(255, 255, 255, 0.4);
            }
        """)
        button_layout.addWidget(self.reset_edit_btn)
        
        self.clear_content_btn = QPushButton("清空")
        self.clear_content_btn.setFixedSize(80, 32)
        self.clear_content_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #E74C3C, stop:1 #C0392B);
                color: #FFFFFF;
                border: none;
                border-radius: 8px;
                font-weight: bold;
                font-size: 11px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #C0392B, stop:1 #E74C3C);
            }
            QPushButton:pressed {
                background-color: #A93226;
            }
        """)
        button_layout.addWidget(self.clear_content_btn)
        
        button_layout.addStretch()
        
        content_layout.addLayout(button_layout)
        edit_layout.addLayout(content_layout)
        
        return edit_frame
    
    def create_config_area(self):
        """创建配置区域"""
        # 创建主容器框架
        main_container = QFrame()
        main_container.setMinimumWidth(300)
        
        container_layout = QVBoxLayout(main_container)
        container_layout.setContentsMargins(0, 0, 0, 0)
        container_layout.setSpacing(15)
        
        # 翻译设置组
        config_frame = QGroupBox("翻译设置")
        config_frame.setStyleSheet("""
            QGroupBox {
                border: 2px solid #444444;
                border-radius: 17px;
                margin-top: 10px;
                padding-top: 8px;
                color: #FFFFFF;
                font-weight: bold;
                background-color: #1B1E24;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 8px 0 8px;
                color: #2B9D7C;
                font-size: 14px;
            }
        """)
        main_layout = QVBoxLayout(config_frame)
        main_layout.setContentsMargins(15, 8, 15, 12)
        main_layout.setSpacing(10)
        
        # 翻译服务
        service_label = QLabel("翻译服务:")
        service_label.setStyleSheet("""
            QLabel {
                font-weight: bold;
                font-size: 14px;
                color: #2B9D7C;
                margin-bottom: 2px;
            }
        """)
        main_layout.addWidget(service_label)
        
        self.translator_combo = QComboBox()
        self.translator_combo.setFixedHeight(35)
        self.translator_combo.setStyleSheet("""
            QComboBox {
                background-color: #14161A !important;
                color: #FFFFFF !important;
                font-size: 13px;
                border: 2px solid #444444;
                border-radius: 17px;
                padding: 8px 12px;
                padding-right: 30px;
            }
            QComboBox:hover {
                border-color: #2B9D7C;
                color: #FFFFFF !important;
            }
            QComboBox:focus {
                border-color: #00CC55;
                outline: none;
                color: #FFFFFF !important;
            }
            QComboBox::drop-down {
                subcontrol-origin: padding;
                subcontrol-position: top right;
                width: 25px;
                border-left: 1px solid #444444;
                border-top-right-radius: 17px;
                border-bottom-right-radius: 17px;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2B9D7C, stop:1 #14161A);
            }
            QComboBox::down-arrow {
                image: none;
                border: 2px solid #FFFFFF;
                width: 6px;
                height: 6px;
                border-top: none;
                border-right: none;
                transform: rotate(-45deg);
                margin: 2px;
            }
            QComboBox QAbstractItemView {
                border: 2px solid #2B9D7C;
                border-radius: 8px;
                background-color: #14161A !important;
                selection-background-color: rgba(43, 157, 124, 0.3) !important;
                selection-color: #FFFFFF !important;
                color: #FFFFFF !important;
                padding: 4px;
            }
            QComboBox QAbstractItemView::item {
                padding: 8px 12px;
                border-radius: 4px;
                margin: 1px;
                color: #FFFFFF !important;
                background-color: transparent;
            }
            QComboBox QAbstractItemView::item:selected {
                background-color: rgba(43, 157, 124, 0.3) !important;
                color: #FFFFFF !important;
            }
            QComboBox QAbstractItemView::item:hover {
                background-color: rgba(43, 157, 124, 0.15) !important;
                color: #FFFFFF !important;
            }
        """)
        main_layout.addWidget(self.translator_combo)
        
        # 源语言
        source_label = QLabel("源语言:")
        source_label.setStyleSheet("""
            QLabel {
                font-weight: bold;
                font-size: 14px;
                color: #2B9D7C;
                margin-bottom: 2px;
            }
        """)
        main_layout.addWidget(source_label)
        
        self.source_lang_combo = QComboBox()
        self.source_lang_combo.setFixedHeight(35)
        self.source_lang_combo.setStyleSheet("""
            QComboBox {
                background-color: #14161A !important;
                color: #FFFFFF !important;
                font-size: 13px;
                border: 2px solid #444444;
                border-radius: 17px;
                padding: 8px 12px;
                padding-right: 30px;
            }
            QComboBox:hover {
                border-color: #2B9D7C;
                color: #FFFFFF !important;
            }
            QComboBox:focus {
                border-color: #00CC55;
                outline: none;
                color: #FFFFFF !important;
            }
            QComboBox::drop-down {
                subcontrol-origin: padding;
                subcontrol-position: top right;
                width: 25px;
                border-left: 1px solid #444444;
                border-top-right-radius: 17px;
                border-bottom-right-radius: 17px;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2B9D7C, stop:1 #14161A);
            }
            QComboBox::down-arrow {
                image: none;
                border: 2px solid #FFFFFF;
                width: 6px;
                height: 6px;
                border-top: none;
                border-right: none;
                transform: rotate(-45deg);
                margin: 2px;
            }
            QComboBox QAbstractItemView {
                border: 2px solid #2B9D7C;
                border-radius: 8px;
                background-color: #14161A !important;
                selection-background-color: rgba(43, 157, 124, 0.3) !important;
                selection-color: #FFFFFF !important;
                color: #FFFFFF !important;
                padding: 4px;
            }
            QComboBox QAbstractItemView::item {
                padding: 8px 12px;
                border-radius: 4px;
                margin: 1px;
                color: #FFFFFF !important;
                background-color: transparent;
            }
            QComboBox QAbstractItemView::item:selected {
                background-color: rgba(43, 157, 124, 0.3) !important;
                color: #FFFFFF !important;
            }
            QComboBox QAbstractItemView::item:hover {
                background-color: rgba(43, 157, 124, 0.15) !important;
                color: #FFFFFF !important;
            }
        """)
        main_layout.addWidget(self.source_lang_combo)
        
        # 目标语言
        target_label = QLabel("目标语言:")
        target_label.setStyleSheet("""
            QLabel {
                font-weight: bold;
                font-size: 14px;
                color: #2B9D7C;
                margin-bottom: 2px;
            }
        """)
        main_layout.addWidget(target_label)
        
        self.target_lang_combo = QComboBox()
        self.target_lang_combo.setFixedHeight(35)
        self.target_lang_combo.setStyleSheet("""
            QComboBox {
                background-color: #14161A !important;
                color: #FFFFFF !important;
                font-size: 13px;
                border: 2px solid #444444;
                border-radius: 17px;
                padding: 8px 12px;
                padding-right: 30px;
            }
            QComboBox:hover {
                border-color: #2B9D7C;
                color: #FFFFFF !important;
            }
            QComboBox:focus {
                border-color: #00CC55;
                outline: none;
                color: #FFFFFF !important;
            }
            QComboBox::drop-down {
                subcontrol-origin: padding;
                subcontrol-position: top right;
                width: 25px;
                border-left: 1px solid #444444;
                border-top-right-radius: 17px;
                border-bottom-right-radius: 17px;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2B9D7C, stop:1 #14161A);
            }
            QComboBox::down-arrow {
                image: none;
                border: 2px solid #FFFFFF;
                width: 6px;
                height: 6px;
                border-top: none;
                border-right: none;
                transform: rotate(-45deg);
                margin: 2px;
            }
            QComboBox QAbstractItemView {
                border: 2px solid #2B9D7C;
                border-radius: 8px;
                background-color: #14161A !important;
                selection-background-color: rgba(43, 157, 124, 0.3) !important;
                selection-color: #FFFFFF !important;
                color: #FFFFFF !important;
                padding: 4px;
            }
            QComboBox QAbstractItemView::item {
                padding: 8px 12px;
                border-radius: 4px;
                margin: 1px;
                color: #FFFFFF !important;
                background-color: transparent;
            }
            QComboBox QAbstractItemView::item:selected {
                background-color: rgba(43, 157, 124, 0.3) !important;
                color: #FFFFFF !important;
            }
            QComboBox QAbstractItemView::item:hover {
                background-color: rgba(43, 157, 124, 0.15) !important;
                color: #FFFFFF !important;
            }
        """)
        main_layout.addWidget(self.target_lang_combo)
        
        # API Key输入
        api_key_label = QLabel("API Key:")
        api_key_label.setStyleSheet("""
            QLabel {
                font-weight: bold;
                font-size: 14px;
                color: #2B9D7C;
                margin: 8px 0px 2px 0px;
            }
        """)
        main_layout.addWidget(api_key_label)
        
        self.api_key_edit = QLineEdit()
        self.api_key_edit.setPlaceholderText("输入API Key（部分服务需要）")
        self.api_key_edit.setEchoMode(QLineEdit.Password)
        self.api_key_edit.setFixedHeight(35)
        self.api_key_edit.setStyleSheet("""
            QLineEdit {
                background-color: #14161A;
                color: #FFFFFF;
                font-size: 13px;
                border: 2px solid #444444;
                border-radius: 17px;
                padding: 8px 12px;
            }
            QLineEdit:hover {
                border-color: #2B9D7C;
            }
            QLineEdit:focus {
                border-color: #00CC55;
                outline: none;
                background-color: #1B1E24;
            }
            QLineEdit::placeholder {
                color: rgba(255, 255, 255, 0.4);
            }
        """)
        main_layout.addWidget(self.api_key_edit)
        
        # 翻译模式选择
        mode_label = QLabel("翻译模式:")
        mode_label.setStyleSheet("""
            QLabel {
                font-weight: bold;
                font-size: 14px;
                color: #2B9D7C;
                margin: 8px 0px 2px 0px;
            }
        """)
        main_layout.addWidget(mode_label)
        
        self.translation_mode_combo = QComboBox()
        self.translation_mode_combo.addItem("智能模式 (自动选择最佳策略)", "smart")
        self.translation_mode_combo.addItem("并发模式 (适合免费API)", "concurrent")
        self.translation_mode_combo.addItem("逐个模式 (最稳定)", "sequential")
        self.translation_mode_combo.setCurrentIndex(0)
        self.translation_mode_combo.setFixedHeight(35)
        self.translation_mode_combo.setStyleSheet("""
            QComboBox {
                background-color: #14161A !important;
                color: #FFFFFF !important;
                font-size: 13px;
                border: 2px solid #444444;
                border-radius: 17px;
                padding: 8px 12px;
                padding-right: 30px;
            }
            QComboBox:hover {
                border-color: #2B9D7C;
                color: #FFFFFF !important;
            }
            QComboBox:focus {
                border-color: #00CC55;
                outline: none;
                color: #FFFFFF !important;
            }
            QComboBox::drop-down {
                subcontrol-origin: padding;
                subcontrol-position: top right;
                width: 25px;
                border-left: 1px solid #444444;
                border-top-right-radius: 17px;
                border-bottom-right-radius: 17px;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2B9D7C, stop:1 #14161A);
            }
            QComboBox::down-arrow {
                image: none;
                border: 2px solid #FFFFFF;
                width: 6px;
                height: 6px;
                border-top: none;
                border-right: none;
                transform: rotate(-45deg);
                margin: 2px;
            }
            QComboBox QAbstractItemView {
                border: 2px solid #2B9D7C;
                border-radius: 8px;
                background-color: #14161A !important;
                selection-background-color: rgba(43, 157, 124, 0.3) !important;
                selection-color: #FFFFFF !important;
                color: #FFFFFF !important;
                padding: 4px;
            }
            QComboBox QAbstractItemView::item {
                padding: 8px 12px;
                border-radius: 4px;
                margin: 1px;
                color: #FFFFFF !important;
                background-color: transparent;
            }
            QComboBox QAbstractItemView::item:selected {
                background-color: rgba(43, 157, 124, 0.3) !important;
                color: #FFFFFF !important;
            }
            QComboBox QAbstractItemView::item:hover {
                background-color: rgba(43, 157, 124, 0.15) !important;
                color: #FFFFFF !important;
            }
        """)
        
        # 添加工具提示
        self.translation_mode_combo.setToolTip(
            "智能模式：根据翻译服务自动选择最佳策略\n"
            "并发模式：多线程并发翻译（适合Google等免费API）\n"
            "逐个模式：逐个翻译，最稳定但较慢"
        )
        main_layout.addWidget(self.translation_mode_combo)
        
        # 文件列表组 - 独立的组（放在翻译设置之前）
        file_group = QGroupBox("文件列表")
        file_group.setStyleSheet("""
            QGroupBox {
                border: 2px solid #444444;
                border-radius: 17px;
                margin-top: 10px;
                padding-top: 8px;
                color: #FFFFFF;
                font-weight: bold;
                background-color: #1B1E24;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 8px 0 8px;
                color: #2B9D7C;
                font-size: 14px;
            }
        """)
        file_layout = QVBoxLayout(file_group)
        file_layout.setContentsMargins(15, 20, 15, 15)
        file_layout.setSpacing(10)
        
        # 创建堆叠控件来切换显示上传区域或文件列表
        self.file_stack = QStackedWidget()
        self.file_stack.setMinimumHeight(200)
        self.file_stack.setMaximumHeight(280)
        
        # 创建拖拽上传区域
        self.upload_area = self.create_upload_area()
        self.file_stack.addWidget(self.upload_area)  # 索引 0
        
        # 创建文件列表控件
        self.file_list_widget = QListWidget()
        self.file_list_widget.setSelectionMode(QListWidget.ExtendedSelection)
        self.file_list_widget.setStyleSheet("""
            QListWidget {
                background-color: #14161A;
                color: #FFFFFF;
                border: 2px solid #444444;
                border-radius: 17px;
                padding: 8px;
                font-size: 13px;
            }
            QListWidget::item {
                background-color: #1B1E24;
                border: 1px solid #444444;
                border-radius: 8px;
                padding: 8px 12px;
                margin: 2px;
            }
            QListWidget::item:hover {
                background-color: rgba(43, 157, 124, 0.15);
                border-color: #2B9D7C;
            }
            QListWidget::item:selected {
                background-color: rgba(43, 157, 124, 0.3);
                border-color: #00CC55;
            }
        """)
        self.file_stack.addWidget(self.file_list_widget)  # 索引 1
        
        file_layout.addWidget(self.file_stack)
        
        # 文件操作按钮
        file_buttons_layout = QHBoxLayout()
        file_buttons_layout.setSpacing(8)
        
        # 添加文件按钮
        self.add_files_btn = QPushButton("添加文件")
        self.add_files_btn.setFixedHeight(35)
        self.add_files_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #2B9D7C, stop:1 #00CC55);
                color: #FFFFFF;
                border: none;
                border-radius: 17px;
                padding: 8px 12px;
                font-weight: bold;
                font-size: 13px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #00CC55, stop:1 #2B9D7C);
            }
            QPushButton:pressed {
                transform: translateY(0px);
            }
            QPushButton:disabled {
                background-color: #444444;
                color: rgba(255, 255, 255, 0.4);
            }
        """)
        file_buttons_layout.addWidget(self.add_files_btn)
        
        # 清空列表按钮
        self.clear_files_btn = QPushButton("清空")
        self.clear_files_btn.setFixedHeight(35)
        self.clear_files_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #E74C3C, stop:1 #C0392B);
                color: #FFFFFF;
                border: none;
                border-radius: 17px;
                padding: 8px 12px;
                font-weight: bold;
                font-size: 13px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #C0392B, stop:1 #E74C3C);
            }
            QPushButton:pressed {
                transform: translateY(0px);
            }
            QPushButton:disabled {
                background-color: #444444;
                color: rgba(255, 255, 255, 0.4);
            }
        """)
        file_buttons_layout.addWidget(self.clear_files_btn)
        
        file_buttons_layout.addStretch()
        file_layout.addLayout(file_buttons_layout)
        
        # 将文件列表组添加到主容器（第一个添加）
        container_layout.addWidget(file_group)
        
        # 将翻译设置组添加到主容器（第二个添加）
        container_layout.addWidget(config_frame)
        
        return main_container
    
    def create_upload_area(self):
        """创建拖拽上传区域"""
        upload_widget = QWidget()
        upload_widget.setAcceptDrops(True)
        
        # 设置样式
        upload_widget.setStyleSheet("""
            QWidget {
                background-color: #14161A;
                border: 2px dashed #2B9D7C;
                border-radius: 17px;
                color: #FFFFFF;
            }
            QWidget:hover {
                border-color: #00CC55;
                background-color: rgba(43, 157, 124, 0.05);
            }
        """)
        
        # 创建布局
        layout = QVBoxLayout(upload_widget)
        layout.setContentsMargins(20, 30, 20, 30)
        layout.setSpacing(15)
        layout.setAlignment(Qt.AlignCenter)
        
        # 图标（使用文本模拟）
        icon_label = QLabel("📁")
        icon_label.setAlignment(Qt.AlignCenter)
        icon_label.setStyleSheet("""
            QLabel {
                font-size: 48px;
                border: none;
                color: #2B9D7C;
                background: transparent;
            }
        """)
        layout.addWidget(icon_label)
        
        # 主要文字
        main_text = QLabel("点击选择文件或拖拽文件")
        main_text.setAlignment(Qt.AlignCenter)
        main_text.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #FFFFFF;
                border: none;
                background: transparent;
            }
        """)
        layout.addWidget(main_text)
        
        # 辅助文字
        sub_text = QLabel("支持 .srt .vtt .ass .ssa 格式")
        sub_text.setAlignment(Qt.AlignCenter)
        sub_text.setStyleSheet("""
            QLabel {
                font-size: 12px;
                color: rgba(255, 255, 255, 0.7);
                border: none;
                background: transparent;
            }
        """)
        layout.addWidget(sub_text)
        
        # 添加拖拽事件处理
        upload_widget.dragEnterEvent = self.upload_area_drag_enter_event
        upload_widget.dragMoveEvent = self.upload_area_drag_move_event
        upload_widget.dropEvent = self.upload_area_drop_event
        upload_widget.mousePressEvent = self.upload_area_click_event
        
        return upload_widget
    
    def upload_area_drag_enter_event(self, event):
        """上传区域拖拽进入事件"""
        if event.mimeData().hasUrls():
            event.acceptProposedAction()
        else:
            event.ignore()
    
    def upload_area_drag_move_event(self, event):
        """上传区域拖拽移动事件"""
        if event.mimeData().hasUrls():
            event.acceptProposedAction()
        else:
            event.ignore()
    
    def upload_area_drop_event(self, event):
        """上传区域拖拽放下事件"""
        if event.mimeData().hasUrls():
            file_paths = []
            for url in event.mimeData().urls():
                if url.isLocalFile():
                    file_path = url.toLocalFile()
                    if file_path.lower().endswith(('.srt', '.vtt', '.ass', '.ssa')):
                        file_paths.append(file_path)
            
            if file_paths:
                self.add_files_from_paths(file_paths)
                event.acceptProposedAction()
            else:
                event.ignore()
        else:
            event.ignore()
    
    def upload_area_click_event(self, event):
        """上传区域点击事件"""
        self.add_files_to_list()
    
    def switch_to_file_list(self):
        """切换到文件列表显示"""
        self.file_stack.setCurrentIndex(1)
    
    def switch_to_upload_area(self):
        """切换到上传区域显示"""
        self.file_stack.setCurrentIndex(0)
    
    def on_translator_changed(self):
        """翻译服务变化"""
        current_data = self.translator_combo.currentData()
        current_text = self.translator_combo.currentText()
        
        # 根据插件信息更新API Key输入框状态
        if current_data in ["microsoft", "deepl"]:
            self.api_key_edit.setEnabled(True)
            if "microsoft" in current_data.lower():
                self.api_key_edit.setPlaceholderText("请输入微软翻译 API Key")
            elif "deepl" in current_data.lower():
                self.api_key_edit.setPlaceholderText("请输入DeepL Pro API Key")
        else:
            self.api_key_edit.setEnabled(False)
            self.api_key_edit.setPlaceholderText("当前翻译服务无需API Key")
    
    def load_subtitle_file(self):
        """加载字幕文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "选择字幕文件",
            "",
            "字幕文件 (*.srt *.vtt *.ass *.ssa);;所有文件 (*.*)"
        )
        
        if file_path:
            # 如果文件不在列表中，添加到列表
            if file_path not in self.file_list:
                self.file_list.append(file_path)
                self.update_file_list_widget()
            
            # 选择这个文件
            file_index = self.file_list.index(file_path)
            self.select_file(file_index)
    
    def parse_subtitle_file(self, file_path):
        """解析字幕文件"""
        try:
            import os
            from PySide6.QtWidgets import QMessageBox
            
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            if file_path.lower().endswith('.srt'):
                self.parse_srt_content(content)
                self.current_file_path = file_path
                self.update_original_table()
                self.status_label.setText(f"已加载字幕文件: {os.path.basename(file_path)}")
            else:
                QMessageBox.information(self, "提示", "目前只支持SRT格式的字幕文件")
                
        except Exception as e:
            from PySide6.QtWidgets import QMessageBox
            QMessageBox.critical(self, "错误", f"加载文件失败: {str(e)}")
    
    def parse_srt_content(self, content):
        """解析SRT内容"""
        import re
        
        self.subtitle_items.clear()
        
        # 分割字幕块
        blocks = re.split(r'\n\s*\n', content.strip())
        
        for block in blocks:
            lines = block.strip().split('\n')
            if len(lines) >= 3:
                # 跳过序号行
                time_line = lines[1]
                text_lines = lines[2:]
                
                # 解析时间
                time_match = re.match(r'(\d{2}:\d{2}:\d{2},\d{3})\s*-->\s*(\d{2}:\d{2}:\d{2},\d{3})', time_line)
                if time_match:
                    start_time = time_match.group(1)
                    end_time = time_match.group(2)
                    text = '\n'.join(text_lines)
                    
                    self.subtitle_items.append({
                        'start_time': start_time,
                        'end_time': end_time,
                        'text': text,
                        'translated': ''
                    })
    
    def update_original_table(self):
        """更新原文表格"""
        self.original_table.setRowCount(len(self.subtitle_items))
        
        for i, item in enumerate(self.subtitle_items):
            # 开始时间
            self.original_table.setItem(i, 0, QTableWidgetItem(item['start_time']))
            # 结束时间
            self.original_table.setItem(i, 1, QTableWidgetItem(item['end_time']))
            # 原文内容
            self.original_table.setItem(i, 2, QTableWidgetItem(item['text']))
        
        self.original_count_label.setText(f"字幕数量: {len(self.subtitle_items)}")
    
    def update_translated_table(self):
        """更新翻译表格"""
        translated_count = sum(1 for item in self.subtitle_items if item['translated'])
        self.translated_table.setRowCount(len(self.subtitle_items))
        
        for i, item in enumerate(self.subtitle_items):
            self.translated_table.setItem(i, 0, QTableWidgetItem(item['start_time']))
            self.translated_table.setItem(i, 1, QTableWidgetItem(item['end_time']))
            self.translated_table.setItem(i, 2, QTableWidgetItem(item['translated'].replace('\n', ' ')))
        
        self.translated_count_label.setText(f"已翻译: {translated_count}")
        
        if translated_count > 0:
            self.save_btn.setEnabled(True)
    
    def start_translation(self):
        """开始翻译 - 使用插件系统"""
        if not self.subtitle_items:
            QMessageBox.warning(self, "警告", "请先加载字幕文件！")
            return
        
        if not self.plugin:
            QMessageBox.warning(self, "警告", "字幕翻译插件未加载！")
            return
        
        # 获取配置
        translator_type = self.translator_combo.currentData() or self.get_translator_type()
        source_lang = self.source_lang_combo.currentData() or self.get_language_code(self.source_lang_combo.currentText())
        target_lang = self.target_lang_combo.currentData() or self.get_language_code(self.target_lang_combo.currentText())
        api_key = self.api_key_edit.text().strip()
        translation_mode = self.translation_mode_combo.currentData() or "smart"
        
        # 验证配置
        if translator_type in ["microsoft", "deepl"] and not api_key:
            service_name = "微软翻译" if translator_type == "microsoft" else "DeepL Pro"
            QMessageBox.warning(self, "警告", f"{service_name}需要输入API Key！")
            return
        

        
        # 准备翻译文本
        texts = [item['text'] for item in self.subtitle_items]
        
        # 初始化翻译统计
        self.translation_start_time = time.time()
        self.translated_count = 0
        self.total_count = len(texts)
        
        # 创建翻译工作线程
        self.translation_worker = TranslationWorker(
            self.plugin, texts, source_lang, target_lang, translator_type, api_key, translation_mode
        )
        
        # 连接信号
        self.translation_worker.progress_updated.connect(self.on_progress_updated)
        self.translation_worker.translation_completed.connect(self.on_translation_completed)
        self.translation_worker.error_occurred.connect(self.on_translation_error)
        self.translation_worker.finished_all.connect(self.on_translation_finished)
        
        # 更新UI状态
        self.translate_btn.setEnabled(False)
        self.stop_btn.setEnabled(True)
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        self.status_label.setText("正在翻译...")
        
        # 开始翻译
        self.translation_worker.start()
    
    def stop_translation(self):
        """停止翻译"""
        if self.translation_worker:
            self.translation_worker.stop()
            self.translation_worker.wait()
        
        self.on_translation_finished()
    
    def on_progress_updated(self, progress):
        """进度更新"""
        self.progress_bar.setValue(progress)
        
        # 计算翻译速度和预估剩余时间
        if hasattr(self, 'translation_start_time'):
            elapsed_time = time.time() - self.translation_start_time
            if elapsed_time > 0 and progress > 0:
                # 计算翻译速度（条/秒）
                completed_items = int(self.total_count * progress / 100)
                speed = completed_items / elapsed_time
                
                # 估算剩余时间
                remaining_items = self.total_count - completed_items
                if speed > 0:
                    remaining_time = remaining_items / speed
                    
                    # 格式化时间显示
                    if remaining_time < 60:
                        time_str = f"{remaining_time:.0f}秒"
                    elif remaining_time < 3600:
                        time_str = f"{remaining_time/60:.1f}分钟"
                    else:
                        time_str = f"{remaining_time/3600:.1f}小时"
                    
                    self.status_label.setText(f"正在翻译... {progress}% (速度: {speed:.1f}条/秒, 剩余: {time_str})")
                else:
                    self.status_label.setText(f"正在翻译... {progress}%")
    
    def on_translation_completed(self, index, original, translated):
        """单个翻译完成"""
        # 使用索引精确匹配字幕项
        if 0 <= index < len(self.subtitle_items):
            self.subtitle_items[index]['translated'] = translated
        elif index == -1:
            # 回退模式：使用文本匹配
            for item in self.subtitle_items:
                if item['text'] == original:
                    item['translated'] = translated
                    break
        
        self.translated_count += 1
        self.update_translated_table()
    
    def on_translation_error(self, error_msg):
        """翻译错误"""
        self.status_label.setText(f"翻译错误: {error_msg}")
    
    def on_translation_finished(self):
        """翻译完成"""
        self.translate_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        self.progress_bar.setVisible(False)
        
        # 显示翻译完成统计
        if hasattr(self, 'translation_start_time'):
            total_time = time.time() - self.translation_start_time
            if total_time > 0:
                avg_speed = self.translated_count / total_time
                self.status_label.setText(f"翻译完成！共翻译 {self.translated_count}/{self.total_count} 条，平均速度: {avg_speed:.1f}条/秒")
            else:
                self.status_label.setText(f"翻译完成！共翻译 {self.translated_count}/{self.total_count} 条")
        else:
            self.status_label.setText("翻译完成")
        
        if self.translation_worker:
            self.translation_worker = None
    
    def get_translator_type(self):
        """获取翻译器类型"""
        current_text = self.translator_combo.currentText()
        if "微软" in current_text:
            return "microsoft"
        elif "谷歌" in current_text:
            return "google"
        elif "DeepL" in current_text:
            return "deepl"
        return "google"
    
    def get_language_code(self, lang_text):
        """获取语言代码"""
        lang_map = {
            "自动检测": "auto",
            "中文 (zh)": "zh",
            "英语 (en)": "en",
            "日语 (ja)": "ja",
            "韩语 (ko)": "ko",
            "法语 (fr)": "fr",
            "德语 (de)": "de",
            "西班牙语 (es)": "es",
            "俄语 (ru)": "ru",
            "阿拉伯语 (ar)": "ar",
            "泰语 (th)": "th"
        }
        
        for key, value in lang_map.items():
            if key in lang_text:
                return value
        return "en"
    
    def save_translated_file(self):
        """保存翻译文件"""
        if not any(item['translated'] for item in self.subtitle_items):
            QMessageBox.warning(self, "警告", "没有翻译内容可保存！")
            return
        
        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "保存翻译文件",
            "",
            "SRT字幕文件 (*.srt);;所有文件 (*.*)"
        )
        
        if file_path:
            try:
                content = ""
                index = 1
                
                for item in self.subtitle_items:
                    if item['translated']:
                        content += f"{index}\n"
                        content += f"{item['start_time']} --> {item['end_time']}\n"
                        content += f"{item['translated']}\n\n"
                        index += 1
                
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                QMessageBox.information(self, "成功", f"翻译文件已保存: {os.path.basename(file_path)}")
                self.status_label.setText(f"已保存: {os.path.basename(file_path)}")
                
            except Exception as e:
                QMessageBox.critical(self, "错误", f"保存文件失败: {str(e)}")
    
    def setup_drag_drop(self):
        """设置拖拽功能"""
        # 启用主对话框的拖拽功能，拖拽到主界面会添加到文件列表
        self.setAcceptDrops(True)
    
    def dragEnterEvent(self, event: QDragEnterEvent):
        """拖拽进入事件"""
        if event.mimeData().hasUrls():
            # 检查是否包含字幕文件
            for url in event.mimeData().urls():
                file_path = url.toLocalFile()
                if file_path.lower().endswith(('.srt', '.ass', '.ssa', '.vtt')):
                    event.acceptProposedAction()
                    return
        event.ignore()
    
    def dragMoveEvent(self, event: QDragMoveEvent):
        """拖拽移动事件"""
        if event.mimeData().hasUrls():
            for url in event.mimeData().urls():
                file_path = url.toLocalFile()
                if file_path.lower().endswith(('.srt', '.ass', '.ssa', '.vtt')):
                    event.acceptProposedAction()
                    return
        event.ignore()
    
    def dragLeaveEvent(self, event):
        """拖拽离开事件"""
        # 不需要特殊处理
        pass
    
    def dropEvent(self, event: QDropEvent):
        """拖拽放下事件"""
        if event.mimeData().hasUrls():
            # 收集所有拖拽的文件路径
            file_paths = []
            for url in event.mimeData().urls():
                file_path = url.toLocalFile()
                file_paths.append(file_path)
            
            # 尝试添加文件到列表
            if self.add_files_from_paths(file_paths):
                event.acceptProposedAction()
                return
            else:
                QMessageBox.information(self, "提示", "没有找到支持的字幕文件！\n支持格式：SRT、ASS、SSA、VTT")
        
        event.ignore()


    # ==================== 字幕编辑功能 ====================
    
    def on_subtitle_selection_changed(self):
        """字幕表格选择变化时触发"""
        # 获取当前选中的行
        sender = self.sender()
        selected_items = sender.selectedItems()
        
        if selected_items:
            # 获取选中行的索引
            row = selected_items[0].row()
            
            # 直接加载选中字幕到编辑区
            self.load_subtitle_to_edit(row)
        else:
            # 没有选中项，清空编辑区
            self.clear_subtitle_edit()
    
    def load_subtitle_to_edit(self, index):
        """加载指定索引的字幕到编辑区"""
        if 0 <= index < len(self.subtitle_items):
            self.current_edit_index = index
            item = self.subtitle_items[index]
            
            # 备份原始内容
            self.original_content = item['text']
            self.original_start_time = item['start_time']
            self.original_end_time = item['end_time']
            
            try:
                # 更新编辑区域
                if hasattr(self, 'start_time_edit') and self.start_time_edit is not None:
                    self.start_time_edit.setText(item['start_time'])
                if hasattr(self, 'end_time_edit') and self.end_time_edit is not None:
                    self.end_time_edit.setText(item['end_time'])
                
                # 根据编辑模式设置内容
                if hasattr(self, 'edit_mode_combo') and self.edit_mode_combo is not None:
                    edit_mode = self.edit_mode_combo.currentData()
                    if edit_mode == "translated" and 'translated' in item and item['translated']:
                        content_to_set = item['translated']
                        self.original_content = item['translated']
                    else:
                        content_to_set = item['text']
                else:
                    # 默认显示原文
                    content_to_set = item['text']
                
                if hasattr(self, 'content_edit') and self.content_edit is not None:
                    self.content_edit.setPlainText(content_to_set)
                    
            except RuntimeError as e:
                print(f"更新编辑区域时出错: {e}")
            
            # 启用编辑控件
            self.enable_edit_controls(True)
            
            try:
                # 更新信息标签
                if hasattr(self, 'edit_info_label') and self.edit_info_label is not None:
                    self.edit_info_label.setText(f"正在编辑第 {index + 1} 条字幕")
                self.update_word_count()
            except RuntimeError as e:
                print(f"更新信息标签时出错: {e}")
            
            self.is_editing = True
    
    def clear_subtitle_edit(self):
        """清空字幕编辑区"""
        self.current_edit_index = -1
        self.original_content = ""
        self.original_start_time = ""
        self.original_end_time = ""
        self.is_editing = False
        
        try:
            # 清空编辑控件
            if hasattr(self, 'start_time_edit') and self.start_time_edit is not None:
                self.start_time_edit.clear()
            if hasattr(self, 'end_time_edit') and self.end_time_edit is not None:
                self.end_time_edit.clear()
            if hasattr(self, 'content_edit') and self.content_edit is not None:
                self.content_edit.clear()
        except RuntimeError as e:
            print(f"清空编辑控件时出错: {e}")
        
        # 禁用编辑控件
        self.enable_edit_controls(False)
        
        try:
            # 更新信息标签
            if hasattr(self, 'edit_info_label') and self.edit_info_label is not None:
                self.edit_info_label.setText("未选择字幕")
            if hasattr(self, 'char_count_label') and self.char_count_label is not None:
                self.char_count_label.setText("0 字符")
        except RuntimeError as e:
            print(f"更新信息标签时出错: {e}")
    
    def enable_edit_controls(self, enabled):
        """启用或禁用编辑控件"""
        try:
            if hasattr(self, 'start_time_edit') and self.start_time_edit is not None:
                self.start_time_edit.setEnabled(enabled)
            if hasattr(self, 'end_time_edit') and self.end_time_edit is not None:
                self.end_time_edit.setEnabled(enabled)
            if hasattr(self, 'content_edit') and self.content_edit is not None:
                self.content_edit.setEnabled(enabled)
            if hasattr(self, 'edit_mode_combo') and self.edit_mode_combo is not None:
                self.edit_mode_combo.setEnabled(enabled)
            if hasattr(self, 'apply_edit_btn') and self.apply_edit_btn is not None:
                self.apply_edit_btn.setEnabled(enabled)
            if hasattr(self, 'reset_edit_btn') and self.reset_edit_btn is not None:
                self.reset_edit_btn.setEnabled(enabled)
            if hasattr(self, 'clear_content_btn') and self.clear_content_btn is not None:
                self.clear_content_btn.setEnabled(enabled)
            if hasattr(self, 'sync_time_btn') and self.sync_time_btn is not None:
                self.sync_time_btn.setEnabled(enabled)
        except RuntimeError as e:
            print(f"设置编辑控件状态时出错: {e}")
    
    def on_edit_mode_changed(self):
        """编辑模式变化时触发"""
        if not self.is_editing or self.current_edit_index == -1:
            return
        
        # 检查是否有未保存的修改
        if self.has_unsaved_changes():
            reply = QMessageBox.question(
                self, "保存修改",
                "切换编辑模式前，是否保存当前修改？",
                QMessageBox.Save | QMessageBox.Discard | QMessageBox.Cancel
            )
            
            if reply == QMessageBox.Save:
                self.apply_subtitle_edit()
            elif reply == QMessageBox.Cancel:
                # 恢复原来的模式选择
                return
        
        # 重新加载字幕内容
        self.load_subtitle_to_edit(self.current_edit_index)
    
    def apply_subtitle_edit(self):
        """应用字幕编辑"""
        if not self.is_editing or self.current_edit_index == -1:
            return
        
        try:
            # 获取编辑内容
            new_content = self.content_edit.toPlainText().strip()
            new_start_time = self.start_time_edit.text().strip()
            new_end_time = self.end_time_edit.text().strip()
            
            # 验证时间格式
            if not self.validate_time_format(new_start_time):
                QMessageBox.warning(self, "时间格式错误", "开始时间格式不正确！\n正确格式：00:00:00,000")
                return
                
            if not self.validate_time_format(new_end_time):
                QMessageBox.warning(self, "时间格式错误", "结束时间格式不正确！\n正确格式：00:00:00,000")
                return
            
            # 验证时间逻辑
            if self.parse_time_to_seconds(new_start_time) >= self.parse_time_to_seconds(new_end_time):
                QMessageBox.warning(self, "时间逻辑错误", "开始时间必须小于结束时间！")
                return
            
            # 更新字幕数据
            item = self.subtitle_items[self.current_edit_index]
            item['start_time'] = new_start_time
            item['end_time'] = new_end_time
            
            edit_mode = self.edit_mode_combo.currentData()
            if edit_mode == "translated":
                item['translated'] = new_content
            else:
                item['text'] = new_content
            
            # 更新表格显示
            self.update_original_table()
            self.update_translated_table()
            
            # 重新选中当前行
            if edit_mode == "translated":
                self.translated_table.selectRow(self.current_edit_index)
            else:
                self.original_table.selectRow(self.current_edit_index)
            
            # 更新备份内容
            self.original_content = new_content
            self.original_start_time = new_start_time
            self.original_end_time = new_end_time
            
            self.status_label.setText(f"第 {self.current_edit_index + 1} 条字幕修改已保存")
            
        except Exception as e:
            QMessageBox.critical(self, "保存失败", f"保存字幕修改时出错：\n{str(e)}")
    
    def reset_subtitle_edit(self):
        """重置字幕编辑到原始状态"""
        if not self.is_editing:
            return
        
        # 恢复原始内容
        self.start_time_edit.setText(self.original_start_time)
        self.end_time_edit.setText(self.original_end_time)
        self.content_edit.setPlainText(self.original_content)
        
        self.update_word_count()
    
    def clear_edit_content(self):
        """清空编辑内容"""
        self.content_edit.clear()
        self.update_word_count()
    
    def sync_time_edits(self):
        """同步时间编辑（从原文字幕同步到翻译字幕）"""
        if not self.is_editing or self.current_edit_index == -1:
            return
        
        item = self.subtitle_items[self.current_edit_index]
        self.start_time_edit.setText(item['start_time'])
        self.end_time_edit.setText(item['end_time'])
    
    def on_content_changed(self):
        """内容变化时触发"""
        self.update_word_count()
    
    def on_time_changed(self):
        """时间变化时触发"""
        # 可以在这里添加时间格式实时验证
        pass
    
    def update_word_count(self):
        """更新字符计数"""
        try:
            if hasattr(self, 'content_edit') and self.content_edit is not None:
                content = self.content_edit.toPlainText()
                char_count = len(content)
                if hasattr(self, 'char_count_label') and self.char_count_label is not None:
                    self.char_count_label.setText(f"{char_count} 字符")
        except RuntimeError as e:
            print(f"更新字符计数时出错: {e}")
    
    def has_unsaved_changes(self):
        """检查是否有未保存的修改"""
        if not self.is_editing:
            return False
        
        try:
            current_content = ""
            current_start_time = ""
            current_end_time = ""
            
            if hasattr(self, 'content_edit') and self.content_edit is not None:
                current_content = self.content_edit.toPlainText().strip()
            if hasattr(self, 'start_time_edit') and self.start_time_edit is not None:
                current_start_time = self.start_time_edit.text().strip()
            if hasattr(self, 'end_time_edit') and self.end_time_edit is not None:
                current_end_time = self.end_time_edit.text().strip()
            
            return (current_content != self.original_content or
                    current_start_time != self.original_start_time or
                    current_end_time != self.original_end_time)
        except RuntimeError as e:
            print(f"检查未保存修改时出错: {e}")
            return False
    
    def validate_time_format(self, time_str):
        """验证时间格式"""
        import re
        pattern = r'^\d{2}:\d{2}:\d{2},\d{3}$'
        return bool(re.match(pattern, time_str))
    
    def parse_time_to_seconds(self, time_str):
        """将时间字符串转换为秒数"""
        try:
            time_parts = time_str.split(':')
            hours = int(time_parts[0])
            minutes = int(time_parts[1])
            seconds_parts = time_parts[2].split(',')
            seconds = int(seconds_parts[0])
            milliseconds = int(seconds_parts[1])
            return hours * 3600 + minutes * 60 + seconds + milliseconds / 1000
        except:
            return 0
    
    def file_list_drag_enter_event(self, event):
        """文件列表拖拽进入事件"""
        if event.mimeData().hasUrls():
            for url in event.mimeData().urls():
                file_path = url.toLocalFile()
                if file_path.lower().endswith(('.srt', '.ass', '.ssa', '.vtt')):
                    event.acceptProposedAction()
                    return
        event.ignore()
    
    def file_list_drag_move_event(self, event):
        """文件列表拖拽移动事件"""
        if event.mimeData().hasUrls():
            for url in event.mimeData().urls():
                file_path = url.toLocalFile()
                if file_path.lower().endswith(('.srt', '.ass', '.ssa', '.vtt')):
                    event.acceptProposedAction()
                    return
        event.ignore()
    
    def file_list_drop_event(self, event):
        """文件列表拖拽放下事件"""
        if event.mimeData().hasUrls():
            file_paths = []
            for url in event.mimeData().urls():
                file_path = url.toLocalFile()
                file_paths.append(file_path)
            
            if self.add_files_from_paths(file_paths):
                event.acceptProposedAction()
            else:
                from PySide6.QtWidgets import QMessageBox
                QMessageBox.information(self, "提示", "没有找到支持的字幕文件！\n支持格式：SRT、ASS、SSA、VTT")
        
        event.ignore()
    
    def add_files_from_paths(self, file_paths):
        """从文件路径列表添加文件（用于拖拽功能）"""
        subtitle_files = []
        for file_path in file_paths:
            if file_path.lower().endswith(('.srt', '.ass', '.ssa', '.vtt')):
                subtitle_files.append(file_path)
        
        if subtitle_files:
            for file_path in subtitle_files:
                if file_path not in self.file_list:
                    self.file_list.append(file_path)
            
            # 如果有文件，切换到文件列表显示
            if self.file_list:
                self.switch_to_file_list()
            
            self.update_file_list_widget()
            
            # 如果之前没有选中文件，自动选择第一个
            if self.current_file_index == -1 and self.file_list:
                self.select_file(0)
            
            self.status_label.setText(f"已添加 {len(subtitle_files)} 个字幕文件")
            return True
        
        return False
    
    def update_file_list_widget(self):
        """更新文件列表控件显示"""
        self.file_list_widget.clear()
        
        for i, file_path in enumerate(self.file_list):
            import os
            file_name = os.path.basename(file_path)
            item = QListWidgetItem(f"{i+1}. {file_name}")
            item.setData(Qt.UserRole, file_path)  # 存储完整路径
            
            # 如果是当前选中的文件，高亮显示
            if i == self.current_file_index:
                item.setSelected(True)
                
            self.file_list_widget.addItem(item)
    
    def select_file(self, index):
        """选择并加载指定索引的文件"""
        if 0 <= index < len(self.file_list):
            # 检查是否有未保存的编辑修改
            if self.is_editing and self.has_unsaved_changes():
                from PySide6.QtWidgets import QMessageBox
                reply = QMessageBox.question(
                    self, "保存修改",
                    "当前有未保存的修改，是否保存？",
                    QMessageBox.Save | QMessageBox.Discard | QMessageBox.Cancel
                )
                
                if reply == QMessageBox.Save:
                    self.apply_subtitle_edit()
                elif reply == QMessageBox.Cancel:
                    return  # 取消文件切换
            
            self.current_file_index = index
            file_path = self.file_list[index]
            
            # 清空编辑区域
            self.clear_subtitle_edit()
            
            # 加载文件内容
            self.parse_subtitle_file(file_path)
            
            # 更新列表显示
            self.update_file_list_widget()
            
            # 清空翻译结果（切换文件时重置翻译状态）
            self.translated_items = []
            self.translated_table.setRowCount(0)
            self.translated_count_label.setText("已翻译: 0")
            self.save_btn.setEnabled(False)
    
    def add_files_to_list(self):
        """添加文件到列表"""
        from PySide6.QtWidgets import QFileDialog
        file_paths, _ = QFileDialog.getOpenFileNames(
            self,
            "选择字幕文件",
            "",
            "字幕文件 (*.srt *.vtt *.ass *.ssa);;所有文件 (*.*)"
        )
        
        if file_paths:
            for file_path in file_paths:
                if file_path not in self.file_list:
                    self.file_list.append(file_path)
            
            # 如果有文件，切换到文件列表显示
            if self.file_list:
                self.switch_to_file_list()
                    
            self.update_file_list_widget()
            
            # 如果列表中有文件且当前没有选中文件，自动选择第一个
            if self.file_list and self.current_file_index == -1:
                self.select_file(0)
    
    def clear_file_list(self):
        """清空文件列表"""
        # 检查是否有未保存的编辑修改
        if self.is_editing and self.has_unsaved_changes():
            from PySide6.QtWidgets import QMessageBox
            reply = QMessageBox.question(
                self, "保存修改",
                "当前有未保存的修改，是否保存？",
                QMessageBox.Save | QMessageBox.Discard | QMessageBox.Cancel
            )
            
            if reply == QMessageBox.Save:
                self.apply_subtitle_edit()
            elif reply == QMessageBox.Cancel:
                return  # 取消清空操作
        
        self.file_list.clear()
        self.current_file_index = -1
        self.file_list_widget.clear()
        
        # 切换回上传区域显示
        self.switch_to_upload_area()
        
        # 清空编辑区域
        self.clear_subtitle_edit()
        
        # 清空原文表格
        self.subtitle_items.clear()
        self.update_original_table()
        
        # 清空翻译结果
        self.translated_items = []
        self.translated_table.setRowCount(0)
        self.translated_count_label.setText("已翻译: 0")
        self.save_btn.setEnabled(False)
        
        self.status_label.setText("文件列表已清空")
    
    def on_file_list_item_clicked(self, item):
        """文件列表项被点击"""
        file_path = item.data(Qt.UserRole)
        if file_path in self.file_list:
            file_index = self.file_list.index(file_path)
            self.select_file(file_index)
    
    def create_original_area(self):
        """创建原文区域"""
        original_frame = QGroupBox("原文字幕")
        original_frame.setStyleSheet("""
            QGroupBox {
                border: 2px solid #444444;
                border-radius: 17px;
                margin-top: 10px;
                padding-top: 8px;
                color: #FFFFFF;
                font-weight: bold;
                background-color: #1B1E24;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 8px 0 8px;
                color: #2B9D7C;
                font-size: 14px;
            }
        """)
        original_layout = QVBoxLayout(original_frame)
        original_layout.setContentsMargins(10, 15, 10, 10)
        
        # 简化的工具栏，只保留计数标签
        toolbar_layout = QHBoxLayout()
        
        self.original_count_label = QLabel("字幕数量: 0")
        self.original_count_label.setStyleSheet("color: #2B9D7C; font-weight: bold;")
        toolbar_layout.addWidget(self.original_count_label)
        
        toolbar_layout.addStretch()
        
        original_layout.addLayout(toolbar_layout)
        
        # 原文表格
        self.original_table = QTableWidget()
        self.original_table.setColumnCount(3)
        self.original_table.setHorizontalHeaderLabels(["开始时间", "结束时间", "原文内容"])
        
        # 设置表格样式
        self.original_table.setStyleSheet("""
            QTableWidget {
                background-color: #14161A;
                color: #FFFFFF;
                border: 2px solid #444444;
                border-radius: 8px;
                gridline-color: #333333;
                font-size: 13px;
                selection-background-color: rgba(43, 157, 124, 0.3);
            }
            QTableWidget::item {
                padding: 8px;
                border: none;
                color: #FFFFFF;
                background-color: transparent;
            }
            QTableWidget::item:selected {
                background-color: rgba(43, 157, 124, 0.4);
                color: #FFFFFF;
            }
            QTableWidget::item:hover {
                background-color: rgba(43, 157, 124, 0.15);
            }
            QHeaderView::section {
                background-color: #2B9D7C;
                color: #FFFFFF;
                padding: 8px;
                border: none;
                font-weight: bold;
                font-size: 12px;
            }
            QHeaderView::section:hover {
                background-color: #00CC55;
            }
        """)
        
        # 设置列宽
        from PySide6.QtWidgets import QHeaderView
        header = self.original_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Fixed)
        header.setSectionResizeMode(1, QHeaderView.Fixed)
        header.setSectionResizeMode(2, QHeaderView.Stretch)
        self.original_table.setColumnWidth(0, 100)
        self.original_table.setColumnWidth(1, 100)
        
        original_layout.addWidget(self.original_table)
        
        return original_frame
    
    def create_translated_area(self):
        """创建翻译结果区域"""
        translated_frame = QGroupBox("翻译结果")
        translated_frame.setStyleSheet("""
            QGroupBox {
                border: 2px solid #444444;
                border-radius: 17px;
                margin-top: 10px;
                padding-top: 8px;
                color: #FFFFFF;
                font-weight: bold;
                background-color: #1B1E24;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 8px 0 8px;
                color: #2B9D7C;
                font-size: 14px;
            }
        """)
        translated_layout = QVBoxLayout(translated_frame)
        translated_layout.setContentsMargins(10, 15, 10, 10)
        
        # 简化的工具栏，只保留计数标签
        toolbar_layout = QHBoxLayout()
        
        self.translated_count_label = QLabel("已翻译: 0")
        self.translated_count_label.setStyleSheet("color: #2B9D7C; font-weight: bold;")
        toolbar_layout.addWidget(self.translated_count_label)
        
        toolbar_layout.addStretch()
        
        translated_layout.addLayout(toolbar_layout)
        
        # 翻译结果表格
        self.translated_table = QTableWidget()
        self.translated_table.setColumnCount(3)
        self.translated_table.setHorizontalHeaderLabels(["开始时间", "结束时间", "翻译内容"])
        
        # 设置表格样式
        self.translated_table.setStyleSheet("""
            QTableWidget {
                background-color: #14161A;
                color: #FFFFFF;
                border: 2px solid #444444;
                border-radius: 8px;
                gridline-color: #333333;
                font-size: 13px;
                selection-background-color: rgba(43, 157, 124, 0.3);
            }
            QTableWidget::item {
                padding: 8px;
                border: none;
                color: #FFFFFF;
                background-color: transparent;
            }
            QTableWidget::item:selected {
                background-color: rgba(43, 157, 124, 0.4);
                color: #FFFFFF;
            }
            QTableWidget::item:hover {
                background-color: rgba(43, 157, 124, 0.15);
            }
            QHeaderView::section {
                background-color: #2B9D7C;
                color: #FFFFFF;
                padding: 8px;
                border: none;
                font-weight: bold;
                font-size: 12px;
            }
            QHeaderView::section:hover {
                background-color: #00CC55;
            }
        """)
        
        # 设置列宽
        from PySide6.QtWidgets import QHeaderView
        header = self.translated_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Fixed)
        header.setSectionResizeMode(1, QHeaderView.Fixed)
        header.setSectionResizeMode(2, QHeaderView.Stretch)
        self.translated_table.setColumnWidth(0, 100)
        self.translated_table.setColumnWidth(1, 100)
        
        translated_layout.addWidget(self.translated_table)
        
        return translated_frame
    
    def create_control_area(self):
        """创建底部控制区域"""
        control_frame = QFrame()
        control_frame.setFixedHeight(60)
        
        control_layout = QVBoxLayout(control_frame)
        control_layout.setContentsMargins(10, 5, 10, 5)
        control_layout.setSpacing(5)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setFixedHeight(6)
        self.progress_bar.setVisible(False)
        control_layout.addWidget(self.progress_bar)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        button_layout.setSpacing(8)
        
        # 状态标签
        self.status_label = QLabel("就绪")
        self.status_label.setStyleSheet("color: #2ECC71; font-weight: bold;")
        button_layout.addWidget(self.status_label)
        
        button_layout.addStretch()
        
        # 翻译控制按钮组
        self.translate_btn = QPushButton("开始翻译")
        self.translate_btn.setFixedSize(100, 35)
        self.translate_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #2B9D7C, stop:1 #00CC55);
                color: #FFFFFF;
                border: none;
                border-radius: 17px;
                padding: 8px 12px;
                font-weight: bold;
                font-size: 13px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #00CC55, stop:1 #2B9D7C);
            }
            QPushButton:pressed {
                background-color: #1E8B6B;
            }
            QPushButton:disabled {
                background-color: #444444;
                color: rgba(255, 255, 255, 0.4);
            }
        """)
        button_layout.addWidget(self.translate_btn)
        
        self.stop_btn = QPushButton("停止翻译")
        self.stop_btn.setEnabled(False)
        self.stop_btn.setFixedSize(100, 35)
        self.stop_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #E74C3C, stop:1 #C0392B);
                color: #FFFFFF;
                border: none;
                border-radius: 17px;
                padding: 8px 12px;
                font-weight: bold;
                font-size: 13px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #C0392B, stop:1 #E74C3C);
            }
            QPushButton:pressed {
                background-color: #A93226;
            }
            QPushButton:disabled {
                background-color: #444444;
                color: rgba(255, 255, 255, 0.4);
            }
        """)
        button_layout.addWidget(self.stop_btn)
        
        # 保存按钮
        self.save_btn = QPushButton("保存翻译结果")
        self.save_btn.setEnabled(False)
        self.save_btn.setFixedSize(120, 35)
        self.save_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #F39C12, stop:1 #E67E22);
                color: #FFFFFF;
                border: none;
                border-radius: 17px;
                padding: 8px 12px;
                font-weight: bold;
                font-size: 13px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #E67E22, stop:1 #F39C12);
            }
            QPushButton:pressed {
                background-color: #D68910;
            }
            QPushButton:disabled {
                background-color: #444444;
                color: rgba(255, 255, 255, 0.4);
            }
        """)
        button_layout.addWidget(self.save_btn)
        
        # 关闭按钮
        self.close_btn = QPushButton("关闭")
        self.close_btn.setFixedSize(80, 35)
        self.close_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #95A5A6, stop:1 #7F8C8D);
                color: #FFFFFF;
                border: none;
                border-radius: 17px;
                padding: 8px 12px;
                font-weight: bold;
                font-size: 13px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #7F8C8D, stop:1 #95A5A6);
            }
            QPushButton:pressed {
                background-color: #6C7B7D;
            }
        """)
        button_layout.addWidget(self.close_btn)
        
        control_layout.addLayout(button_layout)
        
        return control_frame
    
    def setup_connections(self):
        """设置信号连接"""
        try:
            self.translate_btn.clicked.connect(self.start_translation)
            self.stop_btn.clicked.connect(self.stop_translation)
            self.save_btn.clicked.connect(self.save_translated_file)
            self.close_btn.clicked.connect(self.close)
        except RuntimeError as e:
            print(f"控制按钮连接错误: {e}")
        
        try:
            # 翻译服务变化
            self.translator_combo.currentTextChanged.connect(self.on_translator_changed)
        except RuntimeError as e:
            print(f"翻译器组合框连接错误: {e}")
        
        try:
            # 文件列表相关
            self.add_files_btn.clicked.connect(self.add_files_to_list)
            self.clear_files_btn.clicked.connect(self.clear_file_list)
            self.file_list_widget.itemClicked.connect(self.on_file_list_item_clicked)
        except RuntimeError as e:
            print(f"文件列表连接错误: {e}")
        
        try:
            # 字幕表格选择变化
            self.original_table.itemSelectionChanged.connect(self.on_subtitle_selection_changed)
            self.translated_table.itemSelectionChanged.connect(self.on_subtitle_selection_changed)
        except RuntimeError as e:
            print(f"表格选择连接错误: {e}")
        
        try:
            # 编辑区域相关
            self.edit_mode_combo.currentTextChanged.connect(self.on_edit_mode_changed)
            self.apply_edit_btn.clicked.connect(self.apply_subtitle_edit)
            self.reset_edit_btn.clicked.connect(self.reset_subtitle_edit)
            self.clear_content_btn.clicked.connect(self.clear_edit_content)
        except RuntimeError as e:
            print(f"编辑控件连接错误: {e}")
            
        try:
            # 同步时间按钮（可能有问题的控件）
            if hasattr(self, 'sync_time_btn') and self.sync_time_btn is not None:
                self.sync_time_btn.clicked.connect(self.sync_time_edits)
        except RuntimeError as e:
            print(f"同步时间按钮连接错误: {e}")
        
        try:
            # 内容变化监听
            self.content_edit.textChanged.connect(self.on_content_changed)
            self.start_time_edit.textChanged.connect(self.on_time_changed)
            self.end_time_edit.textChanged.connect(self.on_time_changed)
        except RuntimeError as e:
            print(f"文本变化监听连接错误: {e}")


if __name__ == "__main__":
    import sys
    from PySide6.QtWidgets import QApplication
    from PySide6.QtGui import QPalette, QColor
    
    app = QApplication(sys.argv)
    
    # 设置全局暗色主题
    app.setStyle('Fusion')
    palette = QPalette()
    palette.setColor(QPalette.Window, QColor(27, 30, 36))
    palette.setColor(QPalette.WindowText, QColor(255, 255, 255))
    palette.setColor(QPalette.Base, QColor(20, 22, 26))
    palette.setColor(QPalette.AlternateBase, QColor(27, 30, 36))
    palette.setColor(QPalette.ToolTipBase, QColor(255, 255, 255))
    palette.setColor(QPalette.ToolTipText, QColor(255, 255, 255))
    palette.setColor(QPalette.Text, QColor(255, 255, 255))
    palette.setColor(QPalette.Button, QColor(27, 30, 36))
    palette.setColor(QPalette.ButtonText, QColor(255, 255, 255))
    palette.setColor(QPalette.BrightText, QColor(255, 0, 0))
    palette.setColor(QPalette.Link, QColor(43, 157, 124))
    palette.setColor(QPalette.Highlight, QColor(43, 157, 124))
    palette.setColor(QPalette.HighlightedText, QColor(255, 255, 255))
    app.setPalette(palette)
    
    dialog = SubtitleTranslationDialog()
    dialog.show()
    sys.exit(app.exec()) 