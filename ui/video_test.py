#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
视频播放测试程序
用于验证PySide6视频播放功能
"""

import sys
import os
from PySide6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QPushButton, QLabel, QFileDialog, QMessageBox
)
from PySide6.QtCore import Qt, QUrl, QSize, QTimer
from PySide6.QtMultimedia import QMediaPlayer, QAudioOutput
from PySide6.QtMultimediaWidgets import QVideoWidget

class VideoTestWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("视频播放测试")
        self.setMinimumSize(800, 600)
        
        # 设置中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        
        # 创建状态标签
        self.status_label = QLabel("请选择视频文件测试播放")
        self.status_label.setAlignment(Qt.AlignCenter)
        self.status_label.setStyleSheet("font-size: 16px; color: white;")
        main_layout.addWidget(self.status_label)
        
        # 创建视频播放区域
        self.video_widget = QVideoWidget()
        self.video_widget.setMinimumSize(QSize(640, 360))
        main_layout.addWidget(self.video_widget)
        
        # 创建播放控制区域
        control_layout = QHBoxLayout()
        
        # 创建播放按钮
        self.play_button = QPushButton("播放")
        self.play_button.setEnabled(False)
        self.play_button.clicked.connect(self.play_video)
        control_layout.addWidget(self.play_button)
        
        # 创建暂停按钮
        self.pause_button = QPushButton("暂停")
        self.pause_button.setEnabled(False)
        self.pause_button.clicked.connect(self.pause_video)
        control_layout.addWidget(self.pause_button)
        
        # 创建停止按钮
        self.stop_button = QPushButton("停止")
        self.stop_button.setEnabled(False)
        self.stop_button.clicked.connect(self.stop_video)
        control_layout.addWidget(self.stop_button)
        
        # 创建选择文件按钮
        self.select_button = QPushButton("选择视频文件")
        self.select_button.clicked.connect(self.select_video)
        control_layout.addWidget(self.select_button)
        
        main_layout.addLayout(control_layout)
        
        # 创建调试按钮区域
        debug_layout = QHBoxLayout()
        
        # 显示视频组件信息按钮
        self.info_button = QPushButton("显示视频组件信息")
        self.info_button.clicked.connect(self.show_video_widget_info)
        debug_layout.addWidget(self.info_button)
        
        # 重新设置视频输出按钮
        self.reset_button = QPushButton("重新设置视频输出")
        self.reset_button.clicked.connect(self.reset_video_output)
        debug_layout.addWidget(self.reset_button)
        
        # 强制刷新按钮
        self.refresh_button = QPushButton("强制刷新视频")
        self.refresh_button.clicked.connect(self.force_refresh)
        debug_layout.addWidget(self.refresh_button)
        
        main_layout.addLayout(debug_layout)
        
        # 创建媒体播放器
        self.media_player = QMediaPlayer()
        self.audio_output = QAudioOutput()
        self.media_player.setAudioOutput(self.audio_output)
        self.media_player.setVideoOutput(self.video_widget)
        
        # 连接信号
        self.media_player.playbackStateChanged.connect(self.playback_state_changed)
        self.media_player.errorOccurred.connect(self.handle_error)
        self.media_player.mediaStatusChanged.connect(self.media_status_changed)
        
        # 当前视频文件
        self.current_video = None
        
        # 应用样式
        self.setup_style()
    
    def setup_style(self):
        """设置应用样式"""
        self.setStyleSheet("""
            QMainWindow {
                background-color: #1B1E24;
            }
            QLabel {
                color: #FFFFFF;
            }
            QPushButton {
                background-color: #2B9D7C;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #33B490;
            }
            QPushButton:disabled {
                background-color: #555555;
                color: #888888;
            }
            QVideoWidget {
                background-color: black;
                border: 1px solid #333333;
                border-radius: 4px;
            }
        """)
    
    def select_video(self):
        """选择视频文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "选择视频文件",
            "",
            "视频文件 (*.mp4 *.avi *.mov *.mkv *.wmv)"
        )
        
        if file_path:
            self.load_video(file_path)
    
    def load_video(self, file_path):
        """加载视频文件"""
        # 验证文件存在
        if not os.path.isfile(file_path):
            QMessageBox.warning(self, "文件错误", f"找不到视频文件:\n{file_path}")
            return
        
        self.current_video = file_path
        self.status_label.setText(f"已加载: {os.path.basename(file_path)}")
        
        # 创建URL并设置源
        url = QUrl.fromLocalFile(os.path.abspath(file_path))
        print(f"视频URL: {url.toString()}")
        
        # 重置播放器
        self.stop_video()
        self.media_player.setSource(url)
        
        # 启用控制按钮
        self.play_button.setEnabled(True)
        self.stop_button.setEnabled(True)
        
        # 自动播放
        QTimer.singleShot(500, self.play_video)
    
    def play_video(self):
        """播放视频"""
        if self.current_video:
            self.media_player.play()
            print("开始播放视频")
    
    def pause_video(self):
        """暂停视频"""
        self.media_player.pause()
    
    def stop_video(self):
        """停止视频"""
        self.media_player.stop()
    
    def playback_state_changed(self, state):
        """播放状态变化处理"""
        from PySide6.QtMultimedia import QMediaPlayer
        
        states = {
            QMediaPlayer.PlaybackState.StoppedState: "已停止",
            QMediaPlayer.PlaybackState.PlayingState: "播放中",
            QMediaPlayer.PlaybackState.PausedState: "已暂停"
        }
        
        state_text = states.get(state, "未知状态")
        print(f"播放状态: {state_text}")
        
        if state == QMediaPlayer.PlaybackState.PlayingState:
            self.play_button.setEnabled(False)
            self.pause_button.setEnabled(True)
            self.stop_button.setEnabled(True)
        elif state == QMediaPlayer.PlaybackState.PausedState:
            self.play_button.setEnabled(True)
            self.pause_button.setEnabled(False)
            self.stop_button.setEnabled(True)
        elif state == QMediaPlayer.PlaybackState.StoppedState:
            self.play_button.setEnabled(True)
            self.pause_button.setEnabled(False)
            self.stop_button.setEnabled(False)
    
    def handle_error(self, error, error_string):
        """处理媒体播放器错误"""
        self.status_label.setText(f"错误: {error_string}")
        print(f"播放错误: {error_string} (代码: {error})")
        QMessageBox.warning(self, "播放错误", f"无法播放视频:\n{error_string}\n错误代码: {error}")
    
    def media_status_changed(self, status):
        """媒体状态变化处理"""
        from PySide6.QtMultimedia import QMediaPlayer
        
        status_map = {
            QMediaPlayer.MediaStatus.NoMedia: "无媒体",
            QMediaPlayer.MediaStatus.LoadingMedia: "加载中",
            QMediaPlayer.MediaStatus.LoadedMedia: "已加载",
            QMediaPlayer.MediaStatus.StalledMedia: "已暂停",
            QMediaPlayer.MediaStatus.BufferingMedia: "缓冲中",
            QMediaPlayer.MediaStatus.BufferedMedia: "已缓冲",
            QMediaPlayer.MediaStatus.EndOfMedia: "播放结束",
            QMediaPlayer.MediaStatus.InvalidMedia: "无效媒体"
        }
        
        status_text = status_map.get(status, "未知状态")
        print(f"媒体状态: {status_text}")
        
        if status == QMediaPlayer.MediaStatus.InvalidMedia:
            self.status_label.setText(f"无效的媒体: {os.path.basename(self.current_video) if self.current_video else '未知'}")
            QMessageBox.warning(self, "无效媒体", "无法播放此视频格式，可能缺少解码器支持。")
    
    def show_video_widget_info(self):
        """显示视频部件信息"""
        info = [
            f"视频部件可见: {self.video_widget.isVisible()}",
            f"视频部件尺寸: {self.video_widget.width()}x{self.video_widget.height()}",
            f"视频部件父对象: {type(self.video_widget.parent()).__name__}",
            f"媒体播放器状态: {self.media_player.playbackState()}",
            f"当前视频: {self.current_video if self.current_video else '无'}"
        ]
        
        info_str = "\n".join(info)
        print(info_str)
        QMessageBox.information(self, "视频组件信息", info_str)
    
    def reset_video_output(self):
        """重新设置视频输出"""
        if self.current_video:
            url = QUrl.fromLocalFile(os.path.abspath(self.current_video))
            
            # 重置视频输出
            self.media_player.stop()
            self.media_player.setVideoOutput(None)
            self.media_player.setVideoOutput(self.video_widget)
            self.media_player.setSource(url)
            
            print("已重新设置视频输出")
            QTimer.singleShot(500, self.play_video)
    
    def force_refresh(self):
        """强制刷新视频显示"""
        if self.current_video:
            # 临时隐藏再显示视频部件
            self.video_widget.hide()
            QTimer.singleShot(100, self.video_widget.show)
            
            # 调整尺寸
            current_size = self.video_widget.size()
            self.video_widget.resize(current_size.width()-1, current_size.height()-1)
            QTimer.singleShot(200, lambda: self.video_widget.resize(current_size))
            
            print("强制刷新视频显示")

def main():
    app = QApplication(sys.argv)
    window = VideoTestWindow()
    window.show()
    sys.exit(app.exec())

if __name__ == "__main__":
    main() 