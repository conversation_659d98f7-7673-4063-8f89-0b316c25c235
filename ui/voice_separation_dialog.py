#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FlipTalk AI - 人声分离对话框
提供音频人声分离功能的用户界面（支持拖拽上传和批量处理）
"""

import os
import sys
from pathlib import Path
from typing import Dict, Optional, List

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QLineEdit, QProgressBar, QTextEdit, QFileDialog, QMessageBox,
    QComboBox, QCheckBox, QGroupBox, QGridLayout, QFrame, QSplitter,
    QScrollArea, QWidget, QButtonGroup, QListWidget, QListWidgetItem,
    QTabWidget, QTableWidget, QTableWidgetItem, QHeaderView
)
from PySide6.QtCore import Qt, QThread, Signal, QPropertyAnimation, QEasingCurve, QRect, QMimeData, QUrl
from PySide6.QtGui import QFont, QPixmap, QPainter, QColor, QBrush, QPen, QDragEnterEvent, QDropEvent


class DragDropFileList(QListWidget):
    """支持拖拽的文件列表组件"""
    
    files_dropped = Signal(list)  # 文件拖拽信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setAcceptDrops(True)
        self.setDragDropMode(QListWidget.DropOnly)
        self.setDefaultDropAction(Qt.CopyAction)
        self.setup_style()
    
    def setup_style(self):
        """设置样式"""
        self.setStyleSheet("""
            QListWidget {
                border: 2px dashed #2B9D7C;
                border-radius: 8px;
                background-color: #14161A;
                color: #FFFFFF;
                font-size: 13px;
                padding: 10px;
                min-height: 120px;
            }
            QListWidget::item {
                padding: 8px;
                border-bottom: 1px solid #444444;
                background-color: #1B1E24;
                border-radius: 4px;
                margin: 2px;
                color: #FFFFFF;
            }
            QListWidget::item:selected {
                background-color: #2B9D7C;
                color: #FFFFFF;
            }
            QListWidget::item:hover {
                background-color: rgba(43, 157, 124, 0.2);
            }
        """)
        
        # 不添加占位符文本，因为有专门的上传区域
    
    def add_placeholder_text(self):
        """添加占位符文本"""
        placeholder_item = QListWidgetItem("📁 拖拽音频文件到此处，或点击浏览按钮选择文件\n支持格式: WAV, MP3, FLAC, AAC, M4A")
        placeholder_item.setFlags(Qt.NoItemFlags)  # 不可选择
        placeholder_item.setTextAlignment(Qt.AlignCenter)
        self.addItem(placeholder_item)
    
    def dragEnterEvent(self, event: QDragEnterEvent):
        """拖拽进入事件"""
        if event.mimeData().hasUrls():
            # 检查是否包含音频文件
            urls = event.mimeData().urls()
            audio_extensions = {'.wav', '.mp3', '.flac', '.aac', '.m4a', '.ogg'}
            
            for url in urls:
                if url.isLocalFile():
                    file_path = Path(url.toLocalFile())
                    if file_path.suffix.lower() in audio_extensions:
                        event.acceptProposedAction()
                        self.setStyleSheet(self.styleSheet().replace('#2B9D7C', '#00CC55'))
                        return
            
        event.ignore()
    
    def dragLeaveEvent(self, event):
        """拖拽离开事件"""
        self.setStyleSheet(self.styleSheet().replace('#00CC55', '#2B9D7C'))
        super().dragLeaveEvent(event)
    
    def dropEvent(self, event: QDropEvent):
        """拖拽放下事件"""
        self.setStyleSheet(self.styleSheet().replace('#00CC55', '#2B9D7C'))
        
        if event.mimeData().hasUrls():
            urls = event.mimeData().urls()
            audio_files = []
            audio_extensions = {'.wav', '.mp3', '.flac', '.aac', '.m4a', '.ogg'}
            
            for url in urls:
                if url.isLocalFile():
                    file_path = Path(url.toLocalFile())
                    if file_path.suffix.lower() in audio_extensions and file_path.exists():
                        audio_files.append(str(file_path))
            
            if audio_files:
                # 清除占位符
                if self.count() == 1 and self.item(0).flags() == Qt.NoItemFlags:
                    self.clear()
                
                # 添加文件到列表
                for file_path in audio_files:
                    self.add_audio_file(file_path)
                
                # 发射信号
                self.files_dropped.emit(audio_files)
                event.acceptProposedAction()
            else:
                event.ignore()
        else:
            event.ignore()
    
    def add_audio_file(self, file_path: str):
        """添加音频文件到列表"""
        file_path_obj = Path(file_path)
        
        # 检查文件是否已存在
        for i in range(self.count()):
            item = self.item(i)
            if item and item.data(Qt.UserRole) == file_path:
                return  # 文件已存在
        
        # 创建列表项
        file_size = file_path_obj.stat().st_size / (1024 * 1024)  # MB
        display_text = f"🎵 {file_path_obj.name}\n📁 {file_path_obj.parent}\n📊 {file_size:.1f} MB"
        
        item = QListWidgetItem(display_text)
        item.setData(Qt.UserRole, file_path)  # 存储完整路径
        item.setToolTip(file_path)
        
        self.addItem(item)
    
    def remove_selected_files(self):
        """移除选中的文件"""
        for item in self.selectedItems():
            self.takeItem(self.row(item))
    
    def clear_all_files(self):
        """清空所有文件"""
        self.clear()
    
    def get_file_paths(self) -> List[str]:
        """获取所有文件路径"""
        file_paths = []
        for i in range(self.count()):
            item = self.item(i)
            if item and item.flags() != Qt.NoItemFlags:  # 排除占位符
                file_path = item.data(Qt.UserRole)
                if file_path:
                    file_paths.append(file_path)
        return file_paths


class DragDropLineEdit(QLineEdit):
    """支持拖拽的文件输入框"""
    
    file_dropped = Signal(str)  # 文件拖拽信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setAcceptDrops(True)
    
    def dragEnterEvent(self, event: QDragEnterEvent):
        """拖拽进入事件"""
        if event.mimeData().hasUrls():
            urls = event.mimeData().urls()
            if urls and urls[0].isLocalFile():
                file_path = Path(urls[0].toLocalFile())
                audio_extensions = {'.wav', '.mp3', '.flac', '.aac', '.m4a', '.ogg'}
                if file_path.suffix.lower() in audio_extensions:
                    event.acceptProposedAction()
                    return
        event.ignore()
    
    def dropEvent(self, event: QDropEvent):
        """拖拽放下事件"""
        if event.mimeData().hasUrls():
            urls = event.mimeData().urls()
            if urls and urls[0].isLocalFile():
                file_path = urls[0].toLocalFile()
                self.setText(file_path)
                self.file_dropped.emit(file_path)
                event.acceptProposedAction()
            else:
                event.ignore()
        else:
            event.ignore()


class UnifiedUploadArea(QWidget):
    """统一的点击/拖拽上传区域，模仿图片中的设计"""
    
    files_selected = Signal(list)  # 文件选择信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setAcceptDrops(True)
        self.selected_files = []
        self.setup_ui()
        self.setup_style()
    
    def setup_ui(self):
        """设置用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)
        
        # 创建上传提示区域（在没有文件时显示）
        self.upload_area = QWidget()
        upload_layout = QVBoxLayout(self.upload_area)
        upload_layout.setAlignment(Qt.AlignCenter)
        upload_layout.setSpacing(8)
        upload_layout.setContentsMargins(10, 15, 10, 15)
        
        # 创建图标和文字的水平布局
        header_layout = QHBoxLayout()
        header_layout.setAlignment(Qt.AlignCenter)
        header_layout.setSpacing(15)
        
        # 文件图标（缩小尺寸）
        self.icon_label = QLabel("📂")
        self.icon_label.setAlignment(Qt.AlignCenter)
        self.icon_label.setStyleSheet("""
            QLabel {
                font-size: 24px;
                color: #2B9D7C;
                background: transparent;
                border: none;
            }
        """)
        
        # 提示文字容器
        text_container = QWidget()
        text_layout = QVBoxLayout(text_container)
        text_layout.setContentsMargins(0, 0, 0, 0)
        text_layout.setSpacing(3)
        
        # 主提示文字
        self.main_hint = QLabel("点击选择文件或拖拽文件")
        self.main_hint.setAlignment(Qt.AlignLeft | Qt.AlignVCenter)
        self.main_hint.setStyleSheet("""
            QLabel {
                color: #FFFFFF;
                font-size: 15px;
                font-weight: bold;
                background: transparent;
                border: none;
            }
        """)
        
        # 副提示文字
        self.sub_hint = QLabel("支持批量选择多个音频文件")
        self.sub_hint.setAlignment(Qt.AlignLeft | Qt.AlignVCenter)
        self.sub_hint.setStyleSheet("""
            QLabel {
                color: rgba(255, 255, 255, 0.7);
                font-size: 13px;
                background: transparent;
                border: none;
            }
        """)
        
        text_layout.addWidget(self.main_hint)
        text_layout.addWidget(self.sub_hint)
        
        header_layout.addWidget(self.icon_label)
        header_layout.addWidget(text_container)
        
        # 格式说明（独立一行，较小字体）
        self.format_hint = QLabel("支持格式: WAV, MP3, FLAC, AAC, M4A, OGG")
        self.format_hint.setAlignment(Qt.AlignCenter)
        self.format_hint.setStyleSheet("""
            QLabel {
                color: rgba(255, 255, 255, 0.4);
                font-size: 11px;
                background: transparent;
                border: none;
                margin-top: 5px;
            }
        """)
        
        # 将元素添加到上传区域
        upload_layout.addLayout(header_layout)
        upload_layout.addWidget(self.format_hint)
        
        # 将上传区域添加到主布局
        layout.addWidget(self.upload_area)
    
    def setup_style(self):
        """设置样式"""
        self.setStyleSheet("""
            UnifiedUploadArea {
                border: 2px dashed #444444;
                border-radius: 6px;
                background-color: #14161A;
                min-height: 100px;
            }
            UnifiedUploadArea:hover {
                border-color: #2B9D7C;
                background-color: rgba(43, 157, 124, 0.1);
            }
        """)
        self.setCursor(Qt.PointingHandCursor)
    
    def mousePressEvent(self, event):
        """鼠标点击事件 - 打开文件选择对话框"""
        if event.button() == Qt.LeftButton:
            self.select_files()
        super().mousePressEvent(event)
    
    def select_files(self):
        """选择文件"""
        file_paths, _ = QFileDialog.getOpenFileNames(
            self,
            "选择音频文件（支持多选）",
            "",
            "音频文件 (*.wav *.mp3 *.flac *.aac *.m4a *.ogg);;所有文件 (*)"
        )
        if file_paths:
            self.add_files(file_paths)
    
    def add_files(self, file_paths):
        """添加文件到选择列表"""
        # 去重添加
        for file_path in file_paths:
            if file_path not in self.selected_files:
                self.selected_files.append(file_path)
        
        self.update_display()
        self.files_selected.emit(self.selected_files)
    
    def update_display(self):
        """更新显示 - 简化版，只保持上传提示"""
        # 始终显示上传区域，不再显示文件列表
        pass
    
    def dragEnterEvent(self, event: QDragEnterEvent):
        """拖拽进入事件"""
        if event.mimeData().hasUrls():
            # 检查是否包含音频文件
            urls = event.mimeData().urls()
            audio_extensions = {'.wav', '.mp3', '.flac', '.aac', '.m4a', '.ogg'}
            
            for url in urls:
                if url.isLocalFile():
                    file_path = Path(url.toLocalFile())
                    if file_path.suffix.lower() in audio_extensions:
                        event.acceptProposedAction()
                        # 添加拖拽时的视觉反馈
                        self.setStyleSheet(self.styleSheet().replace('#444444', '#2B9D7C'))
                        return
            
        event.ignore()
    
    def dragLeaveEvent(self, event):
        """拖拽离开事件"""
        # 恢复原始样式
        self.setStyleSheet(self.styleSheet().replace('#2B9D7C', '#444444'))
        super().dragLeaveEvent(event)
    
    def dropEvent(self, event: QDropEvent):
        """拖拽放下事件"""
        # 恢复原始样式
        self.setStyleSheet(self.styleSheet().replace('#2B9D7C', '#444444'))
        
        if event.mimeData().hasUrls():
            urls = event.mimeData().urls()
            audio_files = []
            audio_extensions = {'.wav', '.mp3', '.flac', '.aac', '.m4a', '.ogg'}
            
            for url in urls:
                if url.isLocalFile():
                    file_path = Path(url.toLocalFile())
                    if file_path.suffix.lower() in audio_extensions and file_path.exists():
                        audio_files.append(str(file_path))
            
            if audio_files:
                self.add_files(audio_files)
                event.acceptProposedAction()
            else:
                event.ignore()
        else:
            event.ignore()


class BatchVoiceSeparationWorker(QThread):
    """批量人声分离工作线程"""
    
    progress_updated = Signal(int)  # 总体进度
    file_progress_updated = Signal(int, int)  # 当前文件进度 (文件索引, 进度)
    status_updated = Signal(str)
    file_completed = Signal(int, dict)  # 文件完成 (文件索引, 输出文件字典)
    file_failed = Signal(int, str)  # 文件失败 (文件索引, 错误信息)
    batch_completed = Signal(list)  # 批量完成 (所有结果列表)
    
    def __init__(self, file_paths: List[str], output_dir: str, config: dict):
        super().__init__()
        self.file_paths = file_paths
        self.output_dir = output_dir
        self.config = config
        self.is_cancelled = False
        self.results = []
    
    def cancel(self):
        """取消处理"""
        self.is_cancelled = True
    
    def _filter_output_files(self, output_files: dict) -> dict:
        """
        根据用户配置过滤输出文件
        删除用户未选择的输出类型对应的文件
        
        Args:
            output_files: 原始输出文件字典
            
        Returns:
            dict: 过滤后的输出文件字典
        """
        import os
        
        if 'output_options' not in self.config:
            return output_files
        
        selected_outputs = self.config['output_options']
        filtered_files = {}
        files_to_delete = []
        
        # 首先确定要保留的文件
        for key, path in output_files.items():
            if key in selected_outputs and selected_outputs[key]:
                filtered_files[key] = path
            else:
                # 标记为待删除
                files_to_delete.append((key, path))
        
        # 特殊处理：如果选择了background但没有选择other，确保background文件不被删除
        if selected_outputs.get('background', False) and 'background' in output_files:
            if 'background' not in filtered_files:
                filtered_files['background'] = output_files['background']
                # 从待删除列表中移除background文件
                files_to_delete = [(k, p) for k, p in files_to_delete if k != 'background']
        
        # 删除未选择的文件
        for key, path in files_to_delete:
            if os.path.exists(path):
                try:
                    # 如果background文件指向other文件，且background被保留，则不删除other文件
                    if key == 'other' and 'background' in filtered_files:
                        background_path = filtered_files['background']
                        if os.path.samefile(path, background_path):
                            continue  # 跳过删除，因为background需要这个文件

                    os.remove(path)
                    print(f"🗑️ 已删除未选择的文件: {key} -> {os.path.basename(path)}")
                except Exception as e:
                    print(f"⚠️ 删除文件失败 {key}: {e}")
        
        return filtered_files
    
    def run(self):
        """执行批量处理"""
        try:
            from core.services import FlipTalkCoreService
            core_service = FlipTalkCoreService()
            
            if not core_service.voice_separation.separator:
                self.status_updated.emit("❌ 人声分离插件未初始化")
                return
            
            # 设置模型配置
            if 'model' in self.config:
                core_service.voice_separation.separator.set_config({
                    'model': self.config['model']
                })
            
            total_files = len(self.file_paths)
            self.results = []
            
            for i, file_path in enumerate(self.file_paths):
                if self.is_cancelled:
                    break
                
                file_name = Path(file_path).name
                # 截断文件名以适应显示区域
                display_name = VoiceSeparationDialog.truncate_filename(file_name, 30)
                self.status_updated.emit(f"🎵 处理文件 {i+1}/{total_files}: {display_name}")
                
                try:
                    # 执行人声分离
                    print(f"🔍 [DEBUG] BatchWorker调用参数:")
                    print(f"   config: {self.config}")
                    print(f"   output_options: {self.config.get('output_options')}")
                    result = core_service.voice_separation.separator.separate(
                        file_path, self.output_dir, self.config.get('output_options')
                    )
                    
                    # 根据用户选择的输出选项过滤文件
                    filtered_result = self._filter_output_files(result)
                    
                    self.results.append({
                        'file_path': file_path,
                        'success': True,
                        'output_files': filtered_result,
                        'error': None
                    })
                    
                    self.file_completed.emit(i, filtered_result)
                    
                except Exception as e:
                    error_msg = str(e)
                    self.results.append({
                        'file_path': file_path,
                        'success': False,
                        'output_files': {},
                        'error': error_msg
                    })
                    
                    self.file_failed.emit(i, error_msg)
                
                # 更新总体进度
                progress = int((i + 1) / total_files * 100)
                self.progress_updated.emit(progress)
            
            self.batch_completed.emit(self.results)
            
        except Exception as e:
            self.status_updated.emit(f"❌ 批量处理失败: {str(e)}")


class CascadedNetVoiceSeparationWorker(QThread):
    """CascadedNet人声分离工作线程"""

    progress_updated = Signal(int)  # 总体进度
    file_progress_updated = Signal(int, int)  # 当前文件进度 (文件索引, 进度)
    status_updated = Signal(str)
    file_completed = Signal(int, dict)  # 文件完成 (文件索引, 输出文件字典)
    file_failed = Signal(int, str)  # 文件失败 (文件索引, 错误信息)
    batch_completed = Signal(list)  # 批量完成 (所有结果列表)

    def __init__(self, file_paths: List[str], output_dir: str, config: dict):
        super().__init__()
        self.file_paths = file_paths
        self.output_dir = output_dir
        self.config = config
        self.is_cancelled = False
    
    def cancel(self):
        """取消处理"""
        self.is_cancelled = True
        
    def run(self):
        """运行CascadedNet批量处理"""
        try:
            # 导入CascadedNet分离器
            from models.vocal_separation import create_separator
            
            # 查找CascadedNet模型文件
            model_path = None
            project_root = Path(__file__).parent.parent
            weights_dir = project_root / "models" / "vocal_separation" / "weights"
            
            # 查找baseline.pth模型文件
            if weights_dir.exists():
                baseline_model = weights_dir / "baseline.pth"
                if baseline_model.exists():
                    model_path = str(baseline_model)
                else:
                    # 查找其他.pth文件
                    pth_files = list(weights_dir.glob("*.pth"))
                    if pth_files:
                        model_path = str(pth_files[0])
            
            if not model_path:
                raise Exception("未找到CascadedNet模型文件，请确保baseline.pth存在于models/vocal_separation/weights/目录中")
            
            # 创建CascadedNet分离器并加载模型
            self.status_updated.emit("正在加载CascadedNet模型...")
            
            # 从配置中获取质量设置
            quality = self.config.get('quality', 'balanced')  # fast, balanced, high
            quality_config = {
                'fast': {'batchsize': 8, 'postprocess': False},
                'balanced': {'batchsize': 4, 'postprocess': False},
                'high': {'batchsize': 2, 'postprocess': True}
            }
            config = quality_config.get(quality, quality_config['balanced'])
            
            separator = create_separator(
                model_path=model_path,
                device="auto",
                batchsize=config['batchsize'],
                postprocess=config['postprocess']
            )
            
            results = []
            total_files = len(self.file_paths)
            
            for i, file_path in enumerate(self.file_paths):
                if self.is_cancelled:
                    break
                    
                try:
                    # 截断文件名以适应显示区域
                    file_name = Path(file_path).name
                    display_name = VoiceSeparationDialog.truncate_filename(file_name, 30)
                    self.status_updated.emit(f"正在处理: {display_name}")
                    
                    # 生成输出文件路径
                    file_stem = Path(file_path).stem
                    output_files = {}
                    
                    vocal_path = None
                    instrumental_path = None
                    
                    if self.config.get('vocals', True):
                        vocal_path = os.path.join(self.output_dir, f"{file_stem}_vocals.wav")
                        output_files['vocals'] = vocal_path
                    
                    if self.config.get('background', True):
                        instrumental_path = os.path.join(self.output_dir, f"{file_stem}_background.wav")
                        output_files['background'] = instrumental_path
                    
                    # 执行CascadedNet人声分离
                    # 截断文件名以适应显示区域 
                    file_name = Path(file_path).name
                    display_name = VoiceSeparationDialog.truncate_filename(file_name, 30)
                    self.status_updated.emit(f"正在分离音频: {display_name}")
                    
                    try:
                        # TTA默认启用
                        use_tta = True
                        
                        # 使用新的分离器接口
                        result_vocal, result_instrumental = separator.separate_audio_file(
                            input_path=file_path,
                            output_vocal_path=vocal_path,
                            output_instrumental_path=instrumental_path,
                            use_tta=use_tta,
                            sr=44100
                        )
                        
                        self.status_updated.emit(f"分离完成，正在验证输出文件...")
                        
                    except Exception as separation_error:
                        raise Exception(f"分离过程失败: {str(separation_error)}")
                    
                    # 验证输出文件是否生成
                    verified_output = {}
                    for key, path in output_files.items():
                        if path and os.path.exists(path) and os.path.getsize(path) > 0:
                            verified_output[key] = path
                    
                    # 发射文件完成信号
                    self.file_completed.emit(i, verified_output)
                    
                    # 记录结果
                    results.append({
                        'file_path': file_path,
                        'success': True,
                        'output_files': verified_output,
                        'error': None
                    })

        except Exception as e:
                    error_msg = f"处理失败: {str(e)}"
                    self.file_failed.emit(i, error_msg)
                    
                    # 记录失败结果
                    results.append({
                        'file_path': file_path,
                        'success': False,
                        'output_files': {},
                        'error': error_msg
                    })
                
                    # 更新总体进度
                    progress = int((i + 1) / total_files * 100)
                    self.progress_updated.emit(progress)
            
        # 发射批量完成信号
        self.batch_completed.emit(results)Z
            
        except Exception as e:
            self.status_updated.emit(f"处理过程中发生错误: {str(e)}")


class VoiceSeparationDialog(QDialog):
    """人声分离对话框（支持拖拽上传和批量处理）"""

    @staticmethod
    def truncate_filename(filename: str, max_length: int = 30) -> str:
        """截断文件名以适应显示区域"""
        if len(filename) <= max_length:
            return filename
        
        # 保留文件扩展名
        name, ext = os.path.splitext(filename)
        if len(ext) > 0:
            available_length = max_length - len(ext) - 3  # 减去扩展名和省略号的长度
            if available_length > 0:
                return f"{name[:available_length]}...{ext}"
        
        # 如果没有扩展名或者太长，直接截断
        return f"{filename[:max_length-3]}..."

    def __init__(self, parent=None):
        super().__init__(parent)
        
        self.selected_file_paths = []
        self.cascadednet_file_paths = []  # CascadedNet文件列表
        self.batch_worker = None
        self.cascadednet_worker = None  # CascadedNet工作线程
        self.is_processing = False
        
        # 配置信息
        self.config = {
            'model': 'htdemucs',
            'output_options': {
                'vocals': True,
                'background': True,
                'drums': False,
                'bass': False,
                'other': False
            }
        }
        
        self.setup_ui()
        self.setup_style()
        self.load_model_info()
        
        # 手动触发一次输出选项更新，确保初始状态正确
        self.on_batch_output_option_changed()

    def setup_ui(self):
        """设置用户界面"""
        self.setWindowTitle("🎵 音频人声分离 - FlipTalk AI")
        self.setFixedSize(1200, 800)  # 增大窗口尺寸以适应新功能
        self.setModal(True)

        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(0)
        main_layout.setContentsMargins(0, 0, 0, 0)

        # 创建标题区域（卡片式布局）
        self.create_header_section(main_layout)

        # 创建主内容区域
        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)
        content_layout.setSpacing(20)
        content_layout.setContentsMargins(30, 10, 30, 30)  # 减少上边距从20px到10px

        # 创建Tab页面
        self.create_tab_interface(content_layout)
        
        main_layout.addWidget(content_widget)

    def create_header_section(self, main_layout):
        """创建标题区域（卡片式布局）"""
        # 创建外层容器
        header_container = QWidget()
        # 移除固定高度限制，让标题区域根据内容自动调整高度
        header_container_layout = QHBoxLayout(header_container)
        header_container_layout.setContentsMargins(30, 15, 30, 8)  # 减少下边距从15px到8px
        
        # 创建卡片式标题框
        header_card = QFrame()
        # 移除最大宽度限制，让它填满可用空间（与下面内容宽度一致）
        header_card.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #2B9D7C, stop:1 #00CC55);
                border: none;
                border-radius: 15px;
                margin: 5px;
            }
        """)

        header_layout = QVBoxLayout(header_card)
        header_layout.setAlignment(Qt.AlignCenter)
        header_layout.setContentsMargins(20, 12, 20, 12)  # 减少上下内边距从20px到12px
        header_layout.setSpacing(5)  # 减少标题和副标题之间的间距从8px到5px

        title_label = QLabel("🎵 音频人声分离")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 24px;
                font-weight: bold;
                background: transparent;
                border: none;
                padding: 3px 0px;
            }
        """)

        subtitle_label = QLabel("使用AI技术智能分离音频中的人声和背景音乐")
        subtitle_label.setAlignment(Qt.AlignCenter)
        subtitle_label.setStyleSheet("""
            QLabel {
                color: rgba(255, 255, 255, 0.9);
                font-size: 14px;
                background: transparent;
                border: none;
                padding: 3px 0px;
            }
        """)

        header_layout.addWidget(title_label)
        header_layout.addWidget(subtitle_label)
        
        # 直接添加卡片，不使用居中布局，让它填满可用宽度
        header_container_layout.addWidget(header_card)
        
        main_layout.addWidget(header_container)

    def create_tab_interface(self, parent_layout):
        """创建Tab页面界面"""
        # 创建Tab控件
        self.tab_widget = QTabWidget()
        self.tab_widget.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #444444;
                background-color: #1B1E24;
                border-radius: 6px;
                margin-top: -1px;
            }
            QTabWidget::tab-bar {
                left: 5px;
            }
            QTabBar::tab {
                background-color: #2B2F36;
                color: #FFFFFF;
                padding: 12px 20px;
                margin-right: 2px;
                border: 1px solid #444444;
                border-bottom: none;
                border-top-left-radius: 6px;
                border-top-right-radius: 6px;
                font-size: 13px;
                font-weight: 500;
            }
            QTabBar::tab:selected {
                background-color: #2B9D7C;
                color: #FFFFFF;
                border-color: #2B9D7C;
            }
            QTabBar::tab:hover:!selected {
                background-color: rgba(43, 157, 124, 0.2);
                color: #FFFFFF;
            }
        """)
        
        # 创建CascadedNet Tab（推荐）
        self.create_cascadednet_tab()
        
        # 创建HTDemucs Tab
        self.create_htdemucs_tab()
        
        parent_layout.addWidget(self.tab_widget)

    def create_htdemucs_tab(self):
        """创建HTDemucs人声分离Tab页"""
        htdemucs_widget = QWidget()
        htdemucs_layout = QVBoxLayout(htdemucs_widget)
        htdemucs_layout.setSpacing(15)
        htdemucs_layout.setContentsMargins(20, 20, 20, 20)
        
        # 创建HTDemucs界面（使用原有的批量处理界面）
        self.create_htdemucs_interface(htdemucs_layout)
        
        # 添加Tab
        self.tab_widget.addTab(htdemucs_widget, "🔬 HTDemucs - 高品质分离")

    def create_cascadednet_tab(self):
        """创建CascadedNet人声分离Tab页"""
        cascadednet_widget = QWidget()
        cascadednet_layout = QVBoxLayout(cascadednet_widget)
        cascadednet_layout.setSpacing(15)
        cascadednet_layout.setContentsMargins(20, 20, 20, 20)
        
        # 创建CascadedNet界面
        self.create_cascadednet_interface(cascadednet_layout)
        
        # 添加Tab
        self.tab_widget.addTab(cascadednet_widget, "🌟 CascadedNet - 智能推荐")

    def create_htdemucs_interface(self, parent_layout):
        """创建HTDemucs批量处理界面（支持单个和多个文件）"""
        # 创建分割器
        batch_splitter = QSplitter(Qt.Horizontal)
        
        # 设置分割器的深色主题样式
        batch_splitter.setStyleSheet("""
            QSplitter {
                background-color: #1B1E24;
                border: none;
            }
            QSplitter::handle {
                background-color: #444444;
                border: none;
                width: 1px;
                margin: 0px;
            }
            QSplitter::handle:hover {
                background-color: #2B9D7C;
            }
        """)

        # 左侧：文件列表和配置
        self.create_batch_config_section(batch_splitter)

        # 右侧：批量处理状态
        self.create_batch_process_section(batch_splitter)

        batch_splitter.setSizes([600, 500])
        parent_layout.addWidget(batch_splitter)

    def create_cascadednet_interface(self, parent_layout):
        """创建CascadedNet人声分离界面"""
        # 创建分割器
        cascadednet_splitter = QSplitter(Qt.Horizontal)
        
        # 设置分割器的深色主题样式
        cascadednet_splitter.setStyleSheet("""
            QSplitter {
                background-color: #1B1E24;
                border: none;
            }
            QSplitter::handle {
                background-color: #444444;
                border: none;
                width: 1px;
                margin: 0px;
            }
            QSplitter::handle:hover {
                background-color: #2B9D7C;
            }
        """)

        # 左侧：CascadedNet配置
        self.create_cascadednet_config_section(cascadednet_splitter)

        # 右侧：CascadedNet处理状态
        self.create_cascadednet_process_section(cascadednet_splitter)

        cascadednet_splitter.setSizes([600, 500])
        parent_layout.addWidget(cascadednet_splitter)

    def create_batch_config_section(self, splitter):
        """创建HTDemucs批量配置区域"""
        config_widget = QWidget()
        config_layout = QVBoxLayout(config_widget)
        config_layout.setSpacing(15)
        config_layout.setContentsMargins(0, 0, 0, 0)

        # 统一的文件上传区域
        file_group = QGroupBox("📁 文件选择")
        file_group.setStyleSheet(self.get_group_style())
        file_layout = QVBoxLayout(file_group)

        # 创建统一的点击/拖拽上传区域
        self.unified_upload_area = UnifiedUploadArea()
        self.unified_upload_area.files_selected.connect(self.on_batch_files_selected)
        file_layout.addWidget(self.unified_upload_area)

        config_layout.addWidget(file_group)

        # 输出目录选择组
        output_group = QGroupBox("📂 输出目录")
        output_group.setStyleSheet(self.get_group_style())
        output_layout = QVBoxLayout(output_group)

        output_dir_layout = QHBoxLayout()
        self.batch_output_input = QLineEdit()
        self.batch_output_input.setPlaceholderText("请选择批量处理输出目录...")
        self.batch_output_input.setStyleSheet(self.get_input_style())

        self.batch_output_browse_button = QPushButton("📁 浏览")
        self.batch_output_browse_button.setStyleSheet(self.get_button_style())
        self.batch_output_browse_button.clicked.connect(self.browse_batch_output_dir)

        output_dir_layout.addWidget(self.batch_output_input)
        output_dir_layout.addWidget(self.batch_output_browse_button)
        output_layout.addLayout(output_dir_layout)

        config_layout.addWidget(output_group)

        # 批量处理设置组
        batch_settings_group = QGroupBox("⚙️ 批量处理设置")
        batch_settings_group.setStyleSheet(self.get_group_style())
        batch_settings_layout = QVBoxLayout(batch_settings_group)

        # 模型选择
        model_layout = QVBoxLayout()
        
        # 模型选择下拉框
        model_select_layout = QHBoxLayout()
        ai_model_label = QLabel("AI模型:")
        ai_model_label.setStyleSheet("""
            QLabel {
                color: #FFFFFF;
                font-size: 13px;
                background: transparent;
                border: none;
            }
        """)
        model_select_layout.addWidget(ai_model_label)
        
        self.batch_model_combo = QComboBox()
        self.batch_model_combo.setStyleSheet(self.get_combo_style())
        self.batch_model_combo.currentTextChanged.connect(self.on_batch_model_changed)
        model_select_layout.addWidget(self.batch_model_combo)
        model_layout.addLayout(model_select_layout)
        
        # 批量处理模型信息显示
        self.batch_model_info_label = QLabel()
        self.batch_model_info_label.setStyleSheet("""
            QLabel {
                color: #FFFFFF;
                font-size: 12px;
                padding: 12px;
                background-color: #14161A;
                border: 1px solid #444444;
                border-radius: 6px;
                line-height: 1.4;
            }
        """)
        self.batch_model_info_label.setWordWrap(True)
        self.batch_model_info_label.setMinimumHeight(40)  # 设置最小高度以显示简洁信息
        self.batch_model_info_label.setAlignment(Qt.AlignTop | Qt.AlignLeft)  # 顶部左对齐
        model_layout.addWidget(self.batch_model_info_label)
        
        batch_settings_layout.addLayout(model_layout)

        # 输出选项（批量处理使用相同配置）
        batch_output_group = QGroupBox("📤 输出选项")
        batch_output_group.setStyleSheet(self.get_group_style())
        batch_output_options_layout = QVBoxLayout(batch_output_group)

        # 创建复选框（使用水平布局实现单行排列）
        batch_checkbox_layout = QHBoxLayout()
        batch_checkbox_layout.setSpacing(15)  # 设置间距

        self.batch_vocals_cb = QCheckBox("🎤 人声")
        self.batch_background_cb = QCheckBox("🎵 背景音乐")
        self.batch_drums_cb = QCheckBox("🥁 鼓声")
        self.batch_bass_cb = QCheckBox("🎸 低音")
        self.batch_other_cb = QCheckBox("🎹 其他乐器")

        # 设置默认选中状态
        self.batch_vocals_cb.setChecked(True)
        self.batch_background_cb.setChecked(True)

        # 单行横向排列所有选项
        batch_checkboxes = [self.batch_vocals_cb, self.batch_background_cb, self.batch_drums_cb, self.batch_bass_cb, self.batch_other_cb]
        
        for cb in batch_checkboxes:
            cb.setStyleSheet(self.get_checkbox_style())
            cb.stateChanged.connect(self.on_batch_output_option_changed)
            batch_checkbox_layout.addWidget(cb)
        
        # 添加弹性空间使复选框均匀分布
        batch_checkbox_layout.addStretch()
        
        batch_output_options_layout.addLayout(batch_checkbox_layout)

        batch_settings_layout.addWidget(batch_output_group)
        config_layout.addWidget(batch_settings_group)
        config_layout.addStretch()

        splitter.addWidget(config_widget)

    def create_batch_process_section(self, splitter):
        """创建HTDemucs批量处理区域"""
        process_widget = QWidget()
        process_layout = QVBoxLayout(process_widget)
        process_layout.setSpacing(15)
        process_layout.setContentsMargins(0, 0, 0, 0)

        # 处理状态组
        status_group = QGroupBox("⚡ 批量处理状态")
        status_group.setStyleSheet(self.get_group_style())
        status_layout = QVBoxLayout(status_group)

        # 进度条
        self.batch_progress_bar = QProgressBar()
        self.batch_progress_bar.setFixedHeight(25)
        self.batch_progress_bar.setValue(0)
        self.batch_progress_bar.setStyleSheet(self.get_progress_style())
        status_layout.addWidget(self.batch_progress_bar)

        # 状态标签
        self.batch_status_label = QLabel("请添加音频文件开始处理")
        self.batch_status_label.setAlignment(Qt.AlignCenter)
        self.batch_status_label.setStyleSheet("""
            QLabel {
                color: #FFFFFF;
                font-size: 14px;
                padding: 10px;
                background-color: #14161A;
                border-radius: 5px;
                border: 1px solid #444444;
            }
        """)
        status_layout.addWidget(self.batch_status_label)

        process_layout.addWidget(status_group)

        # 处理结果表格
        result_group = QGroupBox("📊 处理结果")
        result_group.setStyleSheet(self.get_group_style())
        result_layout = QVBoxLayout(result_group)

        self.batch_result_table = QTableWidget()
        self.batch_result_table.setColumnCount(5)
        self.batch_result_table.setHorizontalHeaderLabels(["文件名", "大小", "状态", "输出文件", "错误信息"])
        
        # 设置表格选择模式
        self.batch_result_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.batch_result_table.setSelectionMode(QTableWidget.SingleSelection)
        
        # 隐藏垂直表头（行号列）
        self.batch_result_table.verticalHeader().setVisible(False)
        
        # 设置表格样式（深色主题）
        self.batch_result_table.setStyleSheet("""
            QTableWidget {
                background-color: #14161A;
                border: 1px solid #444444;
                border-radius: 5px;
                gridline-color: #444444;
                color: #FFFFFF;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #333333;
                color: #FFFFFF;
            }
            QTableWidget::item:selected {
                background-color: rgba(43, 157, 124, 0.3);
                color: #FFFFFF;
            }
            QHeaderView::section {
                background-color: #1B1E24;
                border: 1px solid #444444;
                padding: 8px;
                font-weight: bold;
                color: #FFFFFF;
            }
            QHeaderView::section:vertical {
                background-color: #14161A;
                border: 1px solid #444444;
            }
        """)
        
        # 设置表格属性
        header = self.batch_result_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(0, QHeaderView.Fixed)  # 文件名
        header.setSectionResizeMode(1, QHeaderView.Fixed)  # 大小
        header.setSectionResizeMode(2, QHeaderView.Fixed)  # 状态
        header.setSectionResizeMode(3, QHeaderView.Fixed)  # 输出文件
        header.setSectionResizeMode(4, QHeaderView.Stretch)  # 错误信息（可拉伸）
        
        # 设置列宽，与CascadedNet表格保持一致
        self.batch_result_table.setColumnWidth(0, 150)  # 文件名列（与CascadedNet一致）
        self.batch_result_table.setColumnWidth(1, 80)   # 大小列（与CascadedNet一致）
        self.batch_result_table.setColumnWidth(2, 90)   # 状态列（与CascadedNet一致）
        self.batch_result_table.setColumnWidth(3, 180)  # 输出文件列（与CascadedNet一致）

        # 设置表格固定高度，与CascadedNet保持一致
        self.batch_result_table.setMinimumHeight(300)
        self.batch_result_table.setMaximumHeight(400)

        result_layout.addWidget(self.batch_result_table)

        # 批量处理按钮区域 - 移动到处理结果Group box内部
        batch_button_layout = QHBoxLayout()

        # 移除选中文件按钮
        self.remove_selected_button = QPushButton("🗑️ 移除选中")
        self.remove_selected_button.setFixedSize(120, 36)
        self.remove_selected_button.setStyleSheet(self.get_button_style())
        self.remove_selected_button.clicked.connect(self.remove_selected_file)
        self.remove_selected_button.setEnabled(False)  # 初始状态禁用

        # 清空列表按钮
        self.clear_list_button = QPushButton("🧹 清空列表")
        self.clear_list_button.setFixedSize(120, 36)
        self.clear_list_button.setStyleSheet(self.get_button_style())
        self.clear_list_button.clicked.connect(self.clear_batch_list)
        self.clear_list_button.setEnabled(False)  # 初始状态禁用

        self.batch_start_button = QPushButton("🔬 开始处理")
        self.batch_start_button.setFixedSize(120, 36)
        self.batch_start_button.setStyleSheet(self.get_primary_button_style())
        self.batch_start_button.clicked.connect(self.start_batch_separation)

        self.batch_cancel_button = QPushButton("⏹️ 取消处理")
        self.batch_cancel_button.setFixedSize(120, 36)
        self.batch_cancel_button.setStyleSheet(self.get_secondary_button_style())
        self.batch_cancel_button.setEnabled(False)
        self.batch_cancel_button.clicked.connect(self.cancel_batch_processing)

        batch_button_layout.addWidget(self.remove_selected_button)
        batch_button_layout.addWidget(self.clear_list_button)
        batch_button_layout.addStretch()
        batch_button_layout.addWidget(self.batch_start_button)
        batch_button_layout.addWidget(self.batch_cancel_button)

        result_layout.addLayout(batch_button_layout)
        process_layout.addWidget(result_group)
        process_layout.addStretch()

        splitter.addWidget(process_widget)

    def create_cascadednet_config_section(self, splitter):
        """创建CascadedNet配置区域"""
        config_widget = QWidget()
        config_layout = QVBoxLayout(config_widget)
        config_layout.setSpacing(15)
        config_layout.setContentsMargins(0, 0, 0, 0)

        # 统一的文件上传区域
        file_group = QGroupBox("📁 文件选择")
        file_group.setStyleSheet(self.get_group_style())
        file_layout = QVBoxLayout(file_group)

        # 创建CascadedNet的文件上传区域
        self.cascadednet_upload_area = UnifiedUploadArea()
        self.cascadednet_upload_area.files_selected.connect(self.on_cascadednet_files_selected)
        file_layout.addWidget(self.cascadednet_upload_area)

        config_layout.addWidget(file_group)

        # 输出目录选择组
        output_group = QGroupBox("📂 输出目录")
        output_group.setStyleSheet(self.get_group_style())
        output_layout = QVBoxLayout(output_group)

        output_dir_layout = QHBoxLayout()
        self.cascadednet_output_input = QLineEdit()
        self.cascadednet_output_input.setPlaceholderText("请选择CascadedNet输出目录...")
        
        # 设置默认输出目录
        default_output_dir = os.path.join(os.path.expanduser("~"), "Downloads")
        self.cascadednet_output_input.setText(default_output_dir)
        
        self.cascadednet_output_input.setStyleSheet(self.get_input_style())

        self.cascadednet_output_browse_button = QPushButton("📁 浏览")
        self.cascadednet_output_browse_button.setStyleSheet(self.get_button_style())
        self.cascadednet_output_browse_button.clicked.connect(self.browse_cascadednet_output_dir)

        output_dir_layout.addWidget(self.cascadednet_output_input)
        output_dir_layout.addWidget(self.cascadednet_output_browse_button)
        output_layout.addLayout(output_dir_layout)

        config_layout.addWidget(output_group)

        # CascadedNet处理设置组
        cascadednet_settings_group = QGroupBox("🌟 CascadedNet设置")
        cascadednet_settings_group.setStyleSheet(self.get_group_style())
        cascadednet_settings_layout = QVBoxLayout(cascadednet_settings_group)

        # CascadedNet模型信息显示
        self.cascadednet_model_info_label = QLabel()
        self.cascadednet_model_info_label.setStyleSheet("""
            QLabel {
                color: #FFFFFF;
                font-size: 12px;
                padding: 12px;
                background-color: #14161A;
                border: 1px solid #444444;
                border-radius: 6px;
                line-height: 1.4;
            }
        """)
        self.cascadednet_model_info_label.setWordWrap(True)
        self.cascadednet_model_info_label.setMinimumHeight(40)
        self.cascadednet_model_info_label.setAlignment(Qt.AlignTop | Qt.AlignLeft)
        
        # 设置CascadedNet模型信息
        cascadednet_info = """🌟 <b>CascadedNet - 智能推荐</b> | 🏆🏆🏆🏆🏆 高品质 | 🔥🔥🔥 中等速度 | 🚫 适合大部分音频文件"""
        self.cascadednet_model_info_label.setText(cascadednet_info)
        
        cascadednet_settings_layout.addWidget(self.cascadednet_model_info_label)

        # CascadedNet处理选项
        cascadednet_options_group = QGroupBox("📤 输出选项")
        cascadednet_options_group.setStyleSheet(self.get_group_style())
        cascadednet_options_layout = QVBoxLayout(cascadednet_options_group)

        # CascadedNet复选框（只支持人声和背景音乐）
        cascadednet_checkbox_layout = QHBoxLayout()
        cascadednet_checkbox_layout.setSpacing(15)

        self.cascadednet_vocals_cb = QCheckBox("🎤 人声")
        self.cascadednet_background_cb = QCheckBox("🎵 背景音乐")

        # 设置默认选中状态
        self.cascadednet_vocals_cb.setChecked(True)
        self.cascadednet_background_cb.setChecked(True)

        cascadednet_checkboxes = [self.cascadednet_vocals_cb, self.cascadednet_background_cb]
        
        for cb in cascadednet_checkboxes:
            cb.setStyleSheet(self.get_checkbox_style())
            cb.stateChanged.connect(self.on_cascadednet_output_option_changed)
            cascadednet_checkbox_layout.addWidget(cb)
        
        # 添加弹性空间
        cascadednet_checkbox_layout.addStretch()
        
        cascadednet_options_layout.addLayout(cascadednet_checkbox_layout)

        # CascadedNet高级选项
        # TTA已默认启用，无需UI控制

        cascadednet_settings_layout.addWidget(cascadednet_options_group)
        config_layout.addWidget(cascadednet_settings_group)
        config_layout.addStretch()

        splitter.addWidget(config_widget)

    def create_cascadednet_process_section(self, splitter):
        """创建CascadedNet处理区域"""
        process_widget = QWidget()
        process_layout = QVBoxLayout(process_widget)
        process_layout.setSpacing(15)
        process_layout.setContentsMargins(0, 0, 0, 0)

        # 处理状态组
        status_group = QGroupBox("🌟 CascadedNet处理状态")
        status_group.setStyleSheet(self.get_group_style())
        status_layout = QVBoxLayout(status_group)

        # 进度条
        self.cascadednet_progress_bar = QProgressBar()
        self.cascadednet_progress_bar.setFixedHeight(25)
        self.cascadednet_progress_bar.setValue(0)
        self.cascadednet_progress_bar.setStyleSheet(self.get_progress_style())
        status_layout.addWidget(self.cascadednet_progress_bar)

        # 状态标签
        self.cascadednet_status_label = QLabel("请添加音频文件开始处理")
        self.cascadednet_status_label.setAlignment(Qt.AlignCenter)
        self.cascadednet_status_label.setStyleSheet("""
            QLabel {
                color: #FFFFFF;
                font-size: 14px;
                padding: 10px;
                background-color: #14161A;
                border-radius: 5px;
                border: 1px solid #444444;
            }
        """)
        status_layout.addWidget(self.cascadednet_status_label)

        process_layout.addWidget(status_group)

        # 处理结果表格
        result_group = QGroupBox("📊 处理结果")
        result_group.setStyleSheet(self.get_group_style())
        result_layout = QVBoxLayout(result_group)

        self.cascadednet_result_table = QTableWidget()
        self.cascadednet_result_table.setColumnCount(5)
        self.cascadednet_result_table.setHorizontalHeaderLabels(["文件名", "大小", "状态", "输出文件", "错误信息"])
        
        # 设置表格选择模式
        self.cascadednet_result_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.cascadednet_result_table.setSelectionMode(QTableWidget.SingleSelection)
        
        # 隐藏垂直表头（行号列）
        self.cascadednet_result_table.verticalHeader().setVisible(False)
        
        # 设置表格样式（深色主题）
        self.cascadednet_result_table.setStyleSheet("""
            QTableWidget {
                background-color: #14161A;
                border: 1px solid #444444;
                border-radius: 5px;
                gridline-color: #444444;
                color: #FFFFFF;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #333333;
                color: #FFFFFF;
            }
            QTableWidget::item:selected {
                background-color: rgba(43, 157, 124, 0.3);
                color: #FFFFFF;
            }
            QHeaderView::section {
                background-color: #1B1E24;
                border: 1px solid #444444;
                padding: 8px;
                font-weight: bold;
                color: #FFFFFF;
            }
            QHeaderView::section:vertical {
                background-color: #14161A;
                border: 1px solid #444444;
            }
        """)
        
        # 设置表格头部
        header = self.cascadednet_result_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(0, QHeaderView.Fixed)  # 文件名
        header.setSectionResizeMode(1, QHeaderView.Fixed)  # 大小  
        header.setSectionResizeMode(2, QHeaderView.Fixed)  # 状态
        header.setSectionResizeMode(3, QHeaderView.Fixed)  # 输出文件
        header.setSectionResizeMode(4, QHeaderView.Stretch)  # 错误信息（可拉伸）
        
        # 设置列宽 - 缩小文件名列以显示更多信息
        self.cascadednet_result_table.setColumnWidth(0, 150)  # 文件名列（缩小从默认到150）
        self.cascadednet_result_table.setColumnWidth(1, 80)   # 大小列
        self.cascadednet_result_table.setColumnWidth(2, 90)   # 状态列
        self.cascadednet_result_table.setColumnWidth(3, 180)  # 输出文件列（增加宽度显示更多信息）

        # 设置表格固定高度
        self.cascadednet_result_table.setMinimumHeight(300)
        self.cascadednet_result_table.setMaximumHeight(400)

        result_layout.addWidget(self.cascadednet_result_table)

        # 控制按钮
        button_layout = QHBoxLayout()

        self.cascadednet_remove_selected_button = QPushButton("🗑️ 移除选中")
        self.cascadednet_remove_selected_button.setFixedSize(120, 36)
        self.cascadednet_remove_selected_button.setStyleSheet(self.get_button_style())
        self.cascadednet_remove_selected_button.setEnabled(False)
        self.cascadednet_remove_selected_button.clicked.connect(self.remove_cascadednet_selected_file)
        
        self.cascadednet_clear_list_button = QPushButton("🧹 清空列表")
        self.cascadednet_clear_list_button.setFixedSize(120, 36)
        self.cascadednet_clear_list_button.setStyleSheet(self.get_button_style())
        self.cascadednet_clear_list_button.setEnabled(False)
        self.cascadednet_clear_list_button.clicked.connect(self.clear_cascadednet_list)
        
        self.cascadednet_start_button = QPushButton("🌟 开始处理")
        self.cascadednet_start_button.setFixedSize(120, 36)
        self.cascadednet_start_button.setStyleSheet(self.get_primary_button_style())
        self.cascadednet_start_button.clicked.connect(self.start_cascadednet_separation)
        
        self.cascadednet_cancel_button = QPushButton("⏹️ 取消处理")
        self.cascadednet_cancel_button.setFixedSize(120, 36)
        self.cascadednet_cancel_button.setStyleSheet(self.get_secondary_button_style())
        self.cascadednet_cancel_button.setEnabled(False)
        self.cascadednet_cancel_button.clicked.connect(self.cancel_cascadednet_processing)

        button_layout.addWidget(self.cascadednet_remove_selected_button)
        button_layout.addWidget(self.cascadednet_clear_list_button)
        button_layout.addStretch()
        button_layout.addWidget(self.cascadednet_start_button)
        button_layout.addWidget(self.cascadednet_cancel_button)

        result_layout.addLayout(button_layout)
        process_layout.addWidget(result_group)

        splitter.addWidget(process_widget)
    
    def setup_style(self):
        """设置样式"""
        self.setStyleSheet("""
            QDialog {
                background-color: #1B1E24;
                border-radius: 10px;
                color: #FFFFFF;
            }
        """)



    def get_group_style(self):
        """获取组框样式"""
        return """
            QGroupBox {
                font-weight: bold;
                font-size: 14px;
                color: #FFFFFF;
                border: 2px solid #444444;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
                background-color: #14161A;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px 0 8px;
                background-color: #14161A;
                color: #2B9D7C;
            }
        """

    def get_input_style(self):
        """获取输入框样式"""
        return """
            QLineEdit {
                padding: 10px;
                border: 2px solid #444444;
                border-radius: 6px;
                font-size: 13px;
                background-color: #14161A;
                color: #FFFFFF;
            }
            QLineEdit:focus {
                border-color: #2B9D7C;
                background-color: #1B1E24;
            }
            QLineEdit:hover {
                border-color: #2B9D7C;
            }
        """

    def get_button_style(self):
        """获取普通按钮样式"""
        return """
            QPushButton {
                background-color: #444444;
                color: #FFFFFF;
                border: none;
                border-radius: 6px;
                font-size: 12px;
                font-weight: bold;
                padding: 8px 16px;
                min-width: 60px;
            }
            QPushButton:hover {
                background-color: #555555;
            }
            QPushButton:pressed {
                background-color: #333333;
            }
        """

    def get_primary_button_style(self):
        """获取主要按钮样式"""
        return """
            QPushButton {
                background-color: #2B9D7C;
                color: #FFFFFF;
                border: none;
                border-radius: 8px;
                font-size: 14px;
                font-weight: bold;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background-color: #00CC55;
                transform: translateY(-1px);
            }
            QPushButton:pressed {
                background-color: #228B22;
            }
            QPushButton:disabled {
                background-color: #444444;
                color: #666666;
            }
        """

    def get_secondary_button_style(self):
        """获取次要按钮样式"""
        return """
            QPushButton {
                background-color: #dc3545;
                color: #FFFFFF;
                border: none;
                border-radius: 8px;
                font-size: 14px;
                font-weight: bold;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background-color: #c82333;
            }
            QPushButton:pressed {
                background-color: #bd2130;
            }
        """

    def get_combo_style(self):
        """获取下拉框样式"""
        return """
            QComboBox {
                padding: 8px 12px;
                border: 2px solid #444444;
                border-radius: 6px;
                font-size: 13px;
                background-color: #14161A;
                color: #FFFFFF;
                min-height: 20px;
            }
            QComboBox:hover {
                border-color: #2B9D7C;
            }
            QComboBox:focus {
                border-color: #2B9D7C;
            }
            QComboBox::drop-down {
                border: none;
                width: 20px;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid #FFFFFF;
                margin-right: 5px;
            }
            QComboBox QAbstractItemView {
                border: 1px solid #444444;
                background-color: #1B1E24;
                selection-background-color: #2B9D7C;
                selection-color: #FFFFFF;
                color: #FFFFFF;
            }
        """

    def get_checkbox_style(self):
        """获取复选框样式"""
        return """
            QCheckBox {
                font-size: 13px;
                color: #FFFFFF;
                spacing: 8px;
                padding: 5px;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
                border: 2px solid #444444;
                border-radius: 4px;
                background-color: #14161A;
            }
            QCheckBox::indicator:hover {
                border-color: #2B9D7C;
            }
            QCheckBox::indicator:checked {
                background-color: #2B9D7C;
                border-color: #2B9D7C;
            }
        """

    def get_progress_style(self):
        """获取进度条样式"""
        return """
            QProgressBar {
                border: 2px solid #444444;
                border-radius: 8px;
                text-align: center;
                font-weight: bold;
                font-size: 12px;
                color: #FFFFFF;
                background-color: #14161A;
            }
            QProgressBar::chunk {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #2B9D7C, stop:1 #00CC55);
                border-radius: 6px;
                margin: 1px;
            }
        """

    def load_model_info(self):
        """加载模型信息"""
        try:
            from core.services import FlipTalkCoreService
            core_service = FlipTalkCoreService()

            if core_service.voice_separation.separator:
                quality_settings = core_service.voice_separation.separator.get_quality_settings()
                models = quality_settings.get('models', {})

                # 按推荐顺序添加模型选项
                model_order = ['htdemucs', 'mdx_extra', 'htdemucs_ft', 'mdx', 'hdemucs_mmi']

                # 填充模型下拉框（批量处理）
                if hasattr(self, 'batch_model_combo'):
                    for model_key in model_order:
                        if model_key in models:
                            model_info = models[model_key]
                            display_name = model_info.get('name', model_key)
                            # 为推荐模型添加标记
                            if model_key == 'htdemucs':
                                display_name += " 🌟推荐"
                        self.batch_model_combo.addItem(display_name, model_key)

                # 设置默认选择
                current_model = quality_settings.get('current_model', 'htdemucs')
                
                if hasattr(self, 'batch_model_combo'):
                    for i in range(self.batch_model_combo.count()):
                        if self.batch_model_combo.itemData(i) == current_model:
                            self.batch_model_combo.setCurrentIndex(i)
                            break

                # 更新批量处理模型信息
                if hasattr(self, 'batch_model_info_label'):
                    self.update_batch_model_info()
        except Exception as e:
            print(f"加载模型信息失败: {e}")
            # 添加默认模型
            default_models = [
                ("HTDemucs - 高质量 🌟推荐", "htdemucs"),
                ("MDX-Net Extra - 超高质量", "mdx_extra"),
                ("HTDemucs Fine-tuned - 微调版", "htdemucs_ft"),
                ("MDX-Net - 高质量", "mdx"),
                ("Hybrid Demucs MMI - 中等质量", "hdemucs_mmi")
            ]
            
            for display_name, model_id in default_models:
            if hasattr(self, 'batch_model_combo'):
                    self.batch_model_combo.addItem(display_name, model_id)


    
    def on_batch_files_selected(self, file_paths: List[str]):
        """批量文件选择处理"""
        # 存储文件列表
        self.selected_file_paths = file_paths[:]
        
        if file_paths:
            # 在处理结果表格中显示文件信息
            self.update_result_table_with_files()
        else:
            self.selected_file_paths = []
            self.batch_result_table.setRowCount(0)
        
        # 更新状态和按钮
        self.update_batch_status_and_buttons()
    
    def update_result_table_with_files(self):
        """在处理结果表格中显示待处理文件"""
        self.batch_result_table.setRowCount(0)
        
        for i, file_path in enumerate(self.selected_file_paths):
            self.add_file_to_result_table(i, file_path, "📋 待处理")
        
        # 连接表格选择信号，启用移除按钮
        self.batch_result_table.itemSelectionChanged.connect(self.on_table_selection_changed)
    
    def add_file_to_result_table(self, row: int, file_path: str, status: str = "📋 待处理"):
        """向结果表格添加文件行"""
        self.batch_result_table.insertRow(row)
        
        # 文件名（只显示文件名，不包含路径）
        filename = Path(file_path).name
        filename_item = QTableWidgetItem(filename)
        filename_item.setToolTip(f"完整路径: {file_path}")  # 鼠标悬停显示完整路径
        filename_item.setData(Qt.UserRole, file_path)  # 存储完整路径
        self.batch_result_table.setItem(row, 0, filename_item)
        
        # 文件大小
        try:
            file_size = Path(file_path).stat().st_size / (1024 * 1024)  # MB
            size_text = f"{file_size:.1f} MB"
        except:
            size_text = "未知"
        size_item = QTableWidgetItem(size_text)
        self.batch_result_table.setItem(row, 1, size_item)
        
        # 状态
        status_item = QTableWidgetItem(status)
        self.batch_result_table.setItem(row, 2, status_item)
        
        # 输出文件（初始为空）
        output_item = QTableWidgetItem("")
        self.batch_result_table.setItem(row, 3, output_item)
        
        # 错误信息（初始为空）
        error_item = QTableWidgetItem("")
        self.batch_result_table.setItem(row, 4, error_item)
        
        # 设置状态颜色（适应深色主题）
        if status == "📋 待处理":
            status_item.setBackground(QColor("#5B4E00"))  # 深色版本的黄色背景
        elif status == "✅ 完成":
            status_item.setBackground(QColor("#0F5132"))  # 深色版本的绿色背景
        elif status == "❌ 失败":
            status_item.setBackground(QColor("#721C24"))  # 深色版本的红色背景
        elif status == "⏳ 处理中":
            status_item.setBackground(QColor("#1B4F72"))  # 深色版本的蓝色背景
    
    def on_table_selection_changed(self):
        """表格选择改变时的处理"""
        selected_rows = self.batch_result_table.selectionModel().selectedRows()
        self.remove_selected_button.setEnabled(len(selected_rows) > 0)
    
    def remove_selected_file(self):
        """移除选中的文件"""
        selected_rows = self.batch_result_table.selectionModel().selectedRows()
        if not selected_rows:
            return
        
        # 获取选中行的文件路径
        row = selected_rows[0].row()
        filename_item = self.batch_result_table.item(row, 0)
        if filename_item:
            file_path = filename_item.data(Qt.UserRole)
            
            # 从文件列表中移除
            if file_path in self.selected_file_paths:
                self.selected_file_paths.remove(file_path)
                
                # 同时从左侧上传区域的选择中移除
                if hasattr(self.unified_upload_area, 'selected_files') and file_path in self.unified_upload_area.selected_files:
                    self.unified_upload_area.selected_files.remove(file_path)
                
                # 更新表格显示
                self.update_result_table_with_files()
                
                # 更新状态和按钮
                self.update_batch_status_and_buttons()

    def clear_batch_list(self):
        """清空批量处理列表"""
        if not self.selected_file_paths:
            return
        
        # 直接执行清空操作，不再弹出确认对话框
        # 清空文件列表
        self.selected_file_paths = []
        
        # 同时清空左侧上传区域的选择
        if hasattr(self.unified_upload_area, 'selected_files'):
            self.unified_upload_area.selected_files = []
            self.unified_upload_area.update_display()  # 更新上传区域显示
        
        # 清空表格
        self.batch_result_table.setRowCount(0)
        
        # 更新状态和按钮
        self.update_batch_status_and_buttons()

    def update_batch_status_and_buttons(self):
        """更新批量处理状态和按钮状态"""
                if self.selected_file_paths:
                    self.batch_status_label.setText(f"✅ 已选择 {len(self.selected_file_paths)} 个文件")
                    self.clear_list_button.setEnabled(True)
                else:
                    self.batch_status_label.setText("请选择音频文件开始处理")
                    self.remove_selected_button.setEnabled(False)
                    self.clear_list_button.setEnabled(False)
    
    # 这些方法已经不需要了，因为使用了统一的上传区域
    
    def browse_batch_output_dir(self):
        """浏览批量输出目录"""
        dir_path = QFileDialog.getExistingDirectory(
            self,
            "选择批量处理输出目录",
            ""
        )
        if dir_path:
            self.batch_output_input.setText(dir_path)
    
    def on_batch_model_changed(self):
        """批量处理模型选择改变"""
        current_model = self.batch_model_combo.currentData()
        if current_model:
            self.config['model'] = current_model
        
        # 更新批量处理模型信息
        self.update_batch_model_info()
    
    def update_batch_model_info(self):
        """更新批量处理模型信息显示"""
        current_model = self.batch_model_combo.currentData()
        if not current_model:
            return

        try:
            from core.services import FlipTalkCoreService
            core_service = FlipTalkCoreService()

            if core_service.voice_separation.separator:
                quality_settings = core_service.voice_separation.separator.get_quality_settings()
                models = quality_settings.get('models', {})

                if current_model in models:
                    model_info = models[current_model]
                    
                    # 构建简洁的模型信息（单行显示）
                    info_text = f"🔬 <b>{model_info.get('description', 'HTDemucs - 高品质分离')}</b> | "
                    info_text += f"📊 {model_info.get('quality', '未知质量')} | "
                    info_text += f"⚡ {model_info.get('speed', '未知速度')} | "
                    info_text += f"🎯 {model_info.get('use_case', '未知场景')} | "
                    info_text += f"💾 {model_info.get('model_size', '未知大小')}"
                    
                    self.batch_model_info_label.setText(info_text)
                else:
                    self.batch_model_info_label.setText("❌ 模型信息不可用")
        except Exception as e:
            self.batch_model_info_label.setText(f"获取模型信息失败: {e}")
    
    def on_batch_output_option_changed(self):
        """批量处理输出选项改变"""
        self.config['output_options'] = {
            'vocals': self.batch_vocals_cb.isChecked(),
            'background': self.batch_background_cb.isChecked(),
            'drums': self.batch_drums_cb.isChecked(),
            'bass': self.batch_bass_cb.isChecked(),
            'other': self.batch_other_cb.isChecked()
        }
    
    def start_batch_separation(self):
        """开始批量人声分离"""
        file_paths = self.selected_file_paths
        output_dir = self.batch_output_input.text().strip()
        
        if not file_paths:
            self._show_warning_message("警告", "请选择要处理的音频文件")
            return
        
        if not output_dir:
            self._show_warning_message("警告", "请选择输出目录")
            return
        
        if not os.path.exists(output_dir):
            try:
                os.makedirs(output_dir, exist_ok=True)
            except Exception as e:
                self._show_warning_message("错误", f"无法创建输出目录: {e}")
                return
        
        # 开始批量处理
        self.batch_start_button.setEnabled(False)
        self.batch_progress_bar.setValue(0)
        
        # 更新表格中的状态为"处理中"
        for i in range(self.batch_result_table.rowCount()):
            status_item = QTableWidgetItem("⏳ 处理中")
            status_item.setBackground(QColor("#cce5ff"))
            self.batch_result_table.setItem(i, 2, status_item)
        
        self.batch_status_label.setText(f"🚀 开始处理 {len(file_paths)} 个文件...")
        
        # 创建批量处理工作线程
        self.batch_worker = BatchVoiceSeparationWorker(file_paths, output_dir, self.config)
        self.batch_worker.progress_updated.connect(self.batch_progress_bar.setValue)
        self.batch_worker.status_updated.connect(self.batch_status_label.setText)
        self.batch_worker.file_completed.connect(self.on_batch_file_completed)
        self.batch_worker.file_failed.connect(self.on_batch_file_failed)
        self.batch_worker.batch_completed.connect(self.on_batch_completed)
        self.batch_worker.start()
    
    def cancel_batch_processing(self):
        """取消批量处理"""
        if self.batch_worker and self.batch_worker.isRunning():
            self.batch_worker.cancel()
            self.batch_worker.wait()
            self.batch_status_label.setText("❌ 处理已取消")
            self.batch_start_button.setEnabled(True)
        else:
            self.close()
    
    def add_batch_result_row(self, filename: str, status: str, output_files: str, error: str):
        """添加批量处理结果行（兼容旧的调用方式）"""
        row_count = self.batch_result_table.rowCount()
        self.batch_result_table.insertRow(row_count)
        
        self.batch_result_table.setItem(row_count, 0, QTableWidgetItem(filename))
        self.batch_result_table.setItem(row_count, 1, QTableWidgetItem(""))  # 大小列，暂时为空
        self.batch_result_table.setItem(row_count, 2, QTableWidgetItem(status))
        self.batch_result_table.setItem(row_count, 3, QTableWidgetItem(output_files))
        self.batch_result_table.setItem(row_count, 4, QTableWidgetItem(error))
        
        # 设置状态颜色
        status_item = self.batch_result_table.item(row_count, 2)  # 状态列现在是第3列
        if status == "✅ 完成":
            status_item.setBackground(QColor("#d4edda"))
        elif status == "❌ 失败":
            status_item.setBackground(QColor("#f8d7da"))
        elif status == "⏳ 处理中":
            status_item.setBackground(QColor("#fff3cd"))
    
    def on_batch_file_completed(self, file_index: int, output_files: dict):
        """批量处理单个文件完成"""
        if file_index < self.batch_result_table.rowCount():
            # 更新状态
            self.batch_result_table.setItem(file_index, 2, QTableWidgetItem("✅ 完成"))
            
            # 更新输出文件信息
            output_info = []
            for key, path in output_files.items():
                if os.path.exists(path):
                    output_info.append(f"{key}: {os.path.basename(path)}")
            
            self.batch_result_table.setItem(file_index, 3, QTableWidgetItem("; ".join(output_info)))
            
            # 设置状态颜色
            status_item = self.batch_result_table.item(file_index, 2)
            status_item.setBackground(QColor("#d4edda"))
    
    def on_batch_file_failed(self, file_index: int, error: str):
        """批量处理单个文件失败"""
        if file_index < self.batch_result_table.rowCount():
            # 更新状态
            self.batch_result_table.setItem(file_index, 2, QTableWidgetItem("❌ 失败"))
            self.batch_result_table.setItem(file_index, 4, QTableWidgetItem(error))
            
            # 设置状态颜色
            status_item = self.batch_result_table.item(file_index, 2)
            status_item.setBackground(QColor("#f8d7da"))
    
    def on_batch_completed(self, results: List[dict]):
        """批量处理完成"""
        self.batch_start_button.setEnabled(True)
        
        # 统计结果
        success_count = sum(1 for r in results if r['success'])
        failure_count = len(results) - success_count
        
        # 更新状态
        if failure_count == 0:
            self.batch_status_label.setText(f"🎉 处理完成！成功处理 {success_count} 个文件")
        else:
            self.batch_status_label.setText(f"⚠️ 处理完成！成功 {success_count} 个，失败 {failure_count} 个")
        
        # 注释掉完成对话框，避免打断用户操作流程
        # message = f"批量人声分离处理完成！\n\n"
        # message += f"✅ 成功处理: {success_count} 个文件\n"
        # if failure_count > 0:
        #     message += f"❌ 处理失败: {failure_count} 个文件\n"
        # message += "\n请查看处理结果表格了解详细信息。"
        # 
        # QMessageBox.information(self, "🎉 处理完成", message)
    
    def _show_completion_message(self, title: str, message: str):
        """显示自定义样式的完成消息对话框"""
        msg_box = QMessageBox(self)
        msg_box.setWindowTitle(title)
        msg_box.setText(message)
        msg_box.setIcon(QMessageBox.Information)
        
        # 设置固定尺寸，避免一长条显示
        msg_box.setFixedSize(380, 200)
        
        # 设置自定义样式，更加简洁美观
        msg_box.setStyleSheet("""
            QMessageBox {
                background-color: #1B1E24;
                color: #FFFFFF;
                font-size: 14px;
                border: 2px solid #2B9D7C;
                border-radius: 12px;
                padding: 20px;
            }
            QMessageBox QLabel {
                background-color: transparent;
                color: #FFFFFF;
                font-size: 15px;
                font-weight: 500;
                padding: 15px;
                min-width: 280px;
                max-width: 280px;
                min-height: 80px;
                border: none;
                line-height: 1.4;
                text-align: center;
            }
            QMessageBox QPushButton {
                background-color: #2B9D7C;
                color: #FFFFFF;
                border: none;
                border-radius: 8px;
                padding: 10px 24px;
                font-size: 14px;
                font-weight: bold;
                min-width: 100px;
                min-height: 35px;
            }
            QMessageBox QPushButton:hover {
                background-color: #00CC55;
                transform: scale(1.02);
            }
            QMessageBox QPushButton:pressed {
                background-color: #1E7A5F;
            }
            QMessageBox QIcon {
                max-width: 48px;
                max-height: 48px;
            }
        """)
        
        msg_box.exec()

    def _show_warning_message(self, title: str, message: str):
        """显示自定义样式的警告消息对话框"""
        msg_box = QMessageBox(self)
        msg_box.setWindowTitle(title)
        msg_box.setText(message)
        msg_box.setIcon(QMessageBox.Warning)
        
        # 设置自定义样式，提高文字和背景的对比度
        msg_box.setStyleSheet("""
            QMessageBox {
                background-color: #2B2B2B;
                color: #FFFFFF;
                font-size: 14px;
                border: 2px solid #FF8C00;
                border-radius: 8px;
            }
            QMessageBox QLabel {
                background-color: #2B2B2B;
                color: #FFFFFF;
                font-size: 14px;
                font-weight: bold;
                padding: 10px;
                min-width: 300px;
            }
            QMessageBox QPushButton {
                background-color: #FF8C00;
                color: #FFFFFF;
                border: none;
                border-radius: 6px;
                padding: 8px 20px;
                font-size: 13px;
                font-weight: bold;
                min-width: 80px;
            }
            QMessageBox QPushButton:hover {
                background-color: #FFA500;
            }
            QMessageBox QPushButton:pressed {
                background-color: #CC7000;
            }
        """)
        
        msg_box.exec()
    
    def closeEvent(self, event):
        """关闭事件处理"""
        if self.batch_worker and self.batch_worker.isRunning():
            self.batch_worker.cancel()
            self.batch_worker.wait()
        
        if hasattr(self, 'cascadednet_worker') and self.cascadednet_worker and self.cascadednet_worker.isRunning():
            self.cascadednet_worker.cancel()
            self.cascadednet_worker.wait()
        
        event.accept() 

    # CascadedNet事件处理方法
    def on_cascadednet_files_selected(self, file_paths: List[str]):
        """CascadedNet文件选择处理"""
        # 存储CascadedNet文件列表
        self.cascadednet_file_paths = file_paths[:]
        
        if file_paths:
            # 在处理结果表格中显示文件信息
            self.update_cascadednet_result_table_with_files()
        else:
            self.cascadednet_file_paths = []
            self.cascadednet_result_table.setRowCount(0)
        
        # 更新状态和按钮
        self.update_cascadednet_status_and_buttons()

    def update_cascadednet_result_table_with_files(self):
        """在CascadedNet处理结果表格中显示待处理文件"""
        self.cascadednet_result_table.setRowCount(0)
        
        for i, file_path in enumerate(self.cascadednet_file_paths):
            self.add_cascadednet_file_to_result_table(i, file_path, "📋 待处理")
        
        # 连接表格选择信号，启用移除按钮
        self.cascadednet_result_table.itemSelectionChanged.connect(self.on_cascadednet_table_selection_changed)

    def add_cascadednet_file_to_result_table(self, row: int, file_path: str, status: str = "📋 待处理"):
        """向CascadedNet结果表格添加文件行"""
        self.cascadednet_result_table.insertRow(row)
        
        # 文件名（只显示文件名，不包含路径）
        filename = Path(file_path).name
        filename_item = QTableWidgetItem(filename)
        filename_item.setToolTip(f"完整路径: {file_path}")  # 鼠标悬停显示完整路径
        filename_item.setData(Qt.UserRole, file_path)  # 存储完整路径
        self.cascadednet_result_table.setItem(row, 0, filename_item)
        
        # 文件大小
        try:
            file_size = Path(file_path).stat().st_size / (1024 * 1024)  # MB
            size_text = f"{file_size:.1f} MB"
        except:
            size_text = "未知"
        size_item = QTableWidgetItem(size_text)
        self.cascadednet_result_table.setItem(row, 1, size_item)
        
        # 状态
        status_item = QTableWidgetItem(status)
        self.cascadednet_result_table.setItem(row, 2, status_item)
        
        # 输出文件（初始为空）
        output_item = QTableWidgetItem("")
        self.cascadednet_result_table.setItem(row, 3, output_item)
        
        # 错误信息（初始为空）
        error_item = QTableWidgetItem("")
        self.cascadednet_result_table.setItem(row, 4, error_item)
        
        # 设置状态颜色（适应深色主题）
        if status == "📋 待处理":
            status_item.setBackground(QColor("#5B4E00"))  # 深色版本的黄色背景
        elif status == "✅ 完成":
            status_item.setBackground(QColor("#0F5132"))  # 深色版本的绿色背景
        elif status == "❌ 失败":
            status_item.setBackground(QColor("#721C24"))  # 深色版本的红色背景
        elif status == "⏳ 处理中":
            status_item.setBackground(QColor("#1B4F72"))  # 深色版本的蓝色背景

    def on_cascadednet_table_selection_changed(self):
        """CascadedNet表格选择改变时的处理"""
        selected_rows = self.cascadednet_result_table.selectionModel().selectedRows()
        self.cascadednet_remove_selected_button.setEnabled(len(selected_rows) > 0)

    def remove_cascadednet_selected_file(self):
        """移除CascadedNet选中的文件"""
        selected_rows = self.cascadednet_result_table.selectionModel().selectedRows()
        if not selected_rows:
            return
        
        # 获取选中行的文件路径
        row = selected_rows[0].row()
        filename_item = self.cascadednet_result_table.item(row, 0)
        if filename_item:
            file_path = filename_item.data(Qt.UserRole)
            
            # 从文件列表中移除
            if file_path in self.cascadednet_file_paths:
                self.cascadednet_file_paths.remove(file_path)
                
                # 同时从左侧上传区域的选择中移除
                if hasattr(self.cascadednet_upload_area, 'selected_files') and file_path in self.cascadednet_upload_area.selected_files:
                    self.cascadednet_upload_area.selected_files.remove(file_path)
                
                # 更新表格显示
                self.update_cascadednet_result_table_with_files()
                
                # 更新状态和按钮
                self.update_cascadednet_status_and_buttons()

    def clear_cascadednet_list(self):
        """清空CascadedNet处理列表"""
        if not self.cascadednet_file_paths:
            return
        
        # 清空文件列表
        self.cascadednet_file_paths = []
        
        # 同时清空左侧上传区域的选择
        if hasattr(self.cascadednet_upload_area, 'selected_files'):
            self.cascadednet_upload_area.selected_files = []
            self.cascadednet_upload_area.update_display()  # 更新上传区域显示
        
        # 清空表格
        self.cascadednet_result_table.setRowCount(0)
        
        # 更新状态和按钮
        self.update_cascadednet_status_and_buttons()

    def update_cascadednet_status_and_buttons(self):
        """更新CascadedNet处理状态和按钮状态"""
        if hasattr(self, 'cascadednet_file_paths') and self.cascadednet_file_paths:
            self.cascadednet_status_label.setText(f"✅ 已选择 {len(self.cascadednet_file_paths)} 个文件")
            self.cascadednet_clear_list_button.setEnabled(True)
        else:
            self.cascadednet_status_label.setText("请选择音频文件开始处理")
            self.cascadednet_remove_selected_button.setEnabled(False)
            self.cascadednet_clear_list_button.setEnabled(False)

    def browse_cascadednet_output_dir(self):
        """浏览CascadedNet输出目录"""
        dir_path = QFileDialog.getExistingDirectory(
            self,
            "选择CascadedNet输出目录",
            ""
        )
        if dir_path:
            self.cascadednet_output_input.setText(dir_path)

    def on_cascadednet_output_option_changed(self):
        """CascadedNet输出选项改变"""
        # 更新CascadedNet配置
        if hasattr(self, 'cascadednet_vocals_cb'):
            vocals = self.cascadednet_vocals_cb.isChecked()
            background = self.cascadednet_background_cb.isChecked()
            
            # 确保至少选择一个输出选项
            if not vocals and not background:
                # 如果都不选择，自动选择人声
                self.cascadednet_vocals_cb.setChecked(True)

    def start_cascadednet_separation(self):
        """开始CascadedNet人声分离处理"""
        if not hasattr(self, 'cascadednet_file_paths') or not self.cascadednet_file_paths:
            self._show_warning_message("警告", "请先选择要处理的音频文件")
            return
            
        output_dir = self.cascadednet_output_input.text().strip()
        if not output_dir:
            self._show_warning_message("警告", "请选择输出目录")
            return
            
        if not os.path.exists(output_dir):
            self._show_warning_message("警告", "输出目录不存在")
            return
        
        # 获取输出选项
        vocals = self.cascadednet_vocals_cb.isChecked()
        background = self.cascadednet_background_cb.isChecked()
        use_tta = True  # TTA默认启用
        
        if not vocals and not background:
            self._show_warning_message("警告", "请至少选择一个输出选项")
            return
        
        # 创建CascadedNet配置
        cascadednet_config = {
            'model': 'cascadednet',
            'vocals': vocals,
            'background': background,
            'use_tta': use_tta,
            'output_dir': output_dir
        }
        
        # 启动CascadedNet处理工作线程
        self.cascadednet_worker = CascadedNetVoiceSeparationWorker(
            self.cascadednet_file_paths, 
            output_dir, 
            cascadednet_config
        )
        
        # 连接信号
        self.cascadednet_worker.progress_updated.connect(self.cascadednet_progress_bar.setValue)
        self.cascadednet_worker.status_updated.connect(self.cascadednet_status_label.setText)
        self.cascadednet_worker.file_completed.connect(self.on_cascadednet_file_completed)
        self.cascadednet_worker.file_failed.connect(self.on_cascadednet_file_failed)
        self.cascadednet_worker.batch_completed.connect(self.on_cascadednet_batch_completed)
        
        # 更新UI状态
        self.cascadednet_start_button.setEnabled(False)
        self.cascadednet_cancel_button.setEnabled(True)
        self.cascadednet_progress_bar.setValue(0)
        
        # 启动工作线程
        self.cascadednet_worker.start()

    def cancel_cascadednet_processing(self):
        """取消CascadedNet处理"""
        if hasattr(self, 'cascadednet_worker') and self.cascadednet_worker:
            self.cascadednet_worker.cancel()
            self.cascadednet_worker.wait()
            
        # 重置UI状态
        self.cascadednet_start_button.setEnabled(True)
        self.cascadednet_cancel_button.setEnabled(False)
        self.cascadednet_progress_bar.setValue(0)
        self.cascadednet_status_label.setText("处理已取消")

    def on_cascadednet_file_completed(self, file_index: int, output_files: dict):
        """CascadedNet文件处理完成"""
        if file_index < self.cascadednet_result_table.rowCount():
            # 更新状态
            status_item = self.cascadednet_result_table.item(file_index, 2)
            if status_item:
                status_item.setText("✅ 完成")
                status_item.setBackground(QColor("#0F5132"))
            
            # 更新输出文件
            output_item = self.cascadednet_result_table.item(file_index, 3)
            if output_item and output_files:
                output_list = []
                for key, path in output_files.items():
                    output_list.append(f"{key}: {Path(path).name}")
                output_item.setText("; ".join(output_list))
                output_item.setToolTip("\n".join([f"{key}: {path}" for key, path in output_files.items()]))

    def on_cascadednet_file_failed(self, file_index: int, error: str):
        """CascadedNet文件处理失败"""
        if file_index < self.cascadednet_result_table.rowCount():
            # 更新状态
            status_item = self.cascadednet_result_table.item(file_index, 2)
            if status_item:
                status_item.setText("❌ 失败")
                status_item.setBackground(QColor("#721C24"))
            
            # 更新错误信息
            error_item = self.cascadednet_result_table.item(file_index, 4)
            if error_item:
                error_item.setText(error)
                error_item.setToolTip(error)

    def on_cascadednet_batch_completed(self, results: List[dict]):
        """CascadedNet批量处理完成"""
        # 重置UI状态
        self.cascadednet_start_button.setEnabled(True)
        self.cascadednet_cancel_button.setEnabled(False)
        self.cascadednet_progress_bar.setValue(100)
        
        # 统计结果
        success_count = sum(1 for result in results if result.get('success', False))
        total_count = len(results)
        
        self.cascadednet_status_label.setText(f"✅ 处理完成！成功: {success_count}/{total_count}")
        
        # 显示完成消息
        self._show_completion_message(
            "CascadedNet处理完成",
            f"CascadedNet人声分离处理完成！\n\n成功处理: {success_count} 个文件\n失败: {total_count - success_count} 个文件"
        ) 