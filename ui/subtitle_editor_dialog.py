#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
字幕编辑器对话框
功能：提供字幕文件的编辑、预览、时间轴调整等功能
"""

import sys
import os
import re
from datetime import datetime, timedelta
from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, 
    QTextEdit, QLineEdit, QScrollArea, QWidget, QFrame,
    QFileDialog, QMessageBox, QSplitter, QTableWidget, 
    QTableWidgetItem, QHeaderView, QComboBox, QSpinBox,
    QCheckBox, QGroupBox, QGridLayout, QProgressBar
)
from PySide6.QtCore import Qt, Signal, QTimer
from PySide6.QtGui import QFont, QTextCursor, QColor, QPalette, QDragEnterEvent, QDropEvent


class SubtitleItem:
    """字幕条目类"""
    def __init__(self, start_time="00:00:00,000", end_time="00:00:03,000", text=""):
        self.start_time = start_time
        self.end_time = end_time
        self.text = text
    
    def to_srt_format(self, index):
        """转换为SRT格式"""
        return f"{index}\n{self.start_time} --> {self.end_time}\n{self.text}\n"
    
    @staticmethod
    def parse_srt_time(time_str):
        """解析SRT时间格式"""
        try:
            time_parts = time_str.split(':')
            hours = int(time_parts[0])
            minutes = int(time_parts[1])
            seconds_parts = time_parts[2].split(',')
            seconds = int(seconds_parts[0])
            milliseconds = int(seconds_parts[1])
            return hours * 3600 + minutes * 60 + seconds + milliseconds / 1000
        except:
            return 0
    
    @staticmethod
    def format_srt_time(total_seconds):
        """格式化为SRT时间格式"""
        hours = int(total_seconds // 3600)
        minutes = int((total_seconds % 3600) // 60)
        seconds = int(total_seconds % 60)
        milliseconds = int((total_seconds % 1) * 1000)
        return f"{hours:02d}:{minutes:02d}:{seconds:02d},{milliseconds:03d}"


class SubtitleEditorDialog(QDialog):
    """字幕编辑器对话框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.subtitle_items = []
        self.current_file_path = None
        self.is_modified = False
        
        self.setup_ui()
        self.setup_connections()
        self.setup_style()
    
    def setup_ui(self):
        """设置UI界面"""
        self.setWindowTitle("字幕编辑器 - FlipTalk AI")
        self.setFixedSize(1200, 800)
        self.setModal(True)
        
        # 启用拖拽功能
        self.setAcceptDrops(True)
        
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(15)
        
        # 创建标题区域
        title_area = self.create_title_area()
        main_layout.addWidget(title_area)
        
        # 创建工具栏
        toolbar = self.create_toolbar()
        main_layout.addWidget(toolbar)
        
        # 创建主要内容区域
        content_splitter = QSplitter(Qt.Horizontal)
        content_splitter.setChildrenCollapsible(False)
        
        # 左侧：字幕列表
        subtitle_list_area = self.create_subtitle_list_area()
        content_splitter.addWidget(subtitle_list_area)
        
        # 右侧：编辑区域
        edit_area = self.create_edit_area()
        content_splitter.addWidget(edit_area)
        
        # 设置分割比例
        content_splitter.setSizes([600, 600])
        main_layout.addWidget(content_splitter)
        
        # 创建底部状态栏
        status_bar = self.create_status_bar()
        main_layout.addWidget(status_bar)
    
    def create_title_area(self):
        """创建标题区域"""
        title_frame = QFrame()
        title_frame.setFixedHeight(65)
        title_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #1B1E24, 
                    stop:0.5 #14161A, 
                    stop:1 #1B1E24);
                border: 1px solid #444444;
                border-radius: 8px;
                margin: 2px;
            }
        """)
        
        title_layout = QHBoxLayout(title_frame)
        title_layout.setContentsMargins(15, 5, 15, 5)
        title_layout.setSpacing(12)
        
        # 简化的图标
        icon_label = QLabel("📝")
        icon_label.setStyleSheet("""
            font-size: 16px; 
            color: #2B9D7C;
            background: transparent;
            border: none;
        """)
        icon_label.setAlignment(Qt.AlignCenter)
        title_layout.addWidget(icon_label)
        
        # 标题信息区域 - 恢复垂直布局
        title_info = QVBoxLayout()
        title_info.setSpacing(2)
        title_info.setContentsMargins(0, 0, 0, 0)
        
        # 主标题
        title_label = QLabel("字幕编辑器")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 14px; 
                font-weight: 600; 
                color: #FFFFFF;
                background: transparent;
                padding: 0px;
                font-family: 'Microsoft YaHei UI', 'Microsoft YaHei', 'SimHei', sans-serif;
                border: none;
            }
        """)
        title_label.setAlignment(Qt.AlignLeft | Qt.AlignVCenter)
        title_info.addWidget(title_label)
        
        # 副标题
        subtitle_label = QLabel("专业的字幕编辑工具")
        subtitle_label.setStyleSheet("""
            QLabel {
                font-size: 10px; 
                color: rgba(255, 255, 255, 0.7);
                background: transparent;
                font-weight: 400;
                padding: 0px;
                font-family: 'Microsoft YaHei UI', 'Microsoft YaHei', 'SimHei', sans-serif;
                border: none;
            }
        """)
        subtitle_label.setAlignment(Qt.AlignLeft | Qt.AlignVCenter)
        title_info.addWidget(subtitle_label)
        
        title_layout.addLayout(title_info)
        title_layout.addStretch()
        
        # 简化的功能标签
        feature_label = QLabel("SRT • 实时编辑 • 时间调整")
        feature_label.setStyleSheet("""
            QLabel {
                font-size: 10px;
                color: rgba(255, 255, 255, 0.4);
                background: transparent;
                font-weight: 400;
                padding: 0px;
                font-family: 'Microsoft YaHei UI', 'Microsoft YaHei', 'SimHei', sans-serif;
                border: none;
            }
        """)
        feature_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        title_layout.addWidget(feature_label)
        
        return title_frame
    
    def create_toolbar(self):
        """创建工具栏"""
        toolbar_frame = QFrame()
        toolbar_frame.setFixedHeight(70)
        toolbar_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #14161A, 
                    stop:0.5 #1B1E24, 
                    stop:1 #14161A);
                border: 1px solid #444444;
                border-radius: 12px;
                margin: 2px;
            }
        """)
        
        toolbar_layout = QHBoxLayout(toolbar_frame)
        toolbar_layout.setContentsMargins(15, 10, 15, 10)
        toolbar_layout.setSpacing(12)
        
        # 文件操作按钮
        self.open_btn = self.create_text_button("打开文件", "#2B9D7C")
        self.save_btn = self.create_text_button("保存", "#2B9D7C")
        self.save_as_btn = self.create_text_button("另存为", "#45B7D1")
        
        toolbar_layout.addWidget(self.open_btn)
        toolbar_layout.addWidget(self.save_btn)
        toolbar_layout.addWidget(self.save_as_btn)
        
        # 分隔线
        separator1 = QFrame()
        separator1.setFrameShape(QFrame.VLine)
        separator1.setFixedHeight(40)
        separator1.setStyleSheet("""
            QFrame {
                background-color: #444444;
                border: none;
                width: 2px;
                margin: 5px 8px;
            }
        """)
        toolbar_layout.addWidget(separator1)
        
        # 编辑操作按钮
        self.add_btn = self.create_text_button("添加字幕", "#2B9D7C")
        self.delete_btn = self.create_text_button("删除字幕", "#FF6B6B")
        self.move_up_btn = self.create_text_button("上移", "#96CEB4")
        self.move_down_btn = self.create_text_button("下移", "#96CEB4")
        
        toolbar_layout.addWidget(self.add_btn)
        toolbar_layout.addWidget(self.delete_btn)
        toolbar_layout.addWidget(self.move_up_btn)
        toolbar_layout.addWidget(self.move_down_btn)
        
        # 分隔线
        separator2 = QFrame()
        separator2.setFrameShape(QFrame.VLine)
        separator2.setFixedHeight(40)
        separator2.setStyleSheet("""
            QFrame {
                background-color: #444444;
                border: none;
                width: 2px;
                margin: 5px 8px;
            }
        """)
        toolbar_layout.addWidget(separator2)
        
        # 时间调整
        time_label = QLabel("时间偏移:")
        time_label.setStyleSheet("""
            color: #FFFFFF; 
            font-size: 14px; 
            font-weight: bold;
            background-color: rgba(43, 157, 124, 0.2);
            padding: 4px 8px;
            border-radius: 4px;
            border: 1px solid rgba(43, 157, 124, 0.3);
        """)
        toolbar_layout.addWidget(time_label)
        
        self.time_offset_spin = QSpinBox()
        self.time_offset_spin.setRange(-999999, 999999)
        self.time_offset_spin.setSuffix(" ms")
        self.time_offset_spin.setFixedWidth(100)
        toolbar_layout.addWidget(self.time_offset_spin)
        
        self.apply_offset_btn = self.create_text_button("应用偏移", "#2B9D7C")
        toolbar_layout.addWidget(self.apply_offset_btn)
        
        toolbar_layout.addStretch()
        
        return toolbar_frame
    
    def create_toolbar_button(self, icon, tooltip, color):
        """创建工具栏图标按钮"""
        btn = QPushButton(icon)
        btn.setToolTip(tooltip)
        btn.setFixedSize(40, 40)
        btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {color};
                border: none;
                border-radius: 20px;
                font-size: 16px;
                color: #FFFFFF;
            }}
            QPushButton:hover {{
                background-color: rgba({self.hex_to_rgb(color)}, 0.8);
            }}
            QPushButton:pressed {{
                background-color: rgba({self.hex_to_rgb(color)}, 0.6);
            }}
        """)
        return btn
    
    def create_text_button(self, text, color):
        """创建工具栏文字按钮"""
        btn = QPushButton(text)
        btn.setFixedHeight(35)
        btn.setMinimumWidth(80)
        
        # 根据不同颜色选择合适的文字颜色以提高对比度
        text_color = self.get_contrast_text_color(color)
        hover_color = self.get_hover_color(color)
        
        btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {color};
                border: 1px solid rgba(255, 255, 255, 0.2);
                border-radius: 8px;
                font-size: 13px;
                font-weight: bold;
                color: {text_color};
                padding: 6px 12px;
            }}
            QPushButton:hover {{
                background-color: {hover_color};
                border: 1px solid rgba(255, 255, 255, 0.4);
                color: #FFFFFF;
            }}
            QPushButton:pressed {{
                background-color: {self.get_pressed_color(color)};
                color: #FFFFFF;
            }}
        """)
        return btn
    
    def get_contrast_text_color(self, bg_color):
        """根据背景颜色获取高对比度的文字颜色"""
        # 浅色背景使用深色文字，深色背景使用白色文字
        light_colors = ["#FFEAA7", "#96CEB4", "#4ECDC4"]
        if bg_color in light_colors:
            return "#2C3E50"  # 深色文字
        else:
            return "#FFFFFF"  # 白色文字
    
    def get_hover_color(self, color):
        """获取悬停时的颜色"""
        color_map = {
            "#4ECDC4": "#45B7D1",  # 青色 -> 蓝色
            "#2B9D7C": "#27AE60",  # 绿色 -> 更亮的绿色
            "#45B7D1": "#3498DB",  # 蓝色 -> 更亮的蓝色
            "#96CEB4": "#52C4A0",  # 浅绿 -> 深绿
            "#FF6B6B": "#E74C3C",  # 红色 -> 深红
            "#FFEAA7": "#F39C12",  # 黄色 -> 橙色
            "#DDA0DD": "#9B59B6"   # 紫色 -> 深紫
        }
        return color_map.get(color, color)
    
    def get_pressed_color(self, color):
        """获取按下时的颜色"""
        color_map = {
            "#4ECDC4": "#2980B9",
            "#2B9D7C": "#1E8449", 
            "#45B7D1": "#2471A3",
            "#96CEB4": "#48C9B0",
            "#FF6B6B": "#C0392B",
            "#FFEAA7": "#D68910",
            "#DDA0DD": "#7D3C98"
        }
        return color_map.get(color, color)
    
    def create_subtitle_list_area(self):
        """创建字幕列表区域"""
        list_frame = QFrame()
        list_layout = QVBoxLayout(list_frame)
        list_layout.setContentsMargins(10, 10, 10, 10)
        list_layout.setSpacing(10)
        
        # 列表标题和提示
        title_container = QHBoxLayout()
        
        list_title = QLabel("字幕列表")
        list_title.setStyleSheet("font-size: 16px; font-weight: bold; color: #FFFFFF;")
        title_container.addWidget(list_title)
        
        title_container.addStretch()
        
        # 拖拽提示
        drag_hint = QLabel("💡 可拖拽字幕文件到此处")
        drag_hint.setStyleSheet("""
            color: rgba(255, 255, 255, 0.6); 
            font-size: 12px; 
            font-style: italic;
            background-color: rgba(43, 157, 124, 0.1);
            padding: 4px 8px;
            border-radius: 4px;
            border: 1px dashed rgba(43, 157, 124, 0.3);
        """)
        title_container.addWidget(drag_hint)
        
        list_layout.addLayout(title_container)
        
        # 字幕表格
        self.subtitle_table = QTableWidget()
        self.subtitle_table.setColumnCount(4)
        self.subtitle_table.setHorizontalHeaderLabels(["序号", "开始时间", "结束时间", "字幕内容"])
        
        # 设置表格样式
        header = self.subtitle_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Fixed)
        header.setSectionResizeMode(1, QHeaderView.Fixed)
        header.setSectionResizeMode(2, QHeaderView.Fixed)
        header.setSectionResizeMode(3, QHeaderView.Stretch)
        
        self.subtitle_table.setColumnWidth(0, 60)
        self.subtitle_table.setColumnWidth(1, 120)
        self.subtitle_table.setColumnWidth(2, 120)
        
        list_layout.addWidget(self.subtitle_table)
        
        return list_frame
    
    def create_edit_area(self):
        """创建编辑区域"""
        edit_frame = QFrame()
        edit_layout = QVBoxLayout(edit_frame)
        edit_layout.setContentsMargins(10, 10, 10, 10)
        edit_layout.setSpacing(15)
        
        # 编辑标题
        edit_title = QLabel("字幕编辑")
        edit_title.setStyleSheet("font-size: 16px; font-weight: bold; color: #FFFFFF;")
        edit_layout.addWidget(edit_title)
        
        # 时间编辑区域
        time_group = QGroupBox("时间设置")
        time_group.setStyleSheet("QGroupBox { color: #FFFFFF; font-weight: bold; }")
        time_layout = QGridLayout(time_group)
        
        # 开始时间
        start_label = QLabel("开始时间:")
        start_label.setStyleSheet("color: #FFFFFF;")
        self.start_time_edit = QLineEdit("00:00:00,000")
        self.start_time_edit.setPlaceholderText("HH:MM:SS,mmm")
        
        time_layout.addWidget(start_label, 0, 0)
        time_layout.addWidget(self.start_time_edit, 0, 1)
        
        # 结束时间
        end_label = QLabel("结束时间:")
        end_label.setStyleSheet("color: #FFFFFF;")
        self.end_time_edit = QLineEdit("00:00:03,000")
        self.end_time_edit.setPlaceholderText("HH:MM:SS,mmm")
        
        time_layout.addWidget(end_label, 1, 0)
        time_layout.addWidget(self.end_time_edit, 1, 1)
        
        edit_layout.addWidget(time_group)
        
        # 字幕内容编辑
        content_group = QGroupBox("字幕内容")
        content_group.setStyleSheet("QGroupBox { color: #FFFFFF; font-weight: bold; }")
        content_layout = QVBoxLayout(content_group)
        
        self.content_edit = QTextEdit()
        self.content_edit.setPlaceholderText("在此输入字幕内容...")
        self.content_edit.setFixedHeight(200)
        content_layout.addWidget(self.content_edit)
        
        edit_layout.addWidget(content_group)
        
        # 编辑操作按钮
        edit_buttons_layout = QHBoxLayout()
        
        self.update_btn = QPushButton("更新字幕")
        self.update_btn.setFixedHeight(40)
        self.cancel_btn = QPushButton("取消编辑")
        self.cancel_btn.setFixedHeight(40)
        
        edit_buttons_layout.addWidget(self.update_btn)
        edit_buttons_layout.addWidget(self.cancel_btn)
        
        edit_layout.addLayout(edit_buttons_layout)
        edit_layout.addStretch()
        
        return edit_frame
    
    def create_status_bar(self):
        """创建状态栏"""
        status_frame = QFrame()
        status_frame.setFixedHeight(40)
        
        status_layout = QHBoxLayout(status_frame)
        status_layout.setContentsMargins(10, 5, 10, 5)
        
        self.status_label = QLabel("就绪")
        self.status_label.setStyleSheet("color: #FFFFFF; font-size: 14px;")
        status_layout.addWidget(self.status_label)
        
        status_layout.addStretch()
        
        self.file_info_label = QLabel("未打开文件")
        self.file_info_label.setStyleSheet("color: rgba(255, 255, 255, 0.7); font-size: 12px;")
        status_layout.addWidget(self.file_info_label)
        
        return status_frame
    
    def setup_connections(self):
        """设置信号连接"""
        # 工具栏按钮连接
        self.open_btn.clicked.connect(self.open_file)
        self.save_btn.clicked.connect(self.save_file)
        self.save_as_btn.clicked.connect(self.save_as_file)
        self.add_btn.clicked.connect(self.add_subtitle)
        self.delete_btn.clicked.connect(self.delete_subtitle)
        self.move_up_btn.clicked.connect(self.move_subtitle_up)
        self.move_down_btn.clicked.connect(self.move_subtitle_down)
        self.apply_offset_btn.clicked.connect(self.apply_time_offset)
        
        # 编辑按钮连接
        self.update_btn.clicked.connect(self.update_subtitle)
        self.cancel_btn.clicked.connect(self.cancel_edit)
        
        # 表格选择变化
        self.subtitle_table.itemSelectionChanged.connect(self.on_selection_changed)
        
        # 内容变化监听
        self.content_edit.textChanged.connect(self.on_content_changed)
        self.start_time_edit.textChanged.connect(self.on_content_changed)
        self.end_time_edit.textChanged.connect(self.on_content_changed)
    
    def setup_style(self):
        """设置样式"""
        self.setStyleSheet("""
            QDialog {
                background-color: #1B1E24;
                color: #FFFFFF;
            }
            QFrame {
                background-color: #1B1E24;
                border: 1px solid rgba(255, 255, 255, 0.1);
                border-radius: 10px;
            }
            QTableWidget {
                background-color: #14161A;
                border: 1px solid rgba(255, 255, 255, 0.2);
                border-radius: 8px;
                gridline-color: rgba(255, 255, 255, 0.1);
                color: #FFFFFF;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            }
            QTableWidget::item:selected {
                background-color: #2B9D7C;
            }
            QTableWidget::item:disabled {
                background-color: rgba(43, 157, 124, 0.1);
                color: rgba(255, 255, 255, 0.7);
                font-style: italic;
                border: 2px dashed rgba(43, 157, 124, 0.3);
            }
            QHeaderView::section {
                background-color: #2B2F36;
                color: #FFFFFF;
                padding: 8px;
                border: none;
                font-weight: bold;
            }
            QLineEdit, QTextEdit {
                background-color: #14161A;
                border: 1px solid rgba(255, 255, 255, 0.2);
                border-radius: 6px;
                padding: 8px;
                color: #FFFFFF;
                font-size: 14px;
            }
            QLineEdit:focus, QTextEdit:focus {
                border-color: #2B9D7C;
            }
            QPushButton {
                background-color: #2B9D7C;
                color: #FFFFFF;
                border: none;
                border-radius: 6px;
                padding: 8px 16px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #00CC55;
            }
            QPushButton:pressed {
                background-color: #00AA44;
            }
            QGroupBox {
                border: 1px solid rgba(255, 255, 255, 0.2);
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
            QSpinBox {
                background-color: #14161A;
                border: 2px solid rgba(255, 255, 255, 0.3);
                border-radius: 6px;
                padding: 6px;
                color: #FFFFFF;
                font-size: 13px;
                font-weight: bold;
            }
            QSpinBox:focus {
                border-color: #2B9D7C;
                background-color: #1A1D23;
            }
            QSpinBox::up-button, QSpinBox::down-button {
                background-color: #2B9D7C;
                border: none;
                width: 16px;
            }
            QSpinBox::up-button:hover, QSpinBox::down-button:hover {
                background-color: #27AE60;
            }
            QMessageBox {
                background-color: #1B1E24;
                color: #FFFFFF;
                border: 2px solid #2B9D7C;
                border-radius: 12px;
            }
            QMessageBox QLabel {
                color: #FFFFFF;
                font-size: 16px;
                font-weight: 600;
                padding: 10px;
                background: transparent;
            }
            QMessageBox QPushButton {
                background-color: #2B9D7C;
                color: #FFFFFF;
                border: 2px solid #27AE60;
                border-radius: 8px;
                padding: 10px 20px;
                font-size: 14px;
                font-weight: bold;
                min-width: 80px;
                margin: 5px;
            }
            QMessageBox QPushButton:hover {
                background-color: #27AE60;
                border-color: #2ECC71;
            }
            QMessageBox QPushButton:pressed {
                background-color: #229954;
                border-color: #27AE60;
            }
        """)
    
    def hex_to_rgb(self, hex_color):
        """将十六进制颜色转换为RGB"""
        hex_color = hex_color.lstrip('#')
        return ', '.join(str(int(hex_color[i:i+2], 16)) for i in (0, 2, 4))
    
    def show_message(self, title, message, msg_type="info"):
        """显示自定义消息框"""
        msg_box = QMessageBox(self)
        msg_box.setWindowTitle(title)
        msg_box.setText(message)
        
        if msg_type == "info":
            msg_box.setIcon(QMessageBox.Information)
        elif msg_type == "warning":
            msg_box.setIcon(QMessageBox.Warning)
        elif msg_type == "error":
            msg_box.setIcon(QMessageBox.Critical)
        elif msg_type == "question":
            msg_box.setIcon(QMessageBox.Question)
        
        # 设置中文按钮
        if msg_type == "question":
            msg_box.setStandardButtons(QMessageBox.Yes | QMessageBox.No)
            msg_box.button(QMessageBox.Yes).setText("是")
            msg_box.button(QMessageBox.No).setText("否")
        else:
            msg_box.setStandardButtons(QMessageBox.Ok)
            msg_box.button(QMessageBox.Ok).setText("确定")
        
        return msg_box.exec()
    
    def show_save_dialog(self, title, message):
        """显示保存确认对话框"""
        msg_box = QMessageBox(self)
        msg_box.setWindowTitle(title)
        msg_box.setText(message)
        msg_box.setIcon(QMessageBox.Question)
        
        # 设置自定义按钮
        save_btn = msg_box.addButton("保存", QMessageBox.AcceptRole)
        discard_btn = msg_box.addButton("不保存", QMessageBox.DestructiveRole)
        cancel_btn = msg_box.addButton("取消", QMessageBox.RejectRole)
        
        msg_box.setDefaultButton(save_btn)
        
        result = msg_box.exec()
        
        if msg_box.clickedButton() == save_btn:
            return QMessageBox.Save
        elif msg_box.clickedButton() == discard_btn:
            return QMessageBox.Discard
        else:
            return QMessageBox.Cancel
    
    def open_file(self):
        """打开字幕文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "打开字幕文件",
            "",
            "字幕文件 (*.srt *.vtt *.ass);;所有文件 (*.*)"
        )
        
        if file_path:
            self.load_subtitle_file(file_path)
    
    def load_subtitle_file(self, file_path):
        """加载字幕文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            if file_path.lower().endswith('.srt'):
                self.parse_srt_content(content)
            else:
                self.show_message("提示", "目前只支持SRT格式的字幕文件", "info")
                return
            
            self.current_file_path = file_path
            self.is_modified = False
            self.update_subtitle_table()
            self.update_status(f"已加载文件: {os.path.basename(file_path)}")
            self.file_info_label.setText(f"文件: {os.path.basename(file_path)} | 字幕数: {len(self.subtitle_items)}")
            
        except Exception as e:
            self.show_message("错误", f"加载文件失败: {str(e)}", "error")
    
    def parse_srt_content(self, content):
        """解析SRT内容"""
        self.subtitle_items.clear()
        
        # 分割字幕块
        blocks = re.split(r'\n\s*\n', content.strip())
        
        for block in blocks:
            lines = block.strip().split('\n')
            if len(lines) >= 3:
                # 跳过序号行
                time_line = lines[1]
                text_lines = lines[2:]
                
                # 解析时间
                time_match = re.match(r'(\d{2}:\d{2}:\d{2},\d{3})\s*-->\s*(\d{2}:\d{2}:\d{2},\d{3})', time_line)
                if time_match:
                    start_time = time_match.group(1)
                    end_time = time_match.group(2)
                    text = '\n'.join(text_lines)
                    
                    subtitle_item = SubtitleItem(start_time, end_time, text)
                    self.subtitle_items.append(subtitle_item)
    
    def save_file(self):
        """保存文件"""
        if self.current_file_path:
            self.save_subtitle_file(self.current_file_path)
        else:
            self.save_as_file()
    
    def save_as_file(self):
        """另存为文件"""
        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "保存字幕文件",
            "",
            "SRT文件 (*.srt);;所有文件 (*.*)"
        )
        
        if file_path:
            self.save_subtitle_file(file_path)
            self.current_file_path = file_path
    
    def save_subtitle_file(self, file_path):
        """保存字幕文件"""
        try:
            content = ""
            for i, item in enumerate(self.subtitle_items, 1):
                content += item.to_srt_format(i) + "\n"
            
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            self.is_modified = False
            self.update_status(f"已保存文件: {os.path.basename(file_path)}")
            
        except Exception as e:
            self.show_message("错误", f"保存文件失败: {str(e)}", "error")
    
    def add_subtitle(self):
        """添加新字幕"""
        new_item = SubtitleItem()
        self.subtitle_items.append(new_item)
        self.update_subtitle_table()
        self.is_modified = True
        
        # 选中新添加的项
        row = len(self.subtitle_items) - 1
        self.subtitle_table.selectRow(row)
        self.update_status("已添加新字幕")
    
    def delete_subtitle(self):
        """删除选中的字幕"""
        current_row = self.subtitle_table.currentRow()
        if current_row >= 0 and current_row < len(self.subtitle_items):
            del self.subtitle_items[current_row]
            self.update_subtitle_table()
            self.is_modified = True
            self.update_status("已删除字幕")
    
    def move_subtitle_up(self):
        """上移字幕"""
        current_row = self.subtitle_table.currentRow()
        if current_row > 0:
            self.subtitle_items[current_row], self.subtitle_items[current_row - 1] = \
                self.subtitle_items[current_row - 1], self.subtitle_items[current_row]
            self.update_subtitle_table()
            self.subtitle_table.selectRow(current_row - 1)
            self.is_modified = True
    
    def move_subtitle_down(self):
        """下移字幕"""
        current_row = self.subtitle_table.currentRow()
        if current_row >= 0 and current_row < len(self.subtitle_items) - 1:
            self.subtitle_items[current_row], self.subtitle_items[current_row + 1] = \
                self.subtitle_items[current_row + 1], self.subtitle_items[current_row]
            self.update_subtitle_table()
            self.subtitle_table.selectRow(current_row + 1)
            self.is_modified = True
    
    def apply_time_offset(self):
        """应用时间偏移"""
        offset_ms = self.time_offset_spin.value()
        if offset_ms == 0:
            return
        
        for item in self.subtitle_items:
            # 转换时间为秒
            start_seconds = SubtitleItem.parse_srt_time(item.start_time)
            end_seconds = SubtitleItem.parse_srt_time(item.end_time)
            
            # 应用偏移
            start_seconds += offset_ms / 1000
            end_seconds += offset_ms / 1000
            
            # 确保时间不为负
            start_seconds = max(0, start_seconds)
            end_seconds = max(start_seconds + 0.1, end_seconds)
            
            # 转换回时间格式
            item.start_time = SubtitleItem.format_srt_time(start_seconds)
            item.end_time = SubtitleItem.format_srt_time(end_seconds)
        
        self.update_subtitle_table()
        self.is_modified = True
        self.update_status(f"已应用时间偏移: {offset_ms}ms")
    
    def update_subtitle_table(self):
        """更新字幕表格"""
        self.subtitle_table.setRowCount(len(self.subtitle_items))
        
        if len(self.subtitle_items) == 0:
            # 如果没有字幕，显示拖拽提示
            self.subtitle_table.setRowCount(1)
            empty_hint = QTableWidgetItem("📁 拖拽字幕文件到此处，或点击'打开文件'按钮加载字幕")
            empty_hint.setTextAlignment(Qt.AlignCenter)
            empty_hint.setFlags(Qt.NoItemFlags)  # 设置为禁用状态以应用特殊样式
            self.subtitle_table.setItem(0, 0, empty_hint)
            self.subtitle_table.setSpan(0, 0, 1, 4)  # 合并所有列
        else:
            for i, item in enumerate(self.subtitle_items):
                self.subtitle_table.setItem(i, 0, QTableWidgetItem(str(i + 1)))
                self.subtitle_table.setItem(i, 1, QTableWidgetItem(item.start_time))
                self.subtitle_table.setItem(i, 2, QTableWidgetItem(item.end_time))
                self.subtitle_table.setItem(i, 3, QTableWidgetItem(item.text.replace('\n', ' ')))
    
    def on_selection_changed(self):
        """表格选择变化"""
        current_row = self.subtitle_table.currentRow()
        if current_row >= 0 and current_row < len(self.subtitle_items):
            item = self.subtitle_items[current_row]
            self.start_time_edit.setText(item.start_time)
            self.end_time_edit.setText(item.end_time)
            self.content_edit.setPlainText(item.text)
    
    def update_subtitle(self):
        """更新字幕"""
        current_row = self.subtitle_table.currentRow()
        if current_row >= 0 and current_row < len(self.subtitle_items):
            item = self.subtitle_items[current_row]
            item.start_time = self.start_time_edit.text()
            item.end_time = self.end_time_edit.text()
            item.text = self.content_edit.toPlainText()
            
            self.update_subtitle_table()
            self.subtitle_table.selectRow(current_row)
            self.is_modified = True
            self.update_status("已更新字幕")
    
    def cancel_edit(self):
        """取消编辑"""
        self.on_selection_changed()  # 重新加载当前选中项的内容
    
    def on_content_changed(self):
        """内容变化"""
        # 这里可以添加实时预览等功能
        pass
    
    def update_status(self, message):
        """更新状态"""
        self.status_label.setText(message)
    
    def dragEnterEvent(self, event: QDragEnterEvent):
        """拖拽进入事件"""
        if event.mimeData().hasUrls():
            # 检查是否包含字幕文件
            urls = event.mimeData().urls()
            for url in urls:
                if url.isLocalFile():
                    file_path = url.toLocalFile()
                    if file_path.lower().endswith(('.srt', '.vtt', '.ass')):
                        event.acceptProposedAction()
                        return
        event.ignore()
    
    def dropEvent(self, event: QDropEvent):
        """拖拽放下事件"""
        if event.mimeData().hasUrls():
            urls = event.mimeData().urls()
            for url in urls:
                if url.isLocalFile():
                    file_path = url.toLocalFile()
                    if file_path.lower().endswith(('.srt', '.vtt', '.ass')):
                        # 如果当前有未保存的修改，询问用户
                        if self.is_modified:
                            reply = self.show_message(
                                "确认打开新文件",
                                "当前字幕内容已修改，打开新文件将丢失未保存的修改。是否继续？",
                                "question"
                            )
                            if reply == QMessageBox.No:
                                return
                        
                        # 加载拖拽的文件
                        self.load_subtitle_file(file_path)
                        event.acceptProposedAction()
                        return
        event.ignore()
    
    def closeEvent(self, event):
        """关闭事件"""
        if self.is_modified:
            reply = self.show_save_dialog(
                "确认关闭",
                "字幕内容已修改，是否保存？"
            )
            
            if reply == QMessageBox.Save:
                if self.current_file_path:
                    self.save_file()
                else:
                    self.save_as_file()
                event.accept()
            elif reply == QMessageBox.Discard:
                event.accept()
            else:
                event.ignore()
        else:
            event.accept()


if __name__ == "__main__":
    from PySide6.QtWidgets import QApplication
    
    app = QApplication(sys.argv)
    dialog = SubtitleEditorDialog()
    dialog.show()
    sys.exit(app.exec()) 