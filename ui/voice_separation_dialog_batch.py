#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FlipTalk AI - 人声分离对话框（支持批量处理和拖拽上传）
"""

import os
import sys
from pathlib import Path
from typing import Dict, Optional, List

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QLineEdit, QProgressBar, QTextEdit, QFileDialog, QMessageBox,
    QComboBox, QCheckBox, QGroupBox, QGridLayout, QFrame, QSplitter,
    QScrollArea, QWidget, QButtonGroup, QListWidget, QListWidgetItem,
    QTabWidget, QTableWidget, QTableWidgetItem, QHeaderView
)
from PySide6.QtCore import Qt, QThread, Signal, QPropertyAnimation, QEasingCurve, QRect, QMimeData, QUrl
from PySide6.QtGui import QFont, QPixmap, QPainter, QColor, QBrush, QPen, QDragEnterEvent, QDropEvent


class DragDropFileList(QListWidget):
    """支持拖拽的文件列表组件"""
    
    files_dropped = Signal(list)  # 文件拖拽信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setAcceptDrops(True)
        self.setDragDropMode(QListWidget.DropOnly)
        self.setDefaultDropAction(Qt.CopyAction)
        self.setup_style()
    
    def setup_style(self):
        """设置样式"""
        self.setStyleSheet("""
            QListWidget {
                border: 2px dashed #3CB371;
                border-radius: 8px;
                background-color: #f8fffe;
                color: #2c3e50;
                font-size: 13px;
                padding: 10px;
                min-height: 120px;
            }
            QListWidget::item {
                padding: 8px;
                border-bottom: 1px solid #e1e8ed;
                background-color: white;
                border-radius: 4px;
                margin: 2px;
            }
            QListWidget::item:selected {
                background-color: #2E8B57;
                color: white;
            }
            QListWidget::item:hover {
                background-color: #e8f5e8;
            }
        """)
        
        # 添加提示文本
        if self.count() == 0:
            self.add_placeholder_text()
    
    def add_placeholder_text(self):
        """添加占位符文本"""
        placeholder_item = QListWidgetItem("📁 拖拽音频文件到此处，或点击浏览按钮选择文件\n支持格式: WAV, MP3, FLAC, AAC, M4A")
        placeholder_item.setFlags(Qt.NoItemFlags)  # 不可选择
        placeholder_item.setTextAlignment(Qt.AlignCenter)
        self.addItem(placeholder_item)
    
    def dragEnterEvent(self, event: QDragEnterEvent):
        """拖拽进入事件"""
        if event.mimeData().hasUrls():
            # 检查是否包含音频文件
            urls = event.mimeData().urls()
            audio_extensions = {'.wav', '.mp3', '.flac', '.aac', '.m4a', '.ogg'}
            
            for url in urls:
                if url.isLocalFile():
                    file_path = Path(url.toLocalFile())
                    if file_path.suffix.lower() in audio_extensions:
                        event.acceptProposedAction()
                        self.setStyleSheet(self.styleSheet().replace('#3CB371', '#2E8B57'))
                        return
            
        event.ignore()
    
    def dragLeaveEvent(self, event):
        """拖拽离开事件"""
        self.setStyleSheet(self.styleSheet().replace('#2E8B57', '#3CB371'))
        super().dragLeaveEvent(event)
    
    def dropEvent(self, event: QDropEvent):
        """拖拽放下事件"""
        self.setStyleSheet(self.styleSheet().replace('#2E8B57', '#3CB371'))
        
        if event.mimeData().hasUrls():
            urls = event.mimeData().urls()
            audio_files = []
            audio_extensions = {'.wav', '.mp3', '.flac', '.aac', '.m4a', '.ogg'}
            
            for url in urls:
                if url.isLocalFile():
                    file_path = Path(url.toLocalFile())
                    if file_path.suffix.lower() in audio_extensions and file_path.exists():
                        audio_files.append(str(file_path))
            
            if audio_files:
                # 清除占位符
                if self.count() == 1 and self.item(0).flags() == Qt.NoItemFlags:
                    self.clear()
                
                # 添加文件到列表
                for file_path in audio_files:
                    self.add_audio_file(file_path)
                
                # 发射信号
                self.files_dropped.emit(audio_files)
                event.acceptProposedAction()
            else:
                event.ignore()
        else:
            event.ignore()
    
    def add_audio_file(self, file_path: str):
        """添加音频文件到列表"""
        file_path_obj = Path(file_path)
        
        # 检查文件是否已存在
        for i in range(self.count()):
            item = self.item(i)
            if item and item.data(Qt.UserRole) == file_path:
                return  # 文件已存在
        
        # 创建列表项
        file_size = file_path_obj.stat().st_size / (1024 * 1024)  # MB
        display_text = f"🎵 {file_path_obj.name}\n📁 {file_path_obj.parent}\n📊 {file_size:.1f} MB"
        
        item = QListWidgetItem(display_text)
        item.setData(Qt.UserRole, file_path)  # 存储完整路径
        item.setToolTip(file_path)
        
        self.addItem(item)
    
    def remove_selected_files(self):
        """移除选中的文件"""
        for item in self.selectedItems():
            self.takeItem(self.row(item))
        
        # 如果列表为空，添加占位符
        if self.count() == 0:
            self.add_placeholder_text()
    
    def clear_all_files(self):
        """清空所有文件"""
        self.clear()
        self.add_placeholder_text()
    
    def get_file_paths(self) -> List[str]:
        """获取所有文件路径"""
        file_paths = []
        for i in range(self.count()):
            item = self.item(i)
            if item and item.flags() != Qt.NoItemFlags:  # 排除占位符
                file_path = item.data(Qt.UserRole)
                if file_path:
                    file_paths.append(file_path)
        return file_paths


class BatchVoiceSeparationWorker(QThread):
    """批量人声分离工作线程"""
    
    progress_updated = Signal(int)  # 总体进度
    file_progress_updated = Signal(int, int)  # 当前文件进度 (文件索引, 进度)
    status_updated = Signal(str)
    file_completed = Signal(int, dict)  # 文件完成 (文件索引, 输出文件字典)
    file_failed = Signal(int, str)  # 文件失败 (文件索引, 错误信息)
    batch_completed = Signal(list)  # 批量完成 (所有结果列表)
    
    def __init__(self, file_paths: List[str], output_dir: str, config: dict):
        super().__init__()
        self.file_paths = file_paths
        self.output_dir = output_dir
        self.config = config
        self.is_cancelled = False
        self.results = []
    
    def cancel(self):
        """取消处理"""
        self.is_cancelled = True
    
    def run(self):
        """执行批量处理"""
        try:
            from core.services import FlipTalkCoreService
            core_service = FlipTalkCoreService()
            
            if not core_service.voice_separation.separator:
                self.status_updated.emit("❌ 人声分离插件未初始化")
                return
            
            # 设置模型配置
            if 'model' in self.config:
                core_service.voice_separation.separator.set_config({
                    'model': self.config['model']
                })
            
            total_files = len(self.file_paths)
            self.results = []
            
            for i, file_path in enumerate(self.file_paths):
                if self.is_cancelled:
                    break
                
                file_name = Path(file_path).name
                self.status_updated.emit(f"🎵 处理文件 {i+1}/{total_files}: {file_name}")
                
                try:
                    # 执行人声分离
                    result = core_service.voice_separation.separator.separate(
                        file_path, self.output_dir, self.config.get('output_options')
                    )
                    
                    self.results.append({
                        'file_path': file_path,
                        'success': True,
                        'output_files': result,
                        'error': None
                    })
                    
                    self.file_completed.emit(i, result)
                    
                except Exception as e:
                    error_msg = str(e)
                    self.results.append({
                        'file_path': file_path,
                        'success': False,
                        'output_files': {},
                        'error': error_msg
                    })
                    
                    self.file_failed.emit(i, error_msg)
                
                # 更新总体进度
                overall_progress = int((i + 1) / total_files * 100)
                self.progress_updated.emit(overall_progress)
            
            if not self.is_cancelled:
                self.status_updated.emit("✅ 批量处理完成！")
                self.batch_completed.emit(self.results)
            
        except Exception as e:
            self.status_updated.emit(f"❌ 批量处理失败: {e}")


class VoiceSeparationWorker(QThread):
    """人声分离工作线程"""
    
    progress_updated = Signal(int)
    status_updated = Signal(str)
    separation_completed = Signal(dict)
    separation_failed = Signal(str)
    
    def __init__(self, audio_path: str, output_dir: str, config: dict):
        super().__init__()
        self.audio_path = audio_path
        self.output_dir = output_dir
        self.config = config
        self.is_cancelled = False
    
    def cancel(self):
        """取消处理"""
        self.is_cancelled = True
    
    def run(self):
        """执行人声分离"""
        try:
            from core.services import FlipTalkCoreService
            core_service = FlipTalkCoreService()
            
            if not core_service.voice_separation.separator:
                self.separation_failed.emit("人声分离插件未初始化")
                return
            
            # 设置模型配置
            if 'model' in self.config:
                core_service.voice_separation.separator.set_config({
                    'model': self.config['model']
                })
            
            self.progress_updated.emit(10)
            self.status_updated.emit("正在加载AI模型...")
            
            if self.is_cancelled:
                return
            
            self.progress_updated.emit(30)
            self.status_updated.emit("正在分析音频文件...")
            
            if self.is_cancelled:
                return
            
            self.progress_updated.emit(50)
            self.status_updated.emit("正在进行人声分离...")
            
            # 执行人声分离
            output_files = core_service.voice_separation.separator.separate(
                self.audio_path, self.output_dir, self.config.get('output_options')
            )
            
            if self.is_cancelled:
                return

            self.progress_updated.emit(90)
            self.status_updated.emit("正在整理输出文件...")

            # 根据配置过滤输出文件
            if 'output_options' in self.config:
                selected_outputs = self.config['output_options']
                # 这里不删除文件，只是标记，让UI层处理删除
                # 因为用户可能想要预览所有文件再决定
            
            self.progress_updated.emit(100)
            self.status_updated.emit("人声分离完成！")

            self.separation_completed.emit(output_files)
            
        except Exception as e:
            self.separation_failed.emit(str(e))


class VoiceSeparationDialogBatch(QDialog):
    """人声分离对话框（支持批量处理）"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.worker = None
        self.batch_worker = None
        self.config = {
            'model': 'htdemucs',
            'output_options': {
                'vocals': True,
                'background': True,
                'drums': False,
                'bass': False,
                'other': False
            }
        }
        self.batch_results = []
        self.setup_ui()
        self.setup_style()
        self.load_model_info()
    
    def setup_ui(self):
        """设置用户界面"""
        self.setWindowTitle("音频人声分离 - FlipTalk AI")
        self.setFixedSize(1100, 800)  # 增大窗口尺寸
        self.setModal(True)

        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(0)
        main_layout.setContentsMargins(0, 0, 0, 0)

        # 创建标题区域
        self.create_header_section(main_layout)

        # 创建主内容区域
        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)
        content_layout.setSpacing(20)
        content_layout.setContentsMargins(30, 20, 30, 30)

        # 创建标签页
        self.tab_widget = QTabWidget()
        self.tab_widget.setStyleSheet(self.get_tab_style())

        # 单文件处理标签页
        self.create_single_file_tab()

        # 批量处理标签页
        self.create_batch_processing_tab()

        content_layout.addWidget(self.tab_widget)
        main_layout.addWidget(content_widget)

    def create_header_section(self, main_layout):
        """创建标题区域"""
        header_frame = QFrame()
        header_frame.setFixedHeight(80)
        header_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #2E8B57, stop:1 #3CB371);
                border: none;
            }
        """)

        header_layout = QVBoxLayout(header_frame)
        header_layout.setAlignment(Qt.AlignCenter)

        title_label = QLabel("🎵 音频人声分离")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 24px;
                font-weight: bold;
                background: transparent;
                border: none;
            }
        """)

        subtitle_label = QLabel("使用AI技术智能分离音频中的人声和背景音乐")
        subtitle_label.setAlignment(Qt.AlignCenter)
        subtitle_label.setStyleSheet("""
            QLabel {
                color: rgba(255, 255, 255, 0.8);
                font-size: 12px;
                background: transparent;
                border: none;
            }
        """)

        header_layout.addWidget(title_label)
        header_layout.addWidget(subtitle_label)
        main_layout.addWidget(header_frame)

    def create_single_file_tab(self):
        """创建单文件处理标签页"""
        single_tab = QWidget()
        single_layout = QVBoxLayout(single_tab)
        single_layout.setSpacing(20)
        single_layout.setContentsMargins(20, 20, 20, 20)

        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)

        # 左侧配置区域
        self.create_config_section(splitter)

        # 右侧处理区域
        self.create_process_section(splitter)

        splitter.setSizes([400, 500])
        single_layout.addWidget(splitter)

        self.tab_widget.addTab(single_tab, "📄 单文件处理")

    def create_batch_processing_tab(self):
        """创建批量处理标签页"""
        batch_tab = QWidget()
        batch_layout = QVBoxLayout(batch_tab)
        batch_layout.setSpacing(20)
        batch_layout.setContentsMargins(20, 20, 20, 20)

        # 创建分割器
        batch_splitter = QSplitter(Qt.Horizontal)

        # 左侧：文件列表和配置
        self.create_batch_config_section(batch_splitter)

        # 右侧：批量处理状态
        self.create_batch_process_section(batch_splitter)

        batch_splitter.setSizes([500, 400])
        batch_layout.addWidget(batch_splitter)

        self.tab_widget.addTab(batch_tab, "📁 批量处理")
