class StyleManager:
    COLORS = {
        'primary': '#2B9D7C',
        'primary_hover': '#00CC55',
        'background_dark': '#1B1E24',
        'background_light': '#14161A',
        'text_white': '#FFFFFF',
        'text_gray': 'rgba(255, 255, 255, 0.7)',
        'text_light_gray': 'rgba(255, 255, 255, 0.4)',
        'border': '#444444',
        'transparent': 'transparent',
        'marked': '#FF0000'
    }
    
    @staticmethod
    def get_label_style(color='#FFFFFF', font_size=14, bg_color='transparent'):
        return f'''QLabel {{ color: {color}; font-size: {font_size}px; background-color: {bg_color}; border: none; }}'''
    
    @staticmethod
    def get_button_style(bg_color='#2B9D7C', text_color='#1B1E24', font_size=20, border_radius=19):
        return f'''QPushButton {{ background-color: {bg_color}; color: {text_color}; font-size: {font_size}px; border: none; border-radius: {border_radius}px; }}'''
    
    @staticmethod
    def get_input_style(bg_color='#14161A', border_radius=17):
        return f'''QLineEdit {{ background-color: {bg_color}; color: #FFFFFF; border: 1px solid #444444; border-radius: {border_radius}px; padding: 5px; }}'''
    
    @staticmethod
    def get_frame_style(bg_color='#1B1E24', border_radius=30):
        return f'''QFrame {{ background-color: {bg_color}; border-radius: {border_radius}px; border: none; }}'''
    
    @staticmethod
    def hex_to_rgb(hex_color):
        hex_color = hex_color.lstrip('#')
        return ', '.join(str(int(hex_color[i:i+2], 16)) for i in (0, 2, 4))
