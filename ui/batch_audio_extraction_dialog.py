#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FlipTalk AI - 批量音频提取对话框
提供批量视频文件上传和音频提取功能
"""

import os
from pathlib import Path
from typing import List, Dict
from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, 
    QProgressBar, QListWidget, QListWidgetItem, QFrame,
    QFileDialog, QMessageBox, QTextEdit, QScrollArea, QWidget,
    QGroupBox, QGridLayout, QSplitter
)
from PySide6.QtCore import Qt, Signal, QThread, QTimer, QMimeData, QUrl
from PySide6.QtGui import QFont, QDragEnterEvent, QDropEvent, QPainter, QBrush

try:
    from core.services import core_service
    CORE_SERVICE_AVAILABLE = True
except ImportError:
    CORE_SERVICE_AVAILABLE = False


class TaskProgressWidget(QFrame):
    """单个任务进度显示组件"""
    
    def __init__(self, filename: str, file_path: str):
        super().__init__()
        self.filename = filename
        self.file_path = file_path
        self.task_id = None
        self.setup_ui()
    
    def setup_ui(self):
        """初始化UI"""
        self.setFixedHeight(80)
        self.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #2A2D35, 
                    stop:0.5 #2E3139, 
                    stop:1 #2A2D35);
                border: 1px solid #3A3D45;
                border-radius: 8px;
                margin: 2px;
            }
            QFrame:hover {
                border: 1px solid #4A4D55;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #2E3139, 
                    stop:0.5 #32353D, 
                    stop:1 #2E3139);
            }
        """)
        
        layout = QHBoxLayout(self)
        layout.setContentsMargins(10, 8, 10, 8)  # 减少边距
        layout.setSpacing(10)  # 减少间距
        
        # 左侧信息区域 - 设置最大宽度避免布局错乱
        info_widget = QWidget()
        info_widget.setMaximumWidth(220)  # 进一步减少宽度
        info_widget.setStyleSheet("QWidget { background: transparent; }")  # 透明背景
        info_layout = QVBoxLayout(info_widget)
        info_layout.setContentsMargins(0, 0, 0, 0)
        info_layout.setSpacing(5)
        
        # 文件名 - 添加省略号处理和tooltip
        self.filename_label = QLabel()
        self.update_filename_display()  # 设置显示文本和tooltip
        self.filename_label.setStyleSheet("""
            QLabel {
                color: #FFFFFF;
                font-size: 14px;
                font-weight: bold;
                background: transparent;
                border: none;
            }
        """)
        self.filename_label.setWordWrap(False)  # 不换行
        info_layout.addWidget(self.filename_label)
        
        # 状态信息
        self.status_label = QLabel("等待处理...")
        self.status_label.setStyleSheet("""
            QLabel {
                color: #CCCCCC;
                font-size: 12px;
                background: transparent;
                border: none;
            }
        """)
        info_layout.addWidget(self.status_label)
        
        layout.addWidget(info_widget)
        
        # 中间进度区域
        progress_widget = QWidget()
        progress_widget.setFixedWidth(140)  # 进一步减少宽度
        progress_widget.setStyleSheet("QWidget { background: transparent; }")  # 透明背景
        progress_layout = QVBoxLayout(progress_widget)
        progress_layout.setContentsMargins(0, 0, 0, 0)
        progress_layout.setSpacing(5)
        
        # 添加垂直居中的stretch
        progress_layout.addStretch()
        
        self.progress_bar = QProgressBar()
        self.progress_bar.setFixedWidth(120)  # 进一步减少进度条宽度
        self.progress_bar.setFixedHeight(8)
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                border: 1px solid #444444;
                border-radius: 4px;
                background-color: #1A1A1A;
                text-align: center;
            }
            QProgressBar::chunk {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #2B9D7C, stop:1 #00CC55);
                border-radius: 3px;
            }
        """)
        self.progress_bar.setVisible(False)
        progress_layout.addWidget(self.progress_bar)
        progress_layout.addStretch()
        
        layout.addWidget(progress_widget)
        
        # 右侧操作区域
        button_widget = QWidget()
        button_widget.setFixedWidth(65)  # 进一步减少宽度
        button_widget.setStyleSheet("QWidget { background: transparent; }")  # 透明背景
        button_layout = QVBoxLayout(button_widget)
        button_layout.setContentsMargins(0, 0, 0, 0)
        
        # 垂直居中
        button_layout.addStretch()
        
        self.remove_btn = QPushButton("移除")
        self.remove_btn.setFixedSize(60, 30)
        self.remove_btn.setStyleSheet("""
            QPushButton {
                background-color: #FF6B6B;
                color: #FFFFFF;
                font-size: 12px;
                border: none;
                border-radius: 15px;
            }
            QPushButton:hover {
                background-color: #FF5555;
            }
        """)
        button_layout.addWidget(self.remove_btn)
        button_layout.addStretch()
        
        layout.addWidget(button_widget)
    
    def set_status(self, status: str, color: str = "#CCCCCC"):
        """设置状态文本"""
        self.status_label.setText(status)
        self.status_label.setStyleSheet(f"""
            QLabel {{
                color: {color};
                font-size: 12px;
                background: transparent;
                border: none;
            }}
        """)
    
    def set_progress(self, progress: int):
        """设置进度"""
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(progress)
    
    def set_task_id(self, task_id: str):
        """设置任务ID"""
        self.task_id = task_id
    
    def update_filename_display(self):
        """更新文件名显示，处理长文件名的省略"""
        # 计算可显示的文件名长度
        max_chars = 25  # 进一步调整为25字符以适应更紧凑的布局
        
        if len(self.filename) <= max_chars:
            display_name = self.filename
        else:
            # 提取文件名和扩展名
            name_parts = self.filename.rsplit('.', 1)
            if len(name_parts) == 2:
                name, ext = name_parts
                # 保留扩展名，截断主文件名
                available_chars = max_chars - len(ext) - 4  # 4个字符用于"..."和点
                if available_chars > 0:
                    display_name = f"{name[:available_chars]}...{ext}"
                else:
                    display_name = f"{self.filename[:max_chars-3]}..."
            else:
                display_name = f"{self.filename[:max_chars-3]}..."
        
        self.filename_label.setText(display_name)
        # 设置tooltip显示完整文件名
        self.filename_label.setToolTip(f"完整文件名: {self.filename}\n文件路径: {self.file_path}")
    
    def get_display_name(self) -> str:
        """获取用于显示的文件名"""
        return self.filename_label.text()


class BatchAudioExtractionDialog(QDialog):
    """批量音频提取对话框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("批量音频提取 - FlipTalk AI")
        self.setFixedSize(900, 600)  # 增加宽度从800到900
        self.setModal(True)
        
        # 文件列表和任务管理
        self.file_tasks: Dict[str, TaskProgressWidget] = {}
        self.active_tasks: Dict[str, str] = {}  # task_id -> file_path
        self.output_directory: str = None  # 用户选择的输出目录
        
        self.setup_ui()
        self.setup_connections()
        
        # 设置拖拽支持
        self.setAcceptDrops(True)
    
    def setup_ui(self):
        """初始化UI"""
        self.setStyleSheet("""
            QDialog {
                background-color: #1B1E24;
                border-radius: 10px;
            }
        """)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)
        
        # 标题区域
        self.create_header_section(layout)
        
        # 主要内容区域
        splitter = QSplitter(Qt.Horizontal)
        
        # 左侧文件列表区域
        self.create_file_list_section(splitter)
        
        # 右侧信息和控制区域
        self.create_control_section(splitter)
        
        splitter.setSizes([600, 280])  # 利用新增宽度给文件列表更多空间
        layout.addWidget(splitter)
        
        # 底部按钮区域
        self.create_bottom_section(layout)
    
    def create_header_section(self, parent_layout):
        """创建标题区域"""
        header_frame = QFrame()
        header_frame.setFixedHeight(80)
        header_frame.setStyleSheet("""
            QFrame {
                background: #14161A;
                border: 1px solid rgba(255, 107, 107, 0.2);
                border-radius: 15px;
            }
        """)
        
        header_layout = QHBoxLayout(header_frame)
        header_layout.setContentsMargins(20, 10, 20, 10)
        
        # 图标和标题
        icon_label = QLabel("🎵")
        icon_label.setStyleSheet("""
            QLabel {
                color: #FF6B6B;
                font-size: 32px;
                background: transparent;
                border: none;
            }
        """)
        header_layout.addWidget(icon_label)
        
        # 标题文本区域
        title_layout = QVBoxLayout()
        
        title_label = QLabel("批量视频音频提取")
        title_label.setStyleSheet("""
            QLabel {
                color: #FFFFFF;
                font-size: 20px;
                font-weight: bold;
                background: transparent;
                border: none;
            }
        """)
        title_layout.addWidget(title_label)
        
        subtitle_label = QLabel("支持拖拽上传多个视频文件，一键提取高质量音频")
        subtitle_label.setStyleSheet("""
            QLabel {
                color: rgba(255, 255, 255, 0.7);
                font-size: 14px;
                background: transparent;
                border: none;
            }
        """)
        title_layout.addWidget(subtitle_label)
        
        header_layout.addLayout(title_layout)
        header_layout.addStretch()
        
        parent_layout.addWidget(header_frame)
    
    def create_file_list_section(self, parent_splitter):
        """创建文件列表区域"""
        file_section = QFrame()
        file_section.setStyleSheet("""
            QFrame {
                background-color: #2A2D35;
                border: 1px solid #404040;
                border-radius: 10px;
            }
        """)
        
        file_layout = QVBoxLayout(file_section)
        file_layout.setContentsMargins(15, 15, 15, 15)
        
        # 文件列表标题
        list_header = QHBoxLayout()
        
        list_title = QLabel("文件列表")
        list_title.setStyleSheet("""
            QLabel {
                color: #FFFFFF;
                font-size: 16px;
                font-weight: bold;
                background: transparent;
                border: none;
            }
        """)
        list_header.addWidget(list_title)
        
        # 添加文件按钮
        self.add_files_btn = QPushButton("添加文件")
        self.add_files_btn.setFixedSize(80, 30)
        self.add_files_btn.setStyleSheet("""
            QPushButton {
                background-color: #2B9D7C;
                color: #FFFFFF;
                font-size: 12px;
                border: none;
                border-radius: 15px;
            }
            QPushButton:hover {
                background-color: #00CC55;
            }
        """)
        list_header.addWidget(self.add_files_btn)
        
        file_layout.addLayout(list_header)
        
        # 滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setStyleSheet("""
            QScrollArea {
                border: none;
                background-color: #1E2129;
            }
            QScrollArea > QWidget > QWidget {
                background-color: #1E2129;
            }
            QScrollBar:vertical {
                background: #1A1A1A;
                width: 8px;
                border-radius: 4px;
            }
            QScrollBar::handle:vertical {
                background: #404040;
                border-radius: 4px;
            }
            QScrollBar::handle:vertical:hover {
                background: #505050;
            }
        """)
        
        # 文件列表容器
        self.file_list_widget = QWidget()
        self.file_list_widget.setStyleSheet("""
            QWidget {
                background-color: #1E2129;
                border: none;
            }
        """)
        self.file_list_layout = QVBoxLayout(self.file_list_widget)
        self.file_list_layout.setSpacing(5)
        self.file_list_layout.addStretch()
        
        scroll_area.setWidget(self.file_list_widget)
        file_layout.addWidget(scroll_area)
        
        parent_splitter.addWidget(file_section)
    
    def create_control_section(self, parent_splitter):
        """创建控制区域"""
        control_section = QFrame()
        control_section.setStyleSheet("""
            QFrame {
                background-color: #2A2D35;
                border: 1px solid #404040;
                border-radius: 10px;
            }
        """)
        
        control_layout = QVBoxLayout(control_section)
        control_layout.setContentsMargins(15, 15, 15, 15)
        control_layout.setSpacing(15)
        
        # 统计信息
        stats_group = QGroupBox("处理统计")
        stats_group.setStyleSheet("""
            QGroupBox {
                color: #FFFFFF;
                font-size: 14px;
                font-weight: bold;
                border: 1px solid #404040;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        
        stats_layout = QGridLayout(stats_group)
        
        # 统计标签
        self.total_files_label = QLabel("总文件数: 0")
        self.completed_files_label = QLabel("已完成: 0")
        self.failed_files_label = QLabel("失败: 0")
        
        for label in [self.total_files_label, self.completed_files_label, self.failed_files_label]:
            label.setStyleSheet("""
                QLabel {
                    color: #CCCCCC;
                    font-size: 12px;
                    background: transparent;
                    border: none;
                }
            """)
        
        stats_layout.addWidget(self.total_files_label, 0, 0)
        stats_layout.addWidget(self.completed_files_label, 1, 0)
        stats_layout.addWidget(self.failed_files_label, 2, 0)
        
        control_layout.addWidget(stats_group)
        
        # 输出设置
        output_group = QGroupBox("输出设置")
        output_group.setStyleSheet(stats_group.styleSheet())
        
        output_layout = QVBoxLayout(output_group)
        
        # 输出目录选择
        output_dir_layout = QHBoxLayout()
        
        output_dir_label = QLabel("输出目录:")
        output_dir_label.setStyleSheet("""
            QLabel {
                color: #CCCCCC;
                font-size: 12px;
                background: transparent;
                border: none;
            }
        """)
        output_dir_layout.addWidget(output_dir_label)
        
        self.output_dir_btn = QPushButton("选择目录")
        self.output_dir_btn.setFixedHeight(25)
        self.output_dir_btn.setStyleSheet("""
            QPushButton {
                background-color: #45B7D1;
                color: #FFFFFF;
                font-size: 11px;
                border: none;
                border-radius: 12px;
                padding: 2px 8px;
            }
            QPushButton:hover {
                background-color: #3A9BC1;
            }
        """)
        output_dir_layout.addWidget(self.output_dir_btn)
        
        output_layout.addLayout(output_dir_layout)
        
        self.output_path_label = QLabel("默认：视频文件同目录")
        self.output_path_label.setStyleSheet("""
            QLabel {
                color: #888888;
                font-size: 10px;
                background: transparent;
                border: none;
            }
        """)
        output_layout.addWidget(self.output_path_label)
        
        control_layout.addWidget(output_group)
        
        # 日志区域
        log_group = QGroupBox("处理日志")
        log_group.setStyleSheet(stats_group.styleSheet())
        
        log_layout = QVBoxLayout(log_group)
        
        self.log_text = QTextEdit()
        self.log_text.setFixedHeight(150)
        self.log_text.setStyleSheet("""
            QTextEdit {
                background-color: #1A1A1A;
                color: #CCCCCC;
                font-family: 'Courier New', monospace;
                font-size: 10px;
                border: 1px solid #404040;
                border-radius: 5px;
                padding: 5px;
            }
        """)
        self.log_text.setReadOnly(True)
        log_layout.addWidget(self.log_text)
        
        control_layout.addWidget(log_group)
        control_layout.addStretch()
        
        parent_splitter.addWidget(control_section)
    
    def create_bottom_section(self, parent_layout):
        """创建底部按钮区域"""
        bottom_layout = QHBoxLayout()
        
        # 清空列表按钮
        self.clear_btn = QPushButton("清空列表")
        self.clear_btn.setFixedSize(80, 35)
        self.clear_btn.setStyleSheet("""
            QPushButton {
                background-color: #6C757D;
                color: #FFFFFF;
                font-size: 14px;
                border: none;
                border-radius: 17px;
            }
            QPushButton:hover {
                background-color: #5A6268;
            }
        """)
        bottom_layout.addWidget(self.clear_btn)
        
        bottom_layout.addStretch()
        
        # 开始处理按钮
        self.start_btn = QPushButton("开始处理")
        self.start_btn.setFixedSize(120, 35)
        self.start_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #FF6B6B, stop:1 #FF5555);
                color: #FFFFFF;
                font-size: 14px;
                font-weight: bold;
                border: none;
                border-radius: 17px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #FF5555, stop:1 #FF4444);
            }
            QPushButton:disabled {
                background-color: #404040;
                color: #888888;
            }
        """)
        self.start_btn.setEnabled(False)
        bottom_layout.addWidget(self.start_btn)
        
        # 关闭按钮
        self.close_btn = QPushButton("关闭")
        self.close_btn.setFixedSize(80, 35)
        self.close_btn.setStyleSheet("""
            QPushButton {
                background-color: #6C757D;
                color: #FFFFFF;
                font-size: 14px;
                border: none;
                border-radius: 17px;
            }
            QPushButton:hover {
                background-color: #5A6268;
            }
        """)
        bottom_layout.addWidget(self.close_btn)
        
        parent_layout.addLayout(bottom_layout)
    
    def setup_connections(self):
        """设置信号连接"""
        self.add_files_btn.clicked.connect(self.add_files)
        self.clear_btn.clicked.connect(self.clear_files)
        self.start_btn.clicked.connect(self.start_processing)
        self.close_btn.clicked.connect(self.close)
        self.output_dir_btn.clicked.connect(self.select_output_directory)
        
        # 连接核心服务信号
        if CORE_SERVICE_AVAILABLE and core_service:
            core_service.audio_extraction.extraction_started.connect(self.on_extraction_started)
            core_service.audio_extraction.extraction_progress.connect(self.on_extraction_progress)
            core_service.audio_extraction.extraction_completed.connect(self.on_extraction_completed)
            core_service.audio_extraction.extraction_failed.connect(self.on_extraction_failed)
    
    def add_files(self):
        """添加文件"""
        file_dialog = QFileDialog(self)
        file_dialog.setFileMode(QFileDialog.ExistingFiles)
        file_dialog.setNameFilter("视频文件 (*.mp4 *.avi *.mkv *.mov *.wmv *.flv *.webm *.m4v)")
        
        if file_dialog.exec():
            files = file_dialog.selectedFiles()
            for file_path in files:
                self.add_file_to_list(file_path)
    
    def add_file_to_list(self, file_path: str):
        """添加文件到列表"""
        if file_path in self.file_tasks:
            return  # 文件已存在
        
        filename = Path(file_path).name
        task_widget = TaskProgressWidget(filename, file_path)
        
        # 连接移除按钮
        task_widget.remove_btn.clicked.connect(lambda: self.remove_file(file_path))
        
        # 添加到布局
        self.file_list_layout.insertWidget(self.file_list_layout.count() - 1, task_widget)
        self.file_tasks[file_path] = task_widget
        
        self.update_statistics()
        self.log_message(f"添加文件: {filename}")
    
    def remove_file(self, file_path: str):
        """移除文件"""
        if file_path in self.file_tasks:
            widget = self.file_tasks[file_path]
            self.file_list_layout.removeWidget(widget)
            widget.deleteLater()
            del self.file_tasks[file_path]
            
            self.update_statistics()
            filename = Path(file_path).name
            self.log_message(f"移除文件: {filename}")
    
    def clear_files(self):
        """清空文件列表"""
        for file_path in list(self.file_tasks.keys()):
            self.remove_file(file_path)
    
    def start_processing(self):
        """开始处理"""
        if not self.file_tasks:
            QMessageBox.warning(self, "警告", "请先添加要处理的视频文件")
            return
        
        if not CORE_SERVICE_AVAILABLE or not core_service:
            QMessageBox.critical(self, "错误", "音频提取服务不可用")
            return
        
        self.start_btn.setEnabled(False)
        
        # 记录输出目录信息
        if self.output_directory:
            self.log_message(f"开始批量音频提取... 输出目录: {self.output_directory}")
        else:
            self.log_message("开始批量音频提取... 使用默认输出目录（视频文件同目录）")
        
        # 启动所有任务
        for file_path, widget in self.file_tasks.items():
            try:
                widget.set_status("正在启动...", "#FFA726")
                # 传递用户选择的输出目录
                task_id = core_service.audio_extraction.extract_audio_async(file_path, self.output_directory)
                widget.set_task_id(task_id)
                self.active_tasks[task_id] = file_path
                self.log_message(f"启动任务: {Path(file_path).name}")
            except Exception as e:
                widget.set_status(f"启动失败: {str(e)}", "#FF6B6B")
                self.log_message(f"启动失败: {Path(file_path).name} - {str(e)}")
    
    def select_output_directory(self):
        """选择输出目录"""
        directory = QFileDialog.getExistingDirectory(self, "选择输出目录")
        if directory:
            self.output_directory = directory
            self.output_path_label.setText(f"输出目录: {directory}")
            self.log_message(f"设置输出目录: {directory}")
    
    def update_statistics(self):
        """更新统计信息"""
        total = len(self.file_tasks)
        completed = sum(1 for widget in self.file_tasks.values() 
                       if "完成" in widget.status_label.text())
        failed = sum(1 for widget in self.file_tasks.values() 
                    if "失败" in widget.status_label.text())
        
        self.total_files_label.setText(f"总文件数: {total}")
        self.completed_files_label.setText(f"已完成: {completed}")
        self.failed_files_label.setText(f"失败: {failed}")
        
        # 更新按钮状态
        self.start_btn.setEnabled(total > 0 and not self.active_tasks)
    
    def log_message(self, message: str):
        """添加日志消息"""
        import datetime
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        self.log_text.append(f"[{timestamp}] {message}")
        
        # 滚动到底部
        from PySide6.QtGui import QTextCursor
        cursor = self.log_text.textCursor()
        cursor.movePosition(QTextCursor.End)
        self.log_text.setTextCursor(cursor)
    
    def on_extraction_started(self, task_id: str, filename: str):
        """处理提取开始信号"""
        if task_id in self.active_tasks:
            file_path = self.active_tasks[task_id]
            widget = self.file_tasks[file_path]
            widget.set_status("正在提取音频...", "#45B7D1")
            widget.set_progress(0)
            self.log_message(f"开始提取: {filename}")
    
    def on_extraction_progress(self, task_id: str, progress: int):
        """处理提取进度信号"""
        if task_id in self.active_tasks:
            file_path = self.active_tasks[task_id]
            widget = self.file_tasks[file_path]
            widget.set_progress(progress)
            widget.set_status(f"正在提取音频... {progress}%", "#45B7D1")
    
    def on_extraction_completed(self, task_id: str, input_path: str, output_path: str):
        """处理提取完成信号"""
        if task_id in self.active_tasks:
            file_path = self.active_tasks[task_id]
            widget = self.file_tasks[file_path]
            widget.set_status("提取完成", "#4ECDC4")
            widget.set_progress(100)
            
            filename = Path(input_path).name
            output_filename = Path(output_path).name if output_path else "未知"
            self.log_message(f"提取完成: {filename} -> {output_filename}")
            
            del self.active_tasks[task_id]
            self.update_statistics()
    
    def on_extraction_failed(self, task_id: str, input_path: str, error: str):
        """处理提取失败信号"""
        if task_id in self.active_tasks:
            file_path = self.active_tasks[task_id]
            widget = self.file_tasks[file_path]
            widget.set_status(f"提取失败: {error}", "#FF6B6B")
            
            filename = Path(input_path).name
            self.log_message(f"提取失败: {filename} - {error}")
            
            del self.active_tasks[task_id]
            self.update_statistics()
    
    def dragEnterEvent(self, event: QDragEnterEvent):
        """拖拽进入事件"""
        if event.mimeData().hasUrls():
            event.acceptProposedAction()
    
    def dropEvent(self, event: QDropEvent):
        """拖拽放下事件"""
        files = []
        for url in event.mimeData().urls():
            if url.isLocalFile():
                file_path = url.toLocalFile()
                # 检查是否为视频文件
                if any(file_path.lower().endswith(ext) for ext in 
                      ['.mp4', '.avi', '.mkv', '.mov', '.wmv', '.flv', '.webm', '.m4v']):
                    files.append(file_path)
        
        for file_path in files:
            self.add_file_to_list(file_path)
        
        if files:
            self.log_message(f"拖拽添加了 {len(files)} 个视频文件") 