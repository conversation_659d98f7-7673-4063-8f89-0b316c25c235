#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
视频下载对话框
支持YouTube等平台的视频下载功能
"""

import sys
import os
import re
import threading
import time
from PySide6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                               QPushButton, QLineEdit, QComboBox, QProgressBar,
                               QTextEdit, QGroupBox, QCheckBox, QFileDialog,
                               QMessageBox, QFrame, QListWidget, QListWidgetItem,
                               QWidget, QScrollArea)
from PySide6.QtCore import Qt, QThread, Signal, QTimer
from PySide6.QtGui import QFont, QIcon

try:
    import yt_dlp
    YT_DLP_AVAILABLE = True
except ImportError:
    YT_DLP_AVAILABLE = False

def get_platform_headers(url):
    """根据平台获取适合的请求头"""
    base_headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'zh-CN,zh;q=0.8,en-US;q=0.5,en;q=0.3',
        'Accept-Encoding': 'gzip, deflate',
        'Connection': 'keep-alive',
    }
    
    # 根据平台设置特定的Referer和其他头部
    if 'douyin.com' in url:
        base_headers['Referer'] = 'https://www.douyin.com/'
        base_headers['Origin'] = 'https://www.douyin.com'
    elif 'bilibili.com' in url:
        base_headers['Referer'] = 'https://www.bilibili.com/'
        base_headers['Origin'] = 'https://www.bilibili.com'
        # B站特有的请求头
        base_headers['Sec-Fetch-Dest'] = 'document'
        base_headers['Sec-Fetch-Mode'] = 'navigate'
        base_headers['Sec-Fetch-Site'] = 'none'
        base_headers['Sec-Fetch-User'] = '?1'
    elif 'youtube.com' in url or 'youtu.be' in url:
        base_headers['Referer'] = 'https://www.youtube.com/'
        base_headers['Origin'] = 'https://www.youtube.com'
    elif 'kuaishou.com' in url:
        base_headers['Referer'] = 'https://www.kuaishou.com/'
        base_headers['Origin'] = 'https://www.kuaishou.com'
    else:
        # 默认使用通用Referer
        base_headers['Referer'] = 'https://www.google.com/'
    
    return base_headers

class VideoDownloadWorker(QThread):
    """视频下载工作线程"""
    progress_updated = Signal(int)
    status_updated = Signal(str)
    download_completed = Signal(str)
    download_failed = Signal(str)
    
    def __init__(self, url, output_path, quality, format_type):
        super().__init__()
        self.url = url
        self.output_path = output_path
        self.quality = quality
        self.format_type = format_type
        self.is_cancelled = False
    
    def run(self):
        """运行下载任务"""
        if not YT_DLP_AVAILABLE:
            self.download_failed.emit("缺少yt-dlp库，请先安装：pip install yt-dlp")
            return
            
        try:
            # 配置yt-dlp选项
            format_selector = self.get_format_selector()
            
            # 获取平台特定的请求头
            platform_headers = get_platform_headers(self.url)
            
            ydl_opts = {
                'outtmpl': os.path.join(self.output_path, '%(title)s.%(ext)s'),
                'format': format_selector,
                'progress_hooks': [self.progress_hook],
                'writeinfojson': False,
                'writedescription': False,
                'writesubtitles': False,
                # 使用平台特定的请求头
                'http_headers': platform_headers,
                # 重试和超时配置
                'retries': 3,
                'socket_timeout': 30,
                'fragment_retries': 3,
            }
            
            self.status_updated.emit(f"格式选择器: {format_selector}")
            
            with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                self.status_updated.emit("正在获取视频信息...")
                info = ydl.extract_info(self.url, download=False)
                title = info.get('title', 'Unknown')
                
                if self.is_cancelled:
                    return
                    
                self.status_updated.emit(f"开始下载: {title}")
                ydl.download([self.url])
                
                if not self.is_cancelled:
                    self.download_completed.emit(f"下载完成: {title}")
                    
        except Exception as e:
            if not self.is_cancelled:
                self.download_failed.emit(f"下载失败: {str(e)}")
    
    def get_format_selector(self):
        """根据选择获取格式选择器"""
        # 处理特殊格式
        if self.quality == "仅音频" or self.format_type == "仅音频":
            return "bestaudio[ext=m4a]/bestaudio[ext=mp3]/bestaudio"
        
        if self.format_type == "仅视频":
            # 仅视频下载
            height = self.extract_height_from_quality(self.quality)
            if height:
                return f"bestvideo[height={height}][ext=mp4]/bestvideo[height={height}]/bestvideo[height<={height}][ext=mp4]/bestvideo[height<={height}]/bestvideo[ext=mp4]/bestvideo"
            else:
                return "bestvideo[ext=mp4]/bestvideo"
        else:
            # 视频+音频下载（默认）
            if self.quality == "最佳质量":
                return "best[ext=mp4]/best"
            
            height = self.extract_height_from_quality(self.quality)
            if height:
                # 优先选择精确的分辨率，如果没有则选择较低的分辨率，避免选择更高质量
                return (f"bestvideo[height={height}][ext=mp4]+bestaudio[ext=m4a]/"
                       f"bestvideo[height={height}]+bestaudio/"
                       f"bestvideo[height<={height}][ext=mp4]+bestaudio[ext=m4a]/"
                       f"bestvideo[height<={height}]+bestaudio/"
                       f"best[height={height}][ext=mp4]/"
                       f"best[height={height}]/"
                       f"best[height<={height}][ext=mp4]/"
                       f"best[height<={height}]/"
                       f"worst[ext=mp4]/worst")
            else:
                return "best[ext=mp4]/best"
    
    def extract_height_from_quality(self, quality_text):
        """从质量文本中提取分辨率高度"""
        import re
        
        # 匹配各种格式的分辨率
        patterns = [
            r'(\d+)p',  # 直接的数字+p格式，如 "1080p"
            r'\((\d+)p\)',  # 括号内的格式，如 "(1080p)"
        ]
        
        for pattern in patterns:
            match = re.search(pattern, quality_text)
            if match:
                return int(match.group(1))
        
        return None
    
    def progress_hook(self, d):
        """进度回调"""
        if self.is_cancelled:
            return
            
        if d['status'] == 'downloading':
            if 'total_bytes' in d:
                percent = int(d['downloaded_bytes'] / d['total_bytes'] * 100)
                self.progress_updated.emit(percent)
            elif '_percent_str' in d:
                percent_str = d['_percent_str'].replace('%', '')
                try:
                    percent = int(float(percent_str))
                    self.progress_updated.emit(percent)
                except:
                    pass
        elif d['status'] == 'finished':
            self.progress_updated.emit(100)
    
    def cancel_download(self):
        """取消下载"""
        self.is_cancelled = True

class VideoDownloadDialog(QDialog):
    """视频下载对话框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.download_worker = None
        self.setup_ui()
        self.setup_style()
    
    def setup_ui(self):
        """初始化UI"""
        self.setWindowTitle("视频下载 - FlipTalk AI")
        self.setFixedSize(800, 670)  # 调整高度适应新的进度区域
        self.setModal(True)
        
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(15)
        
        # 标题区域
        title_frame = self.create_title_section()
        main_layout.addWidget(title_frame)
        
        # URL输入区域
        url_group = self.create_url_section()
        main_layout.addWidget(url_group)
        
        # 下载设置区域
        settings_group = self.create_settings_section()
        main_layout.addWidget(settings_group)
        
        # 进度区域
        progress_group = self.create_progress_section()
        main_layout.addWidget(progress_group)
        
        # 按钮区域
        button_layout = self.create_button_section()
        main_layout.addLayout(button_layout)
        
        # 状态检查
        self.check_dependencies()
    
    def create_title_section(self):
        """创建标题区域"""
        title_frame = QFrame()
        title_frame.setFixedHeight(80)
        
        title_layout = QHBoxLayout(title_frame)
        title_layout.setContentsMargins(20, 10, 20, 10)
        
        # 图标和标题
        icon_label = QLabel("📥")
        icon_label.setStyleSheet("font-size: 32px;")
        title_layout.addWidget(icon_label)
        
        title_text = QVBoxLayout()
        main_title = QLabel("视频下载")
        main_title.setStyleSheet("font-size: 20px; font-weight: bold; color: #FFFFFF;")
        
        sub_title = QLabel("支持YouTube、Bilibili等主流平台")
        sub_title.setStyleSheet("font-size: 12px; color: #999999;")
        
        title_text.addWidget(main_title)
        title_text.addWidget(sub_title)
        title_layout.addLayout(title_text)
        
        title_layout.addStretch()
        
        return title_frame
    
    def create_url_section(self):
        """创建URL输入区域"""
        url_group = QGroupBox("视频链接")
        url_layout = QVBoxLayout(url_group)
        
        # URL输入框
        self.url_input = QLineEdit()
        self.url_input.setPlaceholderText("请输入视频链接 (支持YouTube、Bilibili等)")
        self.url_input.setFixedHeight(40)
        url_layout.addWidget(self.url_input)
        
        # 支持平台提示
        platforms_label = QLabel("支持平台: YouTube, Bilibili, 抖音, 快手, 西瓜视频等")
        platforms_label.setStyleSheet("color: #666666; font-size: 11px;")
        url_layout.addWidget(platforms_label)
        
        return url_group
    
    def create_settings_section(self):
        """创建下载设置区域"""
        settings_group = QGroupBox("下载设置")
        settings_layout = QVBoxLayout(settings_group)
        
        # 第一行：质量和格式
        first_row = QHBoxLayout()
        
        # 视频质量选择
        quality_layout = QVBoxLayout()
        quality_label = QLabel("视频质量:")
        self.quality_combo = QComboBox()
        self.quality_combo.addItems(["请先检查格式"])
        self.quality_combo.setEnabled(False)
        self.quality_combo.setFixedHeight(35)
        quality_layout.addWidget(quality_label)
        quality_layout.addWidget(self.quality_combo)
        
        # 下载格式选择
        format_layout = QVBoxLayout()
        format_label = QLabel("下载格式:")
        self.format_combo = QComboBox()
        self.format_combo.addItems(["视频+音频", "仅视频", "仅音频"])
        self.format_combo.setFixedHeight(35)
        format_layout.addWidget(format_label)
        format_layout.addWidget(self.format_combo)
        
        first_row.addLayout(quality_layout)
        first_row.addLayout(format_layout)
        settings_layout.addLayout(first_row)
        
        # 第二行：保存路径
        path_layout = QHBoxLayout()
        path_label = QLabel("保存路径:")
        self.path_input = QLineEdit()
        # 设置项目目录下的Downloads文件夹为默认路径
        project_dir = os.path.dirname(os.path.abspath(__file__))
        default_download_path = os.path.join(project_dir, "Downloads")
        
        # 确保Downloads文件夹存在
        if not os.path.exists(default_download_path):
            os.makedirs(default_download_path, exist_ok=True)
            
        self.path_input.setText(default_download_path)
        self.path_input.setFixedHeight(35)
        
        self.browse_button = QPushButton("浏览")
        self.browse_button.setFixedSize(60, 35)
        self.browse_button.clicked.connect(self.browse_folder)
        
        path_layout.addWidget(path_label)
        path_layout.addWidget(self.path_input)
        path_layout.addWidget(self.browse_button)
        settings_layout.addLayout(path_layout)
        
        return settings_group
    
    def create_progress_section(self):
        """创建进度显示区域"""
        progress_group = QGroupBox("下载进度")
        progress_group.setFixedHeight(220)  # 增加总高度
        
        # 设置紧凑的布局
        progress_layout = QVBoxLayout(progress_group)
        progress_layout.setContentsMargins(8, 15, 8, 8)  # 进一步减少边距
        progress_layout.setSpacing(5)  # 减少组件间距
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setFixedHeight(18)  # 减小高度
        self.progress_bar.setValue(0)
        progress_layout.addWidget(self.progress_bar)
        
        # 状态文本
        self.status_label = QLabel("请输入视频链接，然后点击'检查格式'获取可用质量选项")
        self.status_label.setStyleSheet("color: #999999; font-size: 11px;")  # 减小字体
        self.status_label.setFixedHeight(16)  # 减小高度
        self.status_label.setWordWrap(True)
        progress_layout.addWidget(self.status_label)
        
        # 日志区域 - 保守计算可用高度
        # QGroupBox标题大约占25px，边框约10px
        # 总高度220 - 标题25 - 边框10 - 上边距15 - 下边距8 - 进度条18 - 状态标签16 - 间距5*2 = 118
        self.log_text = QTextEdit()
        self.log_text.setFixedHeight(118)  # 保守的可用高度
        self.log_text.setReadOnly(True)
        self.log_text.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        self.log_text.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        self.log_text.setStyleSheet("""
            QTextEdit {
                background-color: #2A2D36;
                border: 1px solid #3A3D46;
                border-radius: 4px;
                color: #FFFFFF;
                font-family: Consolas, Monaco, monospace;
                font-size: 10px;
                padding: 4px;
            }
        """)
        progress_layout.addWidget(self.log_text)
        
        return progress_group
    
    def create_button_section(self):
        """创建按钮区域"""
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        # 检查格式按钮
        self.check_formats_button = QPushButton("检查格式")
        self.check_formats_button.setFixedSize(100, 35)
        self.check_formats_button.clicked.connect(self.check_available_formats)
        
        # 开始下载按钮
        self.download_button = QPushButton("开始下载")
        self.download_button.setFixedSize(100, 35)
        self.download_button.setEnabled(False)  # 初始状态禁用
        self.download_button.clicked.connect(self.start_download)
        
        # 取消下载按钮
        self.cancel_button = QPushButton("取消下载")
        self.cancel_button.setFixedSize(100, 35)
        self.cancel_button.setEnabled(False)
        self.cancel_button.clicked.connect(self.cancel_download)
        
        # 打开文件夹按钮
        self.open_folder_button = QPushButton("📂 打开文件夹")
        self.open_folder_button.setFixedSize(110, 35)
        self.open_folder_button.clicked.connect(self.open_download_folder)
        self.open_folder_button.setToolTip("打开下载文件保存目录")
        
        # 关闭按钮
        self.close_button = QPushButton("关闭")
        self.close_button.setFixedSize(100, 35)
        self.close_button.clicked.connect(self.close)
        
        button_layout.addWidget(self.check_formats_button)
        button_layout.addWidget(self.download_button)
        button_layout.addWidget(self.cancel_button)
        button_layout.addWidget(self.open_folder_button)
        button_layout.addWidget(self.close_button)
        
        return button_layout
    
    def setup_style(self):
        """设置样式"""
        self.setStyleSheet("""
            QDialog {
                background-color: #1B1E24;
                color: #FFFFFF;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #6C5CE7;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
                color: #FFFFFF;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                color: #6C5CE7;
            }
            QLineEdit {
                background-color: #2A2D36;
                border: 2px solid #3A3D46;
                border-radius: 6px;
                padding: 8px;
                color: #FFFFFF;
                font-size: 12px;
            }
            QLineEdit:focus {
                border-color: #6C5CE7;
            }
            QComboBox {
                background-color: #2A2D36;
                border: 2px solid #3A3D46;
                border-radius: 6px;
                padding: 8px;
                color: #FFFFFF;
                font-size: 12px;
            }
            QComboBox:focus {
                border-color: #6C5CE7;
            }
            QComboBox::drop-down {
                border: none;
            }
            QComboBox::down-arrow {
                image: none;
                border: none;
            }
            QPushButton {
                background-color: #6C5CE7;
                color: #FFFFFF;
                border: none;
                border-radius: 6px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #5A4FD5;
            }
            QPushButton:pressed {
                background-color: #4A3FC3;
            }
            QPushButton:disabled {
                background-color: #3A3D46;
                color: #666666;
            }
            QProgressBar {
                border: 2px solid #3A3D46;
                border-radius: 6px;
                text-align: center;
                color: #FFFFFF;
                font-weight: bold;
            }
            QProgressBar::chunk {
                background-color: #6C5CE7;
                border-radius: 4px;
            }
            QTextEdit {
                background-color: #2A2D36;
                border: 2px solid #3A3D46;
                border-radius: 6px;
                color: #FFFFFF;
                font-family: Consolas, Monaco, monospace;
                font-size: 11px;
            }
            QLabel {
                color: #FFFFFF;
            }
        """)
    
    def check_dependencies(self):
        """检查依赖"""
        if not YT_DLP_AVAILABLE:
            self.log_text.append("⚠️ 未检测到yt-dlp库")
            self.log_text.append("请运行以下命令安装:")
            self.log_text.append("pip install yt-dlp")
            self.download_button.setEnabled(False)
            self.status_label.setText("缺少必要依赖，无法使用下载功能")
        else:
            self.log_text.append("✅ yt-dlp库已就绪")
            self.status_label.setText("就绪，请输入视频链接开始下载")
    
    def browse_folder(self):
        """浏览文件夹"""
        folder = QFileDialog.getExistingDirectory(self, "选择保存目录", self.path_input.text())
        if folder:
            self.path_input.setText(folder)
    
    def open_download_folder(self):
        """打开下载文件夹"""
        download_path = self.path_input.text().strip()
        
        # 检查路径是否存在
        if not download_path or not os.path.exists(download_path):
            QMessageBox.warning(self, "警告", "下载路径不存在！\n请先设置有效的保存路径。")
            return
        
        try:
            # 根据操作系统打开文件管理器
            import platform
            import subprocess
            
            system = platform.system()
            if system == "Windows":
                # Windows: 使用explorer
                os.startfile(download_path)
            elif system == "Darwin":  # macOS
                # macOS: 使用open命令
                subprocess.run(["open", download_path])
            else:  # Linux和其他Unix系统
                # Linux: 使用xdg-open
                subprocess.run(["xdg-open", download_path])
                
            self.log_text.append(f"📂 已打开文件夹: {download_path}")
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"无法打开文件夹：{str(e)}")
            self.log_text.append(f"❌ 打开文件夹失败: {str(e)}")
    
    def preprocess_url(self, url):
        """预处理URL，处理各平台的特殊URL格式"""
        url = url.strip()
        
        # 处理抖音URL
        if 'douyin.com' in url:
            # 如果是主页或推荐页链接，提示用户输入具体视频链接
            if '?recommend=' in url or url.endswith('douyin.com') or url.endswith('douyin.com/'):
                raise ValueError("请输入具体的抖音视频链接，而不是首页或推荐页链接\n💡 正确格式: https://www.douyin.com/video/xxxxxxxxx 或分享的短链接")
            
            # 处理抖音短链接 (例如: https://v.douyin.com/xxxxx)
            if 'v.douyin.com' in url:
                return url  # 短链接通常可以直接使用
            
            # 处理网页版抖音链接 (例如: https://www.douyin.com/video/xxx)
            if '/video/' in url:
                return url
                
            # 处理移动端分享链接 (例如: https://www.iesdouyin.com/share/video/xxx)
            if 'iesdouyin.com' in url:
                return url
                
        # 处理B站链接
        elif 'bilibili.com' in url:
            # 处理BV号链接
            if '/video/BV' in url or '/video/av' in url:
                return url
        
        # 处理YouTube链接
        elif 'youtube.com' in url or 'youtu.be' in url:
            return url
        
        # 处理快手链接
        elif 'kuaishou.com' in url or 'chenzhongtech.com' in url:
            return url
            
        # 其他链接直接返回
        return url
    

    def check_available_formats(self):
        """检查可用格式并更新质量选择下拉框"""
        url = self.url_input.text().strip()
        if not url:
            QMessageBox.warning(self, "警告", "请先输入视频链接！")
            return
        
        if not YT_DLP_AVAILABLE:
            QMessageBox.critical(self, "错误", "缺少yt-dlp库，无法检查格式！")
            return
        
        self.log_text.clear()
        self.log_text.append("🔍 正在检查可用格式...")
        self.check_formats_button.setEnabled(False)
        self.download_button.setEnabled(False)
        self.quality_combo.setEnabled(False)
        
        # 重置质量选择框
        self.quality_combo.clear()
        self.quality_combo.addItem("检查中...")
        
        try:
            # 预处理URL
            processed_url = self.preprocess_url(url)
            self.log_text.append(f"📎 处理后的链接: {processed_url}")
            
            # 根据平台获取适合的请求头
            platform_headers = get_platform_headers(processed_url)
            self.log_text.append(f"🌐 平台检测: {platform_headers.get('Referer', 'Unknown')}")
            
            ydl_opts = {
                'quiet': True,
                'no_warnings': True,
                # 使用平台特定的请求头
                'http_headers': platform_headers,
                # 跳过不可用的提取器
                'ignoreerrors': False,
                # 重试配置
                'retries': 3,
                'socket_timeout': 30,
            }
            
            with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                info = ydl.extract_info(processed_url, download=False)
                
                if 'formats' in info:
                    self.log_text.append(f"📹 视频标题: {info.get('title', 'Unknown')}")
                    self.log_text.append("检查可用格式中...")
                    
                    # 收集可用的视频分辨率
                    available_heights = set()
                    video_formats = []
                    audio_formats = []
                    
                    for fmt in info['formats']:
                        if fmt.get('vcodec') != 'none' and fmt.get('height'):
                            height = fmt['height']
                            available_heights.add(height)
                            
                            # 视频格式信息用于显示
                            quality = f"{height}p"
                            ext = fmt.get('ext', 'unknown')
                            filesize = fmt.get('filesize')
                            size_str = f" (~{filesize//1024//1024}MB)" if filesize else ""
                            vcodec = fmt.get('vcodec', 'unknown')
                            acodec = fmt.get('acodec', 'none')
                            
                            format_info = f"🎬 {quality} {ext} (视频:{vcodec}, 音频:{acodec}){size_str}"
                            if (height, format_info) not in video_formats:
                                video_formats.append((height, format_info))
                        elif fmt.get('acodec') != 'none':
                            # 音频格式
                            ext = fmt.get('ext', 'unknown')
                            abr = fmt.get('abr', 'unknown')
                            filesize = fmt.get('filesize')
                            size_str = f" (~{filesize//1024//1024}MB)" if filesize else ""
                            
                            format_info = f"🎵 音频 {ext} ({abr}kbps){size_str}"
                            if format_info not in audio_formats:
                                audio_formats.append(format_info)
                    
                    # 更新质量选择下拉框
                    self.update_quality_combo(available_heights)
                    
                    # 按分辨率排序视频格式
                    video_formats.sort(key=lambda x: x[0], reverse=True)
                    
                    # 显示详细格式信息
                    self.log_text.append("\n📺 可用视频格式:")
                    for _, fmt_str in video_formats[:10]:  # 只显示前10个
                        self.log_text.append(f"  {fmt_str}")
                    
                    # 显示音频格式
                    if audio_formats:
                        self.log_text.append("\n🎵 可用音频格式:")
                        for fmt_str in audio_formats[:5]:  # 只显示前5个
                            self.log_text.append(f"  {fmt_str}")
                    
                    # 显示推荐设置
                    max_height = max(available_heights) if available_heights else 0
                    self.log_text.append(f"\n✅ 格式检查完成！最高可用质量: {max_height}p")
                    self.log_text.append("📋 质量选择框已更新，请选择合适的清晰度后开始下载")
                    
                    # 启用下载功能
                    self.quality_combo.setEnabled(True)
                    self.download_button.setEnabled(True)
                    
                else:
                    self.log_text.append("❌ 无法获取格式信息")
                    self.quality_combo.clear()
                    self.quality_combo.addItem("检查失败")
                    
        except ValueError as e:
            # URL预处理错误
            self.log_text.append(f"❌ URL错误: {str(e)}")
            self.log_text.append("💡 请检查您输入的链接是否正确")
            self.quality_combo.clear()
            self.quality_combo.addItem("URL错误")
        except Exception as e:
            error_msg = str(e)
            self.log_text.append(f"❌ 检查格式失败: {error_msg}")
            
            # 提供针对性的错误提示
            if "Fresh cookies" in error_msg or "cookies" in error_msg.lower():
                self.log_text.append("💡 抖音需要登录状态的cookies：")
                self.log_text.append("  • 这是抖音的反爬虫机制，部分视频需要登录才能访问")
                self.log_text.append("  • 建议尝试其他公开的抖音视频链接")
                self.log_text.append("  • 或等待几分钟后重试（服务器可能临时限制）")
            elif "Unsupported URL" in error_msg:
                self.log_text.append("💡 建议:")
                self.log_text.append("  • 确认链接是具体的视频页面，而不是首页")
                self.log_text.append("  • 抖音请使用具体视频链接 (如: douyin.com/video/xxxxx)")
                self.log_text.append("  • B站请使用BV号链接 (如: bilibili.com/video/BVxxxxx)")
            elif "network" in error_msg.lower() or "timeout" in error_msg.lower():
                self.log_text.append("💡 网络连接问题，请检查网络后重试")
            elif "blocked" in error_msg.lower() or "403" in error_msg:
                self.log_text.append("💡 访问被阻止：")
                self.log_text.append("  • 服务器可能检测到自动化访问")
                self.log_text.append("  • 建议稍后重试或使用其他视频链接")
            else:
                self.log_text.append("💡 请检查链接格式是否正确，或尝试其他视频链接")
                
            self.quality_combo.clear()
            self.quality_combo.addItem("检查失败")
        finally:
            self.check_formats_button.setEnabled(True)
    
    def update_quality_combo(self, available_heights):
        """根据可用分辨率更新质量选择下拉框"""
        self.quality_combo.clear()
        
        # 定义标准质量级别
        standard_qualities = [
            (2160, "4K (2160p)"),
            (1440, "2K (1440p)"),
            (1080, "1080p (全高清)"),
            (720, "720p (高清)"),
            (480, "480p (标清)"),
            (360, "360p (流畅)"),
            (240, "240p (极速)"),
        ]
        
        # 添加最佳质量选项
        self.quality_combo.addItem("最佳质量")
        
        # 添加可用的质量选项
        for height, label in standard_qualities:
            if height in available_heights:
                self.quality_combo.addItem(label)
        
        # 如果有特殊分辨率，也添加进去
        for height in sorted(available_heights, reverse=True):
            standard_heights = [q[0] for q in standard_qualities]
            if height not in standard_heights and height > 240:
                self.quality_combo.addItem(f"{height}p")
        
        # 添加仅音频选项
        self.quality_combo.addItem("仅音频")
        
        # 默认选择最佳质量
        self.quality_combo.setCurrentIndex(0)
    
    def start_download(self):
        """开始下载"""
        url = self.url_input.text().strip()
        if not url:
            QMessageBox.warning(self, "警告", "请输入视频链接！")
            return
        
        if not YT_DLP_AVAILABLE:
            QMessageBox.critical(self, "错误", "缺少yt-dlp库，无法下载！")
            return
        
        # 检查是否已完成格式检查
        current_quality = self.quality_combo.currentText()
        if current_quality in ["请先检查格式", "检查中...", "检查失败"]:
            QMessageBox.warning(self, "警告", "请先点击'检查格式'按钮获取可用的视频质量选项！")
            return
        
        output_path = self.path_input.text().strip()
        if not output_path or not os.path.exists(output_path):
            QMessageBox.warning(self, "警告", "请选择有效的保存路径！")
            return
        
        # 重置进度
        self.progress_bar.setValue(0)
        self.log_text.clear()
        
        # 获取下载参数
        quality = self.quality_combo.currentText()
        format_type = self.format_combo.currentText()
        
        # 预处理URL
        try:
            processed_url = self.preprocess_url(url)
        except ValueError as e:
            QMessageBox.warning(self, "URL错误", str(e))
            return
        
        # 创建并启动下载线程
        self.download_worker = VideoDownloadWorker(processed_url, output_path, quality, format_type)
        self.download_worker.progress_updated.connect(self.update_progress)
        self.download_worker.status_updated.connect(self.update_status)
        self.download_worker.download_completed.connect(self.download_finished)
        self.download_worker.download_failed.connect(self.download_error)
        
        self.download_worker.start()
        
        # 更新按钮状态
        self.download_button.setEnabled(False)
        self.cancel_button.setEnabled(True)
        
        self.log_text.append(f"🚀 开始下载: {url}")
    
    def cancel_download(self):
        """取消下载"""
        if self.download_worker and self.download_worker.isRunning():
            self.download_worker.cancel_download()
            self.download_worker.wait()
            
        self.status_label.setText("下载已取消")
        self.log_text.append("❌ 下载已被用户取消")
        
        # 重置按钮状态
        self.download_button.setEnabled(True)
        self.cancel_button.setEnabled(False)
    
    def update_progress(self, value):
        """更新进度条"""
        self.progress_bar.setValue(value)
    
    def update_status(self, status):
        """更新状态"""
        self.status_label.setText(status)
        self.log_text.append(f"📝 {status}")
    
    def download_finished(self, message):
        """下载完成"""
        self.status_label.setText(message)
        self.log_text.append(f"✅ {message}")
        self.progress_bar.setValue(100)
        
        # 重置按钮状态
        self.download_button.setEnabled(True)
        self.cancel_button.setEnabled(False)
        
        QMessageBox.information(self, "下载完成", message)
    
    def download_error(self, error):
        """下载错误"""
        self.status_label.setText(f"下载失败: {error}")
        self.log_text.append(f"❌ {error}")
        
        # 重置按钮状态
        self.download_button.setEnabled(True)
        self.cancel_button.setEnabled(False)
        
        QMessageBox.critical(self, "下载失败", error)
    
    def closeEvent(self, event):
        """关闭事件"""
        if self.download_worker and self.download_worker.isRunning():
            reply = QMessageBox.question(self, "确认关闭", 
                                       "下载正在进行中，确定要关闭吗？",
                                       QMessageBox.Yes | QMessageBox.No)
            if reply == QMessageBox.Yes:
                self.download_worker.cancel_download()
                self.download_worker.wait()
                event.accept()
            else:
                event.ignore()
        else:
            event.accept()

def main():
    """测试函数"""
    from PySide6.QtWidgets import QApplication
    
    app = QApplication(sys.argv)
    dialog = VideoDownloadDialog()
    dialog.show()
    sys.exit(app.exec())

if __name__ == "__main__":
    main() 