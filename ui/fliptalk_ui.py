#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FlipTalk AI - 视频翻译软件主界面
功能：提供视频上传、处理监控、字幕编辑等完整UI界面
"""

import sys
# 添加动画相关导入
import random
from PySide6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QPushButton, QScrollArea, QProgressBar, QLineEdit,
    QCheckBox, QFrame, QFileDialog, QGridLayout, QTextEdit, QComboBox,
    QDialog, QMessageBox, QGroupBox, QFormLayout, QDoubleSpinBox, QSpinBox,
    QTableWidget, QTableWidgetItem, QHeaderView, QListWidget, QSlider,
    QStackedWidget, QPlainTextEdit, QStyledItemDelegate
)
from PySide6.QtCore import Qt, QMimeData, QThread, Signal, QPropertyAnimation, QEasingCurve, QRect, QTimer, QUrl, \
    QPoint, Slot
from PySide6.QtGui import QFont, QDragEnterEvent, QDropEvent, QPainter, QBrush, QPen, QPixmap, QColor, QImage, QIcon
from PySide6.QtMultimedia import QMediaPlayer, QAudioOutput
from PySide6.QtMultimediaWidgets import QVideoWidget
import time
import os

from pydub import AudioSegment

# 导入WhisperX字幕提取插件（用于模型检查）
try:
    from plugins.subtitle_extractor.plugin import WhisperXSubtitleExtractor

    WHISPERX_AVAILABLE = True
except ImportError as e:
    print(f"警告：无法导入WhisperX插件 - {e}")
    WHISPERX_AVAILABLE = False


    # 创建占位符类
    class WhisperXSubtitleExtractor:
        def __init__(self): pass

        def initialize(self, config): return False

# 导入OpenCV
try:
    import cv2
    import numpy as np

    OPENCV_AVAILABLE = True
    print("OpenCV已加载，版本:", cv2.__version__)
except ImportError:
    print("警告: OpenCV 模块不可用，将使用默认视频预览")
    OPENCV_AVAILABLE = False

# 导入多媒体模块
try:
    from PySide6.QtMultimedia import QMediaPlayer, QAudioOutput
    from PySide6.QtMultimediaWidgets import QVideoWidget

    MULTIMEDIA_AVAILABLE = True
except ImportError:
    print("警告: QtMultimedia 模块不可用，视频预览功能将被禁用")
    MULTIMEDIA_AVAILABLE = False

# 导入参数设置窗口
try:
    from .config_window import ConfigWindow
except ImportError:
    try:
        from config_window import ConfigWindow
    except ImportError:
        print("警告：无法导入ConfigWindow，某些功能可能不可用")


        class ConfigWindow:
            def __init__(self, *args, **kwargs): pass

# 导入LogArea组件
try:
    from .widgets.log_area import LogArea
except ImportError:
    try:
        from widgets.log_area import LogArea
    except ImportError:
        print("警告：无法导入LogArea，日志功能可能不可用")


        # 创建一个简单的占位符类
        class LogArea(QWidget):
            def __init__(self):
                super().__init__()
                self.setFixedSize(1359, 776)
                layout = QVBoxLayout(self)
                layout.addWidget(QLabel("日志区域加载失败"))

# 导入核心服务
try:
    from core.services import core_service

    CORE_SERVICE_AVAILABLE = True
except ImportError as e:
    print(f"警告：无法导入核心服务 - {e}")
    CORE_SERVICE_AVAILABLE = False


class StyleManager:
    """
    统一的样式管理类
    避免重复定义相同的样式
    """
    # 颜色常量 - 苹果风格设计
    COLORS = {
        'primary': '#0A84FF',  # 苹果蓝（优化对比度）
        'primary_hover': '#0056CC',
        'secondary': '#34C759',  # 苹果绿
        'background_dark': '#1C1C1E',  # 苹果深色背景
        'background_light': '#2C2C2E',  # 苹果浅深色背景
        'background_modal': '#1C1C1E',  # 弹窗背景
        'text_primary': '#FFFFFF',  # 主要文字
        'text_secondary': '#EBEBF5',  # 次要文字
        'text_tertiary': '#EBEBF599',  # 三级文字（60%透明度）
        'text_quaternary': '#EBEBF54D',  # 四级文字（30%透明度）
        'border': '#38383A',  # 边框颜色
        'separator': '#38383A',  # 分隔线
        'transparent': 'transparent',
        'error': '#FF3B30',  # 苹果红
        'warning': '#FF9500',  # 苹果橙
        'success': '#34C759',  # 苹果绿
        'info': '#007AFF',  # 苹果蓝
        'marked': '#FF0000',
        # 弹窗专用颜色
        'modal_overlay': 'rgba(0, 0, 0, 0.4)',
        'modal_background': '#1C1C1E',
        'modal_border': '#38383A',
        'button_destructive': '#FF3B30',
        'button_cancel': '#8E8E93',
        # 兼容性映射（保持向后兼容）
        'text_white': '#FFFFFF',
        'text_gray': '#EBEBF599',  # 映射到 text_tertiary
        'text_light_gray': '#EBEBF54D'  # 映射到 text_quaternary
    }

    @staticmethod
    def get_apple_message_box_style():
        """获取苹果风格消息框样式"""
        return f"""
            QMessageBox {{
                background-color: {StyleManager.COLORS['modal_background']};
                border: 1px solid {StyleManager.COLORS['modal_border']};
                border-radius: 14px;
                color: {StyleManager.COLORS['text_primary']};
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                font-size: 14px;
                min-width: 320px;
                max-width: 480px;
                padding: 0px;
            }}
            QMessageBox QLabel {{
                color: {StyleManager.COLORS['text_primary']};
                font-size: 16px;
                font-weight: 600;
                padding: 20px 24px 16px 24px;
                background: transparent;
                border: none;
                line-height: 1.4;
            }}
            QMessageBox QPushButton {{
                background-color: {StyleManager.COLORS['primary']};
                color: {StyleManager.COLORS['text_primary']};
                border: none;
                border-radius: 8px;
                font-size: 16px;
                font-weight: 600;
                padding: 12px 24px;
                margin: 8px 12px 16px 12px;
                min-width: 80px;
                min-height: 44px;
            }}
            QMessageBox QPushButton:hover {{
                background-color: {StyleManager.COLORS['primary_hover']};
            }}
            QMessageBox QPushButton:pressed {{
                background-color: {StyleManager.COLORS['primary_hover']};
                border: 2px solid {StyleManager.COLORS['primary']};
            }}
            QMessageBox QPushButton[text="取消"],
            QMessageBox QPushButton[text="否"],
            QMessageBox QPushButton[text="No"] {{
                background-color: {StyleManager.COLORS['button_cancel']};
                color: {StyleManager.COLORS['text_primary']};
            }}
            QMessageBox QPushButton[text="取消"]:hover,
            QMessageBox QPushButton[text="否"]:hover,
            QMessageBox QPushButton[text="No"]:hover {{
                background-color: #A0A0A8;
            }}
            QMessageBox QLabel#qt_msgbox_label {{
                color: {StyleManager.COLORS['text_secondary']};
                font-size: 14px;
                font-weight: 400;
                padding: 0px 24px 20px 24px;
                line-height: 1.5;
            }}
        """

    @staticmethod
    def get_apple_warning_style():
        """获取苹果风格警告框样式"""
        return f"""
            QMessageBox {{
                background-color: {StyleManager.COLORS['modal_background']};
                border: 1px solid {StyleManager.COLORS['warning']};
                border-radius: 14px;
                color: {StyleManager.COLORS['text_primary']};
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                min-width: 320px;
                max-width: 480px;
            }}
            QMessageBox QLabel {{
                color: {StyleManager.COLORS['warning']};
                font-size: 16px;
                font-weight: 600;
                padding: 20px 24px 16px 24px;
                background: transparent;
                border: none;
            }}
            QMessageBox QPushButton {{
                background-color: {StyleManager.COLORS['warning']};
                color: {StyleManager.COLORS['text_primary']};
                border: none;
                border-radius: 8px;
                font-size: 16px;
                font-weight: 600;
                padding: 12px 24px;
                margin: 8px 12px 16px 12px;
                min-width: 80px;
                min-height: 44px;
            }}
            QMessageBox QPushButton:hover {{
                background-color: #FFB340;
            }}
        """

    @staticmethod
    def get_apple_error_style():
        """获取苹果风格错误框样式"""
        return f"""
            QMessageBox {{
                background-color: {StyleManager.COLORS['modal_background']};
                border: 1px solid {StyleManager.COLORS['error']};
                border-radius: 14px;
                color: {StyleManager.COLORS['text_primary']};
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                min-width: 320px;
                max-width: 480px;
            }}
            QMessageBox QLabel {{
                color: {StyleManager.COLORS['error']};
                font-size: 16px;
                font-weight: 600;
                padding: 20px 24px 16px 24px;
                background: transparent;
                border: none;
            }}
            QMessageBox QPushButton {{
                background-color: {StyleManager.COLORS['error']};
                color: {StyleManager.COLORS['text_primary']};
                border: none;
                border-radius: 8px;
                font-size: 16px;
                font-weight: 600;
                padding: 12px 24px;
                margin: 8px 12px 16px 12px;
                min-width: 80px;
                min-height: 44px;
            }}
            QMessageBox QPushButton:hover {{
                background-color: #FF6B5A;
            }}
        """

    @staticmethod
    def get_apple_success_style():
        """获取苹果风格成功框样式"""
        return f"""
            QMessageBox {{
                background-color: {StyleManager.COLORS['modal_background']};
                border: 1px solid {StyleManager.COLORS['success']};
                border-radius: 14px;
                color: {StyleManager.COLORS['text_primary']};
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                min-width: 320px;
                max-width: 480px;
            }}
            QMessageBox QLabel {{
                color: {StyleManager.COLORS['success']};
                font-size: 16px;
                font-weight: 600;
                padding: 20px 24px 16px 24px;
                background: transparent;
                border: none;
            }}
            QMessageBox QPushButton {{
                background-color: {StyleManager.COLORS['success']};
                color: {StyleManager.COLORS['text_primary']};
                border: none;
                border-radius: 8px;
                font-size: 16px;
                font-weight: 600;
                padding: 12px 24px;
                margin: 8px 12px 16px 12px;
                min-width: 80px;
                min-height: 44px;
            }}
            QMessageBox QPushButton:hover {{
                background-color: #5DD579;
            }}
        """

    @staticmethod
    def get_label_style(color='#FFFFFF', font_size=14, bg_color='transparent'):
        """获取标签通用样式"""
        return f"""
            QLabel {{
                color: {color};
                font-size: {font_size}px;
                background-color: {bg_color};
                border: none;
            }}
        """

    @staticmethod
    def get_button_style(bg_color='#2B9D7C', text_color='#1B1E24', font_size=20, border_radius=19):
        """获取按钮通用样式"""
        return f"""
            QPushButton {{
                background-color: {bg_color};
                color: {text_color};
                font-size: {font_size}px;
                border: none;
                border-radius: {border_radius}px;
                outline: none;
            }}
            QPushButton:hover {{
                background-color: #00CC55;
                outline: none;
            }}
            QPushButton:focus {{
                outline: none;
                border: 1px solid rgba(43, 157, 124, 0.5);
            }}
        """

    @staticmethod
    def get_input_style(bg_color='#14161A', border_radius=17):
        """获取输入框通用样式"""
        return f"""
            QLineEdit {{
                background-color: {bg_color};
                color: #FFFFFF;
                font-size: 14px;
                border: 1px solid #444444;
                border-radius: {border_radius}px;
                padding: 5px;
                text-align: center;
            }}
        """

    @staticmethod
    def get_frame_style(bg_color='#1B1E24', border_radius=30):
        """获取Frame通用样式"""
        return f"""
            QFrame {{
                background-color: {bg_color};
                border-radius: {border_radius}px;
                border: none;
            }}
        """

    @staticmethod
    def hex_to_rgb(hex_color):
        """将十六进制颜色转换为RGB格式"""
        hex_color = hex_color.lstrip('#')
        return ', '.join(str(int(hex_color[i:i + 2], 16)) for i in (0, 2, 4))


class RoundedWidget(QWidget):
    """
    圆角矩形组件基类
    提供统一的圆角矩形样式
    """

    def __init__(self, bg_color="#1B1E24", border_radius=30):
        super().__init__()
        self.bg_color = bg_color  # 背景颜色
        self.border_radius = border_radius  # 圆角半径
        self.setup_style()

    def setup_style(self):
        """设置组件样式"""
        self.setStyleSheet(f"""
            QWidget {{
                background-color: {self.bg_color};
                border-radius: {self.border_radius}px;
            }}
        """)


class AppleMessageBox:
    """苹果风格消息框管理器"""

    @staticmethod
    def show_info(parent, title, message):
        """显示信息提示框"""
        msg = QMessageBox(parent)
        msg.setWindowTitle(title)
        msg.setText(message)
        msg.setIcon(QMessageBox.Icon.Information)
        msg.setStandardButtons(QMessageBox.StandardButton.Ok)

        # 设置中文按钮文本
        msg.button(QMessageBox.StandardButton.Ok).setText("确定")

        # 应用苹果风格样式
        msg.setStyleSheet(StyleManager.get_apple_message_box_style())

        # 设置窗口属性
        msg.setWindowFlags(Qt.Dialog | Qt.CustomizeWindowHint | Qt.WindowTitleHint)

        return msg.exec()

    @staticmethod
    def show_warning(parent, title, message):
        """显示警告提示框"""
        msg = QMessageBox(parent)
        msg.setWindowTitle(title)
        msg.setText(message)
        msg.setIcon(QMessageBox.Icon.Warning)
        msg.setStandardButtons(QMessageBox.StandardButton.Ok)

        # 设置中文按钮文本
        msg.button(QMessageBox.StandardButton.Ok).setText("确定")

        # 应用苹果风格警告样式
        msg.setStyleSheet(StyleManager.get_apple_warning_style())

        # 设置窗口属性
        msg.setWindowFlags(Qt.Dialog | Qt.CustomizeWindowHint | Qt.WindowTitleHint)

        return msg.exec()

    @staticmethod
    def show_error(parent, title, message):
        """显示错误提示框"""
        msg = QMessageBox(parent)
        msg.setWindowTitle(title)
        msg.setText(message)
        msg.setIcon(QMessageBox.Icon.Critical)
        msg.setStandardButtons(QMessageBox.StandardButton.Ok)

        # 设置中文按钮文本
        msg.button(QMessageBox.StandardButton.Ok).setText("确定")

        # 应用苹果风格错误样式
        msg.setStyleSheet(StyleManager.get_apple_error_style())

        # 设置窗口属性
        msg.setWindowFlags(Qt.Dialog | Qt.CustomizeWindowHint | Qt.WindowTitleHint)

        return msg.exec()

    @staticmethod
    def show_success(parent, title, message):
        """显示成功提示框"""
        msg = QMessageBox(parent)
        msg.setWindowTitle(title)
        msg.setText(message)
        msg.setIcon(QMessageBox.Icon.Information)
        msg.setStandardButtons(QMessageBox.StandardButton.Ok)

        # 设置中文按钮文本
        msg.button(QMessageBox.StandardButton.Ok).setText("确定")

        # 应用苹果风格成功样式
        msg.setStyleSheet(StyleManager.get_apple_success_style())

        # 设置窗口属性
        msg.setWindowFlags(Qt.Dialog | Qt.CustomizeWindowHint | Qt.WindowTitleHint)

        return msg.exec()

    @staticmethod
    def show_question(parent, title, message, yes_text="确定", no_text="取消"):
        """显示确认对话框"""
        msg = QMessageBox(parent)
        msg.setWindowTitle(title)
        msg.setText(message)
        msg.setIcon(QMessageBox.Icon.Question)
        msg.setStandardButtons(QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No)

        # 设置中文按钮文本
        msg.button(QMessageBox.StandardButton.Yes).setText(yes_text)
        msg.button(QMessageBox.StandardButton.No).setText(no_text)

        # 应用苹果风格样式
        msg.setStyleSheet(StyleManager.get_apple_message_box_style())

        # 设置窗口属性
        msg.setWindowFlags(Qt.Dialog | Qt.CustomizeWindowHint | Qt.WindowTitleHint)

        result = msg.exec()
        return result == QMessageBox.StandardButton.Yes


class NavigationBar(QWidget):
    """
    左侧导航栏组件
    使用QFrame作为背景容器，包含logo和功能按钮
    """
    # 添加信号用于通知主窗口切换界面
    page_changed = Signal(str)

    def __init__(self):
        super().__init__()
        self.setFixedSize(50, 776)  # 设置固定尺寸：50*776
        self.current_page = "首页"  # 当前选中页面
        self.last_click_time = 0  # 上次点击时间，用于防抖
        self.setup_ui()

    def setup_ui(self):
        """初始化导航栏UI - 极简图标版"""
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)

        # 创建背景Frame - 极简设计
        background_frame = QFrame()
        background_frame.setFixedSize(50, 776)
        background_frame.setStyleSheet("""
            QFrame {
                background: #14161A;
                border-radius: 15px;
                border: 1px solid rgba(43, 157, 124, 0.1);
            }
        """)

        # Frame内部布局
        frame_layout = QVBoxLayout(background_frame)
        frame_layout.setContentsMargins(5, 20, 5, 20)
        frame_layout.setSpacing(8)

        # 顶部简化Logo/图标
        logo_btn = self.create_icon_button("≡", "菜单", is_logo=True)
        frame_layout.addWidget(logo_btn)

        # 添加间距
        frame_layout.addSpacing(15)

        # 导航图标按钮列表
        nav_items = [
            ("🏠", "首页"),
            ("⚙️", "功能"),
            ("🔧", "API设置"),
            ("🔨", "系统设置"),
            ("📋", "运行日志")
        ]

        for icon, nav_text in nav_items:
            btn = self.create_icon_button(icon, nav_text)
            frame_layout.addWidget(btn)
            frame_layout.addSpacing(5)

        # 添加弹性空间
        frame_layout.addStretch()

        # 将背景Frame添加到主布局
        main_layout.addWidget(background_frame)

    def create_icon_button(self, icon, nav_text, is_logo=False):
        """创建图标按钮"""
        btn = QPushButton(icon)
        btn.setFixedSize(40, 40)

        # 保存导航文本用于事件处理
        btn.nav_text = nav_text

        # 添加tooltip
        btn.setToolTip(nav_text)

        if is_logo:
            # Logo样式
            btn.setStyleSheet("""
                QPushButton {
                    background: rgba(43, 157, 124, 0.15);
                color: #2B9D7C;
                font-size: 18px;
                font-weight: bold;
                    border: 1px solid rgba(43, 157, 124, 0.3);
                    border-radius: 20px;
                    outline: none;
                }
                QPushButton:hover {
                    background-color: #00CC55;
                }
                QToolTip {
                    background-color: #1B1E24;
                    color: white;
                    border: 1px solid #2B9D7C;
                    border-radius: 4px;
                    padding: 5px;
            }
        """)
        else:
            # 根据是否选中设置不同样式
            if nav_text == self.current_page:
                btn.setStyleSheet(self.get_icon_button_style(selected=True))
            else:
                btn.setStyleSheet(self.get_icon_button_style(selected=False))

        # 绑定点击事件
        btn.clicked.connect(lambda checked, t=nav_text: self.on_nav_clicked(t))

        return btn

    def get_icon_button_style(self, selected=False):
        """获取图标按钮样式"""
        if selected:
            return """
                QPushButton {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 #2B9D7C, stop:1 #25876A);
                    color: #FFFFFF;
                    font-size: 16px;
                    font-weight: bold;
                    border: none;
                    border-radius: 20px;
                    outline: none;
                }
                QPushButton:hover {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 #32B189, stop:1 #2B9D7C);
                    outline: none;
                }
                QToolTip {
                    background-color: #1B1E24;
                    color: white;
                    border: 1px solid #2B9D7C;
                    border-radius: 4px;
                    padding: 5px;
                }
            """
        else:
            return """
                QPushButton {
                    background: #1B1E24;
                    color: rgba(255, 255, 255, 0.7);
                    font-size: 16px;
                    font-weight: normal;
                    border: none;
                    border-radius: 20px;
                    outline: none;
                }
                QPushButton:hover {
                    background: rgba(43, 157, 124, 0.2);
                    color: rgba(255, 255, 255, 0.9);
                    border: none;
                    outline: none;
                }
                QToolTip {
                    background-color: #1B1E24;
                    color: white;
                    border: 1px solid #2B9D7C;
                    border-radius: 4px;
                    padding: 5px;
            }
            """

    def on_nav_clicked(self, text):
        """导航按钮点击事件处理"""
        # 防抖：如果距离上次点击时间小于300毫秒，则忽略
        current_time = time.time() * 1000  # 转换为毫秒
        if current_time - self.last_click_time < 300:
            return

        self.last_click_time = current_time

        # 如果点击的是当前页面，则不处理
        if self.current_page == text:
            return

        self.current_page = text
        # 重新设置所有按钮样式
        for i in range(self.layout().itemAt(0).widget().layout().count()):
            item = self.layout().itemAt(0).widget().layout().itemAt(i)
            if item and item.widget() and isinstance(item.widget(), QPushButton):
                button = item.widget()
                # 检查按钮是否有nav_text属性
                if hasattr(button, 'nav_text'):
                    button_text = button.nav_text
                    if button_text == text:
                        button.setStyleSheet(self.get_icon_button_style(selected=True))
                    else:
                        button.setStyleSheet(self.get_icon_button_style(selected=False))

        # 发射信号通知主窗口切换界面
        self.page_changed.emit(text)


class ProcessListItem(RoundedWidget):
    """
    处理列表中的单个视频项 - 简洁设计
    显示文件名、处理状态和进度
    """

    def __init__(self, filename, status, progress):
        super().__init__(bg_color="transparent", border_radius=0)
        self.setFixedSize(470, 50)  # 减小尺寸
        self.filename = filename
        self.status = status
        self.progress = progress
        self.setup_ui()

    def setup_ui(self):
        """初始化处理项UI - 简洁设计"""
        # 主布局
        main_layout = QHBoxLayout(self)
        main_layout.setContentsMargins(8, 8, 8, 8)
        main_layout.setSpacing(10)

        # 简洁的状态指示器
        status_dot = QLabel("●")
        if self.progress == 100:
            status_color = "#2B9D7C"  # 绿色 - 完成
        elif self.progress == 0:
            status_color = "#888888"  # 灰色 - 等待
        else:
            status_color = "#FFA726"  # 橙色 - 处理中

        status_dot.setStyleSheet(f"""
            QLabel {{
                color: {status_color};
                font-size: 12px;
                background-color: transparent;
                border: none;
            }}
        """)
        status_dot.setFixedWidth(12)
        main_layout.addWidget(status_dot)

        # 文件名
        filename_label = QLabel(self.filename)
        filename_label.setStyleSheet("""
            QLabel {
                color: #FFFFFF;
                font-size: 13px;
                font-weight: normal;
                background-color: transparent;
                border: none;
            }
        """)
        main_layout.addWidget(filename_label)

        # 弹性空间
        main_layout.addStretch()

        # 状态文字
        status_label = QLabel(self.status)
        status_label.setStyleSheet(f"""
            QLabel {{
                color: {status_color};
                font-size: 11px;
                background-color: transparent;
                border: none;
            }}
        """)
        status_label.setFixedWidth(60)
        main_layout.addWidget(status_label)

        # 简洁的进度条
        progress_container = QFrame()
        progress_container.setFixedSize(80, 4)
        progress_container.setStyleSheet("""
            QFrame {
                background-color: rgba(255, 255, 255, 0.2);
                border-radius: 2px;
            }
        """)

        # 进度填充
        progress_fill = QFrame(progress_container)
        fill_width = int((self.progress / 100) * 80)
        progress_fill.setFixedSize(fill_width, 4)
        progress_fill.move(0, 0)
        progress_fill.setStyleSheet(f"""
            QFrame {{
                background-color: {status_color};
                border-radius: 2px;
            }}
        """)

        progress_widget = QWidget()
        progress_layout = QVBoxLayout(progress_widget)
        progress_layout.setContentsMargins(0, 0, 0, 0)
        progress_layout.setAlignment(Qt.AlignCenter)
        progress_layout.addWidget(progress_container)
        progress_widget.setFixedWidth(80)
        main_layout.addWidget(progress_widget)

        # 简洁的进度百分比
        progress_text = QLabel(f"{self.progress}%")
        progress_text.setStyleSheet("""
            QLabel {
                color: rgba(255, 255, 255, 0.7);
                font-size: 10px;
                background-color: transparent;
                border: none;
            }
        """)
        progress_text.setFixedWidth(30)
        progress_text.setAlignment(Qt.AlignRight)
        main_layout.addWidget(progress_text)


class VideoUploadArea(RoundedWidget):
    """
    视频文件上传区域
    支持拖拽和点击上传视频文件，上传成功后显示视频预览
    """

    # 定义信号
    video_uploaded = Signal(str)  # 视频上传成功信号，传递文件路径
    video_cleared = Signal()  # 视频清除信号

    def __init__(self):
        super().__init__(bg_color="#1B1E24", border_radius=30)
        self.uploaded_video_path = None
        self.is_preview_mode = False
        self.setAcceptDrops(True)
        self.setup_ui()

    def setup_ui(self):
        """初始化视频上传区域UI"""
        # 主布局
        self.main_layout = QVBoxLayout(self)
        self.main_layout.setContentsMargins(5, 5, 5, 5)  # 减少边距
        self.main_layout.setSpacing(8)  # 减少间距

        # 创建上传提示区域（初始状态）
        self.upload_prompt_area = self.create_upload_prompt()
        self.main_layout.addWidget(self.upload_prompt_area)

        # 创建视频预览区域（上传后显示）
        self.video_preview_area = self.create_video_preview()
        self.video_preview_area.hide()  # 初始隐藏
        self.main_layout.addWidget(self.video_preview_area)

    def create_upload_prompt(self):
        """创建上传提示区域"""
        prompt_widget = QWidget()
        prompt_widget.setStyleSheet("""
            QWidget {
                background-color:#1B1E24;
                border: 2px dashed #2B9D7C;
                border-radius: 20px;
            }
            QWidget:hover {
                border-color: #00CC55;
                background-color: rgba(43, 157, 124, 0.05);
            }
        """)

        layout = QVBoxLayout(prompt_widget)
        layout.setContentsMargins(20, 25, 20, 25)
        layout.setSpacing(12)
        layout.setAlignment(Qt.AlignCenter)

        # 视频图标
        icon_label = QLabel("🎬")
        icon_label.setAlignment(Qt.AlignCenter)
        icon_label.setStyleSheet("""
            QLabel {
                font-size: 32px;
                color: #2B9D7C;
                background: transparent;
                border: none;
            }
        """)
        layout.addWidget(icon_label)

        # 主要提示文字
        main_text = QLabel("拖拽视频文件到此处")
        main_text.setAlignment(Qt.AlignCenter)
        main_text.setStyleSheet("""
            QLabel {
                color: #FFFFFF;
                font-size: 16px;
                font-weight: bold;
                background: transparent;
                border: none;
            }
        """)
        layout.addWidget(main_text)

        # 副提示文字
        sub_text = QLabel("或点击选择文件")
        sub_text.setAlignment(Qt.AlignCenter)
        sub_text.setStyleSheet("""
            QLabel {
                color: #AAAAAA;
                font-size: 12px;
                background: transparent;
                border: none;
            }
        """)
        layout.addWidget(sub_text)

        # 支持格式提示
        format_text = QLabel("支持 MP4, AVI, MOV, MKV 格式")
        format_text.setAlignment(Qt.AlignCenter)
        format_text.setStyleSheet("""
            QLabel {
                color: #666666;
                font-size: 10px;
                background: transparent;
                border: none;
            }
        """)
        layout.addWidget(format_text)

        # 点击事件
        prompt_widget.mousePressEvent = self.on_click_upload

        return prompt_widget

    def create_video_preview(self):
        """创建视频预览区域"""
        preview_widget = QWidget()
        preview_widget.setStyleSheet("""
            QWidget {
                background-color: #14161A;
                border: 2px solid #2B9D7C;
                border-radius: 20px;
            }
        """)

        layout = QVBoxLayout(preview_widget)
        layout.setContentsMargins(5, 5, 5, 5)  # 适当边距，为居中留出空间
        layout.setSpacing(0)  # 无间距

        # 创建视频预览容器，支持叠加播放按钮
        self.video_container = QWidget()

        container_width = 410
        container_height = container_width * 9 / 16
        margin = 5

        # 设置容器尺寸
        self.video_container.setFixedSize(container_width, container_height)

        # 创建视频预览标签
        self.thumbnail_label = QLabel(self.video_container)
        self.thumbnail_label.setFixedSize(container_width, container_height)
        self.thumbnail_label.setAlignment(Qt.AlignCenter)
        self.thumbnail_label.setStyleSheet("""
            QLabel {
                background-color: #1B1E24;
                border: 1px solid #333333;
                border-radius: 15px;
                color: #AAAAAA;
                font-size: 12px;
            }
        """)
        self.thumbnail_label.setText("视频预览加载中...")

        # 创建视频播放组件（初始隐藏）
        self.video_widget = QVideoWidget(self.video_container)
        self.video_widget.setFixedSize(container_width, container_height)
        self.video_widget.setStyleSheet("""
            QVideoWidget {
                background-color: #000000;
                border: 1px solid #333333;
                border-radius: 15px;
            }
        """)
        self.video_widget.hide()  # 初始隐藏

        # 创建媒体播放器
        self.media_player = QMediaPlayer()
        self.audio_output = QAudioOutput()
        self.audio_output.setVolume(1.0)  # 设置音量为最大
        self.media_player.setAudioOutput(self.audio_output)
        self.media_player.setVideoOutput(self.video_widget)

        # 创建字幕音频播放器
        self.subtitle_audio_player = QMediaPlayer()
        self.subtitle_audio_output = QAudioOutput()
        self.subtitle_audio_output.setVolume(0.8)  # 设置音量为80%
        self.subtitle_audio_player.setAudioOutput(self.subtitle_audio_output)

        # 设置默认音频设备
        try:
            from PySide6.QtMultimedia import QMediaDevices
            default_device = QMediaDevices.defaultAudioOutput()
            if not default_device.isNull():
                self.subtitle_audio_output.setDevice(default_device)
                print(f"🔧 字幕音频播放器已设置默认设备: {default_device.description()}")
        except Exception as e:
            print(f"⚠️ 设置字幕音频播放器默认设备失败: {e}")

        # 连接音频播放器信号（在初始化时就连接）
        self.subtitle_audio_player.mediaStatusChanged.connect(self._on_subtitle_audio_status_changed)
        self.subtitle_audio_player.playbackStateChanged.connect(self._on_subtitle_audio_playback_state_changed)
        self.subtitle_audio_player.errorOccurred.connect(self._on_subtitle_audio_error)

        # 同时连接主窗口的信号处理（用于UI更新）
        self.subtitle_audio_player.mediaStatusChanged.connect(self._forward_to_main_window_status)
        self.subtitle_audio_player.playbackStateChanged.connect(self._forward_to_main_window_playback)
        self.subtitle_audio_player.errorOccurred.connect(self._forward_to_main_window_error)

        # 当前播放的字幕行号
        self.current_playing_row = -1

        # 创建叠加的播放按钮
        self.overlay_play_button = QPushButton("▶", self.video_container)
        self.overlay_play_button.setFixedSize(60, 60)

        # 计算播放按钮的居中位置
        button_x = (container_width - 60) // 2
        button_y = (container_height - 60) // 2
        self.overlay_play_button.move(button_x, button_y)

        self.overlay_play_button.setStyleSheet("""
            QPushButton {
                background: rgba(0, 0, 0, 120);
                color: #FFFFFF;
                font-size: 24px;
                font-weight: bold;
                border: 2px solid rgba(255, 255, 255, 150);
                border-radius: 30px;
            }
            QPushButton:hover {
                background: rgba(0, 0, 0, 160);
                border: 2px solid rgba(255, 255, 255, 200);
                transform: scale(1.1);
            }
            QPushButton:pressed {
                background: rgba(0, 0, 0, 200);
                transform: scale(0.95);
            }
        """)

        # 连接播放按钮点击事件
        self.overlay_play_button.clicked.connect(self.play_preview_video)

        # 连接播放状态变化信号
        self.media_player.playbackStateChanged.connect(self.on_playback_state_changed)

        # 添加双击停止功能
        self.video_widget.mouseDoubleClickEvent = self.stop_video_playback

        # 初始隐藏播放按钮（只在有视频时显示）
        self.overlay_play_button.hide()

        layout.addWidget(self.video_container, 0, Qt.AlignCenter)  # 居中对齐

        return preview_widget

    def play_preview_video(self):
        """在预览区域直接播放视频"""
        if self.uploaded_video_path:
            try:
                import os
                # 停止当前播放并完全清除媒体播放器状态
                self.media_player.stop()
                self.media_player.setSource(QUrl())  # 清除媒体源

                # 立即隐藏缩略图，防止显示旧内容
                if hasattr(self, 'thumbnail_label'):
                    self.thumbnail_label.hide()

                # 强制刷新视频输出，清除之前的帧
                if hasattr(self, 'video_widget'):
                    self.video_widget.update()
                    self.video_widget.repaint()
                    # 显示视频播放器
                    self.video_widget.show()

                # 短暂延迟确保清除完成
                from PySide6.QtCore import QTimer
                QTimer.singleShot(100, lambda: self._load_and_play_video(self.uploaded_video_path))

                print(f"准备播放视频: {os.path.basename(self.uploaded_video_path)}")
            except Exception as e:
                print(f"播放视频失败: {e}")
        else:
            print("没有可播放的视频文件")

    def _load_and_play_video(self, video_path):
        """延迟加载并播放视频"""
        try:
            import os

            # 再次确保视频输出清除
            if hasattr(self, 'video_widget'):
                self.video_widget.update()
                self.video_widget.repaint()

            # 切换到视频播放模式
            self.switch_to_video_mode()

            # 设置新的视频文件
            video_url = QUrl.fromLocalFile(os.path.abspath(video_path))
            self.media_player.setSource(video_url)

            # 开始播放
            self.media_player.play()

            print(f"正在播放视频: {os.path.basename(video_path)}")
        except Exception as e:
            print(f"延迟播放视频失败: {e}")

    def switch_to_video_mode(self):
        """切换到视频播放模式"""
        # 强制刷新视频输出，清除之前的帧
        if hasattr(self, 'video_widget'):
            self.video_widget.update()
            self.video_widget.repaint()

        # 隐藏缩略图，显示视频播放器
        self.thumbnail_label.hide()
        self.video_widget.show()

        # 更新播放按钮样式和功能
        self.overlay_play_button.setText("⏸")
        self.overlay_play_button.clicked.disconnect()
        self.overlay_play_button.clicked.connect(self.toggle_play_pause)

    def switch_to_thumbnail_mode(self):
        """切换回缩略图预览模式"""
        # 停止播放并清除媒体源
        if hasattr(self, 'media_player'):
            self.media_player.stop()
            # 不在这里清除媒体源，因为可能还需要播放同一个视频

        # 强制刷新视频输出，清除之前的帧
        if hasattr(self, 'video_widget'):
            self.video_widget.update()
            self.video_widget.repaint()

        # 显示缩略图，隐藏视频播放器
        self.video_widget.hide()
        self.thumbnail_label.show()

        # 恢复播放按钮
        self.overlay_play_button.setText("▶")
        self.overlay_play_button.clicked.disconnect()
        self.overlay_play_button.clicked.connect(self.play_preview_video)

    def stop_video_playback(self, event):
        """停止视频播放（双击触发）"""
        if hasattr(self, 'media_player'):
            self.media_player.stop()

    def toggle_play_pause(self):
        """切换播放/暂停状态"""
        if hasattr(self, 'media_player'):
            if self.media_player.playbackState() == QMediaPlayer.PlaybackState.PlayingState:
                self.media_player.pause()
            else:
                self.media_player.play()

    def on_playback_state_changed(self, state):
        """播放状态变化时更新按钮文本"""
        if hasattr(self, 'overlay_play_button'):
            if state == QMediaPlayer.PlaybackState.PlayingState:
                self.overlay_play_button.setText("⏸")
            elif state == QMediaPlayer.PlaybackState.PausedState:
                self.overlay_play_button.setText("▶")
            elif state == QMediaPlayer.PlaybackState.StoppedState:
                # 视频停止时切换回缩略图模式
                self.switch_to_thumbnail_mode()

    def on_click_upload(self, event):
        """点击上传事件"""
        self.select_video_file()

    def select_video_file(self):
        """选择视频文件"""
        file_dialog = QFileDialog()
        file_path, _ = file_dialog.getOpenFileName(
            self,
            "选择视频文件",
            "",
            "视频文件 (*.mp4 *.avi *.mov *.mkv *.flv *.wmv *.webm);;所有文件 (*)"
        )

        if file_path:
            self.load_video(file_path)

    def load_video(self, file_path):
        """加载视频文件"""
        try:
            import os
            if not os.path.exists(file_path):
                print(f"视频文件不存在: {file_path}")
                return

            self.uploaded_video_path = file_path
            file_name = os.path.basename(file_path)
            file_size = os.path.getsize(file_path)

            # 更新视频信息
            self.update_video_info(file_path, file_size)

            # 切换到预览模式
            self.switch_to_preview_mode()

            # 发送信号
            self.video_uploaded.emit(file_path)

            print(f"视频文件已加载: {file_name}")

        except Exception as e:
            print(f"加载视频文件失败: {e}")

    def update_video_info(self, file_path, file_size):
        """更新视频信息显示"""
        try:
            # 格式化文件大小
            if file_size < 1024 * 1024:
                size_str = f"{file_size / 1024:.1f} KB"
            elif file_size < 1024 * 1024 * 1024:
                size_str = f"{file_size / (1024 * 1024):.1f} MB"
            else:
                size_str = f"{file_size / (1024 * 1024 * 1024):.1f} GB"

            # 尝试获取视频信息（需要OpenCV或其他库）
            try:
                import cv2
                cap = cv2.VideoCapture(file_path)
                if cap.isOpened():
                    fps = cap.get(cv2.CAP_PROP_FPS)
                    frame_count = cap.get(cv2.CAP_PROP_FRAME_COUNT)
                    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
                    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))

                    if fps > 0:
                        duration = frame_count / fps
                        duration_str = f"{int(duration // 60):02d}:{int(duration % 60):02d}"
                    else:
                        duration_str = "未知"

                    resolution_str = f"{width}x{height}"
                    cap.release()
                else:
                    duration_str = "未知"
                    resolution_str = "未知"
            except ImportError:
                duration_str = "未知"
                resolution_str = "未知"
            except Exception:
                duration_str = "未知"
                resolution_str = "未知"

            # 更新缩略图
            self.update_thumbnail(file_path)

        except Exception as e:
            print(f"更新视频信息失败: {e}")

    def update_thumbnail(self, file_path):
        """更新视频缩略图"""
        try:
            import cv2

            # 立即清除之前的缩略图，防止显示旧内容
            if hasattr(self, 'thumbnail_label'):
                self.thumbnail_label.clear()
                self.thumbnail_label.setText("正在生成预览...")
                self.thumbnail_label.update()
                self.thumbnail_label.repaint()

            # 确保切换到缩略图模式
            self.switch_to_thumbnail_mode()

            cap = cv2.VideoCapture(file_path)
            if cap.isOpened():
                # 读取第一帧作为缩略图
                ret, frame = cap.read()
                if ret:
                    # 转换为Qt格式并显示
                    height, width, channel = frame.shape
                    bytes_per_line = 3 * width
                    q_image = QImage(frame.data, width, height, bytes_per_line, QImage.Format_RGB888).rgbSwapped()

                    # 获取实际容器大小
                    container_width = self.video_container.width()
                    container_height = self.video_container.height()

                    # 创建pixmap并进行精确缩放，确保完全填充容器
                    pixmap = QPixmap.fromImage(q_image)
                    scaled_pixmap = pixmap.scaled(
                        container_width,  # 使用与视频播放区域完全相同的宽度
                        container_height,  # 使用与视频播放区域完全相同的高度
                        Qt.KeepAspectRatio,  # 保持宽高比
                        Qt.SmoothTransformation  # 平滑变换
                    )

                    # 创建背景pixmap用于填充整个区域
                    background = QPixmap(container_width, container_height)
                    background.fill(QColor('#1B1E24'))  # 使用与视频播放区域相同的背景色

                    # 创建画家绘制居中的缩略图
                    painter = QPainter(background)

                    # 计算居中位置
                    x = (container_width - scaled_pixmap.width()) // 2
                    y = (container_height - scaled_pixmap.height()) // 2

                    # 绘制缩略图
                    painter.drawPixmap(x, y, scaled_pixmap)
                    painter.end()

                    # 清除之前的内容并设置新的缩略图
                    self.thumbnail_label.clear()
                    self.thumbnail_label.setPixmap(background)

                    print(f"缩略图已更新: {file_path}")
                else:
                    self.thumbnail_label.setText("无法生成预览")
                cap.release()
            else:
                self.thumbnail_label.setText("无法打开视频")
        except ImportError:
            self.thumbnail_label.setText("视频预览\n(需要OpenCV)")
        except Exception as e:
            print(f"生成缩略图失败: {e}")
            self.thumbnail_label.setText("预览生成失败")

    def switch_to_preview_mode(self):
        """切换到预览模式"""
        self.upload_prompt_area.hide()
        self.video_preview_area.show()
        self.overlay_play_button.show()  # 显示叠加的播放按钮
        self.is_preview_mode = True

    def switch_to_upload_mode(self):
        """切换到上传模式"""
        self.video_preview_area.hide()
        self.upload_prompt_area.show()
        self.overlay_play_button.hide()  # 隐藏叠加的播放按钮
        self.is_preview_mode = False
        self.uploaded_video_path = None

        # 重置媒体播放器状态
        if hasattr(self, 'media_player'):
            self.media_player.stop()
            self.media_player.setSource(QUrl())  # 清除媒体源

            # 强制刷新视频输出，清除之前的帧
            if hasattr(self, 'video_widget'):
                self.video_widget.update()
                # 强制重绘，确保清除所有视频帧
                self.video_widget.repaint()

        # 立即清除缩略图显示，防止显示上一个视频的内容
        if hasattr(self, 'thumbnail_label'):
            self.thumbnail_label.clear()
            self.thumbnail_label.setText("视频预览")
            # 强制立即重绘缩略图标签
            self.thumbnail_label.update()
            self.thumbnail_label.repaint()

        # 确保切换回缩略图模式
        self.switch_to_thumbnail_mode()

        # 发送视频清除信号
        self.video_cleared.emit()

    def dragEnterEvent(self, event):
        """拖拽进入事件"""
        if event.mimeData().hasUrls():
            urls = event.mimeData().urls()
            if urls and urls[0].isLocalFile():
                file_path = urls[0].toLocalFile()
                # 检查是否为视频文件
                video_extensions = ['.mp4', '.avi', '.mov', '.mkv', '.flv', '.wmv', '.webm']
                if any(file_path.lower().endswith(ext) for ext in video_extensions):
                    event.acceptProposedAction()
                    # 添加视觉反馈
                    if not self.is_preview_mode:
                        self.upload_prompt_area.setStyleSheet("""
                            QWidget {
                                background-color: rgba(43, 157, 124, 0.1);
                                border: 2px dashed #00CC55;
                                border-radius: 20px;
                            }
                        """)
                else:
                    event.ignore()
            else:
                event.ignore()
        else:
            event.ignore()

    def dragLeaveEvent(self, event):
        """拖拽离开事件"""
        if not self.is_preview_mode:
            self.upload_prompt_area.setStyleSheet("""
                QWidget {
                    background-color: #14161A;
                    border: 2px dashed #2B9D7C;
                    border-radius: 20px;
                }
                QWidget:hover {
                    border-color: #00CC55;
                    background-color: rgba(43, 157, 124, 0.05);
                }
            """)

    def dropEvent(self, event):
        """拖拽放下事件"""
        if event.mimeData().hasUrls():
            urls = event.mimeData().urls()
            if urls and urls[0].isLocalFile():
                file_path = urls[0].toLocalFile()
                # 检查是否为视频文件
                video_extensions = ['.mp4', '.avi', '.mov', '.mkv', '.flv', '.wmv', '.webm']
                if any(file_path.lower().endswith(ext) for ext in video_extensions):
                    self.load_video(file_path)
                    event.acceptProposedAction()
                else:
                    print("不支持的文件格式")
                    event.ignore()
            else:
                event.ignore()
        else:
            event.ignore()

        # 恢复样式
        if not self.is_preview_mode:
            self.dragLeaveEvent(event)

    def get_uploaded_video_path(self):
        """获取已上传的视频文件路径"""
        return self.uploaded_video_path

    def _on_subtitle_audio_status_changed(self, status):
        """处理字幕音频播放状态变化"""
        try:
            print(f"🔄 字幕音频播放状态变化: {status}")

            # 检查状态类型，避免属性错误
            if hasattr(QMediaPlayer, 'MediaStatus'):
                if status == QMediaPlayer.MediaStatus.EndOfMedia:
                    print("✅ 字幕音频播放完成")
                elif status == QMediaPlayer.MediaStatus.InvalidMedia:
                    print("❌ 字幕音频媒体无效")
                elif status == QMediaPlayer.MediaStatus.LoadedMedia:
                    print("📂 字幕音频媒体已加载")
                elif status == QMediaPlayer.MediaStatus.LoadingMedia:
                    print("⏳ 字幕音频媒体加载中...")
                elif status == QMediaPlayer.MediaStatus.NoMedia:
                    print("📭 字幕音频无媒体")
                elif status == QMediaPlayer.MediaStatus.StalledMedia:
                    print("⏸ 字幕音频媒体暂停")

        except Exception as e:
            print(f"❌ 处理字幕音频状态变化失败: {e}")

    def _on_subtitle_audio_playback_state_changed(self, state):
        """处理字幕音频播放状态变化"""
        try:
            print(f"🎵 字幕音频播放状态: {state}")

            if hasattr(QMediaPlayer, 'PlaybackState'):
                if state == QMediaPlayer.PlaybackState.PlayingState:
                    print("▶️ 字幕音频正在播放")
                elif state == QMediaPlayer.PlaybackState.PausedState:
                    print("⏸ 字幕音频已暂停")
                elif state == QMediaPlayer.PlaybackState.StoppedState:
                    print("⏹ 字幕音频已停止")

        except Exception as e:
            print(f"❌ 处理字幕音频播放状态失败: {e}")

    def _on_subtitle_audio_error(self, error, error_string):
        """处理字幕音频播放错误"""
        try:
            print(f"❌ 字幕音频播放错误: {error_string}")
            print(f"   错误码: {error}")

        except Exception as e:
            print(f"❌ 处理字幕音频错误失败: {e}")

    def _forward_to_main_window_status(self, status):
        """转发状态信号到主窗口"""
        try:
            # 获取主窗口实例并调用其方法
            main_window = self.get_main_window()
            if main_window and hasattr(main_window, 'on_subtitle_audio_status_changed'):
                main_window.on_subtitle_audio_status_changed(status)
                print(f"✅ 已转发音频状态信号到主窗口: {status}")
        except Exception as e:
            print(f"❌ 转发状态信号失败: {e}")

    def _forward_to_main_window_playback(self, state):
        """转发播放状态信号到主窗口"""
        try:
            # 获取主窗口实例并调用其方法
            main_window = self.get_main_window()
            if main_window and hasattr(main_window, 'on_subtitle_audio_playback_state_changed'):
                main_window.on_subtitle_audio_playback_state_changed(state)
                print(f"✅ 已转发播放状态信号到主窗口: {state}")
        except Exception as e:
            print(f"❌ 转发播放状态信号失败: {e}")

    def _forward_to_main_window_error(self, error, error_string):
        """转发错误信号到主窗口"""
        try:
            # 获取主窗口实例并调用其方法
            main_window = self.get_main_window()
            if main_window and hasattr(main_window, 'on_subtitle_audio_error'):
                main_window.on_subtitle_audio_error(error, error_string)
                print(f"✅ 已转发音频错误信号到主窗口: {error_string}")
        except Exception as e:
            print(f"❌ 转发错误信号失败: {e}")

    def get_main_window(self):
        """获取主窗口实例"""
        try:
            # 向上遍历父级控件，找到主窗口
            widget = self
            while widget:
                # 检查是否是主窗口（具有字幕音频处理方法）
                if hasattr(widget, 'on_subtitle_audio_status_changed') and hasattr(widget, 'subtitles_table'):
                    return widget
                widget = widget.parent()
            return None
        except Exception as e:
            print(f"❌ 获取主窗口失败: {e}")
            return None


class ParameterSettingsPanel(RoundedWidget):
    """
    参数设置栏组件
    显示主要的参数设置选项，以紧凑的形式展示在主界面上
    包含滚动区域，支持更多设置项
    """

    def __init__(self):
        super().__init__(bg_color="#252525", border_radius=16)
        # 固定宽度会在主窗口中设置

        # 初始化试听相关变量
        self._preview_in_progress = False
        self._preview_audio_path = None
        self._preview_media_player = None
        self._preview_audio_output = None

        # 初始化字幕提取器插件（用于检测模型状态）
        self.extractor = None
        if WHISPERX_AVAILABLE:
            try:
                self.extractor = WhisperXSubtitleExtractor()

                # 默认配置：使用auto模式，支持GPU和CPU自动切换
                default_config = {
                    "device": "auto",  # 支持GPU/CPU自动选择
                    "default_model": "medium",  # 使用更小的模型提高稳定性
                    "enable_uvr5": False,  # 默认禁用UVR5
                    "models_dir": "models/whisperx_subtitle/weights"  # 指向weights目录
                }

                self.extractor.initialize(default_config)
                print("参数设置面板：成功初始化WhisperX插件")
            except Exception as e:
                print(f"参数设置面板：初始化WhisperX插件失败 - {e}")
                self.extractor = None

        self.setup_ui()

        # 加载模型状态
        self.load_model_combo()

    def setup_ui(self):
        """初始化参数设置栏UI"""
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(8, 8, 8, 8)
        main_layout.setSpacing(15)

        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setFrameShape(QFrame.NoFrame)  # 无边框
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)  # 禁用水平滚动条
        scroll_area.setStyleSheet("""
            QScrollArea {
                background-color: transparent;
                border: none;
            }
            QScrollBar:vertical {
                background-color: #353535;
                width: 8px;
                margin: 0px;
                border-radius: 4px;
            }
            QScrollBar::handle:vertical {
                background-color: #555555;
                min-height: 20px;
                border-radius: 4px;
            }
            QScrollBar::handle:vertical:hover {
                background-color: #666666;
            }
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
                height: 0px;
            }
            QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {
                background-color: transparent;
            }
        """)

        # 创建内容容器
        content_widget = QWidget()
        content_widget.setStyleSheet("background-color: transparent;")
        content_layout = QVBoxLayout(content_widget)
        content_layout.setContentsMargins(0, 0, 8, 0)  # 右侧留出滚动条空间
        content_layout.setSpacing(15)
        # 选项设置
        options_frame = self.create_options_section()
        content_layout.addWidget(options_frame)

        # 语音识别设置 - 替换为增强版
        sr_frame = self.create_speech_recognition_section()
        content_layout.addWidget(sr_frame)

        # 字幕翻译设置
        trans_frame = self.create_settings_section("字幕翻译", [
            ("翻译渠道", ["谷歌翻译 (免费)", "微软翻译 (需要API Key)", "DeepL Pro (需要API Key)"], "谷歌翻译 (免费)"),
            ("目标语言", ["中文 (zh)", "英语 (en)", "日语 (ja)", "韩语 (ko)", "法语 (fr)", "德语 (de)", "西班牙语 (es)",
                          "俄语 (ru)", "阿拉伯语 (ar)", "泰语 (th)"], "中文 (zh)")
        ])
        content_layout.addWidget(trans_frame)

        # 配音设置（特殊处理，包含试听和语速控制）
        voice_frame = self.create_voice_settings_section()
        content_layout.addWidget(voice_frame)

        # 合成设置
        composition_frame = self.create_composition_section()
        content_layout.addWidget(composition_frame)

        # 添加弹性空间
        content_layout.addStretch()

        # 将内容部件设置为滚动区域的部件
        scroll_area.setWidget(content_widget)

        # 将滚动区域添加到主布局
        main_layout.addWidget(scroll_area, 1)  # 1表示拉伸因子，使其填充剩余空间

        # 删除底部按钮区域（详细设置、应用）
        # 原有如下代码：
        # button_layout = QHBoxLayout()
        # detail_btn = QPushButton("详细设置")
        # ...
        # apply_btn = QPushButton("应用")
        # ...
        # button_layout.addWidget(detail_btn)
        # button_layout.addStretch()
        # button_layout.addWidget(apply_btn)
        # main_layout.addLayout(button_layout)
        # 现全部删除

    def set_default_output_path(self):
        """设置默认输出路径"""
        if hasattr(self, 'output_path_edit'):
            default_path = self.get_default_output_path()
            self.output_path_edit.setText(default_path)
            self.output_path_edit.setPlaceholderText("点击浏览选择输出目录")
            print(f"默认输出路径已设置为: {default_path}")

    def create_settings_section(self, title, options):
        """创建设置区域"""
        section = QFrame()
        section.setStyleSheet("""
            QFrame {
                background-color: #353535;
                border-radius: 12px;
                border: none;
            }
        """)

        layout = QVBoxLayout(section)
        layout.setContentsMargins(12, 12, 12, 12)
        layout.setSpacing(8)

        # 标题
        title_label = QLabel(title)
        title_label.setStyleSheet("""
            QLabel {
                color: #FFFFFF;
                font-size: 15px;
                font-weight: bold;
                background-color: transparent;
            }
        """)
        layout.addWidget(title_label)

        # 选项
        for i, (label_text, items, default_value) in enumerate(options):
            option_layout = QHBoxLayout()
            option_layout.setContentsMargins(0, 4, 0, 4)

            # 标签
            label = QLabel(label_text + ":")
            label.setStyleSheet("""
                QLabel {
                    color: #E0E0E0;
                    font-size: 13px;
                    background-color: transparent;
                }
            """)
            label.setFixedWidth(70)

            # 下拉框
            combo = QComboBox()
            combo.addItems(items)
            combo.setCurrentText(default_value)
            combo.setFixedHeight(26)

            # 为翻译设置添加objectName
            if title == "字幕翻译":
                if label_text == "翻译渠道":
                    combo.setObjectName("translation_channel_combo")
                elif label_text == "目标语言":
                    combo.setObjectName("target_language_combo")

            # 为配音设置添加objectName
            if title == "语音合成":
                if label_text == "语音渠道":
                    combo.setObjectName("voice_channel_combo")
                elif label_text == "声音角色":
                    combo.setObjectName("voice_role_combo")
            combo.setStyleSheet("""
                QComboBox {
                    background-color: #252525;
                    color: #FFFFFF;
                    font-size: 13px;
                    border: none;
                    border-radius: 6px;
                    padding: 2px 8px;
                }
                QComboBox::drop-down {
                    border: none;
                    width: 20px;
                }
                QComboBox::down-arrow {
                    image: none;
                    border-left: 4px solid transparent;
                    border-right: 4px solid transparent;
                    border-top: 6px solid #CCCCCC;
                    margin-right: 6px;
                }
                QComboBox QAbstractItemView {
                    background-color: #252525;
                    color: #FFFFFF;
                    selection-background-color: #0076FF;
                    border: none;
                    border-radius: 6px;
                    padding: 5px;
                }
            """)

            option_layout.addWidget(label)
            option_layout.addWidget(combo)
            layout.addLayout(option_layout)

            # 添加分割线，除了最后一项
            if i < len(options) - 1:
                line = QFrame()
                line.setFrameShape(QFrame.HLine)
                line.setStyleSheet("background-color: #454545;")
                line.setFixedHeight(1)
                layout.addWidget(line)

        return section

    def create_voice_settings_section(self):
        """创建配音设置区域（包含试听和语速控制）"""
        from plugins.tts.tts_manager_plugin import TtsManagerPlugin
        from core.config_manager import get_config_manager

        # 创建TTS管理器实例
        tts_manager = TtsManagerPlugin()

        # 获取TTS配置并初始化
        config_manager = get_config_manager()
        tts_config = config_manager.get_tts_config()

        # 初始化TTS管理器
        if tts_manager.initialize(tts_config):
            print("✅ TTS管理器初始化成功")
            engines = tts_manager.get_available_engines()
            print(f"可用TTS引擎: {engines}")
        else:
            print("❌ TTS管理器初始化失败")
            engines = []

        # 创建区域
        section = QFrame()
        section.setStyleSheet("""
            QFrame {
                background-color: #353535;
                border-radius: 12px;
                border: none;
            }
        """)

        layout = QVBoxLayout(section)
        layout.setContentsMargins(15, 12, 15, 12)
        layout.setSpacing(8)

        # 标题
        title_label = QLabel("配音设置")
        title_label.setStyleSheet("""
            QLabel {
                color: #FFFFFF;
                font-size: 15px;
                font-weight: bold;
                background-color: transparent;
                border: none;
                padding: 0px 0px 5px 0px;
            }
        """)
        layout.addWidget(title_label)

        # 配音渠道
        channel_layout = QHBoxLayout()
        channel_label = QLabel("配音渠道:")
        channel_layout.setContentsMargins(0, 4, 0, 4)
        channel_label.setStyleSheet("""
            QLabel {
                color: #CCCCCC;
                font-size: 13px;
                background-color: transparent;
                border: none;
            }
        """)
        channel_label.setFixedWidth(70)
        # 获取可用引擎列表
        engines = tts_manager.get_available_engines()

        # 将原始引擎名称映射为带描述的名称
        engine_display_names = []
        for engine_name in engines:
            engine_info = tts_manager.get_engine_info(engine_name)
            if engine_name == "edge_tts":
                display_name = f"{engine_info['name']} (免费)"
            elif engine_name == "azure_tts":
                display_name = f"{engine_info['name']} (高级)"
            else:
                display_name = engine_info['name']

            engine_display_names.append(display_name)
        self.voice_channel_combo = QComboBox()
        self.voice_channel_combo.addItems(engine_display_names)
        self.voice_channel_combo.setCurrentText("Edge TTS (免费)")  # 默认选择Edge TTS
        self.voice_channel_combo.setFixedHeight(26)
        # 设置objectName以便配音设置获取
        self.voice_channel_combo.setObjectName("voice_channel_combo")
        # 连接渠道变化信号
        self.voice_channel_combo.currentTextChanged.connect(self.on_voice_engine_changed)

        # 初始化声音列表
        self.on_voice_engine_changed(self.voice_channel_combo.currentText())
        self.voice_channel_combo.setStyleSheet("""
           QComboBox {
                background-color: #252525;
                color: #FFFFFF;
                font-size: 13px;
                border: none;
                border-radius: 6px;
                padding: 2px 8px;
            }
            QComboBox::drop-down {
                border: none;
                width: 20px;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 4px solid transparent;
                border-right: 4px solid transparent;
                border-top: 6px solid #CCCCCC;
                margin-right: 6px;
            }
            QComboBox QAbstractItemView {
                background-color: #252525;
                color: #FFFFFF;
                selection-background-color: #0076FF;
                border: none;
                border-radius: 6px;
                padding: 5px;
            }
        """)

        channel_layout.addWidget(channel_label)
        channel_layout.addWidget(self.voice_channel_combo)
        # channel_layout.addStretch()
        layout.addLayout(channel_layout)
        # 添加分割线
        line1 = QFrame()
        line1.setFrameShape(QFrame.HLine)
        line1.setStyleSheet("background-color: #454545;")
        line1.setFixedHeight(1)
        layout.addWidget(line1)

        # 配音语言
        language_layout = QHBoxLayout()
        language_label = QLabel("配音语言:")
        language_layout.setContentsMargins(0, 4, 0, 4)
        language_label.setStyleSheet("""
                    QLabel {
                        color: #CCCCCC;
                        font-size: 13px;
                        background-color: transparent;
                        border: none;
                    }
                """)
        language_label.setFixedWidth(70)
        # 获取支持的语言列表
        languages = tts_manager.get_supported_languages()
        print(f"支持的语言列表: {languages}")
        if not languages:
            print("❌ 获取语言列表失败")
            self.init_completed.emit(False)
            return
        # 将字典值转换为列表并添加到 QComboBox 中
        language_list = list(languages.values())
        # 获取每种语言的语音列表
        all_voices = {}
        total_languages = len(languages)
        for i, (lang_code, lang_name) in enumerate(languages.items()):

            voices = tts_manager.get_available_voices(lang_code)
            if voices:
                all_voices[lang_code] = voices
                print(f"✅ 语言 {lang_code} ({lang_name}) 可用声音数量: {len(voices)}")

        if not all_voices:
            print("❌ 没有可用的语音")
            self.init_completed.emit(False)
            return

        self.languages_combo = QComboBox()
        self.languages_combo.addItems(language_list)
        # 连接语言变化信号到更新角色列表
        self.languages_combo.currentTextChanged.connect(self.on_language_changed)

        self.languages_combo.setFixedHeight(26)
        self.languages_combo.setStyleSheet("""
                   QComboBox {
                        background-color: #252525;
                        color: #FFFFFF;
                        font-size: 13px;
                        border: none;
                        border-radius: 6px;
                        padding: 2px 8px;
                    }
                    QComboBox::drop-down {
                        border: none;
                        width: 20px;
                    }
                    QComboBox::down-arrow {
                        image: none;
                        border-left: 4px solid transparent;
                        border-right: 4px solid transparent;
                        border-top: 6px solid #CCCCCC;
                        margin-right: 6px;
                    }
                    QComboBox QAbstractItemView {
                        background-color: #252525;
                        color: #FFFFFF;
                        selection-background-color: #0076FF;
                        border: none;
                        border-radius: 6px;
                        padding: 5px;
                    }
                """)

        language_layout.addWidget(language_label)
        language_layout.addWidget(self.languages_combo)
        # channel_layout.addStretch()
        layout.addLayout(language_layout)
        # 添加分割线
        line1 = QFrame()
        line1.setFrameShape(QFrame.HLine)
        line1.setStyleSheet("background-color: #454545;")
        line1.setFixedHeight(1)
        layout.addWidget(line1)

        # 配音角色和试听按钮
        voice_layout = QHBoxLayout()
        voice_label = QLabel("配音角色:")
        voice_layout.setContentsMargins(0, 4, 0, 4)
        voice_label.setStyleSheet("""
            QLabel {
                color: #CCCCCC;
                font-size: 13px;
                background-color: transparent;
                border: none;
            }
        """)
        voice_label.setFixedWidth(70)

        self.voice_role_combo = QComboBox()
        # 添加声音角色，包含声音ID数据
        voice_options = [
            ("晓晓 (女声)", "zh-CN-XiaoxiaoNeural"),
            ("云希 (男声)", "zh-CN-YunxiNeural"),
            ("云健 (男声)", "zh-CN-YunjianNeural"),
            ("晓伊 (女声)", "zh-CN-XiaoyiNeural"),
            ("云扬 (男声)", "zh-CN-YunyangNeural"),
            ("云夏 (男声)", "zh-CN-YunxiaNeural"),
            ("晓北 (女声, 东北口音)", "zh-CN-liaoning-XiaobeiNeural"),
            ("晓妮 (女声, 陕西口音)", "zh-CN-shaanxi-XiaoniNeural")
        ]

        for voice_name, voice_id in voice_options:
            self.voice_role_combo.addItem(voice_name, voice_id)

        self.voice_role_combo.setCurrentIndex(0)  # 默认选择第一个
        self.voice_role_combo.setFixedHeight(26)
        # 设置objectName以便配音设置获取
        self.voice_role_combo.setObjectName("voice_role_combo")
        self.voice_role_combo.setStyleSheet("""
            QComboBox {
                background-color: #252525;
                color: #FFFFFF;
                font-size: 13px;
                border: none;
                border-radius: 6px;
                padding: 2px 8px;
            }
            QComboBox::drop-down {
                border: none;
                width: 20px;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 4px solid transparent;
                border-right: 4px solid transparent;
                border-top: 6px solid #CCCCCC;
                margin-right: 6px;
            }
            QComboBox QAbstractItemView {
                background-color: #252525;
                color: #FFFFFF;
                selection-background-color: #0076FF;
                border: none;
                border-radius: 6px;
                padding: 5px;
            }
        """)

        # 试听按钮
        self.voice_test_btn = QPushButton("试听")
        self.voice_test_btn.setFixedSize(60, 30)
        self.voice_test_btn.setCheckable(True)
        self.voice_test_btn.setStyleSheet("""
            QPushButton {
                background-color: #007AFF;
                color: #FFFFFF;
                font-size: 11px;
                font-weight: bold;
                border: none;
                border-radius: 12px;
                padding: 2px 8px;
            }
            QPushButton:hover {
                background-color: #0056CC;
            }
            QPushButton:checked {
                background-color: #FF3B30;
                color: #FFFFFF;
            }
            QPushButton:checked:hover {
                background-color: #D70015;
            }
        """)
        self.voice_test_btn.clicked.connect(self.toggle_voice_test)

        voice_layout.addWidget(voice_label)
        voice_layout.addWidget(self.voice_role_combo)
        voice_layout.addWidget(self.voice_test_btn)
        # voice_layout.addStretch()
        layout.addLayout(voice_layout)

        # 语速控制
        speed_layout = QVBoxLayout()
        speed_layout.setContentsMargins(0, 4, 0, 4)
        speed_layout.setSpacing(4)

        # 语速标题行
        speed_title_layout = QHBoxLayout()
        speed_title_label = QLabel("语速:")
        speed_title_label.setStyleSheet("""
            QLabel {
                color: #CCCCCC;
                font-size: 13px;
                background-color: transparent;
                border: none;
            }
        """)

        self.speed_value_label = QLabel("1.0x")
        self.speed_value_label.setStyleSheet("""
            QLabel {
                color: #007AFF;
                font-size: 12px;
                font-weight: bold;
                background-color: transparent;
                border: none;
            }
        """)

        speed_title_layout.addWidget(speed_title_label)
        speed_title_layout.addStretch()
        speed_title_layout.addWidget(self.speed_value_label)
        speed_layout.addLayout(speed_title_layout)
        # 添加分割线
        line1 = QFrame()
        line1.setFrameShape(QFrame.HLine)
        line1.setStyleSheet("background-color: #454545;")
        line1.setFixedHeight(1)
        layout.addWidget(line1)
        # 语速滑块
        self.speed_slider = QSlider(Qt.Horizontal)
        self.speed_slider.setRange(50, 200)  # 0.5x to 2.0x
        self.speed_slider.setValue(100)  # 1.0x
        self.speed_slider.setFixedHeight(20)
        # 设置objectName以便配音设置获取
        self.speed_slider.setObjectName("voice_speed_slider")
        self.speed_slider.setStyleSheet("""
            QSlider::groove:horizontal {
                border: 1px solid rgba(255, 255, 255, 0.2);
                height: 2px;
                background: rgba(255, 255, 255, 0.1);
                border-radius: 2px;
                margin: 8px 0;
            }
            QSlider::handle:horizontal {
                background: #007AFF;
                border: none;
                width: 14px;
                height: 14px;
                margin: -5px 0;
                border-radius: 7px;
            }
            QSlider::handle:horizontal:hover {
                background: #0056CC;
            }

        """)
        self.speed_slider.valueChanged.connect(self.on_speed_changed)
        speed_layout.addWidget(self.speed_slider)

        layout.addLayout(speed_layout)

        # 在self.voice_channel_combo设置完成后添加信号连接
        self.voice_channel_combo.currentTextChanged.connect(self.on_engine_changed)

        # --- 修复：初始化后自动刷新voice列表，确保默认值有效 ---
        self.update_voice_list()

        return section

    def on_voice_engine_changed(self, engine_text):
        """当配音引擎改变时更新声音列表"""
        try:
            print(f"🔄 配音引擎切换到: {engine_text}")

            # 清空当前声音列表
            self.voice_role_combo.clear()

            if "Edge TTS" in engine_text:
                # Edge TTS 声音选项
                voice_options = [
                    ("晓晓 (女声)", "zh-CN-XiaoxiaoNeural"),
                    ("云希 (男声)", "zh-CN-YunxiNeural"),
                    ("云健 (男声)", "zh-CN-YunjianNeural"),
                    ("晓伊 (女声)", "zh-CN-XiaoyiNeural"),
                    ("云扬 (男声)", "zh-CN-YunyangNeural"),
                    ("云夏 (男声)", "zh-CN-YunxiaNeural"),
                    ("晓北 (女声, 东北口音)", "zh-CN-liaoning-XiaobeiNeural"),
                    ("晓妮 (女声, 陕西口音)", "zh-CN-shaanxi-XiaoniNeural")
                ]
            elif "Azure TTS" in engine_text:
                # Azure TTS 声音选项（更多选择）
                voice_options = [
                    ("晓晓多语言 (女声, 多语言)", "zh-CN-XiaoxiaoMultilingualNeural"),
                    ("晓晓 (女声)", "zh-CN-XiaoxiaoNeural"),
                    ("云希 (男声)", "zh-CN-YunxiNeural"),
                    ("云健 (男声)", "zh-CN-YunjianNeural"),
                    ("晓伊 (女声)", "zh-CN-XiaoyiNeural"),
                    ("云扬 (男声)", "zh-CN-YunyangNeural"),
                    ("云夏 (男声)", "zh-CN-YunxiaNeural"),
                    ("晓辰 (女声)", "zh-CN-XiaochenNeural"),
                    ("晓涵 (女声)", "zh-CN-XiaohanNeural"),
                    ("晓墨 (女声)", "zh-CN-XiaomoNeural"),
                    ("晓秋 (女声)", "zh-CN-XiaoqiuNeural"),
                    ("晓睿 (女声)", "zh-CN-XiaoruiNeural"),
                    ("晓双 (女声, 儿童)", "zh-CN-XiaoshuangNeural"),
                    ("晓萱 (女声)", "zh-CN-XiaoxuanNeural"),
                    ("晓颜 (女声)", "zh-CN-XiaoyanNeural"),
                    ("晓悠 (女声)", "zh-CN-XiaoyouNeural"),
                    ("晓甄 (女声)", "zh-CN-XiaozhenNeural"),
                    ("云枫 (男声)", "zh-CN-YunfengNeural"),
                    ("云皓 (男声)", "zh-CN-YunhaoNeural"),
                    ("云野 (男声)", "zh-CN-YunyeNeural"),
                    ("云泽 (男声)", "zh-CN-YunzeNeural")
                ]
            else:
                # 默认使用Edge TTS选项
                voice_options = [
                    ("晓晓 (女声)", "zh-CN-XiaoxiaoNeural"),
                    ("云希 (男声)", "zh-CN-YunxiNeural")
                ]

            # 添加声音选项到下拉框
            for voice_name, voice_id in voice_options:
                self.voice_role_combo.addItem(voice_name, voice_id)

            # 设置默认选择
            self.voice_role_combo.setCurrentIndex(0)

            print(f"✅ 已更新声音列表，共 {len(voice_options)} 个选项")

        except Exception as e:
            print(f"❌ 更新声音列表失败: {e}")

    def create_speech_recognition_section(self):
        """创建增强版语音识别设置区域（包含模型检查和下载按钮）"""
        section = QFrame()
        section.setStyleSheet("""
            QFrame {
                background-color: #353535;
                border-radius: 12px;
                border: none;
            }
        """)

        layout = QVBoxLayout(section)
        layout.setContentsMargins(12, 12, 12, 12)
        layout.setSpacing(8)

        # 标题区域
        title_layout = QHBoxLayout()

        # 标题
        title_label = QLabel("语音识别")
        title_label.setStyleSheet("""
            QLabel {
                color: #FFFFFF;
                font-size: 15px;
                font-weight: bold;
                background-color: transparent;
            }
        """)
        title_layout.addWidget(title_label)

        # 模型状态标签
        self.model_status_label = QLabel("")
        self.model_status_label.setStyleSheet("""
            QLabel {
                font-size: 11px;
                color: rgba(255, 255, 255, 0.7);
                background: transparent;
                border: none;
            }
        """)
        title_layout.addWidget(self.model_status_label)
        title_layout.addStretch()

        # 下载模型按钮
        self.download_model_button = QPushButton("⬇️")
        self.download_model_button.setToolTip("下载选中的模型")
        self.download_model_button.setFixedSize(24, 24)
        self.download_model_button.setStyleSheet("""
            QPushButton {
                background-color: #555555;
                color: #FFFFFF;
                font-size: 12px;
                border: none;
                border-radius: 12px;
            }
            QPushButton:hover {
                background-color: #666666;
            }
            QPushButton:pressed {
                background-color: #444444;
            }
            QPushButton:disabled {
                background-color: #333333;
                color: #666666;
            }
        """)
        self.download_model_button.clicked.connect(self.download_selected_model)

        title_layout.addWidget(self.download_model_button)

        layout.addLayout(title_layout)

        # 识别引擎
        engine_layout = QHBoxLayout()
        engine_layout.setContentsMargins(0, 4, 0, 4)

        engine_label = QLabel("识别引擎:")
        engine_label.setStyleSheet("""
            QLabel {
                color: #E0E0E0;
                font-size: 13px;
                background-color: transparent;
            }
        """)
        engine_label.setFixedWidth(70)

        self.engine_combo = QComboBox()
        self.engine_combo.addItems(["whisperX 本地", "whisperX API"])
        self.engine_combo.setCurrentText("whisperX 本地")
        self.engine_combo.setFixedHeight(26)
        self.engine_combo.setStyleSheet("""
            QComboBox {
                background-color: #252525;
                color: #FFFFFF;
                font-size: 13px;
                border: none;
                border-radius: 6px;
                padding: 2px 8px;
            }
            QComboBox::drop-down {
                border: none;
                width: 20px;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 4px solid transparent;
                border-right: 4px solid transparent;
                border-top: 6px solid #CCCCCC;
                margin-right: 6px;
            }
            QComboBox QAbstractItemView {
                background-color: #252525;
                color: #FFFFFF;
                selection-background-color: #0076FF;
                border: none;
                border-radius: 6px;
                padding: 5px;
            }
        """)

        engine_layout.addWidget(engine_label)
        engine_layout.addWidget(self.engine_combo)
        layout.addLayout(engine_layout)

        # 添加分割线
        line1 = QFrame()
        line1.setFrameShape(QFrame.HLine)
        line1.setStyleSheet("background-color: #454545;")
        line1.setFixedHeight(1)
        layout.addWidget(line1)

        # 识别模型
        model_layout = QHBoxLayout()
        model_layout.setContentsMargins(0, 4, 0, 4)

        model_label = QLabel("识别模型:")
        model_label.setStyleSheet("""
            QLabel {
                color: #E0E0E0;
                font-size: 13px;
                background-color: transparent;
            }
        """)
        model_label.setFixedWidth(70)

        self.model_combo = QComboBox()
        # 模型列表将在load_model_combo方法中加载
        self.model_combo.setFixedHeight(26)
        self.model_combo.setStyleSheet("""
            QComboBox {
                background-color: #252525;
                color: #FFFFFF;
                font-size: 13px;
                border: none;
                border-radius: 6px;
                padding: 2px 8px;
            }
            QComboBox::drop-down {
                border: none;
                width: 20px;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 4px solid transparent;
                border-right: 4px solid transparent;
                border-top: 6px solid #CCCCCC;
                margin-right: 6px;
            }
            QComboBox QAbstractItemView {
                background-color: #252525;
                color: #FFFFFF;
                selection-background-color: #0076FF;
                border: none;
                border-radius: 6px;
                padding: 5px;
            }
        """)
        self.model_combo.currentIndexChanged.connect(self.update_model_status)

        model_layout.addWidget(model_label)
        model_layout.addWidget(self.model_combo)
        layout.addLayout(model_layout)

        # 添加分割线
        line2 = QFrame()
        line2.setFrameShape(QFrame.HLine)
        line2.setStyleSheet("background-color: #454545;")
        line2.setFixedHeight(1)
        layout.addWidget(line2)

        # 源语言
        lang_layout = QHBoxLayout()
        lang_layout.setContentsMargins(0, 4, 0, 4)

        lang_label = QLabel("源语言:")
        lang_label.setStyleSheet("""
            QLabel {
                color: #E0E0E0;
                font-size: 13px;
                background-color: transparent;
            }
        """)
        lang_label.setFixedWidth(70)

        self.lang_combo = QComboBox()
        self.lang_combo.addItems(
            ["自动检测", "中文", "英语", "日语", "韩语", "西班牙语", "法语", "德语", "意大利语", "葡萄牙语", "俄语"])
        self.lang_combo.setCurrentText("自动检测")
        self.lang_combo.setFixedHeight(26)
        self.lang_combo.setStyleSheet("""
            QComboBox {
                background-color: #252525;
                color: #FFFFFF;
                font-size: 13px;
                border: none;
                border-radius: 6px;
                padding: 2px 8px;
            }
            QComboBox::drop-down {
                border: none;
                width: 20px;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 4px solid transparent;
                border-right: 4px solid transparent;
                border-top: 6px solid #CCCCCC;
                margin-right: 6px;
            }
            QComboBox QAbstractItemView {
                background-color: #252525;
                color: #FFFFFF;
                selection-background-color: #0076FF;
                border: none;
                border-radius: 6px;
                padding: 5px;
            }
        """)

        lang_layout.addWidget(lang_label)
        lang_layout.addWidget(self.lang_combo)
        layout.addLayout(lang_layout)

        return section

    def create_options_section(self):
        """创建选项设置区域"""
        section = QFrame()
        section.setStyleSheet("""
            QFrame {
                background-color: #353535;
                border-radius: 12px;
                border: none;
            }
        """)

        layout = QVBoxLayout(section)
        layout.setContentsMargins(12, 12, 12, 12)
        layout.setSpacing(8)

        # 标题
        title_label = QLabel("选项设置")
        title_label.setStyleSheet("""
            QLabel {
                color: #FFFFFF;
                font-size: 15px;
                font-weight: bold;
                background-color: transparent;
            }
        """)
        layout.addWidget(title_label)

        # 选项复选框
        options = [
            ("启用GPU加速", True, "gpu_acceleration_checkbox"),
            ("启用人声分离", False, "voice_separation_checkbox"),
        ]
        Item_layout = QHBoxLayout()
        Item_layout.addStretch()
        for i, (option_text, default_checked, attr_name) in enumerate(options):
            option_layout = QHBoxLayout()
            option_layout.setContentsMargins(0, 4, 0, 4)

            # 标签
            label = QLabel(option_text)
            label.setStyleSheet("""
                QLabel {
                    color: #E0E0E0;
                    font-size: 13px;
                    background-color: transparent;
                }
            """)

            # 复选框
            checkbox = QCheckBox()
            checkbox.setChecked(default_checked)
            checkbox.setStyleSheet("""
                QCheckBox {
                    background-color: transparent;
                    border: none;  /* 明确移除边框 */
                    padding: 0;
                }
                QCheckBox::indicator {
                    width: 18px;
                    height: 18px;
                    background-color: #252525;
                    border: none;
                    border-radius: 4px;
                }
                QCheckBox::indicator:checked {
                    background-color: #0076FF;
                    image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iMTAiIHZpZXdCb3g9IjAgMCAxMiAxMCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTQgNy4zMzMzM0wxLjMzMzM0IDQuNjY2NjZMMC4zMzMzMzcgNS42NjY2Nkw0IDkuMzMzMzNMMTEuMzMzMyAyTDEwLjMzMzMgMUw0IDcuMzMzMzNaIiBmaWxsPSJ3aGl0ZSIvPgo8L3N2Zz4K);
                    background-repeat: no-repeat;
                    background-position: center;
                }
            """)

            # 将复选框存储为实例属性，以便后续访问
            setattr(self, attr_name, checkbox)

            option_layout.addWidget(label)
            option_layout.addStretch()
            option_layout.addWidget(checkbox)
            Item_layout.addLayout(option_layout)
        Item_layout.addStretch()
        layout.addLayout(Item_layout)

        # ===== 新增：最终生成文件保存路径选择 =====
        path_layout = QHBoxLayout()
        path_layout.setContentsMargins(0, 8, 0, 0)

        path_label = QLabel("保存路径：")
        path_label.setStyleSheet("""
            QLabel {
                color: #E0E0E0;
                font-size: 13px;
                background-color: transparent;
            }
        """)
        self.output_path_edit = QLineEdit()
        self.output_path_edit.setPlaceholderText("请选择最终生成文件的保存文件夹")
        self.output_path_edit.setFixedHeight(28)
        self.output_path_edit.setStyleSheet("""
            QLineEdit {
                background-color: #232323;
                color: #FFFFFF;
                border: 1px solid #444444;
                border-radius: 6px;
                padding-left: 8px;
                font-size: 13px;
            }
        """)

        # 设置默认输出路径
        default_path = self.get_default_output_path()
        self.output_path_edit.setText(default_path)
        print(f"输出路径输入框默认路径已设置为: {default_path}")
        browse_btn = QPushButton("浏览")
        browse_btn.setFixedHeight(28)
        browse_btn.setStyleSheet("""
            QPushButton {
                background-color: #007AFF;
                color: #FFFFFF;
                font-size: 13px;
                border: none;
                border-radius: 6px;
                padding: 0 16px;
            }
            QPushButton:hover {
                background-color: #0056CC;
            }
        """)
        browse_btn.clicked.connect(self.browse_output_path)
        path_layout.addWidget(path_label)
        path_layout.addWidget(self.output_path_edit)
        path_layout.addWidget(browse_btn)
        layout.addLayout(path_layout)
        return section

    def create_composition_section(self):
        """创建合成设置区域"""
        section = QFrame()
        section.setStyleSheet("""
            QFrame {
                background-color: #353535;
                border-radius: 12px;
                border: none;
            }
        """)

        layout = QVBoxLayout(section)
        layout.setContentsMargins(12, 12, 12, 12)
        layout.setSpacing(8)

        # 标题
        title_label = QLabel("合成设置")
        title_label.setStyleSheet("""
            QLabel {
                color: #FFFFFF;
                font-size: 15px;
                font-weight: bold;
                background-color: transparent;
            }
        """)
        layout.addWidget(title_label)

        # 选项复选框
        options = [
            ("保留视频原声", False),
            ("保留背景音乐", False)  # 默认不勾选，需要用户主动选择
        ]

        # 存储复选框引用
        self.composition_checkboxes = {}

        Item_layout = QHBoxLayout()
        Item_layout.addStretch()
        for i, (option_text, default_checked) in enumerate(options):
            option_layout = QHBoxLayout()
            option_layout.setContentsMargins(0, 4, 0, 4)

            # 标签
            label = QLabel(option_text)
            label.setStyleSheet("""
                QLabel {
                    color: #E0E0E0;
                    font-size: 13px;
                    background-color: transparent;
                }
            """)

            # 复选框
            checkbox = QCheckBox()
            checkbox.setChecked(default_checked)
            checkbox.setStyleSheet("""
                QCheckBox {
                    background-color: transparent;
                    border: none;  /* 明确移除边框 */
                    padding: 0;
                }
                QCheckBox::indicator {
                    width: 18px;
                    height: 18px;
                    background-color: #252525;
                    border: none;
                    border-radius: 4px;
                }
                QCheckBox::indicator:checked {
                    background-color: #0076FF;
                    image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iMTAiIHZpZXdCb3g9IjAgMCAxMiAxMCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTQgNy4zMzMzM0wxLjMzMzM0IDQuNjY2NjZMMC4zMzMzMzcgNS42NjY2Nkw0IDkuMzMzMzNMMTEuMzMzMyAyTDEwLjMzMzMgMUw0IDcuMzMzMzNaIiBmaWxsPSJ3aGl0ZSIvPgo8L3N2Zz4K);
                    background-repeat: no-repeat;
                    background-position: center;
                }
            """)

            # 存储复选框引用
            if option_text == "保留视频原声":
                self.composition_checkboxes['keep_original_audio'] = checkbox
            elif option_text == "保留背景音乐":
                self.composition_checkboxes['keep_background_music'] = checkbox
                # 连接背景音乐复选框的状态变化事件
                checkbox.stateChanged.connect(self.on_background_music_checkbox_changed)

            option_layout.addWidget(label)
            # option_layout.addStretch()
            option_layout.addWidget(checkbox)
            Item_layout.addLayout(option_layout)
        Item_layout.addStretch()
        layout.addLayout(Item_layout)
        return section

    def on_background_music_checkbox_changed(self, state):
        """处理保留背景音乐复选框状态变化"""
        try:
            is_checked = state == 2  # Qt.Checked = 2
            print(f"🎵 保留背景音乐选项: {'已勾选' if is_checked else '已取消'}")

            if is_checked:
                # 检查是否满足保留背景音乐的条件
                # 获取主窗口实例
                main_window = self.get_main_window()
                if main_window and hasattr(main_window, 'validate_background_music_requirements'):
                    validation_result = main_window.validate_background_music_requirements()

                    if not validation_result['valid']:
                        # 条件不满足，显示提示并取消勾选
                        AppleMessageBox.show_warning(
                            self, "无法保留背景音乐",
                            validation_result['message']
                        )
                        # 取消勾选
                        self.composition_checkboxes['keep_background_music'].setChecked(False)
                        return
                    else:
                        print("✅ 背景音乐保留条件检查通过")
                else:
                    print("❌ 无法获取主窗口实例或验证方法")

        except Exception as e:
            print(f"❌ 处理背景音乐复选框状态变化失败: {e}")

    def get_main_window(self):
        """获取主窗口实例"""
        try:
            # 向上遍历父级控件，找到主窗口
            widget = self
            while widget:
                # 检查是否是主窗口（具有validate_background_music_requirements方法）
                if hasattr(widget, 'validate_background_music_requirements'):
                    return widget
                widget = widget.parent()
            return None
        except Exception as e:
            print(f"❌ 获取主窗口失败: {e}")
            return None

    def load_model_combo(self):
        """加载模型下拉列表，并检查模型状态"""
        # 初始默认模型列表（如果无法获取已下载模型）
        default_models = ["tiny", "base", "small", "medium", "large-v1", "large-v2", "large-v3"]

        try:
            if not hasattr(self, 'model_combo'):
                print("模型下拉框尚未创建")
                return

            if not self.extractor:
                # 如果没有初始化extractor，使用默认列表
                self.model_combo.clear()
                for model_name in default_models:
                    self.model_combo.addItem(model_name, model_name)

                # 设置默认为large-v3
                for i in range(self.model_combo.count()):
                    if self.model_combo.itemText(i) == "large-v3":
                        self.model_combo.setCurrentIndex(i)
                        break

                # 更新状态标签 - 提示无法检测模型
                if hasattr(self, 'model_status_label'):
                    self.model_status_label.setText("(无法检测模型状态)")
                    self.model_status_label.setStyleSheet("""
                        QLabel {
                            font-size: 11px;
                            color: #FFAA44;
                            background: transparent;
                            border: none;
                        }
                    """)
                return

            # 有extractor，获取模型信息
            models = self.extractor.get_available_models()
            downloaded_models = self.extractor.get_downloaded_models()

            # 清空现有项
            self.model_combo.clear()

            # 添加带下载状态的模型列表
            for model_name, info in models.items():
                status = "已下载" if model_name in downloaded_models else "未下载"
                display_text = f"{model_name} ({info['size']}) - {status}"
                self.model_combo.addItem(display_text, model_name)

            # 设置默认模型为large-v3
            default_model = "large-v3"
            for i in range(self.model_combo.count()):
                if self.model_combo.itemData(i) == default_model:
                    self.model_combo.setCurrentIndex(i)
                    break

            # 更新模型状态标签
            self.update_model_status()

        except Exception as e:
            print(f"加载模型列表失败: {e}")

            # 回退到默认列表
            self.model_combo.clear()
            for model_name in default_models:
                self.model_combo.addItem(model_name, model_name)

            # 设置默认为large-v3
            for i in range(self.model_combo.count()):
                if self.model_combo.itemText(i) == "large-v3":
                    self.model_combo.setCurrentIndex(i)
                    break

            # 更新状态标签 - 提示错误
            if hasattr(self, 'model_status_label'):
                self.model_status_label.setText(f"(加载失败: {str(e)[:20]})")
                self.model_status_label.setStyleSheet("""
                    QLabel {
                        font-size: 11px;
                        color: #FF4444;
                        background: transparent;
                        border: none;
                    }
                """)

    def update_model_status(self):
        """更新模型状态显示"""
        if not self.extractor or not hasattr(self, 'model_combo') or not hasattr(self, 'model_status_label'):
            return

        try:
            current_model = self.model_combo.currentData()
            downloaded_models = self.extractor.get_downloaded_models()

            if current_model in downloaded_models:
                # 模型已下载
                self.model_status_label.setText("✅ 模型已下载")
                self.model_status_label.setStyleSheet("""
                    QLabel {
                        font-size: 11px;
                        color: #44FF44;
                        background: transparent;
                        border: none;
                    }
                """)
                if hasattr(self, 'download_model_button'):
                    self.download_model_button.setEnabled(False)
            else:
                # 模型未下载
                self.model_status_label.setText("❌ 模型未下载")
                self.model_status_label.setStyleSheet("""
                    QLabel {
                        font-size: 11px;
                        color: #FF4444;
                        background: transparent;
                        border: none;
                    }
                """)
                if hasattr(self, 'download_model_button'):
                    self.download_model_button.setEnabled(True)
        except Exception as e:
            print(f"更新模型状态失败: {e}")
            self.model_status_label.setText("(状态未知)")
            self.model_status_label.setStyleSheet("""
                QLabel {
                    font-size: 11px;
                    color: #FFAA44;
                    background: transparent;
                    border: none;
                }
            """)

    def download_selected_model(self):
        """下载选中的模型"""
        if not self.extractor:
            AppleMessageBox.show_warning(self, "警告", "WhisperX插件未初始化，无法下载模型")
            return

        selected_model = self.model_combo.currentData()
        if not selected_model:
            return

        # 提示下载确认
        reply = AppleMessageBox.show_question(
            self,
            "下载模型",
            f"确定要下载 {selected_model} 模型吗？这可能需要一段时间。",
            "下载", "取消"
        )

        if reply:
            # 显示下载中状态
            self.model_status_label.setText("⏳ 模型下载中...")
            self.model_status_label.setStyleSheet("""
                QLabel {
                    font-size: 11px;
                    color: #0076FF;
                    background: transparent;
                    border: none;
                }
            """)
            self.download_model_button.setEnabled(False)
            QApplication.processEvents()

            try:
                # 定义进度回调函数
                def progress_callback(message, percentage):
                    self.model_status_label.setText(f"⏳ 下载中: {percentage}%")
                    QApplication.processEvents()

                # 下载模型
                success = self.extractor.download_model(selected_model, progress_callback)

                if success:
                    self.model_status_label.setText("✅ 下载完成")
                    self.model_status_label.setStyleSheet("""
                        QLabel {
                            font-size: 11px;
                            color: #44FF44;
                            background: transparent;
                            border: none;
                        }
                    """)
                    AppleMessageBox.show_success(self, "下载成功", f"模型 {selected_model} 下载完成！")

                    # 刷新模型列表
                    self.load_model_combo()
                else:
                    self.model_status_label.setText("❌ 下载失败")
                    self.model_status_label.setStyleSheet("""
                        QLabel {
                            font-size: 11px;
                            color: #FF4444;
                            background: transparent;
                            border: none;
                        }
                    """)
                    AppleMessageBox.show_error(self, "下载失败", f"模型 {selected_model} 下载失败！")
                    self.download_model_button.setEnabled(True)

            except Exception as e:
                self.model_status_label.setText("❌ 下载出错")
                self.model_status_label.setStyleSheet("""
                    QLabel {
                        font-size: 11px;
                        color: #FF4444;
                        background: transparent;
                        border: none;
                    }
                """)
                AppleMessageBox.show_error(self, "下载错误", f"下载模型时出错: {e}")
                self.download_model_button.setEnabled(True)

    def toggle_voice_test(self):
        """切换语音预览状态"""
        if not self._preview_in_progress:
            self.start_voice_test()
        else:
            self.stop_voice_test()

    def start_voice_test(self):
        """开始语音试听"""
        try:
            # 检查voice_test_btn是否存在
            if not hasattr(self, 'voice_test_btn') or not self.voice_test_btn:
                print("警告：voice_test_btn 不存在，可能未正确初始化")
                return

            # 设置预览状态为进行中
            self._preview_in_progress = True
            self.voice_test_btn.setEnabled(False)
            self.voice_test_btn.setText("生成中...")

            # 获取当前设置
            engine_name = None
            engine_display_name = self.voice_channel_combo.currentText()
            if "Edge TTS" in engine_display_name:
                engine_name = "edge_tts"
            elif "Azure TTS" in engine_display_name:
                engine_name = "azure_tts"

            # 获取当前选择的角色
            voice = self.voice_role_combo.currentData()
            if not voice:
                voice = self.voice_role_combo.currentText()
            if not voice:
                voice = "zh-CN-XiaoxiaoNeural"

            speed = self.speed_slider.value() / 100.0

            # 使用默认试听文本
            text = "欢迎使用FlipTalk Ai，支持多种语音设置。"

            print(f"🎵 开始试听 - 引擎: {engine_name}, 声音: {voice}, 语速: {speed}x")
            print(f"📝 试听文本: {text}")

            # 创建异步任务
            import asyncio
            from threading import Thread

            async def preview_task():
                try:
                    # 使用TTS管理器的preview_voice方法
                    audio_path = await self._generate_preview_audio(text, voice, speed)

                    # 在主线程中播放音频
                    from PySide6.QtCore import QMetaObject, Qt, Q_ARG
                    QMetaObject.invokeMethod(
                        self,
                        "_play_preview_audio_slot",
                        Qt.QueuedConnection,
                        Q_ARG(str, audio_path)
                    )

                except Exception as e:
                    # 在主线程中显示错误
                    from PySide6.QtCore import QMetaObject, Qt, Q_ARG
                    QMetaObject.invokeMethod(
                        self,
                        "on_preview_failed",
                        Qt.QueuedConnection,
                        Q_ARG(str, str(e))
                    )

            # 在新线程中运行异步任务
            def run_async_task():
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                loop.run_until_complete(preview_task())
                loop.close()

            preview_thread = Thread(target=run_async_task)
            preview_thread.start()

        except Exception as e:
            print(f"❌ 语音试听失败: {e}")
            self._reset_preview_state()
            AppleMessageBox.show_warning(self, "试听失败", f"语音试听失败: {e}")

    def stop_voice_test(self):
        """停止语音预览"""
        print("🛑 停止语音预览")

        # 立即停止媒体播放器
        if hasattr(self, '_preview_media_player') and self._preview_media_player:
            try:
                self._preview_media_player.stop()
                self._preview_media_player.setSource(QUrl())  # 清空媒体源
                print("✅ 媒体播放器已停止")
            except Exception as e:
                print(f"⚠️ 停止媒体播放器时出错: {e}")

        # 重置状态
        self._reset_preview_state()

        # 清理预览文件（延迟清理以避免文件占用冲突）
        from PySide6.QtCore import QTimer
        QTimer.singleShot(500, self._cleanup_preview_file)

    def on_speed_changed(self, value):
        """语速滑块值改变时的处理"""
        speed_value = value / 100.0
        self.speed_value_label.setText(f"{speed_value:.1f}x")

        # 如果正在试听，可以实时调整语速
        if hasattr(self, 'voice_test_btn') and self._preview_in_progress:
            print(f"语速调整为: {speed_value:.1f}x")
            # 停止当前试听并重新开始
            self.stop_voice_test()
            self.start_voice_test()

    def on_language_changed(self, language_name):
        """当语言选择改变时更新角色列表"""
        try:
            print(f"🔄 语言改变为: {language_name}，正在更新角色列表...")
            # 直接调用更新角色列表的方法
            self.update_voice_list()
        except Exception as e:
            print(f"❌ 更新角色列表失败: {e}")
            import traceback
            traceback.print_exc()

    def on_engine_changed(self, engine_display_name):
        """当TTS引擎改变时更新语言和角色列表"""
        try:
            print(f"🔄 正在切换到TTS引擎: {engine_display_name}")

            # 避免重复触发
            self.voice_channel_combo.blockSignals(True)

            # 从显示名称中提取实际引擎名称
            engine_name = None
            if "Edge TTS" in engine_display_name:
                engine_name = "edge_tts"
            elif "Azure TTS" in engine_display_name:
                engine_name = "azure_tts"

            # 恢复信号
            self.voice_channel_combo.blockSignals(False)

            if not engine_name:
                print("⚠️ 未识别的TTS引擎")
                return

            # 获取TTS管理器
            from plugins.tts.tts_manager_plugin import TtsManagerPlugin
            tts_manager = TtsManagerPlugin()

            # 初始化TTS管理器
            from core.config_manager import get_config_manager
            config_manager = get_config_manager()
            tts_config = config_manager.get_tts_config()
            tts_manager.initialize(tts_config)

            # 如果是Azure TTS，检查API密钥配置
            if engine_name == "azure_tts":
                # 检查配置是否有API密钥
                from core.config_manager import get_config_manager
                config_manager = get_config_manager()
                azure_key = config_manager.get("api_keys.azure_tts_key", "")

                if not azure_key:
                    from PySide6.QtWidgets import QMessageBox
                    QMessageBox.warning(self, "Azure TTS未配置",
                                        "请先在API设置中配置Azure TTS的API密钥")
                    # 切换回Edge TTS
                    self.voice_channel_combo.setCurrentText("Edge TTS (免费)")
                    return

            # 切换TTS引擎
            try:
                if tts_manager.set_current_engine(engine_name):
                    print(f"✅ 切换到TTS引擎成功: {engine_name}")
                    # 更新语言列表
                    self.update_language_list()
                else:
                    print(f"❌ 切换TTS引擎失败: {engine_name}")
                    # 如果切换失败，回退到Edge TTS，但不再递归调用on_engine_changed
                    self.voice_channel_combo.blockSignals(True)
                    self.voice_channel_combo.setCurrentText("Edge TTS (免费)")
                    self.voice_channel_combo.blockSignals(False)
                    # 直接使用Edge TTS更新
                    tts_manager.set_current_engine("edge_tts")
                    self.update_language_list()
            except Exception as e:
                print(f"❌ 切换引擎出错: {e}")
                import traceback
                traceback.print_exc()
                # 下面的else不会执行，因为已经在try中处理了失败情况
        except Exception as e:
            print(f"❌ 切换TTS引擎时出错: {e}")
            import traceback
            traceback.print_exc()

    def update_language_list(self):
        """更新语言列表"""
        try:
            # 获取TTS管理器
            from plugins.tts.tts_manager_plugin import TtsManagerPlugin
            tts_manager = TtsManagerPlugin()

            # 初始化TTS管理器
            from core.config_manager import get_config_manager
            config_manager = get_config_manager()
            tts_config = config_manager.get_tts_config()
            tts_manager.initialize(tts_config)

            # 获取当前引擎
            engine_display_name = self.voice_channel_combo.currentText()
            engine_name = None
            if "Edge TTS" in engine_display_name:
                engine_name = "edge_tts"
            elif "Azure TTS" in engine_display_name:
                engine_name = "azure_tts"

            if not engine_name:
                print("⚠️ 未识别的TTS引擎")
                return

            # 确保当前引擎设置正确
            tts_manager.set_current_engine(engine_name)

            # 获取当前引擎支持的语言
            supported_languages = tts_manager.get_supported_languages()
            print(f"📋 获取到支持的语言列表: {supported_languages}")

            if not supported_languages:
                print("⚠️ 没有找到支持的语言")
                return

            # 清空语言列表
            self.languages_combo.clear()

            # 添加语言到下拉框
            language_list = list(supported_languages.values())
            self.languages_combo.addItems(language_list)

            # 暂时阻断信号避免重复触发
            self.languages_combo.blockSignals(True)

            # 尝试选择中文
            for i in range(self.languages_combo.count()):
                lang_name = self.languages_combo.itemText(i)
                if "中文" in lang_name or "Chinese" in lang_name:
                    self.languages_combo.setCurrentIndex(i)
                    print(f"✅ 选择语言: {lang_name}")
                    break

            # 恢复信号连接
            self.languages_combo.blockSignals(False)

            # 如果没有找到中文，选择第一个
            if self.languages_combo.currentIndex() == -1 and self.languages_combo.count() > 0:
                # 这里会触发on_language_changed信号，所以不需要单独调用update_voice_list()
                self.languages_combo.setCurrentIndex(0)
                print(f"✅ 选择第一个语言: {self.languages_combo.currentText()}")
            else:
                # 只有在语言没有变化时才需要手动更新角色列表
                self.update_voice_list()

        except Exception as e:
            print(f"❌ 更新语言列表失败: {e}")
            import traceback
            traceback.print_exc()

    def update_voice_list(self):
        """更新角色列表"""
        try:
            from plugins.tts.tts_manager_plugin import TtsManagerPlugin
            tts_manager = TtsManagerPlugin()
            from core.config_manager import get_config_manager
            config_manager = get_config_manager()
            tts_config = config_manager.get_tts_config()
            tts_manager.initialize(tts_config)
            engine_display_name = self.voice_channel_combo.currentText()
            engine_name = None
            if "Edge TTS" in engine_display_name:
                engine_name = "edge_tts"
            elif "Azure TTS" in engine_display_name:
                engine_name = "azure_tts"
            if not engine_name:
                print("⚠️ 未识别的TTS引擎")
                return
            tts_manager.set_current_engine(engine_name)
            print(f"🔧 已将当前引擎设置为 {engine_display_name}")
            current_lang_name = self.languages_combo.currentText()
            languages = tts_manager.get_supported_languages()
            current_lang_code = None
            for code, name in languages.items():
                if name == current_lang_name:
                    current_lang_code = code
                    break
            if not current_lang_code:
                print(f"⚠️ 未找到语言代码: {current_lang_name}")
                return
            voices = tts_manager.get_available_voices(current_lang_code)
            print(f"📋 获取到可用声音列表: {voices}")
            if not voices:
                print(f"⚠️ 语言 {current_lang_name} 没有可用角色")
                return
            self.voice_role_combo.clear()
            # voices: {voice_id: 显示名}
            for voice_id, display_name in voices.items():
                self.voice_role_combo.addItem(display_name, voice_id)
            if self.voice_role_combo.count() > 0:
                self.voice_role_combo.setCurrentIndex(0)
        except Exception as e:
            print(f"❌ 更新角色列表失败: {e}")
            import traceback
            traceback.print_exc()

    # 修改试听时获取voice的方式
    def start_voice_test(self):
        """开始语音试听"""
        try:
            if not hasattr(self, 'voice_test_btn') or not self.voice_test_btn:
                print("警告：voice_test_btn 不存在，可能未正确初始化")
                return
            self._preview_in_progress = True
            self.voice_test_btn.setEnabled(False)
            self.voice_test_btn.setText("生成中...")
            engine_name = None
            engine_display_name = self.voice_channel_combo.currentText()
            if "Edge TTS" in engine_display_name:
                engine_name = "edge_tts"
            elif "Azure TTS" in engine_display_name:
                engine_name = "azure_tts"
            # 优先用voice_role_combo.currentData()
            voice = self.voice_role_combo.currentData()
            if not voice:
                voice = self.voice_role_combo.currentText()
            if not voice:
                voice = "zh-CN-XiaoxiaoNeural"
            speed = self.speed_slider.value() / 100.0
            text = "欢迎使用FlipTalk Ai，支持多种语音设置。"
            print(f"🎵 开始试听 - 引擎: {engine_name}, 声音: {voice}, 语速: {speed}x")
            print(f"📝 试听文本: {text}")
            import asyncio
            from threading import Thread
            async def preview_task():
                try:
                    audio_path = await self._generate_preview_audio(text, voice, speed)
                    from PySide6.QtCore import QMetaObject, Qt, Q_ARG
                    QMetaObject.invokeMethod(
                        self,
                        "_play_preview_audio_slot",
                        Qt.QueuedConnection,
                        Q_ARG(str, audio_path)
                    )
                except Exception as e:
                    from PySide6.QtCore import QMetaObject, Qt, Q_ARG
                    QMetaObject.invokeMethod(
                        self,
                        "on_preview_failed",
                        Qt.QueuedConnection,
                        Q_ARG(str, str(e))
                    )

            def run_async_task():
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                loop.run_until_complete(preview_task())
                loop.close()

            preview_thread = Thread(target=run_async_task)
            preview_thread.start()
        except Exception as e:
            print(f"❌ 语音试听失败: {e}")
            self._reset_preview_state()
            AppleMessageBox.show_warning(self, "试听失败", f"语音试听失败: {e}")

    def _reset_preview_state(self):
        """重置预览状态"""
        self._preview_in_progress = False
        if hasattr(self, 'voice_test_btn'):
            self.voice_test_btn.setText("试听")
            self.voice_test_btn.setEnabled(True)
            self.voice_test_btn.setChecked(False)  # 重置按钮选中状态，恢复默认背景色

    def _cleanup_preview_file(self):
        """清理预览音频文件"""
        if self._preview_audio_path:
            try:
                if os.path.exists(self._preview_audio_path):
                    os.unlink(self._preview_audio_path)
                    print(f"🧹 已清理预览音频文件: {self._preview_audio_path}")
            except Exception as e:
                print(f"⚠️ 清理预览文件失败: {e}")
            finally:
                self._preview_audio_path = None

    @Slot(str)
    def _play_preview_audio_slot(self, audio_path: str):
        """播放预览音频的槽方法"""
        self._play_preview_audio(audio_path)

    def _play_preview_audio(self, audio_path: str):
        """播放预览音频"""
        try:
            # 清理之前的音频
            self._cleanup_preview_file()

            # 保存新的音频路径
            self._preview_audio_path = audio_path

            # 初始化播放器
            if not self._preview_media_player:
                self._preview_media_player = QMediaPlayer()
                self._preview_audio_output = QAudioOutput()
                self._preview_media_player.setAudioOutput(self._preview_audio_output)

                # 连接信号
                self._preview_media_player.mediaStatusChanged.connect(self._on_preview_status_changed)
                self._preview_media_player.errorOccurred.connect(self._on_preview_error)

            # 设置音频文件
            self._preview_media_player.setSource(QUrl.fromLocalFile(audio_path))

            # 开始播放
            self._preview_media_player.play()

            # 更新按钮状态
            self.voice_test_btn.setText("停止")
            self.voice_test_btn.setEnabled(True)
            self.voice_test_btn.setChecked(True)  # 设置为选中状态，显示红色背景

        except Exception as e:
            self.on_preview_failed(f"播放预览失败: {e}")

    def _on_preview_status_changed(self, status):
        """处理预览状态变化"""
        if status == QMediaPlayer.MediaStatus.EndOfMedia:
            self._reset_preview_state()

    def _on_preview_error(self, error, error_string):
        """处理预览错误"""
        self.on_preview_failed(f"预览播放错误: {error_string}")

    def on_preview_failed(self, error_message: str):
        """处理预览失败"""
        print(f"❌ 试听失败: {error_message}")
        self._reset_preview_state()
        from PySide6.QtWidgets import QMessageBox
        QMessageBox.warning(self, "预览失败", error_message)

    async def _generate_preview_audio(self, text: str, voice: str, speed: float) -> str:
        """生成预览音频"""
        try:
            print(f"📢 准备生成试听音频，使用声音: {voice}")

            # 获取TTS管理器
            from plugins.tts.tts_manager_plugin import TtsManagerPlugin
            tts_manager = TtsManagerPlugin()

            # 初始化TTS管理器
            from core.config_manager import get_config_manager
            config_manager = get_config_manager()
            tts_config = config_manager.get_tts_config()
            tts_manager.initialize(tts_config)

            # 获取当前引擎
            engine_display_name = self.voice_channel_combo.currentText()
            engine_name = None
            if "Edge TTS" in engine_display_name:
                engine_name = "edge_tts"
            elif "Azure TTS" in engine_display_name:
                engine_name = "azure_tts"

            if not engine_name:
                raise Exception("未识别的TTS引擎")

            # 确保当前引擎设置正确
            tts_manager.set_current_engine(engine_name)

            # 使用TTS管理器的preview_voice方法
            import asyncio
            loop = asyncio.get_event_loop()

            # 在线程池中运行同步的preview_voice方法
            audio_path = await loop.run_in_executor(
                None,
                tts_manager.preview_voice,
                text, voice, speed
            )

            if not audio_path:
                raise Exception("预览音频生成失败")

            return audio_path

        except Exception as e:
            raise Exception(f"生成预览音频时出错: {e}")

    def browse_output_path(self):
        """浏览输出路径"""
        from PySide6.QtWidgets import QFileDialog
        import os

        # 获取当前输入框中的路径作为起始目录
        current_path = self.output_path_edit.text().strip()
        if not current_path or not os.path.exists(current_path):
            # 如果当前路径为空或不存在，使用项目的output目录作为起始目录
            current_path = self.get_default_output_path()

        folder_path = QFileDialog.getExistingDirectory(
            self,
            "选择输出目录",
            current_path
        )
        if folder_path:
            self.output_path_edit.setText(folder_path)
            print(f"输出路径已设置为: {folder_path}")

    def get_default_output_path(self):
        """获取默认的输出路径"""
        import os
        # 获取项目根目录
        project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        output_dir = os.path.join(project_root, "output")

        # 如果output目录不存在，创建它
        if not os.path.exists(output_dir):
            try:
                os.makedirs(output_dir)
                print(f"创建输出目录: {output_dir}")
            except Exception as e:
                print(f"创建输出目录失败: {e}")
                # 如果创建失败，使用用户的Documents目录
                output_dir = os.path.join(os.path.expanduser("~"), "Documents", "FlipTalk_Output")
                if not os.path.exists(output_dir):
                    try:
                        os.makedirs(output_dir)
                    except Exception as e2:
                        print(f"创建备用输出目录失败: {e2}")
                        # 最后的备选方案，使用用户主目录
                        output_dir = os.path.expanduser("~")

        return output_dir

    # 重写closeEvent方法，确保关闭窗口时清理资源
    def closeEvent(self, event):
        """关闭窗口时清理资源"""
        if hasattr(self, 'stop_voice_test'):
            self.stop_voice_test()
        super().closeEvent(event)

    def on_single_voiceover_completed(self, row, subtitle_data, success, result):
        """单个字幕配音完成回调"""
        try:
            if success:
                audio_path = result
                print(f"✅ 第 {row + 1} 行字幕配音成功: {os.path.basename(audio_path)}")

                # 检测音频重叠和优化
                self.process_single_audio_optimization(row, subtitle_data, audio_path)

            else:
                error_msg = result
                print(f"❌ 第 {row + 1} 行字幕配音失败: {error_msg}")
                self.update_subtitle_status(row, "❌配音失败", QColor("#E74C3C"))  # 红色

                AppleMessageBox.show_error(
                    self, "配音失败",
                    f"第 {row + 1} 条字幕配音失败:\n{error_msg}"
                )

        except Exception as e:
            print(f"❌ 处理配音完成回调失败: {e}")
            self.update_subtitle_status(row, "❌配音失败", QColor("#E74C3C"))

    def on_single_voiceover_error(self, row, error_msg):
        """单个字幕配音错误回调"""
        try:
            print(f"❌ 第 {row + 1} 行字幕配音错误: {error_msg}")
            self.update_subtitle_status(row, "❌配音失败", QColor("#E74C3C"))  # 红色

            QMessageBox.critical(
                self, "配音错误",
                f"第 {row + 1} 条字幕配音出错:\n{error_msg}"
            )

        except Exception as e:
            print(f"❌ 处理配音错误回调失败: {e}")

    def process_single_audio_optimization(self, row, subtitle_data, audio_path):
        """处理单个音频的优化（重叠检测和加速）"""
        try:
            print(f"🔍 开始检测第 {row + 1} 行音频优化...")

            # 检查音频文件是否存在
            if not os.path.exists(audio_path):
                print(f"❌ 音频文件不存在: {audio_path}")
                self.update_subtitle_status(row, "❌配音失败", QColor("#E74C3C"))
                return

            # 导入音频处理库
            try:
                from pydub import AudioSegment
            except ImportError:
                print("⚠️ 音频处理库不可用，跳过优化检测")
                self.update_subtitle_status(row, "已配音", QColor("#9B59B6"))  # 紫色
                return

            # 获取音频时长
            audio = AudioSegment.from_wav(audio_path)
            audio_duration = len(audio) / 1000.0  # 转换为秒
            subtitle_duration = subtitle_data['duration']

            print(f"📊 音频时长: {audio_duration:.2f}秒, 字幕时长: {subtitle_duration:.2f}秒")

            # 计算所需加速比例
            if audio_duration > subtitle_duration:
                required_speedup = audio_duration / subtitle_duration
                print(f"⚡ 需要加速比例: {required_speedup:.3f}x")

                MAX_SPEED_UP = 1.2  # 最大推荐加速比例（仅用于警告）

                # 显示加速警告（如果超过推荐值）
                if required_speedup > MAX_SPEED_UP:
                    print(f"⚠️ 第 {row + 1} 行加速比例 {required_speedup:.2f}x 超过推荐值 {MAX_SPEED_UP}x，但仍会应用加速")

                # 无论加速比例多少，都尝试应用加速
                print(f"🟡 应用音频加速: {required_speedup:.3f}x")
                success = self.apply_single_audio_speedup(audio_path, required_speedup)

                if success:
                    if required_speedup > MAX_SPEED_UP:
                        # 超过推荐值但加速成功，标记为需要用户确认
                        self.update_subtitle_status(row, f"已配音({required_speedup:.2f}x)", QColor("#FF8C00"))  # 橙色
                        print(f"✅ 第 {row + 1} 行音频加速成功（超速）")
                    else:
                        # 正常加速
                        self.update_subtitle_status(row, "已配音", QColor("#9B59B6"))  # 紫色
                        print(f"✅ 第 {row + 1} 行音频加速成功")
                else:
                    self.update_subtitle_status(row, "❌配音失败", QColor("#E74C3C"))
                    print(f"❌ 第 {row + 1} 行音频加速失败")
            else:
                # 时长合适
                print(f"✅ 第 {row + 1} 行音频时长合适")
                self.update_subtitle_status(row, "已配音", QColor("#9B59B6"))  # 紫色

        except Exception as e:
            print(f"❌ 音频优化处理失败: {e}")
            self.update_subtitle_status(row, "已配音", QColor("#9B59B6"))  # 紫色（至少配音成功了）

    def apply_single_audio_speedup(self, audio_path, speedup_ratio):
        """应用单个音频加速"""
        try:


            from pydub import AudioSegment
            import tempfile
            import os
            print(f"🔄 开始音频加速: {os.path.basename(audio_path)}, 比例: {speedup_ratio:.3f}x")
            # 备份原始文件
            backup_path = audio_path + ".backup"
            import shutil
            shutil.copy2(audio_path, backup_path)

            # 使用ffmpeg加速音频（通过临时文件）
            try:
                import subprocess

                # 创建临时文件
                temp_fd, temp_path = tempfile.mkstemp(suffix='.wav')
                os.close(temp_fd)

                # 构建ffmpeg命令
                cmd = [
                    "ffmpeg", "-y",
                    "-i", audio_path,
                    "-filter:a", f"atempo={speedup_ratio}",
                    "-vn", temp_path
                ]

                # 执行命令
                process = subprocess.Popen(
                    cmd,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    universal_newlines=True
                )

                # 等待进程完成
                stdout, stderr = process.communicate()

                if process.returncode == 0 and os.path.exists(temp_path):
                    # 成功，替换原文件
                    shutil.move(temp_path, audio_path)
                    print(f"✅ 音频加速成功: {speedup_ratio:.3f}x (ffmpeg)")
                    return True
                else:
                    print(f"⚠️ ffmpeg加速失败，尝试使用pydub: {stderr}")
                    # 如果临时文件存在，删除它
                    if os.path.exists(temp_path):
                        os.remove(temp_path)
                    # 继续使用pydub方法
            except Exception as e:
                print(f"⚠️ ffmpeg加速失败，尝试使用pydub: {e}")
                # 继续使用pydub方法

            # 使用pydub加速音频
            audio = AudioSegment.from_wav(audio_path)
            audio_speedup = audio.speedup(playback_speed=speedup_ratio)
            audio_speedup.export(audio_path, format="wav")

            print(f"✅ 音频加速成功: {speedup_ratio:.3f}x (pydub)")
            return True

        except Exception as e:
            print(f"❌ 音频加速失败: {e}")
            return False


class APISettingsArea(QWidget):
    """
    API设置区域
    支持不同宽度的API配置界面，可用于右侧区域或全宽显示
    """

    def __init__(self, width=612):
        super().__init__()
        self.area_width = width  # 支持自定义宽度
        self.setFixedSize(width, 776)
        self.setup_ui()

    def setup_ui(self):
        """初始化API设置区域UI"""
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)

        # 创建背景Frame - 深灰色背景，圆角30px
        background_frame = QFrame()
        background_frame.setFixedSize(self.area_width, 776)
        background_frame.setStyleSheet(StyleManager.get_frame_style())

        # Frame内部布局
        frame_layout = QVBoxLayout(background_frame)
        # 根据宽度调整内边距
        margin = 40 if self.area_width > 800 else 20
        frame_layout.setContentsMargins(margin, 20, margin, 20)
        frame_layout.setSpacing(15)

        # 顶部状态显示区域
        status_area = self.create_status_area()
        frame_layout.addWidget(status_area)

        # API设置区域
        api_settings = self.create_api_settings()
        frame_layout.addWidget(api_settings)

        # 将背景Frame添加到主布局
        main_layout.addWidget(background_frame)

    def create_status_area(self):
        """创建顶部状态显示区域"""
        # 根据宽度决定是否显示状态区域
        if self.area_width <= 800:
            # 窄版本：显示简化的状态区域
            status_container = QWidget()
            status_container.setFixedHeight(50)

            status_layout = QVBoxLayout(status_container)
            status_layout.setContentsMargins(30, 30, 30, 30)
            status_layout.setSpacing(10)  # 减小间距

            # 当前选择标题
            title_label = QLabel("当前选择：")
            title_label.setStyleSheet(StyleManager.get_label_style(font_size=20, color='#FFFFFF'))
            title_label.setProperty("font-weight", "bold")
            status_layout.addWidget(title_label)

            # 状态信息布局
            status_info_layout = QVBoxLayout()
            status_info_layout.setSpacing(8)

            # 语音识别状态
            speech_layout = QHBoxLayout()
            speech_label = QLabel("语音识别：")
            speech_label.setFixedWidth(100)
            speech_label.setStyleSheet(StyleManager.get_label_style(font_size=16))
            speech_value = QLabel("WhisperX（本地）")
            speech_value.setStyleSheet(StyleManager.get_label_style(color='#2B9D7C', font_size=16))
            speech_layout.addWidget(speech_label)
            speech_layout.addWidget(speech_value)
            speech_layout.addStretch()

            # 翻译渠道状态
            translate_layout = QHBoxLayout()
            translate_label = QLabel("翻译渠道：")
            translate_label.setFixedWidth(100)
            translate_label.setStyleSheet(StyleManager.get_label_style(font_size=16))
            translate_value = QLabel("deepl pro")
            translate_value.setStyleSheet(StyleManager.get_label_style(color='#2B9D7C', font_size=16))
            translate_layout.addWidget(translate_label)
            translate_layout.addWidget(translate_value)
            translate_layout.addStretch()

            # TTS配音状态
            tts_layout = QHBoxLayout()
            tts_label = QLabel("TTS配音：")
            tts_label.setFixedWidth(100)
            tts_label.setStyleSheet(StyleManager.get_label_style(font_size=16))
            tts_value = QLabel("Azure TTS")
            tts_value.setStyleSheet(StyleManager.get_label_style(color='#2B9D7C', font_size=16))
            tts_layout.addWidget(tts_label)
            tts_layout.addWidget(tts_value)
            tts_layout.addStretch()

            status_info_layout.addLayout(speech_layout)
            status_info_layout.addLayout(translate_layout)
            status_info_layout.addLayout(tts_layout)

            status_layout.addLayout(status_info_layout)

            return status_container
        else:
            # 宽版本：显示带黑色Frame的状态区域
            # 创建状态区域容器
            status_container = QWidget()
            status_container.setFixedHeight(210)

            # 状态区域主布局
            container_layout = QVBoxLayout(status_container)
            container_layout.setContentsMargins(0, 0, 0, 0)
            container_layout.setSpacing(0)

            # 创建黑色背景Frame
            status_frame = QFrame()
            status_frame.setFixedSize(self.area_width - 80, 210)  # 减去左右边距
            status_frame.setStyleSheet("""
                QFrame {
                    background-color: #14161A;
                    border-radius: 30px;
                    border: none;
                }
            """)

            # Frame内部布局
            frame_layout = QVBoxLayout(status_frame)
            frame_layout.setContentsMargins(40, 0, 40, 25)  # 调整边距
            frame_layout.setSpacing(10)

            # 顶部标题区域
            title_container = QWidget()
            title_layout = QHBoxLayout(title_container)
            title_layout.setContentsMargins(0, 0, 0, 0)

            # 主标题
            title_label = QLabel("当前配置状态")
            title_label.setStyleSheet("""
                QLabel {
                    color: #FFFFFF;
                    font-size: 22px;
                    font-weight: bold;
                    background-color: transparent;
                    border: none;
                }
            """)

            # 状态指示器
            status_indicator = QLabel("●")
            status_indicator.setStyleSheet("""
                QLabel {
                    color: #2B9D7C;
                    font-size: 20px;
                    background-color: transparent;
                    border: none;
                }
            """)

            title_layout.addWidget(title_label)
            title_layout.addWidget(status_indicator)
            title_layout.addStretch()

            frame_layout.addWidget(title_container)

            # 状态卡片容器
            cards_container = QWidget()
            cards_layout = QHBoxLayout(cards_container)
            cards_layout.setContentsMargins(0, 0, 0, 0)
            cards_layout.setSpacing(20)

            # 语音识别卡片
            speech_card = self.create_status_card("🎤", "语音识别", "WhisperX（本地）", "#FF6B6B")
            cards_layout.addWidget(speech_card)

            # 翻译渠道卡片
            translate_card = self.create_status_card("🌐", "翻译渠道", "DeepL Pro", "#4ECDC4")
            cards_layout.addWidget(translate_card)

            # TTS配音卡片
            tts_card = self.create_status_card("🔊", "TTS配音", "Azure TTS", "#45B7D1")
            cards_layout.addWidget(tts_card)

            frame_layout.addWidget(cards_container)

            # 将Frame添加到容器布局中，并水平居中
            container_layout.addWidget(status_frame, 0, Qt.AlignCenter)

            return status_container

    def create_status_card(self, icon, title, value, accent_color):
        """创建美观的状态卡片"""
        card = QFrame()
        card.setFixedSize(320, 100)
        card.setStyleSheet(f"""
            QFrame {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(42, 42, 42, 0.9),
                    stop:0.5 rgba(35, 35, 35, 0.8),
                    stop:1 rgba(27, 30, 36, 0.9));
                border: 0px solid rgba(255, 255, 255, 0.1);
                border-radius: 15px;
            }}
        """)

        card_layout = QHBoxLayout(card)
        card_layout.setContentsMargins(15, 12, 15, 12)
        card_layout.setSpacing(12)

        # 左侧图标区域
        icon_container = QFrame()
        icon_container.setFixedSize(50, 50)
        icon_container.setStyleSheet(f"""
            QFrame {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {accent_color},
                    stop:1 rgba({StyleManager.hex_to_rgb(accent_color)}, 0.7));
                border-radius: 25px;
                border: none;
            }}
        """)

        icon_layout = QVBoxLayout(icon_container)
        icon_layout.setContentsMargins(0, 0, 0, 0)

        icon_label = QLabel(icon)
        icon_label.setStyleSheet(StyleManager.get_label_style(color='#FFFFFF', font_size=24))
        icon_label.setAlignment(Qt.AlignCenter)
        icon_layout.addWidget(icon_label)

        card_layout.addWidget(icon_container)

        # 右侧文本区域
        text_container = QWidget()
        text_layout = QVBoxLayout(text_container)
        text_layout.setContentsMargins(0, 0, 0, 0)
        text_layout.setSpacing(2)

        # 标题
        title_label = QLabel(title)
        title_label.setStyleSheet(StyleManager.get_label_style(color='rgba(255, 255, 255, 0.7)', font_size=14))

        # 值
        value_label = QLabel(value)
        value_label.setStyleSheet(StyleManager.get_label_style(color=accent_color, font_size=16))
        value_label.setProperty("font-weight", "bold")

        text_layout.addWidget(title_label)
        text_layout.addWidget(value_label)

        card_layout.addWidget(text_container)

        return card

    def create_api_settings(self):
        """创建API设置区域"""
        settings_container = QWidget()

        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setStyleSheet("""
            QScrollArea {
                background-color: #1B1E24;
                border: none;
            }
            QScrollBar:vertical {
                background-color: #14161A; /* 设置垂直滚动条的背景颜色为 #14161A (一种深灰色) */
                width: 0px;                /* 将垂直滚动条的宽度设置为0像素，使其在视觉上不可见，达到隐藏滚动条的效果 */
                /* background-color: #14161A; */ /* 此为重复设置，通常在同一规则中，属性的最后一次声明生效，这条可以移除 */
            }
            QScrollBar::handle:vertical {
                background-color: #14161A;
            }
            QScrollBar::add-line:vertical {
                background-color: #14161A; /* 设置滚动条的添加线（即滚动条的末端）的背景颜色为 #14161A */
            }
            QScrollBar::sub-line:vertical {
                background-color: #14161A;
            }
        """)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAlwaysOff)

        # 滚动内容
        scroll_content = QWidget()
        scroll_content.setStyleSheet("""
            QWidget {
                background-color: #1B1E24;
            }
        """)
        scroll_layout = QVBoxLayout(scroll_content)
        scroll_layout.setSpacing(15)  # 减小卡片间距
        scroll_layout.setAlignment(Qt.AlignTop | Qt.AlignHCenter)  # 顶部和水平居中对齐
        scroll_layout.setContentsMargins(0, 10, 0, 20)  # 不设置左右边距，让卡片自己居中

        # OpenAI API设置卡片
        openai_section = self.create_modern_setting_section(
            "🤖 OpenAI API设置",
            "配置OpenAI API密钥和服务地址",
            [
                ("API密钥", "sk-...", "请输入您的OpenAI API密钥"),
                ("API基础URL", "https://api.openai.com", "可选，自定义API服务地址")
            ],
            "#FF6B6B"
        )
        scroll_layout.addWidget(openai_section)

        # Azure TTS设置卡片
        azure_section = self.create_azure_tts_setting_section()
        scroll_layout.addWidget(azure_section)

        # DeepL API设置卡片
        deepl_section = self.create_modern_setting_section(
            "🌐 DeepL API设置",
            "配置DeepL翻译API密钥",
            [
                ("API密钥", "", "请输入DeepL API密钥")
            ],
            "#4ECDC4"
        )
        scroll_layout.addWidget(deepl_section)

        # Google API设置卡片
        google_section = self.create_modern_setting_section(
            "📱 Google API设置",
            "配置Google翻译API密钥",
            [
                ("API密钥", "", "请输入Google API密钥")
            ],
            "#FFA726"
        )
        scroll_layout.addWidget(google_section)

        scroll_area.setWidget(scroll_content)

        # 设置滚动区域布局
        container_layout = QVBoxLayout(settings_container)
        container_layout.setContentsMargins(0, 0, 0, 0)
        container_layout.addWidget(scroll_area)

        return settings_container

    def create_azure_tts_setting_section(self):
        """创建Azure TTS设置卡片（支持自动保存）"""
        try:
            from core.config_manager import get_config_manager
            config_manager = get_config_manager()
        except ImportError:
            config_manager = None

        # 从配置中加载当前值
        current_key = config_manager.get("api_keys.azure_tts_key", "") if config_manager else ""
        current_region = config_manager.get("api_keys.azure_tts_region", "eastus") if config_manager else "eastus"

        accent_color = "#45B7D1"

        # 主卡片容器
        section_card = QFrame()
        card_width = self.area_width - 80
        section_card.setFixedWidth(card_width)
        section_card.setStyleSheet(f"""
            QFrame {{
                background-color: #14161A;
                border: 0px solid rgba(255, 255, 255, 0.1);
                border-radius: 30px;
                margin: 0px;
            }}
            QFrame:hover {{
                border: 0px solid rgba({StyleManager.hex_to_rgb(accent_color)}, 0.4);
            }}
        """)

        card_layout = QVBoxLayout(section_card)
        card_layout.setContentsMargins(25, 20, 25, 20)
        card_layout.setSpacing(15)

        # 头部区域
        header_container = QWidget()
        header_layout = QHBoxLayout(header_container)
        header_layout.setContentsMargins(0, 0, 0, 0)
        header_layout.setSpacing(15)

        # 左侧标题区域
        title_container = QWidget()
        header_container.setStyleSheet("background-color: #14161A;")
        title_layout = QVBoxLayout(title_container)
        title_layout.setContentsMargins(0, 0, 0, 0)
        title_layout.setSpacing(4)

        # 主标题
        title_label = QLabel("🔊 Azure TTS设置")
        title_label.setStyleSheet(f"""
            QLabel {{
                color: {accent_color};
                font-size: 18px;
                font-weight: bold;
                background-color: transparent;
                border: none;
            }}
        """)

        # 描述文字
        desc_label = QLabel("配置Azure TTS服务密钥和区域")
        desc_label.setStyleSheet("""
            QLabel {
                color: rgba(255, 255, 255, 0.6);
                font-size: 13px;
                background-color: #14161A;
                border: none;
            }
        """)
        desc_label.setWordWrap(False)

        title_layout.addWidget(title_label)
        title_layout.addWidget(desc_label)

        header_layout.addWidget(title_container)
        header_layout.addStretch()

        card_layout.addWidget(header_container)

        # 分隔线
        separator = QFrame()
        separator.setFixedHeight(1)
        separator.setStyleSheet(f"""
            QFrame {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 transparent,
                    stop:0.2 rgba({StyleManager.hex_to_rgb(accent_color)}, 0.4),
                    stop:0.8 rgba({StyleManager.hex_to_rgb(accent_color)}, 0.4),
                    stop:1 transparent);
                border: none;
                border-radius: 1px;
            }}
        """)
        card_layout.addWidget(separator)

        # 设置项容器
        settings_container = QWidget()
        settings_container.setStyleSheet("background-color: #14161A;")
        settings_layout = QVBoxLayout(settings_container)
        settings_layout.setContentsMargins(0, 0, 0, 0)
        settings_layout.setSpacing(12)

        # Azure TTS密钥输入
        key_field = self.create_smart_input_field(
            "Azure TTS密钥",
            current_key,
            "请输入Azure TTS服务密钥",
            accent_color,
            lambda value: self.save_azure_config("key", value) if config_manager else None
        )
        settings_layout.addWidget(key_field)

        # Azure 区域输入
        region_field = self.create_smart_input_field(
            "Azure 区域",
            current_region,
            "请输入Azure服务区域",
            accent_color,
            lambda value: self.save_azure_config("region", value) if config_manager else None
        )
        settings_layout.addWidget(region_field)

        card_layout.addWidget(settings_container)

        return section_card

    def create_smart_input_field(self, label_text, default_value, placeholder, accent_color, save_callback):
        """创建支持自动保存的智能输入字段"""
        field_container = QWidget()

        field_layout = QVBoxLayout(field_container)
        field_layout.setContentsMargins(0, 0, 0, 0)
        field_layout.setSpacing(6)

        # 字段标签
        label = QLabel(label_text)
        label.setStyleSheet(StyleManager.get_label_style(color='rgba(255, 255, 255, 0.9)', bg_color='#14161A'))
        label.setProperty("font-weight", "500")
        field_layout.addWidget(label)

        # 输入框容器
        input_container = QFrame()
        input_container.setFixedHeight(45)
        input_container.setStyleSheet(f"""
            QFrame {{
                background: #1B1E24;
                border: 1px solid rgba(255, 255, 255, 0.1);
                border-radius: 12px;
            }}
            QFrame:hover {{
                border: 2px solid rgba({StyleManager.hex_to_rgb(accent_color)}, 0.4);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(25, 25, 25, 0.9),
                    stop:1 rgba(30, 30, 30, 0.9));
            }}
        """)

        input_layout = QHBoxLayout(input_container)
        input_layout.setContentsMargins(12, 0, 12, 0)

        # 输入框
        input_field = QLineEdit(default_value)
        input_field.setPlaceholderText(placeholder)
        input_field.setStyleSheet(f"""
            QLineEdit {{
                background-color: #1B1E24;
                color: #FFFFFF;
                font-size: 14px;
                border: none;
                padding: 6px 0px;
            }}
            QLineEdit::placeholder {{
                color: rgba(255, 255, 255, 0.4);
            }}
            QLineEdit:focus {{
                color: {accent_color};
            }}
        """)

        # 连接自动保存信号
        if save_callback:
            input_field.textChanged.connect(save_callback)

        input_layout.addWidget(input_field)
        field_layout.addWidget(input_container)

        return field_container

    def save_azure_config(self, key_type, value):
        """保存Azure TTS配置"""
        try:
            from core.config_manager import get_config_manager
            config_manager = get_config_manager()
            success = config_manager.update_api_key("azure_tts", key_type, value)
            if success:
                print(f"Azure TTS {key_type} 配置已保存: {'***' if key_type == 'key' and value else value}")
        except ImportError:
            print("配置管理器不可用，无法保存配置")

    def create_modern_setting_section(self, title, description, settings, accent_color):
        """创建现代化的设置卡片"""
        # 主卡片容器
        section_card = QFrame()
        # 设置固定宽度，与状态区域保持一致
        card_width = self.area_width - 80  # 与状态Frame宽度一致
        section_card.setFixedWidth(card_width)
        section_card.setStyleSheet(f"""
            QFrame {{
                background-color: #14161A;
                border: 0px solid rgba(255, 255, 255, 0.1);
                border-radius: 30px;
                margin: 0px;
            }}
            QFrame:hover {{
                border: 0px solid rgba({StyleManager.hex_to_rgb(accent_color)}, 0.4);
            }}
        """)

        card_layout = QVBoxLayout(section_card)
        card_layout.setContentsMargins(25, 20, 25, 20)  # 减小内边距
        card_layout.setSpacing(15)  # 减小间距

        # 头部区域
        header_container = QWidget()
        header_layout = QHBoxLayout(header_container)
        header_layout.setContentsMargins(0, 0, 0, 0)
        header_layout.setSpacing(15)

        # 左侧标题区域
        title_container = QWidget()
        header_container.setStyleSheet("background-color: #14161A;")
        title_layout = QVBoxLayout(title_container)
        title_layout.setContentsMargins(0, 0, 0, 0)
        title_layout.setSpacing(4)  # 减小标题和描述间距

        # 主标题
        title_label = QLabel(title)
        title_label.setStyleSheet(f"""
            QLabel {{
                color: {accent_color};
                font-size: 18px;
                font-weight: bold;
                background-color: transparent;
                border: none;
            }}
        """)

        # 描述文字
        desc_label = QLabel(description)
        desc_label.setStyleSheet("""
            QLabel {
                color: rgba(255, 255, 255, 0.6);
                font-size: 13px;
                background-color: #14161A;
                border: none;
            }
        """)
        desc_label.setWordWrap(False)  # 禁用自动换行，保持一行显示

        title_layout.addWidget(title_label)
        title_layout.addWidget(desc_label)

        header_layout.addWidget(title_container)
        header_layout.addStretch()

        card_layout.addWidget(header_container)

        # 分隔线
        separator = QFrame()
        separator.setFixedHeight(1)  # 减小分隔线高度
        separator.setStyleSheet(f"""
            QFrame {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 transparent,
                    stop:0.2 rgba({StyleManager.hex_to_rgb(accent_color)}, 0.4),
                    stop:0.8 rgba({StyleManager.hex_to_rgb(accent_color)}, 0.4),
                    stop:1 transparent);
                border: none;
                border-radius: 1px;
            }}
        """)
        card_layout.addWidget(separator)

        # 设置项容器
        settings_container = QWidget()
        settings_container.setStyleSheet("background-color: #14161A;")
        settings_layout = QVBoxLayout(settings_container)
        settings_layout.setContentsMargins(0, 0, 0, 0)
        settings_layout.setSpacing(12)  # 减小设置项间距

        # 创建设置项
        for field_name, default_value, placeholder in settings:
            field_container = self.create_modern_input_field(field_name, default_value, placeholder, accent_color)
            settings_layout.addWidget(field_container)

        card_layout.addWidget(settings_container)

        return section_card

    def create_modern_input_field(self, label_text, default_value, placeholder, accent_color):
        """创建现代化的输入字段"""
        field_container = QWidget()

        field_layout = QVBoxLayout(field_container)
        field_layout.setContentsMargins(0, 0, 0, 0)
        field_layout.setSpacing(6)  # 减小标签和输入框间距

        # 字段标签
        label = QLabel(label_text)
        label.setStyleSheet(StyleManager.get_label_style(color='rgba(255, 255, 255, 0.9)', bg_color='#14161A'))
        label.setProperty("font-weight", "500")
        field_layout.addWidget(label)

        # 输入框容器
        input_container = QFrame()
        input_container.setFixedHeight(45)  # 减小输入框高度
        input_container.setStyleSheet(f"""
            QFrame {{
                background: #1B1E24; /* 设置输入框的背景颜色为 #14161A */
                border: 1px solid rgba(255, 255, 255, 0.1);
                border-radius: 12px;
            }}
            QFrame:hover {{
                border: 2px solid rgba({StyleManager.hex_to_rgb(accent_color)}, 0.4);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(25, 25, 25, 0.9),
                    stop:1 rgba(30, 30, 30, 0.9));
            }}
        """)

        input_layout = QHBoxLayout(input_container)
        input_layout.setContentsMargins(12, 0, 12, 0)  # 减小内边距

        # 输入框
        input_field = QLineEdit(default_value)

        input_field.setPlaceholderText(placeholder)
        input_field.setStyleSheet(f"""
            QLineEdit {{
                background-color: #1B1E24;
                color: #FFFFFF;
                font-size: 14px;
                border: none;
                padding: 6px 0px;
            }}
            QLineEdit::placeholder {{
                color: rgba(255, 255, 255, 0.4);
            }}
            QLineEdit:focus {{
                color: {accent_color};/* 设置输入框聚焦时的文字颜色为 accent_color */
            }}
        """)

        input_layout.addWidget(input_field)

        field_layout.addWidget(input_container)

        return field_container

    def create_setting_section(self, title, settings):
        """旧版本的设置区域组件（保持兼容性）"""
        return self.create_modern_setting_section(title, "", settings, "#2B9D7C")


class FlipTalkMainWindow(QMainWindow):
    """
    FlipTalk AI主窗口
    整合所有UI组件，提供完整的应用界面
    """

    def __init__(self):
        super().__init__()
        self.setWindowTitle("FlipTalk AI")
        self.setFixedSize(1440, 800)  # 设置窗口固定尺寸

        # 初始化动画对象和状态
        self.slide_animation = None
        self.is_animating = False  # 动画状态锁
        self.current_page = "首页"  # 当前页面状态

        # 自动化流程状态
        self.is_auto_processing = False  # 是否正在自动化处理
        self.auto_process_step = 0  # 当前自动化步骤
        self.voiceover_completed = False  # 配音是否完成

        # 初始化GPU检测器
        self.gpu_is_available = False
        self.gpu_name = "检测中..."
        try:
            from core.gpu_detector import GPUDetector
            self.gpu_detector = GPUDetector()
            self.gpu_detector.gpu_status_updated.connect(self.on_gpu_status_updated)
        except Exception as e:
            print(f"GPU检测器初始化失败: {e}")

        self.setup_ui()
        self.setup_style()

        # 连接字幕音频播放器信号
        self.setup_subtitle_audio_player()

        # 强制显示窗口
        self.setVisible(True)
        self.raise_()
        self.activateWindow()
        print("主窗口初始化完成并强制显示")

    def setup_subtitle_audio_player(self):
        """设置字幕音频播放器"""
        try:
            # 获取视频上传区域的字幕音频播放器
            if hasattr(self, 'video_upload_area') and hasattr(self.video_upload_area, 'subtitle_audio_player'):
                # 检查音频设备
                self.check_audio_device()

                # 确保信号连接正确建立
                player = self.video_upload_area.subtitle_audio_player

                # 重新连接信号到主窗口（确保信号能正确传递）
                try:
                    # 断开可能存在的旧连接
                    player.mediaStatusChanged.disconnect(self.on_subtitle_audio_status_changed)
                    player.playbackStateChanged.disconnect(self.on_subtitle_audio_playback_state_changed)
                    player.errorOccurred.disconnect(self.on_subtitle_audio_error)
                except:
                    pass  # 如果没有连接，忽略错误

                # 建立新的信号连接
                player.mediaStatusChanged.connect(self.on_subtitle_audio_status_changed)
                player.playbackStateChanged.connect(self.on_subtitle_audio_playback_state_changed)
                player.errorOccurred.connect(self.on_subtitle_audio_error)

                print("✅ 字幕音频播放器已准备就绪，信号连接已建立")
            else:
                print("⚠️ 未找到字幕音频播放器")
        except Exception as e:
            print(f"❌ 设置字幕音频播放器失败: {e}")
            import traceback
            traceback.print_exc()

    def check_audio_device(self):
        """检查音频设备状态"""
        try:
            from PySide6.QtMultimedia import QMediaDevices

            # 检查默认音频输出设备
            default_device = QMediaDevices.defaultAudioOutput()
            if default_device.isNull():
                print("⚠️ 没有检测到默认音频输出设备")

                # 获取所有可用音频设备
                available_devices = QMediaDevices.audioOutputs()
                if available_devices:
                    print(f"🔍 找到 {len(available_devices)} 个可用音频设备:")
                    for i, device in enumerate(available_devices):
                        print(f"  {i+1}. {device.description()}")

                    # 设置第一个可用设备
                    if hasattr(self, 'video_upload_area') and hasattr(self.video_upload_area, 'subtitle_audio_output'):
                        self.video_upload_area.subtitle_audio_output.setDevice(available_devices[0])
                        print(f"✅ 已设置音频设备: {available_devices[0].description()}")
                else:
                    print("❌ 没有找到任何可用的音频输出设备")
            else:
                print(f"✅ 默认音频设备: {default_device.description()}")

                # 确保使用默认设备
                if hasattr(self, 'video_upload_area') and hasattr(self.video_upload_area, 'subtitle_audio_output'):
                    self.video_upload_area.subtitle_audio_output.setDevice(default_device)

        except Exception as e:
            print(f"❌ 检查音频设备失败: {e}")
            import traceback
            traceback.print_exc()

    def start_video_synthesis(self, auto_start=False):
        """开始视频合成流程

        Args:
            auto_start (bool): 是否为自动启动（从字幕配音完成后自动调用），如果是则跳过确认对话框
        """
        try:
            # 检查是否有视频文件
            if not hasattr(self, 'current_video_path') or not self.current_video_path:
                AppleMessageBox.show_warning(self, "错误", "请先上传视频文件！")
                return

            # 检查是否有字幕数据
            if self.subtitles_table.rowCount() == 0:
                AppleMessageBox.show_warning(self, "错误", "请先提取字幕！")
                return

            # 检查是否有配音文件
            output_dir = self.parameter_panel.output_path_edit.text().strip()
            if not output_dir:
                AppleMessageBox.show_warning(self, "错误", "输出路径未设置！")
                return

            voiceover_dir = os.path.join(output_dir, "voiceover")
            if not os.path.exists(voiceover_dir):
                AppleMessageBox.show_warning(self, "错误", "配音文件不存在，请先完成字幕配音！")
                return

            # 只有在非自动启动时才显示确认对话框
            if not auto_start:
                # 确认开始合成
                reply = AppleMessageBox.show_question(
                    self, "确认视频合成",
                    "即将开始视频合成，将会：\n\n"
                    "1. 合并所有字幕配音为完整音频\n"
                    "2. 替换原视频的音频轨道\n"
                    "3. 生成最终的翻译视频\n\n"
                    "此过程可能需要几分钟时间，确定要开始吗？",
                    "开始合成", "取消"
                )

                if not reply:
                    return

            # 开始合成
            print("🎬 开始执行视频合成...")
            self.execute_video_synthesis()

        except Exception as e:
            print(f"❌ 开始视频合成失败: {e}")
            AppleMessageBox.show_error(self, "错误", f"开始视频合成失败: {e}")

    def execute_video_synthesis(self):
        """执行视频合成"""
        try:
            self.update_progress_display("正在准备视频合成...", 10)

            # 获取输出目录
            output_dir = self.parameter_panel.output_path_edit.text().strip()
            voiceover_dir = os.path.join(output_dir, "voiceover")

            # 创建合成输出目录
            synthesis_dir = os.path.join(output_dir, "synthesis")
            os.makedirs(synthesis_dir, exist_ok=True)

            # 第一步：合并字幕配音
            self.update_progress_display("正在合并字幕配音...", 30)
            combined_audio_path = self.merge_subtitle_voiceovers(voiceover_dir, synthesis_dir)

            if not combined_audio_path:
                self.reset_progress_display()
                QMessageBox.critical(self, "合成失败", "合并字幕配音失败！")
                return

            # 检查是否需要保留背景音乐
            keep_background_music = (
                hasattr(self, 'composition_checkboxes') and
                'keep_background_music' in self.composition_checkboxes and
                self.composition_checkboxes['keep_background_music'].isChecked()
            )

            final_audio_path = combined_audio_path

            if keep_background_music:
                # 第二步：混合背景音乐
                self.update_progress_display("正在混合背景音乐...", 50)
                final_audio_path = self.mix_background_music(combined_audio_path, synthesis_dir)

                if not final_audio_path:
                    print("⚠️ 背景音乐混合失败，使用纯配音音频")
                    final_audio_path = combined_audio_path

            # 第三步：替换视频音频
            progress_value = 70 if not keep_background_music else 80
            self.update_progress_display("正在替换视频音频...", progress_value)
            final_video_path = self.replace_video_audio(final_audio_path, synthesis_dir)

            if not final_video_path:
                self.reset_progress_display()
                QMessageBox.critical(self, "合成失败", "替换视频音频失败！")
                return

            # 完成
            self.update_progress_display("视频合成完成！", 100)

            # 显示结果
            AppleMessageBox.show_success(
                self, "视频合成完成",
                f"视频合成成功完成！\n\n"
                f"输出文件: {os.path.basename(final_video_path)}\n"
                f"保存位置: {synthesis_dir}\n\n"
                f"您可以在输出目录中找到最终的翻译视频。"
            )

            # 重置进度显示
            from PySide6.QtCore import QTimer
            QTimer.singleShot(2000, self.reset_progress_display)

        except Exception as e:
            self.reset_progress_display()
            error_msg = f"视频合成失败: {str(e)}"
            print(error_msg)
            AppleMessageBox.show_error(self, "视频合成失败", error_msg)

    def merge_subtitle_voiceovers(self, voiceover_dir, output_dir):
        """合并字幕配音为完整音频（基于 refer.py 的已验证方案）"""
        try:
            from pydub import AudioSegment
            import datetime

            print("🎵 开始合并字幕配音（使用已验证的 refer.py 方案）...")

            # 配置参数（与 refer.py 完全一致）
            MAX_SPEED_UP = 1.2  # 最大音频加速
            MIN_SPEED_UP = 1.05  # 最小音频加速
            MIN_GAP_DURATION = 0.1  # 最小间隔时间，单位秒

            # 首先检查配音目录中的实际文件
            print(f"🔍 检查配音目录: {voiceover_dir}")
            if not os.path.exists(voiceover_dir):
                print(f"❌ 配音目录不存在: {voiceover_dir}")
                return None

            actual_files = os.listdir(voiceover_dir)
            wav_files = [f for f in actual_files if f.endswith('.wav')]
            print(f"📁 找到 {len(wav_files)} 个音频文件:")
            for f in wav_files[:10]:  # 只显示前10个文件
                print(f"   - {f}")
            if len(wav_files) > 10:
                print(f"   ... 还有 {len(wav_files) - 10} 个文件")

            # 收集字幕时间信息和音频文件（模拟 refer.py 中的 voiceMapSrt）
            voice_map_data = []
            for row in range(self.subtitles_table.rowCount()):
                try:
                    # 获取时间信息
                    start_time_item = self.subtitles_table.item(row, 1)
                    end_time_item = self.subtitles_table.item(row, 2)

                    if start_time_item and end_time_item:
                        start_time_str = start_time_item.text()
                        end_time_str = end_time_item.text()

                        # 转换时间格式为秒数
                        start_seconds = self.parse_srt_time_to_seconds(start_time_str)
                        end_seconds = self.parse_srt_time_to_seconds(end_time_str)

                        # 查找对应的音频文件
                        audio_path = self.get_subtitle_audio_path(row)
                        if audio_path and os.path.exists(audio_path):
                            voice_map_data.append({
                                'index': row,
                                'start_seconds': start_seconds,
                                'end_seconds': end_seconds,
                                'audio_path': audio_path
                            })
                            print(f"📝 字幕 {row + 1}: {start_seconds:.2f}s - {end_seconds:.2f}s, 音频: {os.path.basename(audio_path)}")
                        else:
                            print(f"⚠️ 字幕 {row + 1} 缺少音频文件")

                except Exception as e:
                    print(f"⚠️ 跳过第 {row + 1} 行字幕，数据不完整: {e}")
                    continue

            if not voice_map_data:
                print("❌ 没有找到有效的字幕配音文件")
                return None

            # 按开始时间排序
            voice_map_data.sort(key=lambda x: x['start_seconds'])

            # 确定音频长度（完全按照 refer.py 的逻辑）
            last_item = voice_map_data[-1]
            duration = last_item['end_seconds'] * 1000  # 转换为毫秒

            # 计算最后一个音频的实际结束时间
            final_audio = AudioSegment.from_wav(last_item['audio_path'])
            final_audio_end = last_item['start_seconds'] * 1000 + final_audio.duration_seconds * 1000
            duration = max(duration, final_audio_end)

            print(f"📊 计算的总音频长度: {duration/1000:.2f}秒")

            # 初始化一个空的音频段（与 refer.py 完全一致）
            print("🔧 初始化空音频段...")
            combined = AudioSegment.silent(duration=int(duration))

            # 逐个添加字幕音频（完全按照 refer.py 的逻辑）
            print(f"🎵 开始处理 {len(voice_map_data)} 个音频文件...")

            for i in range(len(voice_map_data)):
                try:
                    item = voice_map_data[i]
                    print(f"🎵 处理音频 {i + 1}/{len(voice_map_data)}: {os.path.basename(item['audio_path'])}")

                    # 加载音频文件（与 refer.py 一致）
                    audio = AudioSegment.from_wav(item['audio_path'])
                    print(f"📊 原始音频: 长度={audio.duration_seconds:.2f}s, 音量={audio.dBFS:.1f}dB")

                    # 检查音频是否为空或静音
                    if audio.duration_seconds == 0:
                        print(f"⚠️ 音频 {i + 1} 文件为空，跳过")
                        continue

                    if audio.dBFS == float('-inf'):
                        print(f"⚠️ 音频 {i + 1} 为静音，跳过")
                        continue

                    # 去除头尾静音（与 refer.py 完全一致）
                    audio = audio.strip_silence(silence_thresh=-40, silence_len=100)
                    print(f"📊 去除静音后: 长度={audio.duration_seconds:.2f}s")

                    if audio.duration_seconds == 0:
                        print(f"⚠️ 音频 {i + 1} 去除静音后为空，跳过")
                        continue

                    # 计算音频位置（与 refer.py 一致，使用毫秒）
                    audio_position = item['start_seconds'] * 1000

                    # 检查重叠并加速（与 refer.py 完全一致的逻辑）
                    if i != len(voice_map_data) - 1:  # 不是最后一个音频
                        # 检查这一句的结尾到下一句的开头之间是否有静音，如果没有则需要缩小音频
                        audio_end_position = audio_position + audio.duration_seconds * 1000 + MIN_GAP_DURATION * 1000
                        audio_next_position = voice_map_data[i + 1]['start_seconds'] * 1000

                        if audio_next_position < audio_end_position:
                            # 计算加速比例（与 refer.py 完全一致）
                            speed_up = (audio.duration_seconds * 1000 + MIN_GAP_DURATION * 1000) / (audio_next_position - audio_position)

                            # 时间格式化（用于日志）
                            seconds = audio_position / 1000.0
                            time_str = str(datetime.timedelta(seconds=seconds))

                            # 显示超速警告（如果需要）
                            if speed_up > MAX_SPEED_UP:
                                print(f"⚠️ 音频 {i + 1} 在 {time_str} 需要加速 {speed_up:.2f}x，超过推荐值 {MAX_SPEED_UP}x，但仍会应用")

                            # 确保最小加速比例（与 refer.py 一致）
                            if speed_up < MIN_SPEED_UP:
                                print(f"⚠️ 音频 {i + 1} 在 {time_str} 加速 {speed_up:.2f}x 太接近1.0，强制设为 {MIN_SPEED_UP}")
                                speed_up = MIN_SPEED_UP

                            print(f"🔄 音频 {i + 1} 加速 {speed_up:.2f}x")
                            audio = audio.speedup(playback_speed=speed_up)
                            print(f"📊 加速后: 长度={audio.duration_seconds:.2f}s")

                    # 叠加音频到指定位置（与 refer.py 完全一致）
                    print(f"🔧 叠加音频到位置: {audio_position/1000:.2f}s")
                    combined = combined.overlay(audio, position=int(audio_position))
                    print(f"✅ 音频 {i + 1} 已成功添加")

                except Exception as e:
                    print(f"❌ 处理音频 {i + 1} 失败: {e}")
                    import traceback
                    traceback.print_exc()
                    continue

            # 检查合并后的音频质量
            print(f"📊 合并完成，音频统计:")
            print(f"   总长度: {combined.duration_seconds:.2f}秒")
            print(f"   音量: {combined.dBFS:.1f}dB")
            print(f"   采样率: {combined.frame_rate}Hz")
            print(f"   声道数: {combined.channels}")

            # 如果音频为静音，报告问题
            if combined.dBFS == float('-inf'):
                print("❌ 合并后的音频为静音！这表明所有输入音频都是静音的。")
                return None

            # 导出合并后的音频（与 refer.py 完全一致）
            output_audio_path = os.path.join(output_dir, "combined_voiceover.wav")
            print(f"💾 导出合并音频: {output_audio_path}")

            # 使用与 refer.py 相同的导出方式
            combined.export(output_audio_path, format="wav")

            # 验证导出的文件
            if os.path.exists(output_audio_path):
                file_size = os.path.getsize(output_audio_path)
                print(f"📁 导出文件大小: {file_size / (1024*1024):.1f} MB")

                # 重新加载验证
                try:
                    test_audio = AudioSegment.from_wav(output_audio_path)
                    print(f"✅ 验证成功: 长度={test_audio.duration_seconds:.2f}s, 音量={test_audio.dBFS:.1f}dB")

                    if test_audio.dBFS == float('-inf'):
                        print("❌ 验证失败：导出的音频为静音")
                        return None

                except Exception as e:
                    print(f"❌ 验证失败: {e}")
                    return None
            else:
                print("❌ 导出文件不存在")
                return None

            print(f"✅ 字幕配音合并完成: {os.path.basename(output_audio_path)}")
            return output_audio_path

        except ImportError:
            print("❌ 缺少 pydub 库，无法合并音频")
            QMessageBox.critical(self, "错误", "缺少音频处理库 pydub，请安装后重试。")
            return None
        except Exception as e:
            print(f"❌ 合并字幕配音失败: {e}")
            import traceback
            traceback.print_exc()
            return None

    def mix_background_music(self, voiceover_audio_path, output_dir):
        """将背景音乐与配音混合"""
        try:
            from pydub import AudioSegment
            import datetime

            print("🎵 开始混合背景音乐...")

            # 获取背景音乐文件路径
            background_music_path = self.get_background_music_path()
            if not background_music_path or not os.path.exists(background_music_path):
                print("❌ 未找到背景音乐文件")
                return None

            print(f"🎵 背景音乐文件: {background_music_path}")
            print(f"🎤 配音文件: {voiceover_audio_path}")

            # 加载音频文件
            try:
                voiceover_audio = AudioSegment.from_wav(voiceover_audio_path)
                background_music = AudioSegment.from_file(background_music_path)

                print(f"📊 配音音频: 长度={len(voiceover_audio)/1000:.2f}s, 音量={voiceover_audio.dBFS:.1f}dB")
                print(f"📊 背景音乐: 长度={len(background_music)/1000:.2f}s, 音量={background_music.dBFS:.1f}dB")

            except Exception as e:
                print(f"❌ 加载音频文件失败: {e}")
                return None

            # 调整背景音乐长度以匹配配音长度
            voiceover_duration = len(voiceover_audio)
            background_duration = len(background_music)

            if background_duration > voiceover_duration:
                # 背景音乐较长，截取前面部分
                background_music = background_music[:voiceover_duration]
                print(f"🔧 背景音乐已截取到 {voiceover_duration/1000:.2f}s")
            elif background_duration < voiceover_duration:
                # 背景音乐较短，循环播放
                repeat_times = int(voiceover_duration / background_duration) + 1
                background_music = background_music * repeat_times
                background_music = background_music[:voiceover_duration]
                print(f"🔧 背景音乐已循环 {repeat_times} 次并截取到 {voiceover_duration/1000:.2f}s")

            # 调整背景音乐音量（降低到配音的30-40%）
            background_volume_reduction = 12  # 降低12dB，约为原音量的25%
            background_music = background_music - background_volume_reduction
            print(f"🔧 背景音乐音量已降低 {background_volume_reduction}dB")

            # 混合音频
            print("🎵 正在混合音频...")
            mixed_audio = voiceover_audio.overlay(background_music)

            # 检查混合后的音频质量
            print(f"📊 混合后音频: 长度={len(mixed_audio)/1000:.2f}s, 音量={mixed_audio.dBFS:.1f}dB")

            # 如果混合后音频过响，进行音量调整
            if mixed_audio.dBFS > -3:  # 避免削波
                volume_adjustment = -3 - mixed_audio.dBFS
                mixed_audio = mixed_audio + volume_adjustment
                print(f"🔧 混合音频音量已调整 {volume_adjustment:.1f}dB 以避免削波")

            # 导出混合后的音频
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            mixed_filename = f"mixed_audio_with_background_{timestamp}.wav"
            mixed_audio_path = os.path.join(output_dir, mixed_filename)

            mixed_audio.export(mixed_audio_path, format="wav")
            print(f"✅ 背景音乐混合完成: {mixed_filename}")

            return mixed_audio_path

        except ImportError:
            print("❌ 缺少 pydub 库，无法混合背景音乐")
            return None
        except Exception as e:
            print(f"❌ 混合背景音乐失败: {e}")
            import traceback
            traceback.print_exc()
            return None

    def replace_video_audio(self, audio_path, output_dir):
        """使用FFmpeg替换视频音频"""
        try:
            import subprocess
            import datetime

            print("🎬 开始替换视频音频...")

            # 生成输出文件名
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            original_name = os.path.splitext(os.path.basename(self.current_video_path))[0]
            output_filename = f"{original_name}_translated_{timestamp}.mp4"
            output_video_path = os.path.join(output_dir, output_filename)

            print(f"📁 输入视频: {self.current_video_path}")
            print(f"📁 输入音频: {audio_path}")
            print(f"📁 输出视频: {output_video_path}")

            # 首先检查输入音频文件
            try:
                test_audio = AudioSegment.from_wav(audio_path)
                print(f"🔍 输入音频检查: 长度={len(test_audio)/1000:.2f}s, 音量={test_audio.dBFS:.1f}dB")
                if test_audio.dBFS == float('-inf'):
                    print("❌ 输入音频为静音，无法进行视频合成")
                    return None
            except Exception as e:
                print(f"❌ 无法读取输入音频文件: {e}")
                return None

            # 构建FFmpeg命令（参考 refer.py）
            ffmpeg_cmd = [
                "ffmpeg",
                "-i", self.current_video_path,  # 输入视频
                "-i", audio_path,               # 输入音频
                "-c:v", "copy",                 # 复制视频流，不重新编码
                "-c:a", "aac",                  # 音频编码为AAC
                "-b:a", "192k",                 # 音频比特率
                "-ar", "44100",                 # 音频采样率
                "-ac", "2",                     # 立体声
                "-map", "0:v:0",                # 使用第一个输入的视频流
                "-map", "1:a:0",                # 使用第二个输入的音频流
                "-shortest",                    # 以最短的流为准
                "-avoid_negative_ts", "make_zero",  # 避免负时间戳
                "-y",                           # 覆盖输出文件
                output_video_path
            ]

            print(f"🔧 FFmpeg命令: {' '.join(ffmpeg_cmd)}")

            # 执行FFmpeg命令
            print("⚙️ 正在执行FFmpeg...")
            result = subprocess.run(
                ffmpeg_cmd,
                capture_output=True,
                text=True,
                timeout=300  # 5分钟超时
            )

            if result.returncode == 0:
                print(f"✅ 视频音频替换成功!")
                print(f"📁 输出文件: {output_video_path}")

                # 检查输出文件
                if os.path.exists(output_video_path):
                    file_size = os.path.getsize(output_video_path)
                    size_mb = file_size / (1024 * 1024)
                    print(f"📊 输出文件大小: {size_mb:.1f} MB")
                    return output_video_path
                else:
                    print("❌ 输出文件不存在")
                    return None
            else:
                print(f"❌ FFmpeg执行失败:")
                print(f"返回码: {result.returncode}")
                print(f"错误输出: {result.stderr}")
                return None

        except subprocess.TimeoutExpired:
            print("❌ FFmpeg执行超时")
            return None
        except FileNotFoundError:
            print("❌ 未找到FFmpeg，请确保已安装FFmpeg并添加到系统PATH")
            QMessageBox.critical(
                self, "FFmpeg未找到",
                "未找到FFmpeg程序！\n\n"
                "请确保已安装FFmpeg并添加到系统PATH环境变量中。\n"
                "您可以从 https://ffmpeg.org/download.html 下载FFmpeg。"
            )
            return None
        except Exception as e:
            print(f"❌ 替换视频音频失败: {e}")
            import traceback
            traceback.print_exc()
            return None

    def create_test_audio(self, output_dir):
        """创建测试音频文件，用于调试"""
        try:
            from pydub import AudioSegment
            from pydub.generators import Sine

            print("🧪 创建测试音频文件...")

            # 创建一个5秒的测试音频（440Hz正弦波）
            test_audio = Sine(440).to_audio_segment(duration=5000)  # 5秒
            test_audio = test_audio - 20  # 降低音量到 -20dB

            test_path = os.path.join(output_dir, "test_audio.wav")
            test_audio.export(test_path, format="wav")

            print(f"✅ 测试音频已创建: {test_path}")
            print(f"📊 测试音频: 长度={len(test_audio)/1000:.2f}s, 音量={test_audio.dBFS:.1f}dB")

            return test_path

        except Exception as e:
            print(f"❌ 创建测试音频失败: {e}")
            return None

    def parse_srt_time_to_seconds(self, time_str):
        """将SRT时间格式转换为秒数"""
        try:
            # SRT时间格式: HH:MM:SS,mmm
            if ',' in time_str:
                time_part, ms_part = time_str.split(',')
                ms = int(ms_part) / 1000.0
            else:
                time_part = time_str
                ms = 0.0

            # 解析时分秒
            time_parts = time_part.split(':')
            if len(time_parts) == 3:
                hours = int(time_parts[0])
                minutes = int(time_parts[1])
                seconds = int(time_parts[2])
                total_seconds = hours * 3600 + minutes * 60 + seconds + ms
                return total_seconds
            else:
                print(f"⚠️ 无法解析时间格式: {time_str}")
                return 0.0
        except Exception as e:
            print(f"⚠️ 解析时间失败: {time_str}, 错误: {e}")
            return 0.0

    def test_audio_playback(self, audio_path):
        """测试音频播放功能"""
        try:
            print(f"🧪 测试音频播放: {audio_path}")

            if not hasattr(self, 'video_upload_area') or not hasattr(self.video_upload_area, 'subtitle_audio_player'):
                print("❌ 音频播放器不存在")
                return False

            player = self.video_upload_area.subtitle_audio_player
            audio_output = self.video_upload_area.subtitle_audio_output

            # 检查并重新设置音频设备（修复Windows音频设备问题）
            try:
                from PySide6.QtMultimedia import QMediaDevices
                default_device = QMediaDevices.defaultAudioOutput()
                if not default_device.isNull():
                    audio_output.setDevice(default_device)
                    print(f"🔧 已设置音频设备: {default_device.description()}")
                else:
                    print("⚠️ 未找到默认音频设备")
            except Exception as e:
                print(f"⚠️ 设置音频设备失败: {e}")

            # 确保音量设置正确
            audio_output.setVolume(0.8)  # 设置为80%音量
            print(f"🔊 音频音量设置为: {audio_output.volume()}")

            # 检查播放器状态
            print(f"🔍 播放器状态检查:")
            print(f"  - 音频输出音量: {audio_output.volume()}")
            print(f"  - 音频设备: {audio_output.device().description() if audio_output.device() else 'None'}")
            print(f"  - 播放状态: {player.playbackState()}")
            print(f"  - 媒体状态: {player.mediaStatus()}")

            # 检查文件
            if not os.path.exists(audio_path):
                print(f"❌ 音频文件不存在: {audio_path}")
                return False

            file_size = os.path.getsize(audio_path)
            print(f"📁 音频文件大小: {file_size} 字节")

            if file_size == 0:
                print("❌ 音频文件为空")
                return False

            # 停止当前播放
            player.stop()

            # 等待停止完成
            import time
            time.sleep(0.1)

            # 尝试播放
            audio_url = QUrl.fromLocalFile(audio_path)
            print(f"🔗 音频URL: {audio_url.toString()}")

            # 验证URL有效性
            if not audio_url.isValid():
                print(f"❌ 音频URL无效: {audio_url.toString()}")
                return False

            player.setSource(audio_url)

            # 等待媒体加载
            time.sleep(0.2)

            # 检查媒体状态
            media_status = player.mediaStatus()
            print(f"📊 媒体加载状态: {media_status}")

            # 开始播放
            player.play()

            # 检查播放状态
            playback_state = player.playbackState()
            print(f"▶️  播放状态: {playback_state}")

            print("✅ 音频播放命令已发送")
            return True

        except Exception as e:
            print(f"❌ 测试音频播放失败: {e}")
            import traceback
            traceback.print_exc()
            return False

    def on_gpu_status_updated(self, is_available: bool, gpu_name: str, status_text: str):
        """GPU状态更新回调"""
        self.gpu_is_available = is_available
        self.gpu_name = gpu_name
        self.gpu_status_text = status_text

        # 更新GPU状态显示
        if hasattr(self, 'gpu_status_widget'):
            self.update_gpu_status_display(is_available, gpu_name, status_text)

        print(f"GPU状态更新: 可用={is_available}, 名称={gpu_name}, 状态={status_text}")

    def create_slide_animation(self, widget, direction):
        """
        创建滑入动画
        widget: 要执行动画的组件
        direction: 滑入方向 ('left', 'right', 'top', 'bottom')
        """
        # 设置动画状态锁
        self.is_animating = True

        if self.slide_animation:
            self.slide_animation.stop()
            self.slide_animation.deleteLater()

        # 获取组件的最终位置
        final_geometry = widget.geometry()

        # 根据滑入方向设置起始位置
        start_geometry = QRect(final_geometry)

        if direction == 'left':
            # 从左侧滑入
            start_geometry.moveLeft(-final_geometry.width())
        elif direction == 'right':
            # 从右侧滑入
            start_geometry.moveLeft(self.width())
        elif direction == 'top':
            # 从顶部滑入
            start_geometry.moveTop(-final_geometry.height())
        elif direction == 'bottom':
            # 从底部滑入
            start_geometry.moveTop(self.height())

        # 设置起始位置
        widget.setGeometry(start_geometry)
        widget.show()

        # 创建动画
        self.slide_animation = QPropertyAnimation(widget, b"geometry")
        self.slide_animation.setDuration(400)  # 动画持续时间400毫秒，从600ms减少到400ms
        self.slide_animation.setStartValue(start_geometry)
        self.slide_animation.setEndValue(final_geometry)

        # 设置缓动曲线，让动画更自然
        self.slide_animation.setEasingCurve(QEasingCurve.OutCubic)

        # 添加动画完成回调
        self.slide_animation.finished.connect(self.on_animation_finished)

        # 启动动画
        self.slide_animation.start()

    def on_animation_finished(self):
        """动画完成回调，重置动画状态"""
        self.is_animating = False
        if self.slide_animation:
            self.slide_animation.deleteLater()
            self.slide_animation = None

    def get_random_slide_direction(self):
        """
        随机获取滑入方向
        返回值: 'left', 'right', 'top', 'bottom' 中的一个
        """
        directions = ['left', 'right', 'top', 'bottom']
        return random.choice(directions)

    def setup_ui(self):
        """初始化主窗口UI"""
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 创建主布局
        main_layout = QHBoxLayout(central_widget)
        # 修改：减小主布局的内边距，从(12, 12, 12, 12)改为(8, 8, 8, 8)
        main_layout.setContentsMargins(3, 3, 3, 3)
        # 修改：减小组件之间的间距，从10改为5
        main_layout.setSpacing(5)

        # 左侧导航栏
        self.nav_bar = NavigationBar()
        main_layout.addWidget(self.nav_bar)

        # 右侧内容区域容器
        self.right_area_container = QWidget()
        self.right_area_container.setFixedSize(1324, 776)

        # 内容区域布局
        self.right_area_layout = QHBoxLayout(self.right_area_container)
        # 设置右侧区域内边距
        self.right_area_layout.setContentsMargins(0, 0, 0, 0)
        # 增加组件之间的间距，为视频区域留出足够空间
        self.right_area_layout.setSpacing(5)

        # 添加右侧区域容器到主布局
        main_layout.addWidget(self.right_area_container)

        # 连接导航栏的页面切换信号
        self.nav_bar.page_changed.connect(self.on_page_changed)

        # 首页三个主要区域组件
        self.create_main_areas()

        # 其他全宽区域组件（API设置、功能、系统设置、日志等）
        self.create_full_width_areas()

        # 显示首页
        self.show_home_page()

        # 设置窗口样式
        self.setStyleSheet("""
            QMainWindow {
                background-color: #121419;
            }
        """)

    def create_gpu_status_widget(self):
        """创建GPU状态显示组件"""
        # 创建GPU状态容器
        self.gpu_status_widget = QWidget()
        self.gpu_status_widget.setFixedWidth(350)  # 固定宽度

        # 创建布局
        layout = QHBoxLayout(self.gpu_status_widget)
        layout.setAlignment(Qt.AlignRight)
        layout.setContentsMargins(12, 8, 12, 8)
        layout.setSpacing(8)

        # GPU状态图标
        self.gpu_status_icon = QLabel("🔍")
        self.gpu_status_icon.setFixedSize(24, 24)
        self.gpu_status_icon.setAlignment(Qt.AlignCenter)
        self.gpu_status_icon.setStyleSheet("""
            QLabel {
                font-size: 16px;
                background-color: transparent;
                border:none;
            }
        """)
        layout.addWidget(self.gpu_status_icon)

        # GPU状态文本
        self.gpu_status_label = QLabel("检测中...")
        self.gpu_status_label.setStyleSheet("""
            QLabel {
                color: #CCCCCC;
                font-size: 12px;
                font-weight: bold;
                background-color: transparent;
            }
        """)
        layout.addWidget(self.gpu_status_label)

        # 帮助按钮
        self.gpu_help_btn = QPushButton("?")
        self.gpu_help_btn.setFixedSize(20, 20)
        self.gpu_help_btn.setStyleSheet("""
            QPushButton {
                background-color: #2B9D7C;
                color: #FFFFFF;
                font-size: 12px;
                font-weight: bold;
                border: none;
                border-radius: 10px;
            }
            QPushButton:hover {
                background-color: #00CC55;
            }
        """)
        self.gpu_help_btn.clicked.connect(self.show_gpu_help_dialog)
        layout.addWidget(self.gpu_help_btn)

        # 设置整体样式
        self.gpu_status_widget.setStyleSheet("""
            QWidget {
                background-color: rgba(20, 22, 26, 0.9);
                border: none;
                border-radius: 15px;
            }
        """)

        # 显示组件
        self.gpu_status_widget.show()

        # 立即进行GPU检测并更新显示
        self.perform_immediate_gpu_detection()
        return self.gpu_status_widget

    def perform_immediate_gpu_detection(self):
        """立即执行GPU检测并更新显示"""
        try:
            if hasattr(self, 'gpu_detector') and self.gpu_detector:
                print("开始立即GPU检测...")
                # 先进行同步检测，立即获取结果
                is_available, gpu_name, status_text = self.gpu_detector._detect_gpu()
                print(f"同步检测结果: 可用={is_available}, 名称={gpu_name}, 状态={status_text}")

                # 立即更新显示
                self.update_gpu_status_display(is_available, gpu_name, status_text)

                # 然后启动异步检测，用于后续更新
                self.gpu_detector.check_gpu_async()
            else:
                print("GPU检测器不可用")
                # 如果GPU检测器不可用，显示相应状态
                self.update_gpu_status_display(False, "检测器不可用", "GPU检测器初始化失败")
        except Exception as e:
            print(f"立即GPU检测失败: {e}")
            # 检测失败时显示错误状态
            self.update_gpu_status_display(False, "检测失败", f"GPU检测出错: {str(e)}")

    def update_gpu_status_display(self, is_available: bool, gpu_name: str, status_text: str):
        """更新GPU状态显示"""
        if not hasattr(self, 'gpu_status_widget') or not self.gpu_status_widget:
            return

        try:
            # 检查组件是否仍然有效
            if not hasattr(self, 'gpu_status_icon') or not hasattr(self, 'gpu_status_label'):
                return

            # 更新图标和颜色
            if is_available:
                self.gpu_status_icon.setText("✅")
                color = "#2B9D7C"
                # 显示完整GPU名称，但限制在合理长度
                if len(gpu_name) > 60:
                    text = f"GPU可用: {gpu_name[:30]}..."
                else:
                    text = f"GPU可用: {gpu_name}"
            elif "AMD" in gpu_name or "Intel" in gpu_name or "集成" in gpu_name:
                self.gpu_status_icon.setText("⚠️")
                color = "#F39C12"
                if len(gpu_name) > 60:
                    text = f"GPU: {gpu_name[:30]}..."
                else:
                    text = f"GPU: {gpu_name}"
            elif "NVIDIA" in gpu_name:
                self.gpu_status_icon.setText("⚠️")
                color = "#F39C12"
                text = "GPU不可用：需要配置CUDA"
            else:
                self.gpu_status_icon.setText("❌")
                color = "#E74C3C"
                text = "GPU不可用"

            # 更新标签文本和颜色
            self.gpu_status_label.setText(text)
            self.gpu_status_label.setStyleSheet(f"""
                QLabel {{
                    color: {color};
                    font-size: 12px;
                    font-weight: bold;
                    background-color: transparent;
                }}
            """)
        except RuntimeError as e:
            # 组件已被删除，忽略错误
            print(f"GPU状态组件已被删除，跳过更新: {e}")
        except Exception as e:
            print(f"更新GPU状态显示时出错: {e}")

    def show_gpu_help_dialog(self):
        """显示GPU帮助对话框"""
        try:
            from PySide6.QtWidgets import QDialog, QVBoxLayout, QTextEdit, QPushButton, QHBoxLayout
            from core.gpu_help_guide import GPUHelpGuide

            # 创建帮助对话框
            dialog = QDialog(self)
            dialog.setWindowTitle("GPU配置指南 - FlipTalk AI")
            dialog.setFixedSize(800, 600)
            dialog.setStyleSheet("""
                QDialog {
                    background-color: #1B1E24;
                    color: #FFFFFF;
                }
            """)

            layout = QVBoxLayout(dialog)
            layout.setContentsMargins(20, 20, 20, 20)
            layout.setSpacing(15)

            # 创建文本显示区域
            text_area = QTextEdit()
            text_area.setReadOnly(True)
            text_area.setStyleSheet("""
                QTextEdit {
                    background-color: #14161A;
                    color: #FFFFFF;
                    border: 1px solid #444444;
                    border-radius: 8px;
                    padding: 15px;
                    font-size: 13px;
                    line-height: 1.6;
                }
                QScrollBar:vertical {
                    background-color: #2B2B2B;
                    width: 12px;
                    border-radius: 6px;
                }
                QScrollBar::handle:vertical {
                    background-color: #2B9D7C;
                    border-radius: 6px;
                    min-height: 20px;
                }
                QScrollBar::handle:vertical:hover {
                    background-color: #00CC55;
                }
            """)

            # 获取帮助内容
            gpu_name = getattr(self, 'gpu_name', '未知')
            is_available = getattr(self, 'gpu_is_available', False)
            help_content = GPUHelpGuide.get_help_content_html(is_available, gpu_name)
            text_area.setHtml(help_content)

            layout.addWidget(text_area)

            # 按钮区域
            button_layout = QHBoxLayout()
            button_layout.addStretch()

            # 复制配置命令按钮
            copy_btn = QPushButton("复制安装命令")
            copy_btn.setFixedSize(120, 35)
            copy_btn.setStyleSheet("""
                QPushButton {
                    background-color: #2B9D7C;
                    color: #FFFFFF;
                    font-size: 13px;
                    font-weight: bold;
                    border: none;
                    border-radius: 6px;
                }
                QPushButton:hover {
                    background-color: #00CC55;
                }
            """)
            copy_btn.clicked.connect(lambda: self.copy_installation_commands())
            button_layout.addWidget(copy_btn)

            # 关闭按钮
            close_btn = QPushButton("关闭")
            close_btn.setFixedSize(80, 35)
            close_btn.setStyleSheet("""
                QPushButton {
                    background-color: #666666;
                    color: #FFFFFF;
                    font-size: 13px;
                    font-weight: bold;
                    border: none;
                    border-radius: 6px;
                }
                QPushButton:hover {
                    background-color: #888888;
                }
            """)
            close_btn.clicked.connect(dialog.close)
            button_layout.addWidget(close_btn)

            layout.addLayout(button_layout)

            # 显示对话框
            dialog.exec()

        except Exception as e:
            print(f"显示GPU帮助对话框时出错: {e}")

    def copy_installation_commands(self):
        """复制安装命令到剪贴板"""
        try:
            from PySide6.QtWidgets import QApplication, QMessageBox

            commands = """# 卸载现有PyTorch
pip uninstall torch torchaudio

# 安装GPU版本PyTorch (CUDA 11.8)
pip install torch torchaudio --index-url https://download.pytorch.org/whl/cu118

# 验证安装
python -c "import torch; print('CUDA可用:', torch.cuda.is_available())"
"""

            clipboard = QApplication.clipboard()
            clipboard.setText(commands)
            QMessageBox.information(self, "复制成功", "安装命令已复制到剪贴板！")

        except Exception as e:
            print(f"复制命令时出错: {e}")

    def resizeEvent(self, event):
        """窗口大小改变事件"""
        super().resizeEvent(event)
        # GPU状态组件现在由布局管理，无需手动定位

    def create_main_areas(self):
        """创建主要区域：参数设置、字幕编辑"""
        print("创建主要区域...")

        # 创建主内容区域
        self.main_content = QWidget()
        main_layout = QHBoxLayout(self.main_content)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(15)  # 增加左侧区域和右侧字幕编辑区的间距

        # 创建左侧区域容器
        left_container = QWidget()

        left_layout = QVBoxLayout(left_container)
        left_layout.setContentsMargins(0, 0, 0, 0)
        left_layout.setSpacing(5)  # 减少间距，使布局更紧凑

        # 创建视频上传区域
        video_upload_container = QWidget()
        video_upload_layout = QVBoxLayout(video_upload_container)
        video_upload_container.setStyleSheet("""
            QWidget {
                background-color:#1B1E24;
                border-radius: 15px;
                border: 2px solid #1B1E24;
                padding: 0px;
            }
        """)
        self.video_upload_area = VideoUploadArea()
        self.video_upload_area.setFixedWidth(450)  # 与参数设置面板保持相同宽度
        self.video_upload_area.setFixedHeight(260)  # 删除播放按钮和视频信息后进一步减少高度
        # 连接视频上传和清除信号
        self.video_upload_area.video_uploaded.connect(self.on_video_uploaded)
        self.video_upload_area.video_cleared.connect(self.on_video_cleared)
        video_upload_layout.addWidget(self.video_upload_area)

        # 创建视频状态提示区域（位于视频上传区和参数设置区之间）
        status_container = QWidget()
        status_container.setFixedHeight(30)  # 减少容器高度，使布局更紧凑
        status_layout = QHBoxLayout(status_container)
        status_layout.setContentsMargins(0, 0, 0, 0)
        status_layout.setSpacing(8)  # 减少间距

        # 状态提示标签
        self.video_status_label = QLabel("📁 未选择文件")
        self.video_status_label.setAlignment(Qt.AlignLeft | Qt.AlignVCenter)
        self.video_status_label.setWordWrap(True)  # 允许文字换行
        self.video_status_label.setStyleSheet("""
                   QLabel {
                       color: #888888;
                       font-size: 13px;
                       background-color: transparent;
                       border: none;
                       padding: 2px 10px;
                       margin: 0px 0px;
                   }
               """)
        status_layout.addWidget(self.video_status_label, 1)  # 拉伸占据大部分空间

        # 重新上传按钮
        self.reupload_button = QPushButton("重新上传")
        self.reupload_button.setFixedSize(80, 30)  # 减少按钮高度，与紧凑布局匹配
        self.reupload_button.setEnabled(False)  # 初始状态禁用
        self.reupload_button.setStyleSheet("""
                   QPushButton {
                       background-color: #3A3A3A;
                       color: #CCCCCC;
                       border: 1px solid #555555;
                       border-radius: 4px;
                       font-size: 13px;
                       padding: 2px 8px;
                   }
                   QPushButton:enabled {
                       background-color: #2B9D7C;
                       color: white;
                       border: 1px solid #2B9D7C;
                   }
                   QPushButton:enabled:hover {
                       background-color: #239B75;
                   }
                   QPushButton:enabled:pressed {
                       background-color: #1E8A68;
                   }
                   QPushButton:disabled {
                       background-color: #2A2A2A;
                       color: #666666;
                       border: 1px solid #444444;
                   }
               """)
        self.reupload_button.clicked.connect(self.on_reupload_clicked)
        status_layout.addWidget(self.reupload_button, 0)  # 不拉伸，固定大小

        video_upload_layout.addWidget(status_container)
        left_layout.addWidget(video_upload_container)

        # 创建参数设置区域
        parameter_panel_container = QWidget()
        parameter_panel_layout = QVBoxLayout(parameter_panel_container)
        parameter_panel_container.setStyleSheet("""
                    QWidget {
                        background-color:#1B1E24;
                        border-radius: 15px;
                        border: 2px solid #1B1E24;
                        padding: 0px;
                    }
                """)
        self.parameter_panel = ParameterSettingsPanel()
        self.parameter_panel.setFixedWidth(450)  # 设置固定宽度
        parameter_panel_layout.addWidget(self.parameter_panel)
        left_layout.addWidget(parameter_panel_container)

        # 创建右侧区域容器
        right_container = QWidget()
        right_layout = QVBoxLayout(right_container)
        right_layout.setContentsMargins(0, 0, 0, 0)
        right_layout.setSpacing(10)

        # 创建字幕编辑区域
        self.subtitle_area = self.create_subtitle_edit()
        right_layout.addWidget(self.subtitle_area, 1)  # 添加拉伸因子，使字幕编辑区占据大部分空间

        # 创建开始处理按钮区域
        self.start_process_button_area = self.create_start_process_button()
        right_layout.addWidget(self.start_process_button_area, 0)  # 不拉伸，固定高度

        # 创建底部状态栏（包含处理进度和GPU状态）
        self.bottom_status_area = self.create_bottom_status_bar()
        right_layout.addWidget(self.bottom_status_area, 0)  # 不拉伸，固定高度
        # 将左侧容器和右侧容器添加到主布局
        main_layout.addWidget(left_container)
        main_layout.addWidget(right_container, 1)  # 添加拉伸因子，使右侧容器占据更多空间

        return self.main_content

    def create_start_process_button(self):
        """创建开始处理按钮区域"""
        # 创建按钮容器
        button_container = QWidget()
        button_container.setFixedHeight(60)
        button_container.setStyleSheet("background-color: transparent;")

        # 创建布局
        button_layout = QHBoxLayout(button_container)
        button_layout.setContentsMargins(20, 4, 20, 4)
        button_layout.setSpacing(0)

        # 左侧留空
        button_layout.addStretch()

        # 开始处理按钮
        self.start_process_btn = QPushButton("🚀 开始处理")
        self.start_process_btn.setFixedSize(150, 50)
        self.start_process_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #2B9D7C, stop:1 #00CC55);
                color: #FFFFFF;
                font-size: 16px;
                font-weight: bold;
                border: none;
                border-radius: 10px;
                padding: 0px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #00CC55, stop:1 #2B9D7C);
                transform: scale(1.05);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #238B6B, stop:1 #00AA44);
            }
        """)
        self.start_process_btn.clicked.connect(self.start_complete_process)
        button_layout.addWidget(self.start_process_btn)

        # 添加间距
        button_layout.addSpacing(20)

        # 视频合成按钮
        self.video_synthesis_btn = QPushButton("🎬 最终合成")
        self.video_synthesis_btn.setFixedSize(150, 50)
        self.video_synthesis_btn.setEnabled(False)  # 初始状态为禁用
        self.video_synthesis_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #6B73FF, stop:1 #9B59B6);
                color: #FFFFFF;
                font-size: 16px;
                font-weight: bold;
                border: none;
                border-radius: 10px;
                padding: 0px;
            }
            QPushButton:hover:enabled {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #9B59B6, stop:1 #6B73FF);
                transform: scale(1.05);
            }
            QPushButton:pressed:enabled {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #5A4FCF, stop:1 #8E44AD);
            }
            QPushButton:disabled {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #CCCCCC, stop:1 #999999);
                color: #666666;
            }
        """)
        self.video_synthesis_btn.clicked.connect(self.start_video_synthesis)
        button_layout.addWidget(self.video_synthesis_btn)

        # 右侧留空
        button_layout.addStretch()

        return button_container

    def create_bottom_status_bar(self):
        """创建底部状态栏（包含处理进度和GPU状态）"""
        # 创建底部状态栏容器
        status_bar_container = QWidget()
        status_bar_container.setFixedHeight(60)
        status_bar_container.setStyleSheet("background-color: transparent;")

        # 创建水平布局
        status_layout = QHBoxLayout(status_bar_container)
        status_layout.setContentsMargins(20, 10, 20, 10)
        status_layout.setSpacing(20)

        # 创建处理进度区域
        self.progress_area = self.create_progress_area()
        status_layout.addWidget(self.progress_area, 1)  # 拉伸占据大部分空间

        # 创建GPU状态区域
        self.gpu_status_area = self.create_gpu_status_widget()
        status_layout.addWidget(self.gpu_status_area, 0)  # 不拉伸，固定大小

        return status_bar_container

    def create_progress_area(self):
        """创建处理进度显示区域"""
        # 创建进度区域容器
        progress_container = QWidget()
        progress_container.setStyleSheet("""
            QWidget {
                background-color: rgba(20, 22, 26, 0.9);
                border: none;
                border-radius: 15px;
            }
        """)

        # 创建布局
        progress_layout = QVBoxLayout(progress_container)
        progress_layout.setContentsMargins(15, 4, 15, 4)
        progress_layout.setSpacing(5)

        # 当前步骤标签
        self.current_step_label = QLabel("等待开始处理...")
        self.current_step_label.setStyleSheet("""
            QLabel {
                color: #CCCCCC;
                font-size: 13px;
                font-weight: bold;
                background-color: transparent;
            }
        """)
        progress_layout.addWidget(self.current_step_label)

        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setFixedHeight(16)
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                border: none;
                background-color: rgba(255, 255, 255, 0.1);
                border-radius: 4px;
                text-align: center;
                color:white;
            }
            QProgressBar::chunk {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #2B9D7C, stop:1 #00CC55);
                border-radius: 4px;
            }
        """)
        progress_layout.addWidget(self.progress_bar)

        # 默认显示进度区域，进度条为0%
        progress_container.show()

        return progress_container

    def update_progress_display(self, step_text: str, progress: int):
        """更新处理进度显示

        Args:
            step_text: 当前步骤文本
            progress: 进度百分比 (0-100)
        """
        try:
            if hasattr(self, 'progress_area') and hasattr(self, 'current_step_label') and hasattr(self, 'progress_bar'):
                self.current_step_label.setText(step_text)
                self.progress_bar.setValue(progress)

                print(f"进度更新: {step_text} - {progress}%")
        except Exception as e:
            print(f"更新进度显示时出错: {e}")

    def reset_progress_display(self):
        """重置进度显示到初始状态"""
        self.update_progress_display("等待开始处理...", 0)

    def on_video_uploaded(self, file_path):
        """视频上传成功回调"""
        import os
        print(f"视频文件已上传: {file_path}")

        # 保存当前视频文件路径和文件名
        self.current_video_path = file_path
        self.current_video_name = os.path.basename(file_path)

        # 示例：可以在这里触发视频分析或预处理
        try:
            file_name = os.path.basename(file_path)
            file_size = os.path.getsize(file_path)
            print(f"文件名: {file_name}")
            print(f"文件大小: {file_size / (1024 * 1024):.2f} MB")

            # 更新视频状态提示
            if hasattr(self, 'video_status_label'):
                # 格式化文件大小
                if file_size < 1024 * 1024:
                    size_str = f"{file_size / 1024:.1f} KB"
                elif file_size < 1024 * 1024 * 1024:
                    size_str = f"{file_size / (1024 * 1024):.1f} MB"
                else:
                    size_str = f"{file_size / (1024 * 1024 * 1024):.1f} GB"

                # 截断过长的文件名，确保能在一行显示
                display_name = file_name if len(file_name) <= 20 else file_name[:17] + "..."
                self.video_status_label.setText(f"✅ {display_name} ({size_str})")
                self.video_status_label.setStyleSheet("""
                    QLabel {
                        color: #2B9D7C;
                        font-size: 13px;
                        background-color: transparent;
                        border: none;
                        padding: 2px 10px;
                        margin: 0px 0px;
                    }
                """)

                # 激活重新上传按钮
                if hasattr(self, 'reupload_button'):
                    self.reupload_button.setEnabled(True)

            # 可以在这里添加更多逻辑，比如：
            # - 检查视频格式兼容性
            # - 预估处理时间
            # - 更新参数设置的默认值
            # - 启用开始处理按钮等

        except Exception as e:
            print(f"处理上传的视频文件时出错: {e}")
            # 如果出错，显示错误状态
            if hasattr(self, 'video_status_label'):
                self.video_status_label.setText("❌ 文件处理出错")
                self.video_status_label.setStyleSheet("""
                    QLabel {
                        color: #FF5555;
                        font-size: 12px;
                        background-color: transparent;
                        border: none;
                        padding: 3px 10px;
                        margin: 5px 0px;
                    }
                """)

    def on_video_cleared(self):
        """视频清除回调"""
        print("视频已清除")

        # 清除当前视频文件路径和文件名
        self.current_video_path = None
        self.current_video_name = None

        # 重置视频状态提示
        if hasattr(self, 'video_status_label'):
            self.video_status_label.setText("📁 未选择文件")
            self.video_status_label.setStyleSheet("""
                QLabel {
                    color: #888888;
                    font-size: 11px;
                    background-color: transparent;
                    border: none;
                    padding: 2px 10px;
                    margin: 0px 0px;
                }
            """)

            # 禁用重新上传按钮
            if hasattr(self, 'reupload_button'):
                self.reupload_button.setEnabled(False)

    def on_reupload_clicked(self):
        """重新上传按钮点击处理"""
        print("重新上传按钮被点击")
        # 立即清除当前视频预览，防止显示旧内容
        if hasattr(self, 'video_upload_area') and hasattr(self.video_upload_area, 'thumbnail_label'):
            self.video_upload_area.thumbnail_label.clear()
            self.video_upload_area.thumbnail_label.setText("选择新视频...")
            self.video_upload_area.thumbnail_label.update()
            self.video_upload_area.thumbnail_label.repaint()

        # 触发视频上传区域的重新选择
        if hasattr(self, 'video_upload_area'):
            self.video_upload_area.switch_to_upload_mode()
            # 可以选择性地触发文件选择对话框
            self.video_upload_area.select_video_file()

    def create_full_width_areas(self):
        """创建API设置、功能、系统设置和日志等全宽区域组件"""
        # API设置区域
        self.api_settings_area = APISettingsArea(width=1324)  # 使用全宽版本
        self.api_settings_area.hide()  # 初始隐藏

        # 功能区域
        self.function_area = FunctionArea()
        self.function_area.hide()  # 初始隐藏
        self.function_area.switch_to_audio_extraction.connect(self.switch_to_audio_extraction_mode)

        # 日志区域
        self.log_area = LogArea()
        self.log_area.hide()  # 初始隐藏

        # 系统设置区域
        self.system_settings_area = SystemSettingsArea()
        self.system_settings_area.hide()  # 初始隐藏

    def on_page_changed(self, page_name):
        """处理页面切换事件"""
        # 防止动画期间重复点击
        if self.is_animating:
            return

        # 如果是相同页面，不需要切换
        if self.current_page == page_name:
            return

        # 首先停止任何正在进行的动画并清理状态
        self.stop_current_animation()

        # 隐藏所有全宽区域
        self.hide_all_full_width_areas()

        if page_name == "API设置":
            # 切换到API设置界面
            self.show_api_settings_page()
        elif page_name == "功能":
            # 切换到功能界面
            self.show_function_page()
        elif page_name == "运行日志":
            # 切换到运行日志界面
            self.show_log_page()
        elif page_name == "系统设置":
            # 切换到系统设置界面
            self.show_system_settings_page()
        else:
            # 切换回首页
            self.show_home_page()

        # 更新当前页面状态
        self.current_page = page_name

    def stop_current_animation(self):
        """停止当前动画并重置状态"""
        if self.slide_animation:
            self.slide_animation.stop()
            self.slide_animation.finished.disconnect()  # 断开信号连接
            self.slide_animation.deleteLater()
            self.slide_animation = None
        self.is_animating = False

    def hide_all_full_width_areas(self):
        """隐藏所有全宽区域"""
        self.api_settings_area.hide()
        self.function_area.hide()
        self.log_area.hide()
        self.system_settings_area.hide()

    def show_api_settings_page(self):
        """显示API设置页面"""
        # 隐藏参数设置栏（非首页不显示）
        self.parameter_panel.hide()

        # 清空右侧区域布局中的所有组件
        for i in reversed(range(self.right_area_layout.count())):
            widget = self.right_area_layout.itemAt(i).widget()
            if widget:
                widget.hide()

        # 添加API设置区域到右侧区域布局
        self.right_area_layout.addWidget(self.api_settings_area)
        self.api_settings_area.show()

        # 应用动画效果
        random_direction = self.get_random_slide_direction()
        self.create_slide_animation(self.api_settings_area, random_direction)

    def show_function_page(self):
        """显示功能页面"""
        # 隐藏参数设置栏（非首页不显示）
        self.parameter_panel.hide()

        # 清空右侧区域布局中的所有组件
        for i in reversed(range(self.right_area_layout.count())):
            widget = self.right_area_layout.itemAt(i).widget()
            if widget:
                widget.hide()

        # 添加功能区域到右侧区域布局
        self.right_area_layout.addWidget(self.function_area)
        self.function_area.show()

        # 应用动画效果
        random_direction = self.get_random_slide_direction()
        self.create_slide_animation(self.function_area, random_direction)

    def show_home_page(self):
        """显示首页"""
        try:
            # 清空右侧区域布局中的所有组件
            for i in reversed(range(self.right_area_layout.count())):
                widget = self.right_area_layout.itemAt(i).widget()
                if widget:
                    widget.hide()
                    self.right_area_layout.removeWidget(widget)

            # 添加主内容区域到右侧布局（包含了左侧容器和字幕编辑区域）
            if hasattr(self, 'main_content') and self.main_content:
                self.right_area_layout.addWidget(self.main_content)
                self.main_content.show()

                # 确保内部组件也显示
                if hasattr(self, 'parameter_panel') and self.parameter_panel:
                    self.parameter_panel.show()
                if hasattr(self, 'subtitle_area') and self.subtitle_area:
                    self.subtitle_area.show()

                print("首页组件已添加并显示")
            else:
                print("错误：主内容区域不存在")
        except Exception as e:
            print(f"显示首页出错: {e}")

    def show_log_page(self):
        """显示运行日志页面"""
        # 隐藏参数设置栏（非首页不显示）
        self.parameter_panel.hide()

        # 清空右侧区域布局中的所有组件
        for i in reversed(range(self.right_area_layout.count())):
            widget = self.right_area_layout.itemAt(i).widget()
            if widget:
                widget.hide()

        # 添加日志区域到右侧区域布局
        self.right_area_layout.addWidget(self.log_area)
        self.log_area.show()

        # 应用动画效果
        random_direction = self.get_random_slide_direction()
        self.create_slide_animation(self.log_area, random_direction)

    def show_system_settings_page(self):
        """显示系统设置页面"""
        # 隐藏参数设置栏（非首页不显示）
        self.parameter_panel.hide()

        # 清空右侧区域布局中的所有组件
        for i in reversed(range(self.right_area_layout.count())):
            widget = self.right_area_layout.itemAt(i).widget()
            if widget:
                widget.hide()

        # 添加系统设置区域到右侧区域布局
        self.right_area_layout.addWidget(self.system_settings_area)
        self.system_settings_area.show()

        # 应用动画效果
        random_direction = self.get_random_slide_direction()
        self.create_slide_animation(self.system_settings_area, random_direction)

    def reset_widget_geometry(self, widget):
        """重置组件几何位置到正确的布局位置"""
        # 确保组件在正确的位置
        widget.setGeometry(0, 0, widget.width(), widget.height())

    def reset_right_area_layout(self):
        """重置右侧区域布局"""
        try:
            # 清空右侧区域布局中的所有组件
            for i in reversed(range(self.right_area_layout.count())):
                widget = self.right_area_layout.itemAt(i).widget()
                if widget:
                    widget.hide()
                    self.right_area_layout.removeWidget(widget)

            # 添加主内容区域到右侧布局（包含了左侧容器和字幕编辑区域）
            if hasattr(self, 'main_content') and self.main_content:
                self.right_area_layout.addWidget(self.main_content)
                self.main_content.show()

                # 确保内部组件也显示
                if hasattr(self, 'parameter_panel') and self.parameter_panel:
                    self.parameter_panel.show()
                if hasattr(self, 'subtitle_area') and self.subtitle_area:
                    self.subtitle_area.show()

                print("首页布局已重置")
            else:
                print("错误：主内容区域不存在")
        except Exception as e:
            print(f"重置右侧区域布局出错: {e}")

    def switch_to_audio_extraction_mode(self):
        """切换到音频提取模式"""
        # 如果当前不在功能页面，先切换到功能页面
        if self.current_page != "功能":
            self.nav_bar.on_nav_clicked("功能")

        # 模拟点击音频提取功能
        self.function_area.on_function_clicked("音频提取")

    def closeEvent(self, event):
        """窗口关闭事件处理，确保所有资源被释放"""
        print("窗口正在关闭，清理资源...")

        # 调用父类的关闭事件处理
        super().closeEvent(event)

    def setup_style(self):
        """设置窗口样式"""
        self.setStyleSheet("""
            QMainWindow {
                background-color: #121419;
            }
        """)

    def create_subtitle_edit(self):
        """创建字幕编辑区域 - 使用QFrame作为背景容器"""

        # 创建背景Frame - 黑色背景，圆角30px
        background_frame = QFrame()
        # background_frame.setFixedHeight(745)  # 与容器同高
        background_frame.setStyleSheet("""
            QFrame {
                background-color: #1B1E24;
                border-radius: 20px;
                border: none;
            }
        """)

        # Frame内部布局
        frame_layout = QVBoxLayout(background_frame)
        frame_layout.setContentsMargins(15, 15, 15, 15)
        frame_layout.setSpacing(10)

        # 字幕编辑区标题
        title_layout = QHBoxLayout()
        title_layout.setContentsMargins(8, 0, 8, 0)
        title_layout.setSpacing(10)

        # 左侧标题
        title_label = QLabel("字幕编辑")
        title_label.setStyleSheet("""
            QLabel {
                color: #FFFFFF;
                font-size: 18px;
                font-weight: bold;
                background-color: transparent;
                border: none;
            }
        """)
        title_layout.addWidget(title_label)

        title_layout.addStretch()

        # 右侧字幕计数
        self.subtitle_count_label = QLabel("字幕条数: 0 | 双击内容编辑")
        self.subtitle_count_label.setStyleSheet("""
            QLabel {
                color: #9CA3AF;
                font-size: 12px;
                background-color: transparent;
                border: none;
            }
        """)
        title_layout.addWidget(self.subtitle_count_label)

        frame_layout.addLayout(title_layout)

        # 创建统一的字幕表格 - 六列设计（序号+开始时间+结束时间+内容+状态+操作）
        self.subtitles_table = QTableWidget()
        self.subtitles_table.setColumnCount(6)
        self.subtitles_table.setHorizontalHeaderLabels(["#", "开始时间", "结束时间", "原文", "状态", "操作"])

        # 设置表格样式 - 参照字幕配音窗口的深色主题
        self.subtitles_table.setStyleSheet("""
            QTableWidget {
                background-color: #14161A;
                border: 1px solid #333333;
                gridline-color: #333333;
                selection-background-color: #2B9D7C;
                selection-color: #FFFFFF;
                color: #FFFFFF;
                font-size: 12px;
                border-radius: 4px;
            }
            QTableWidget::item {
                padding: 6px;
                border-bottom: 1px solid #333333;
            }
            QTableWidget::item:selected {
                background-color: #2B9D7C;
                color: #FFFFFF;
            }
            QTableWidget::item:hover {
                background-color: rgba(43, 157, 124, 0.3);
            }
            QTableWidget::item:focus {
                background-color: rgba(43, 157, 124, 0.5);
                border: 1px solid #2B9D7C;
            }
            QHeaderView::section {
                background-color: #2B9D7C;
                color: #FFFFFF;
                padding: 6px;
                border: none;
                border-right: 1px solid #1B1E24;
                font-weight: bold;
                font-size: 11px;
            }
            QHeaderView::section:last {
                border-right: none;
            }
            QScrollBar:vertical {
                background-color: #1B1E24;
                width: 12px;
                border-radius: 6px;
            }
            QScrollBar::handle:vertical {
                background-color: #555555;
                border-radius: 6px;
                min-height: 10px;
            }
            QScrollBar::handle:vertical:hover {
                background-color: #666666;
            }
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
                height: 0px;
            }
            QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {
                background: transparent;
            }
        """)

        # 设置列宽 - 优化六列布局
        subtitles_header = self.subtitles_table.horizontalHeader()
        subtitles_header.setSectionResizeMode(0, QHeaderView.Fixed)  # 序号
        subtitles_header.setSectionResizeMode(1, QHeaderView.Fixed)  # 开始时间
        subtitles_header.setSectionResizeMode(2, QHeaderView.Fixed)  # 结束时间
        subtitles_header.setSectionResizeMode(3, QHeaderView.Stretch)  # 字幕内容
        subtitles_header.setSectionResizeMode(4, QHeaderView.Fixed)  # 状态
        subtitles_header.setSectionResizeMode(5, QHeaderView.Fixed)  # 操作

        self.subtitles_table.setColumnWidth(0, 50)  # 序号列宽
        self.subtitles_table.setColumnWidth(1, 110)  # 开始时间列宽
        self.subtitles_table.setColumnWidth(2, 110)  # 结束时间列宽
        self.subtitles_table.setColumnWidth(4, 70)  # 状态列宽
        self.subtitles_table.setColumnWidth(5, 80)  # 操作列宽

        # 设置行高和选择模式
        self.subtitles_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.subtitles_table.setSelectionMode(QTableWidget.SingleSelection)
        self.subtitles_table.verticalHeader().setVisible(False)
        self.subtitles_table.setShowGrid(True)
        self.subtitles_table.verticalHeader().setDefaultSectionSize(40)

        # 设置垂直滚动条策略为始终显示，确保可滚动
        self.subtitles_table.setVerticalScrollBarPolicy(Qt.ScrollBarAlwaysOn)

        frame_layout.addWidget(self.subtitles_table)

        # 初始化空表格
        self.clear_subtitle_table()

        # 更新字幕计数
        self.subtitle_count_label.setText(f"字幕条数: 0 | 双击内容编辑")

        # 设置自定义委托用于字幕文本编辑
        self.subtitle_delegate = SubtitleTextDelegate(self)
        self.subtitles_table.setItemDelegateForColumn(3, self.subtitle_delegate)  # 字幕内容列

        # 连接双击事件
        self.subtitles_table.itemDoubleClicked.connect(self.on_subtitle_item_double_clicked)

        return background_frame

    def start_complete_process(self):
        """开始完整的处理流程"""
        try:
            from PySide6.QtWidgets import QMessageBox

            # 检查是否有视频文件
            if not hasattr(self, 'current_video_path') or not self.current_video_path:
                QMessageBox.warning(self, "提示", "请先上传视频文件！")
                return

            # 检查输出路径
            if not hasattr(self, 'parameter_panel') or not hasattr(self.parameter_panel,
                                                                   'output_path_edit') or not self.parameter_panel.output_path_edit.text().strip():
                QMessageBox.warning(self, "提示", "请先设置输出路径！")
                return

            # 显示确认对话框
            reply = QMessageBox.question(
                self,
                "确认开始自动化处理",
                "即将开始自动化视频翻译处理流程，包括：\n\n"
                "1. 视频转音频\n"
                "2. 人声分离（如果启用）\n"
                "3. 字幕提取\n"
                "4. 字幕翻译\n"
                "5. 字幕配音\n\n"
                "完成后将停止自动化，您可以选择是否进行视频合成。\n"
                "此过程可能需要较长时间，确定要开始吗？",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.Yes
            )

            if reply == QMessageBox.Yes:
                # 启动自动化流程
                self.is_auto_processing = True
                self.auto_process_step = 1
                self.voiceover_completed = False
                self.execute_complete_process()

        except Exception as e:
            print(f"开始处理流程时出错: {e}")
            QMessageBox.critical(self, "错误", f"启动处理流程失败: {str(e)}")

    def execute_complete_process(self):
        """执行完整的处理流程"""
        try:
            from PySide6.QtWidgets import QMessageBox, QProgressDialog
            from PySide6.QtCore import QTimer, QThread, QObject, Signal
            import os

            # 获取输出路径
            output_dir = self.parameter_panel.output_path_edit.text().strip()
            if not output_dir:
                QMessageBox.warning(self, "错误", "请先设置输出路径！")
                return

            # 确保输出目录存在
            os.makedirs(output_dir, exist_ok=True)

            # 创建进度对话框
            progress = QProgressDialog("正在初始化...", "取消", 0, 100, self)
            progress.setWindowTitle("FlipTalk AI - 视频处理中")
            progress.setModal(True)
            progress.setMinimumDuration(0)
            progress.show()

            # 开始音频提取流程
            self.start_audio_extraction(progress, output_dir)

        except Exception as e:
            print(f"执行处理流程时出错: {e}")
            QMessageBox.critical(self, "错误", f"处理过程中发生错误: {str(e)}")

    def start_audio_extraction(self, progress_dialog, output_dir):
        """开始音频提取流程"""
        try:
            # 显示进度并更新状态
            self.update_progress_display("正在初始化音频提取...", 5)
            progress_dialog.setLabelText("正在提取音频...")
            progress_dialog.setValue(10)

            # 初始化音频提取插件
            from plugins.audio_extractor import FFmpegAudioExtractor

            audio_extractor = FFmpegAudioExtractor()

            # 初始化插件
            config = {
                'output_format': 'wav',
                'sample_rate': 44100,
                'channels': 2
            }

            self.update_progress_display("正在配置音频提取参数...", 15)

            if not audio_extractor.initialize(config):
                raise Exception("音频提取插件初始化失败")

            self.update_progress_display("正在从视频中提取音频...", 30)
            progress_dialog.setLabelText("正在从视频中提取音频...")
            progress_dialog.setValue(30)

            print(f"开始从视频提取音频: {self.current_video_path}")
            print(f"输出目录: {output_dir}")

            # 提取音频
            self.update_progress_display("正在执行FFmpeg音频提取...", 60)
            audio_path = audio_extractor.extract(self.current_video_path, output_dir)

            self.update_progress_display("音频提取完成！", 100)
            progress_dialog.setLabelText("音频提取完成！")
            progress_dialog.setValue(100)

            # 延迟关闭进度对话框，让用户看到完成状态
            from PySide6.QtCore import QTimer
            def close_progress():
                progress_dialog.close()
                # 延迟重置进度显示，让用户看到完成状态
                QTimer.singleShot(2000, self.reset_progress_display)
                self.show_audio_extraction_result(audio_path)

            QTimer.singleShot(1000, close_progress)

        except Exception as e:
            progress_dialog.close()
            self.reset_progress_display()  # 重置进度显示
            error_msg = f"音频提取失败: {str(e)}"
            print(error_msg)
            QMessageBox.critical(self, "音频提取失败", error_msg)

    def show_audio_extraction_result(self, audio_path):
        """显示音频提取结果并检查是否需要进行人声分离"""
        try:
            import os
            from PySide6.QtWidgets import QMessageBox

            file_name = os.path.basename(audio_path)
            file_size = os.path.getsize(audio_path)

            # 格式化文件大小
            if file_size < 1024 * 1024:
                size_str = f"{file_size / 1024:.1f} KB"
            elif file_size < 1024 * 1024 * 1024:
                size_str = f"{file_size / (1024 * 1024):.1f} MB"
            else:
                size_str = f"{file_size / (1024 * 1024 * 1024):.1f} GB"

            # 保存音频文件路径，供后续步骤使用
            self.current_audio_path = audio_path

            print(f"音频提取完成: {audio_path}")

            # 检查是否启用了人声分离
            voice_separation_enabled = hasattr(self.parameter_panel, 'voice_separation_checkbox') and \
                                       self.parameter_panel.voice_separation_checkbox.isChecked()

            if voice_separation_enabled:
                print(f"✅ 音频提取完成: {audio_path}")
                print("🔄 检测到已启用人声分离，自动开始人声分离处理...")

                if self.is_auto_processing:
                    self.auto_process_step = 2  # 人声分离步骤
                    self.update_progress_display("自动化流程：正在进行人声分离...", 25)

                # 直接开始人声分离流程，不弹窗确认
                self.start_voice_separation(audio_path)
            else:
                print(f"✅ 音频提取完成: {audio_path}")
                print("⏭️ 人声分离功能未启用，跳过人声分离步骤")

                if self.is_auto_processing:
                    self.auto_process_step = 3  # 字幕提取步骤
                    self.update_progress_display("自动化流程：正在进行字幕提取...", 40)
                else:
                    # 非自动化模式才显示确认对话框
                    QMessageBox.information(
                        self,
                        "音频提取完成",
                        f"音频提取成功！\n\n"
                        f"文件名: {file_name}\n"
                        f"文件大小: {size_str}\n"
                        f"保存路径: {audio_path}\n\n"
                        f"人声分离功能未启用，即将开始字幕提取..."
                    )

                # 直接对原始音频进行字幕提取
                print("🔄 开始对原始音频文件进行字幕提取...")
                self.start_subtitle_extraction(audio_path)

        except Exception as e:
            print(f"显示音频提取结果时出错: {e}")
            QMessageBox.warning(self, "提示", f"音频提取完成，但显示结果时出错: {str(e)}")

    def start_voice_separation(self, audio_path):
        """开始人声分离流程"""
        try:
            from PySide6.QtCore import QTimer
            import os

            # 获取输出路径
            output_dir = self.parameter_panel.output_path_edit.text().strip()
            if not output_dir:
                QMessageBox.warning(self, "错误", "输出路径未设置！")
                return

            # 创建人声分离子目录
            voice_separation_dir = os.path.join(output_dir, "voice_separation")
            os.makedirs(voice_separation_dir, exist_ok=True)

            # 更新进度显示（只使用底部进度条）
            self.update_progress_display("正在初始化CascadedNet人声分离...", 10)

            # 导入CascadedNet插件
            from plugins.voice_separator import CascadedNetVoiceSeparationPlugin

            # 创建插件实例
            voice_separator = CascadedNetVoiceSeparationPlugin()

            self.update_progress_display("正在加载CascadedNet模型...", 20)

            # 初始化插件
            if not voice_separator.initialize({}):
                self.reset_progress_display()
                QMessageBox.critical(self, "人声分离失败", "CascadedNet插件初始化失败！\n\n请检查模型文件是否存在。")
                return

            self.update_progress_display("正在执行CascadedNet人声分离...", 40)

            print(f"开始人声分离: {audio_path}")
            print(f"输出目录: {voice_separation_dir}")

            # 执行人声分离
            result = voice_separator.separate(
                audio_path=audio_path,
                output_dir=voice_separation_dir,
                output_options={'vocals': True, 'background': True}
            )

            self.update_progress_display("人声分离完成！", 100)

            # 清理插件资源
            voice_separator.cleanup()

            # 延迟重置进度显示，让用户看到完成状态
            def show_result():
                # 延迟重置进度显示，让用户看到完成状态
                QTimer.singleShot(2000, self.reset_progress_display)
                self.show_voice_separation_result(result, voice_separation_dir)

            QTimer.singleShot(1000, show_result)

        except Exception as e:
            self.reset_progress_display()
            error_msg = f"人声分离失败: {str(e)}"
            print(error_msg)
            QMessageBox.critical(self, "人声分离失败", error_msg)

    def show_voice_separation_result(self, result, output_dir):
        """显示人声分离结果"""
        try:
            import os
            from PySide6.QtWidgets import QMessageBox

            if not result:
                QMessageBox.warning(self, "人声分离结果", "人声分离完成，但未生成输出文件。")
                return

            # 统计结果信息
            result_info = []
            total_size = 0

            for track_type, file_path in result.items():
                if os.path.exists(file_path):
                    file_name = os.path.basename(file_path)
                    file_size = os.path.getsize(file_path)
                    total_size += file_size

                    if file_size < 1024 * 1024:
                        size_str = f"{file_size / 1024:.1f} KB"
                    elif file_size < 1024 * 1024 * 1024:
                        size_str = f"{file_size / (1024 * 1024):.1f} MB"
                    else:
                        size_str = f"{file_size / (1024 * 1024 * 1024):.1f} GB"

                    track_name = "人声" if track_type == "vocals" else "背景音乐"
                    result_info.append(f"{track_name}: {file_name} ({size_str})")

            # 格式化总大小
            if total_size < 1024 * 1024:
                total_size_str = f"{total_size / 1024:.1f} KB"
            elif total_size < 1024 * 1024 * 1024:
                total_size_str = f"{total_size / (1024 * 1024):.1f} MB"
            else:
                total_size_str = f"{total_size / (1024 * 1024 * 1024):.1f} GB"

            # 获取原始音频信息
            original_audio_info = ""
            if hasattr(self, 'current_audio_path') and self.current_audio_path:
                try:
                    original_file_name = os.path.basename(self.current_audio_path)
                    original_file_size = os.path.getsize(self.current_audio_path)

                    if original_file_size < 1024 * 1024:
                        original_size_str = f"{original_file_size / 1024:.1f} KB"
                    elif original_file_size < 1024 * 1024 * 1024:
                        original_size_str = f"{original_file_size / (1024 * 1024):.1f} MB"
                    else:
                        original_size_str = f"{original_file_size / (1024 * 1024 * 1024):.1f} GB"

                    original_audio_info = f"原始音频: {original_file_name} ({original_size_str})\n\n"
                except:
                    pass

            # 保存分离结果路径，供后续步骤使用
            self.current_vocals_path = result.get('vocals')
            self.current_background_path = result.get('background')

            print(f"✅ 人声分离完成:")
            for track_type, file_path in result.items():
                print(f"  {track_type}: {file_path}")

            if self.is_auto_processing:
                # 自动化模式：不显示确认对话框，直接进入下一步
                self.auto_process_step = 3  # 字幕提取步骤
                self.update_progress_display("自动化流程：正在进行字幕提取...", 50)
                print("🔄 自动化流程：人声分离完成，开始字幕提取...")
            else:
                # 非自动化模式：显示确认对话框
                QMessageBox.information(
                    self,
                    "音频处理完成",
                    f"音频提取和人声分离处理完成！\n\n"
                    f"{original_audio_info}"
                    f"人声分离结果:\n" + "\n".join(result_info) + f"\n\n"
                                                                  f"分离文件总大小: {total_size_str}\n"
                                                                  f"保存目录: {output_dir}\n\n"
                                                                  f"即将开始字幕提取..."
                )

            # 开始字幕提取流程 - 使用人声文件
            if self.current_vocals_path and os.path.exists(self.current_vocals_path):
                print("🔄 开始对人声文件进行字幕提取...")
                self.start_subtitle_extraction(self.current_vocals_path)
            else:
                print("❌ 警告：人声文件不存在，无法进行字幕提取")
                if self.is_auto_processing:
                    self.is_auto_processing = False
                    QMessageBox.critical(self, "自动化流程中断", "人声文件不存在，无法继续字幕提取！")

        except Exception as e:
            print(f"显示人声分离结果时出错: {e}")
            QMessageBox.warning(self, "提示", f"人声分离完成，但显示结果时出错: {str(e)}")

    def clear_subtitle_table(self):
        """清空字幕表格"""
        try:
            self.subtitles_table.setRowCount(0)
            self.subtitle_count_label.setText("字幕条数: 0 | 双击内容编辑")
        except Exception as e:
            print(f"清空字幕表格时出错: {e}")

    def update_subtitle_table(self, segments):
        """更新字幕表格数据"""
        try:
            # 清空现有数据
            self.subtitles_table.setRowCount(0)

            # 添加新的字幕数据
            for i, segment in enumerate(segments):
                self.subtitles_table.insertRow(i)

                # 序号
                item = QTableWidgetItem(str(i + 1))
                item.setTextAlignment(Qt.AlignCenter)
                self.subtitles_table.setItem(i, 0, item)

                # 开始时间 - 转换为SRT格式
                start_time = self.format_time_for_srt(segment.get('start', 0))
                start_item = QTableWidgetItem(start_time)
                start_item.setTextAlignment(Qt.AlignCenter)
                self.subtitles_table.setItem(i, 1, start_item)

                # 结束时间 - 转换为SRT格式
                end_time = self.format_time_for_srt(segment.get('end', 0))
                end_item = QTableWidgetItem(end_time)
                end_item.setTextAlignment(Qt.AlignCenter)
                self.subtitles_table.setItem(i, 2, end_item)

                # 字幕内容
                original_text = segment.get('text', '').strip()
                # 显示时将多行文本转换为单行显示
                display_text = original_text.replace('\n', ' ').strip()
                content = QTableWidgetItem(display_text)
                content.setFlags(Qt.ItemIsSelectable | Qt.ItemIsEnabled | Qt.ItemIsEditable)  # 允许编辑
                content.setToolTip("双击编辑字幕内容")
                # 存储原始完整文本
                content.setData(Qt.UserRole + 1, original_text)
                self.subtitles_table.setItem(i, 3, content)

                # 状态
                status = QTableWidgetItem("已提取")
                status.setTextAlignment(Qt.AlignCenter)
                status.setBackground(QColor("#2B9D7C"))
                status.setForeground(QColor("#FFFFFF"))
                self.subtitles_table.setItem(i, 4, status)

                # 操作按钮
                self.create_subtitle_operation_buttons(i)

            # 更新字幕计数
            self.subtitle_count_label.setText(f"字幕条数: {len(segments)} | 双击内容编辑")

            print(f"字幕表格更新完成，共 {len(segments)} 条字幕")

        except Exception as e:
            print(f"更新字幕表格时出错: {e}")

    def create_subtitle_operation_buttons(self, row):
        """为指定行创建操作按钮"""
        try:
            # 操作按钮单元格
            operation_cell = QWidget()
            operation_layout = QHBoxLayout(operation_cell)
            operation_layout.setContentsMargins(0, 0, 0, 0)
            operation_layout.setSpacing(8)

            # 播放按钮
            play_btn = QPushButton("🔊")
            play_btn.setFixedSize(26, 26)
            play_btn.setToolTip("播放音频")
            play_btn.setStyleSheet("""
                QPushButton {
                    background-color: #3B82F6;
                    color: white;
                    border: none;
                    border-radius: 13px;
                    font-size: 12px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color:#60A5FA;
                }
                QPushButton:pressed {
                    background-color: #1D4ED8;
                }
            """)
            # 连接播放按钮点击事件，传递行号
            play_btn.clicked.connect(lambda checked, r=row: self.play_subtitle_audio(r))
            operation_layout.addWidget(play_btn)

            # 重新配音按钮
            retry_btn = QPushButton("🔄")
            retry_btn.setFixedSize(26, 26)
            retry_btn.setToolTip("重新配音")
            retry_btn.setStyleSheet("""
                QPushButton {
                    background-color: #2B9D7C;
                    color: white;
                    border: none;
                    border-radius: 13px;
                    font-size: 12px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #00CC55;
                }
            """)
            # 连接重新配音按钮
            retry_btn.clicked.connect(lambda checked, r=row: self.retry_single_subtitle_voiceover(r))
            operation_layout.addWidget(retry_btn)

            # 使布局居中
            operation_layout.setAlignment(Qt.AlignCenter)

            # 将操作单元格添加到表格
            self.subtitles_table.setCellWidget(row, 5, operation_cell)

        except Exception as e:
            print(f"创建操作按钮时出错: {e}")

    def format_time_for_srt(self, seconds):
        """将秒数转换为SRT时间格式 (HH:MM:SS,mmm)"""
        try:
            hours = int(seconds // 3600)
            minutes = int((seconds % 3600) // 60)
            secs = int(seconds % 60)
            milliseconds = int((seconds % 1) * 1000)
            return f"{hours:02d}:{minutes:02d}:{secs:02d},{milliseconds:03d}"
        except:
            return "00:00:00,000"

    def start_subtitle_extraction(self, audio_path):
        """开始字幕提取流程"""
        try:
            from PySide6.QtCore import QTimer
            import os

            # 获取输出路径
            output_dir = self.parameter_panel.output_path_edit.text().strip()
            if not output_dir:
                QMessageBox.warning(self, "错误", "输出路径未设置！")
                return

            # 创建字幕提取子目录
            subtitle_dir = os.path.join(output_dir, "subtitles")
            os.makedirs(subtitle_dir, exist_ok=True)

            # 更新进度显示
            self.update_progress_display("正在初始化WhisperX字幕提取...", 10)

            # 导入WhisperX插件
            from plugins.subtitle_extractor.plugin import WhisperXSubtitleExtractor

            # 创建插件实例
            subtitle_extractor = WhisperXSubtitleExtractor()

            self.update_progress_display("正在加载WhisperX模型...", 20)

            # 初始化插件
            config = {
                "device": "auto",  # 自动选择GPU/CPU
                "default_model": "medium",  # 使用medium模型平衡速度和质量
                "default_language": "auto",  # 自动语言检测
                "enable_uvr5": False,  # 禁用UVR5，因为我们已经有了分离的音频
                "output_formats": ["srt"]  # 仅保留SRT格式
            }

            if not subtitle_extractor.initialize(config):
                self.reset_progress_display()
                QMessageBox.critical(self, "字幕提取失败", "WhisperX插件初始化失败！\n\n请检查模型文件是否存在。")
                return

            self.update_progress_display("正在执行字幕提取...", 40)

            print(f"开始字幕提取: {audio_path}")
            print(f"输出目录: {subtitle_dir}")

            # 定义进度回调函数
            def progress_callback(message, progress):
                if progress is not None:
                    # 将WhisperX的进度映射到40-90%范围
                    mapped_progress = 40 + int(progress * 0.5)
                    self.update_progress_display(f"字幕提取中: {message}", mapped_progress)
                else:
                    self.update_progress_display(f"字幕提取中: {message}", 70)

            # 执行字幕提取
            result = subtitle_extractor.extract_subtitle(
                audio_path=audio_path,
                output_dir=subtitle_dir,
                model_name="medium",
                language="auto",
                output_formats=["srt"],  # 仅保留SRT格式
                enable_uvr5=False,
                target_duration=20 * 60,  # 20分钟分割
                progress_callback=progress_callback
            )

            self.update_progress_display("字幕提取完成！", 100)

            # 清理插件资源
            subtitle_extractor.cleanup()

            # 延迟显示结果
            def show_result():
                # 延迟重置进度显示，让用户看到完成状态
                QTimer.singleShot(2000, self.reset_progress_display)
                self.show_subtitle_extraction_result(result, subtitle_dir)

            QTimer.singleShot(1000, show_result)

        except Exception as e:
            self.reset_progress_display()
            error_msg = f"字幕提取失败: {str(e)}"
            print(error_msg)
            QMessageBox.critical(self, "字幕提取失败", error_msg)

    def show_subtitle_extraction_result(self, result, output_dir):
        """显示字幕提取结果并更新字幕编辑区"""
        try:
            import os
            from PySide6.QtWidgets import QMessageBox

            if not result or not result.get("success", False):
                error_msg = result.get("error", "未知错误") if result else "字幕提取失败"
                QMessageBox.warning(self, "字幕提取结果", f"字幕提取失败：{error_msg}")
                return

            # 获取提取结果信息
            segments_count = result.get("segments_count", 0)
            processing_time = result.get("processing_time", 0)
            model_used = result.get("model_used", "unknown")
            language = result.get("language", "unknown")
            output_files = result.get("output_files", {})

            # 统计SRT文件信息
            srt_file_info = ""
            srt_file_size = 0

            srt_file_path = output_files.get("srt")
            if srt_file_path and os.path.exists(srt_file_path):
                file_name = os.path.basename(srt_file_path)
                srt_file_size = os.path.getsize(srt_file_path)

                if srt_file_size < 1024:
                    size_str = f"{srt_file_size} B"
                elif srt_file_size < 1024 * 1024:
                    size_str = f"{srt_file_size / 1024:.1f} KB"
                else:
                    size_str = f"{srt_file_size / (1024 * 1024):.1f} MB"

                srt_file_info = f"SRT字幕文件: {file_name} ({size_str})"
            else:
                srt_file_info = "SRT字幕文件: 未生成"

            # 保存原始SRT文件路径
            srt_file_path = output_files.get("srt")
            if srt_file_path:
                self.original_srt_file_path = srt_file_path
                print(f"📁 原始SRT文件路径已保存: {srt_file_path}")

            # 初始化改动标记
            self.subtitle_has_changes = False

            # 加载字幕数据到编辑区
            self.load_subtitles_from_result(result)

            if self.is_auto_processing:
                # 自动化模式：不显示确认对话框，直接进入下一步
                self.auto_process_step = 4  # 字幕翻译步骤
                self.update_progress_display("自动化流程：正在进行字幕翻译...", 65)
                print(f"🔄 自动化流程：字幕提取完成（{segments_count}条），开始字幕翻译...")
            else:
                # 非自动化模式：显示确认对话框
                QMessageBox.information(
                    self,
                    "字幕提取完成",
                    f"WhisperX字幕提取成功！\n\n"
                    f"提取信息:\n"
                    f"字幕条数: {segments_count}\n"
                    f"处理时间: {processing_time:.1f}秒\n"
                    f"使用模型: {model_used}\n"
                    f"检测语言: {language}\n\n"
                    f"输出文件:\n{srt_file_info}\n\n"
                    f"保存目录: {output_dir}\n\n"
                    f"字幕已显示在编辑区，即将开始翻译..."
                )

            # 开始字幕翻译流程
            self.start_subtitle_translation(language)

            print(f"字幕提取完成:")
            print(f"  字幕条数: {segments_count}")
            print(f"  处理时间: {processing_time:.1f}秒")
            print(f"  使用模型: {model_used}")
            print(f"  检测语言: {language}")
            if srt_file_path:
                print(f"  SRT文件: {srt_file_path}")

        except Exception as e:
            print(f"显示字幕提取结果时出错: {e}")
            QMessageBox.warning(self, "提示", f"字幕提取完成，但显示结果时出错: {str(e)}")

    def load_subtitles_from_result(self, result):
        """从字幕提取结果加载字幕到编辑区"""
        try:
            # 从详细结果文件加载字幕数据
            details_file = result.get("details_file")
            if details_file and os.path.exists(details_file):
                import json
                with open(details_file, 'r', encoding='utf-8') as f:
                    details = json.load(f)
                    segments = details.get("segments", [])
            else:
                # 如果没有详细文件，尝试从结果中直接获取
                segments = result.get("segments", [])

            if segments:
                # 更新字幕表格
                self.update_subtitle_table(segments)
                print(f"已加载 {len(segments)} 条字幕到编辑区")
            else:
                print("警告：未找到字幕数据")

        except Exception as e:
            print(f"加载字幕数据时出错: {e}")

    def on_subtitle_item_double_clicked(self, item):
        """处理字幕表格双击事件 - 编辑字幕内容"""
        try:
            row = item.row()
            col = item.column()

            # 如果是字幕内容列，进入内联编辑模式
            if col == 3:
                print(f"📝 编辑字幕 {row + 1}")
                # 创建模型索引
                model_index = self.subtitles_table.model().index(row, col)
                self.subtitles_table.edit(model_index)
                return

            # 其他列：显示提示信息
            print(f"💡 双击字幕内容列（第4列）可以编辑字幕文本")

        except Exception as e:
            print(f"❌ 处理双击事件失败: {e}")

    def update_subtitle_text(self, new_text, row_index):
        """更新字幕文本"""
        try:
            if row_index < self.subtitles_table.rowCount():
                print(f"📝 字幕文本更新:")
                print(f"   行索引: {row_index + 1}")
                print(f"   新文本: {new_text}")

                # 更新表格显示
                item = self.subtitles_table.item(row_index, 3)  # 字幕内容列
                if item:
                    # 显示时将多行文本转换为单行显示
                    display_text = new_text.replace('\n', ' ').strip()
                    item.setText(display_text)
                    # 保存原始多行文本到用户数据
                    item.setData(Qt.UserRole + 1, new_text)

                    # 更新状态为已编辑
                    status_item = self.subtitles_table.item(row_index, 4)
                    if status_item:
                        status_item.setText("已编辑")
                        status_item.setBackground(QColor("#FFA500"))  # 橙色背景
                        status_item.setForeground(QColor("#FFFFFF"))

                    print(f"✅ 字幕 {row_index + 1} 文本已更新并保存")

                    # 关闭编辑器
                    self.subtitles_table.closePersistentEditor(item)

                    # 触发自动保存
                    self.auto_save_subtitles()
                else:
                    print(f"❌ 未找到字幕 {row_index + 1} 的表格项")
            else:
                print(f"❌ 行索引 {row_index} 超出范围")

        except Exception as e:
            print(f"❌ 更新字幕文本失败: {e}")

    def save_subtitles_to_srt(self):
        """保存编辑后的字幕到原始SRT文件"""
        try:
            # 检查是否有字幕数据
            if self.subtitles_table.rowCount() == 0:
                print("⚠️ 没有字幕数据可保存")
                return False

            # 使用原始SRT文件路径，如果不存在则创建新路径
            if hasattr(self, 'original_srt_file_path') and self.original_srt_file_path:
                srt_file_path = self.original_srt_file_path
                print(f"📝 更新原始SRT文件: {srt_file_path}")
            else:
                # 如果没有原始路径，创建新的SRT文件
                output_dir = self.parameter_panel.output_path_edit.text().strip()
                if not output_dir:
                    print("❌ 输出路径未设置")
                    return False

                subtitle_dir = os.path.join(output_dir, "subtitles")
                os.makedirs(subtitle_dir, exist_ok=True)

                # 使用视频文件名生成SRT文件名
                if hasattr(self, 'current_video_name') and self.current_video_name:
                    base_name = os.path.splitext(self.current_video_name)[0]
                    srt_file_path = os.path.join(subtitle_dir, f"{base_name}.srt")
                else:
                    srt_file_path = os.path.join(subtitle_dir, "subtitles.srt")

                print(f"📝 创建新SRT文件: {srt_file_path}")

            # 收集所有字幕数据
            subtitle_data = []
            for row in range(self.subtitles_table.rowCount()):
                try:
                    # 获取时间信息
                    start_time_item = self.subtitles_table.item(row, 1)
                    end_time_item = self.subtitles_table.item(row, 2)
                    content_item = self.subtitles_table.item(row, 3)

                    if start_time_item and end_time_item and content_item:
                        start_time = start_time_item.text()
                        end_time = end_time_item.text()
                        # 获取完整的文本内容（包括多行）
                        content = content_item.data(Qt.UserRole + 1) or content_item.text()

                        subtitle_data.append({
                            'index': row + 1,
                            'start_time': start_time,
                            'end_time': end_time,
                            'content': content.strip()
                        })
                except Exception as e:
                    print(f"⚠️ 跳过第 {row + 1} 行字幕，数据不完整: {e}")
                    continue

            # 写入SRT文件（替换原文件）
            with open(srt_file_path, 'w', encoding='utf-8') as f:
                for subtitle in subtitle_data:
                    f.write(f"{subtitle['index']}\n")
                    f.write(f"{subtitle['start_time']} --> {subtitle['end_time']}\n")
                    f.write(f"{subtitle['content']}\n\n")

            # 计算文件大小
            file_size = os.path.getsize(srt_file_path)
            if file_size < 1024:
                size_str = f"{file_size} B"
            elif file_size < 1024 * 1024:
                size_str = f"{file_size / 1024:.1f} KB"
            else:
                size_str = f"{file_size / (1024 * 1024):.1f} MB"

            print(f"✅ 字幕保存成功:")
            print(f"   文件路径: {srt_file_path}")
            print(f"   字幕条数: {len(subtitle_data)}")
            print(f"   文件大小: {size_str}")

            # 保存文件路径供后续使用
            self.current_srt_file_path = srt_file_path
            self.original_srt_file_path = srt_file_path

            # 标记为已保存，重置改动标记
            self.subtitle_has_changes = False

            return True

        except Exception as e:
            print(f"❌ 保存字幕文件失败: {e}")
            return False

    def auto_save_subtitles(self):
        """智能自动保存字幕（只在有改动时触发一次）"""
        try:
            # 标记有改动
            self.subtitle_has_changes = True

            # 如果已经有保存定时器在运行，重置定时器
            if hasattr(self, 'auto_save_timer') and self.auto_save_timer.isActive():
                self.auto_save_timer.stop()
                print("⏰ 重置自动保存定时器...")

            # 创建延迟保存定时器（单次触发）
            self.auto_save_timer = QTimer()
            self.auto_save_timer.setSingleShot(True)
            self.auto_save_timer.timeout.connect(self.execute_auto_save)
            self.auto_save_timer.start(2000)  # 2秒后自动保存

            print("⏰ 检测到字幕改动，2秒后自动保存...")

        except Exception as e:
            print(f"❌ 设置自动保存失败: {e}")

    def execute_auto_save(self):
        """执行自动保存"""
        try:
            # 只有在有改动时才保存
            if hasattr(self, 'subtitle_has_changes') and self.subtitle_has_changes:
                print("💾 执行自动保存...")
                success = self.save_subtitles_to_srt()
                if success:
                    print("✅ 自动保存完成")
                else:
                    print("❌ 自动保存失败")
            else:
                print("ℹ️ 无改动，跳过自动保存")

        except Exception as e:
            print(f"❌ 执行自动保存失败: {e}")

    def start_subtitle_translation(self, detected_language="auto"):
        """开始字幕翻译流程"""
        try:
            # 检查是否有字幕数据
            if self.subtitles_table.rowCount() == 0:
                print("⚠️ 没有字幕数据可翻译")
                return

            # 获取翻译设置
            translation_settings = self.get_translation_settings()
            if not translation_settings:
                print("❌ 获取翻译设置失败")
                return

            # 更新进度显示
            self.update_progress_display("正在初始化字幕翻译...", 10)

            # 导入字幕翻译插件
            from plugins.subtitle_translator.plugin import SubtitleTranslatorPlugin

            # 创建插件实例
            self.translation_plugin = SubtitleTranslatorPlugin()

            self.update_progress_display("正在设置翻译器...", 20)

            # 设置翻译器
            translator_type = translation_settings['translator_type']
            api_key = translation_settings.get('api_key', '')

            if not self.translation_plugin.set_translator(translator_type, api_key):
                self.reset_progress_display()
                QMessageBox.critical(self, "字幕翻译失败", f"设置{translator_type}翻译器失败！\n\n请检查API密钥配置。")
                return

            self.update_progress_display("正在准备翻译数据...", 30)

            # 收集字幕文本
            subtitle_texts = []
            for row in range(self.subtitles_table.rowCount()):
                content_item = self.subtitles_table.item(row, 3)
                if content_item:
                    # 获取完整的文本内容（包括多行）
                    text = content_item.data(Qt.UserRole + 1) or content_item.text()
                    subtitle_texts.append(text.strip())
                else:
                    subtitle_texts.append("")

            print(f"开始翻译 {len(subtitle_texts)} 条字幕")
            print(f"翻译器: {translator_type}")
            print(f"源语言: {translation_settings['source_lang']}")
            print(f"目标语言: {translation_settings['target_lang']}")

            # 创建翻译工作线程
            from ui.subtitle_translation_dialog import TranslationWorker

            self.translation_worker = TranslationWorker(
                texts=subtitle_texts,
                plugin=self.translation_plugin,
                translator_type=translator_type,
                source_lang=translation_settings['source_lang'],
                target_lang=translation_settings['target_lang']
            )

            # 连接信号
            self.translation_worker.translation_completed.connect(self.on_translation_completed)
            self.translation_worker.progress_updated.connect(self.on_translation_progress)
            self.translation_worker.error_occurred.connect(self.on_translation_error)
            self.translation_worker.finished.connect(self.on_translation_finished)

            # 开始翻译
            self.update_progress_display("正在执行字幕翻译...", 40)
            self.translation_worker.start()

        except Exception as e:
            self.reset_progress_display()
            error_msg = f"字幕翻译失败: {str(e)}"
            print(error_msg)
            QMessageBox.critical(self, "字幕翻译失败", error_msg)

    def get_translation_settings(self):
        """获取翻译设置"""
        try:
            # 从参数面板获取翻译设置
            translation_channel = "谷歌翻译 (免费)"  # 默认值
            target_language = "中文 (zh)"  # 默认值

            # 尝试从参数面板获取实际设置
            if hasattr(self, 'parameter_panel'):
                # 查找翻译渠道设置
                for i in range(self.parameter_panel.layout().count()):
                    widget = self.parameter_panel.layout().itemAt(i).widget()
                    if hasattr(widget, 'findChild'):
                        # 查找翻译渠道下拉框
                        combos = widget.findChildren(QComboBox)
                        for combo in combos:
                            if combo.objectName() == "translation_channel_combo":
                                translation_channel = combo.currentText()
                            elif combo.objectName() == "target_language_combo":
                                target_language = combo.currentText()

            # 解析翻译器类型
            if "谷歌翻译" in translation_channel:
                translator_type = "google"
                api_key = ""
            elif "微软翻译" in translation_channel:
                translator_type = "microsoft"
                api_key = ""  # 需要从配置中获取
            elif "DeepL" in translation_channel:
                translator_type = "deepl"
                api_key = ""  # 需要从配置中获取
            else:
                translator_type = "google"
                api_key = ""

            # 解析目标语言
            if "中文" in target_language:
                target_lang = "zh"
            elif "英语" in target_language:
                target_lang = "en"
            elif "日语" in target_language:
                target_lang = "ja"
            elif "韩语" in target_language:
                target_lang = "ko"
            elif "法语" in target_language:
                target_lang = "fr"
            elif "德语" in target_language:
                target_lang = "de"
            elif "西班牙语" in target_language:
                target_lang = "es"
            elif "俄语" in target_language:
                target_lang = "ru"
            elif "阿拉伯语" in target_language:
                target_lang = "ar"
            elif "泰语" in target_language:
                target_lang = "th"
            else:
                target_lang = "zh"

            return {
                'translator_type': translator_type,
                'source_lang': 'auto',  # 自动检测源语言
                'target_lang': target_lang,
                'api_key': api_key
            }

        except Exception as e:
            print(f"获取翻译设置失败: {e}")
            return None

    def on_translation_completed(self, index, original, translated):
        """单个翻译完成回调"""
        try:
            if 0 <= index < self.subtitles_table.rowCount():
                # 更新字幕表格中的翻译内容
                content_item = self.subtitles_table.item(index, 3)
                if content_item:
                    # 显示翻译后的文本
                    display_text = translated.replace('\n', ' ').strip()
                    content_item.setText(display_text)
                    # 保存原始翻译文本到用户数据
                    content_item.setData(Qt.UserRole + 1, translated)

                    # 更新状态为已翻译
                    status_item = self.subtitles_table.item(index, 4)
                    if status_item:
                        status_item.setText("已翻译")
                        status_item.setBackground(QColor("#4ECDC4"))  # 青色背景
                        status_item.setForeground(QColor("#FFFFFF"))

                    print(f"✅ 字幕 {index + 1} 翻译完成: {original[:30]}... → {translated[:30]}...")

                    # 触发自动保存
                    self.auto_save_subtitles()

        except Exception as e:
            print(f"❌ 更新翻译结果失败: {e}")

    def on_translation_progress(self, progress):
        """翻译进度更新回调"""
        try:
            # 将翻译进度映射到40-90%范围
            mapped_progress = 40 + int(progress * 0.5)
            self.update_progress_display(f"正在翻译字幕... {progress}%", mapped_progress)
        except Exception as e:
            print(f"❌ 更新翻译进度失败: {e}")

    def on_translation_error(self, error_msg):
        """翻译错误回调"""
        try:
            print(f"❌ 翻译错误: {error_msg}")
        except Exception as e:
            print(f"❌ 处理翻译错误失败: {e}")

    def on_translation_finished(self):
        """翻译完成回调"""
        try:
            self.update_progress_display("字幕翻译完成！", 100)

            # 统计翻译结果
            translated_count = 0
            total_count = self.subtitles_table.rowCount()

            for row in range(total_count):
                status_item = self.subtitles_table.item(row, 4)
                if status_item and status_item.text() == "已翻译":
                    translated_count += 1

            print(f"字幕翻译完成:")
            print(f"  总字幕数: {total_count}")
            print(f"  翻译成功: {translated_count}")
            print(f"  成功率: {translated_count / total_count * 100:.1f}%")

            # 延迟显示结果并开始配音
            def show_result_and_start_voiceover():
                if self.is_auto_processing:
                    # 自动化模式：不显示确认对话框，直接进入下一步
                    if translated_count > 0:
                        self.auto_process_step = 5  # 字幕配音步骤
                        self.update_progress_display("自动化流程：正在进行字幕配音...", 80)
                        print(f"🔄 自动化流程：字幕翻译完成（{translated_count}/{total_count}），开始字幕配音...")
                        QTimer.singleShot(1000, self.start_subtitle_voiceover)
                    else:
                        # 翻译失败，停止自动化
                        self.is_auto_processing = False
                        self.reset_progress_display()
                        QMessageBox.critical(
                            self, "自动化流程中断",
                            f"字幕翻译失败，自动化流程已停止！\n\n"
                            f"可能的原因:\n"
                            f"1. 网络连接问题\n"
                            f"2. API密钥配置错误\n"
                            f"3. 翻译服务暂时不可用"
                        )
                else:
                    # 非自动化模式：显示结果并询问是否继续
                    QTimer.singleShot(2000, self.reset_progress_display)
                    self.show_translation_result(translated_count, total_count)

                    # 如果翻译成功，开始配音流程
                    if translated_count > 0:
                        QTimer.singleShot(3000, self.start_subtitle_voiceover)

            QTimer.singleShot(1000, show_result_and_start_voiceover)

        except Exception as e:
            print(f"❌ 处理翻译完成失败: {e}")

    def show_translation_result(self, translated_count, total_count):
        """显示翻译结果"""
        try:
            success_rate = translated_count / total_count * 100 if total_count > 0 else 0

            if translated_count == total_count:
                # 全部翻译成功
                QMessageBox.information(
                    self,
                    "字幕翻译完成",
                    f"字幕翻译全部完成！\n\n"
                    f"翻译统计:\n"
                    f"总字幕数: {total_count}\n"
                    f"翻译成功: {translated_count}\n"
                    f"成功率: {success_rate:.1f}%\n\n"
                    f"所有字幕已更新为翻译内容，可以继续编辑或进行下一步处理。"
                )
            elif translated_count > 0:
                # 部分翻译成功
                QMessageBox.warning(
                    self,
                    "字幕翻译部分完成",
                    f"字幕翻译部分完成！\n\n"
                    f"翻译统计:\n"
                    f"总字幕数: {total_count}\n"
                    f"翻译成功: {translated_count}\n"
                    f"翻译失败: {total_count - translated_count}\n"
                    f"成功率: {success_rate:.1f}%\n\n"
                    f"已翻译的字幕已更新，未翻译的字幕保持原文。\n"
                    f"您可以手动编辑未翻译的字幕或重新尝试翻译。"
                )
            else:
                # 翻译失败
                QMessageBox.critical(
                    self,
                    "字幕翻译失败",
                    f"字幕翻译失败！\n\n"
                    f"总字幕数: {total_count}\n"
                    f"翻译成功: 0\n\n"
                    f"可能的原因:\n"
                    f"1. 网络连接问题\n"
                    f"2. API密钥配置错误\n"
                    f"3. 翻译服务暂时不可用\n\n"
                    f"请检查网络和配置后重试。"
                )

        except Exception as e:
            print(f"显示翻译结果时出错: {e}")
            QMessageBox.warning(self, "提示", f"字幕翻译完成，但显示结果时出错: {str(e)}")

    def start_subtitle_voiceover(self):
        """开始字幕配音流程"""
        try:
            # 检查是否有字幕数据
            if self.subtitles_table.rowCount() == 0:
                print("⚠️ 没有字幕数据可配音")
                return

            # 更新进度显示
            self.update_progress_display("正在初始化TTS配音...", 10)

            # 获取配音设置
            voiceover_settings = self.get_voiceover_settings()
            if not voiceover_settings:
                print("❌ 获取配音设置失败")
                return

            print(f"开始字幕配音:")
            print(f"  TTS引擎: {voiceover_settings['engine_name']}")
            print(f"  声音: {voiceover_settings['voice']}")
            print(f"  语速: {voiceover_settings['speed']}x")

            # 导入TTS管理器
            from plugins.tts.tts_manager_plugin import TtsManagerPlugin

            # 创建TTS管理器实例
            self.tts_manager = TtsManagerPlugin()

            self.update_progress_display("正在初始化TTS引擎...", 20)

            # 初始化TTS管理器
            from core.config_manager import get_config_manager
            config_manager = get_config_manager()
            tts_config = config_manager.get_tts_config()

            if not self.tts_manager.initialize(tts_config):
                self.reset_progress_display()
                QMessageBox.critical(self, "配音失败", "TTS引擎初始化失败！\n\n请检查TTS配置。")
                return

            # 设置当前引擎
            engine_name = voiceover_settings['engine_name']
            print(f"🔄 正在切换到TTS引擎: {engine_name}")

            # 如果是Azure TTS，检查API密钥配置
            if engine_name == "azure_tts":
                from core.config_manager import get_config_manager
                config_manager = get_config_manager()
                azure_key = config_manager.get("api_keys.azure_tts_key", "")

                if not azure_key:
                    self.reset_progress_display()
                    QMessageBox.warning(self, "Azure TTS未配置",
                                        "Azure TTS API密钥未配置，将使用Edge TTS进行配音。\n\n"
                                        "如需使用Azure TTS，请在设置中配置API密钥。")
                    engine_name = "edge_tts"
                    voiceover_settings['engine_name'] = "edge_tts"
                    print("⚠️ Azure TTS未配置，切换到Edge TTS")

            # 尝试设置引擎
            if not self.tts_manager.set_current_engine(engine_name):
                self.reset_progress_display()
                QMessageBox.critical(self, "配音失败", f"无法切换到{engine_name}引擎！\n\n请检查引擎配置。")
                return

            print(f"✅ 成功切换到TTS引擎: {engine_name}")

            self.update_progress_display("正在准备配音数据...", 30)

            # 收集字幕数据
            subtitles_data = []
            for row in range(self.subtitles_table.rowCount()):
                try:
                    # 获取时间信息
                    start_time_item = self.subtitles_table.item(row, 1)
                    end_time_item = self.subtitles_table.item(row, 2)
                    content_item = self.subtitles_table.item(row, 3)

                    if start_time_item and end_time_item and content_item:
                        start_time_str = start_time_item.text()
                        end_time_str = end_time_item.text()

                        # 转换时间格式为秒数
                        start_seconds = self.parse_srt_time_to_seconds(start_time_str)
                        end_seconds = self.parse_srt_time_to_seconds(end_time_str)

                        # 获取完整的文本内容（包括多行）
                        text = content_item.data(Qt.UserRole + 1) or content_item.text()

                        subtitles_data.append({
                            'index': row + 1,
                            'start': start_seconds,
                            'end': end_seconds,
                            'duration': end_seconds - start_seconds,
                            'text': text.strip()
                        })
                except Exception as e:
                    print(f"⚠️ 跳过第 {row + 1} 行字幕，数据不完整: {e}")
                    continue

            if not subtitles_data:
                self.reset_progress_display()
                QMessageBox.warning(self, "配音失败", "没有有效的字幕数据可配音！")
                return

            # 创建输出目录
            output_dir = self.parameter_panel.output_path_edit.text().strip()
            if not output_dir:
                self.reset_progress_display()
                QMessageBox.critical(self, "配音失败", "输出路径未设置！")
                return

            voiceover_dir = os.path.join(output_dir, "voiceover")
            os.makedirs(voiceover_dir, exist_ok=True)

            print(f"配音输出目录: {voiceover_dir}")
            print(f"字幕数量: {len(subtitles_data)}")

            # 创建配音工作线程
            from ui.subtitle_voiceover_dialog import VoiceoverWorker

            self.voiceover_worker = VoiceoverWorker(
                tts_manager=self.tts_manager,
                subtitles=subtitles_data,
                voice=voiceover_settings['voice'],
                speed=voiceover_settings['speed'],
                output_dir=voiceover_dir,
                engine_name=voiceover_settings['engine_name']
            )

            # 连接信号
            self.voiceover_worker.progress_updated.connect(self.on_voiceover_progress)
            self.voiceover_worker.voiceover_completed.connect(self.on_voiceover_completed)
            self.voiceover_worker.error_occurred.connect(self.on_voiceover_error)
            self.voiceover_worker.finished_all.connect(self.on_voiceover_finished)

            # 初始化音频重叠检测器
            from ui.subtitle_voiceover_dialog import AudioOverlapDetector
            self.overlap_detector = AudioOverlapDetector()

            # 保存当前配音设置，供重新配音使用
            self.current_voice_id = voiceover_settings['voice']
            self.current_speed = voiceover_settings['speed']
            self.current_engine = voiceover_settings['engine_name']
            self.subtitle_segments = subtitles_data  # 保存字幕数据

            print(f"💾 已保存配音设置: 引擎={self.current_engine}, 声音={self.current_voice_id}, 语速={self.current_speed}")

            # 开始配音
            self.update_progress_display("正在生成配音音频...", 40)
            self.voiceover_worker.start()

        except Exception as e:
            self.reset_progress_display()
            error_msg = f"字幕配音失败: {str(e)}"
            print(error_msg)
            QMessageBox.critical(self, "配音失败", error_msg)

    def get_voiceover_settings(self):
        """获取配音设置"""
        try:
            # 从参数面板获取配音设置
            voice_channel = "Edge TTS (免费)"  # 默认值
            voice_role = "zh-CN-XiaoxiaoNeural"  # 默认值
            voice_speed = 1.0  # 默认语速

            # 从参数面板实例获取设置
            if hasattr(self, 'parameter_panel') and hasattr(self.parameter_panel, 'voice_channel_combo'):
                voice_channel = self.parameter_panel.voice_channel_combo.currentText()
                print(f"获取到配音渠道: {voice_channel}")

            if hasattr(self, 'parameter_panel') and hasattr(self.parameter_panel, 'voice_role_combo'):
                # 优先获取currentData（声音ID），如果没有则使用currentText
                voice_role = self.parameter_panel.voice_role_combo.currentData()
                if not voice_role:
                    voice_role = self.parameter_panel.voice_role_combo.currentText()
                print(f"获取到配音角色: {voice_role}")
                print(f"  - currentData: {self.parameter_panel.voice_role_combo.currentData()}")
                print(f"  - currentText: {self.parameter_panel.voice_role_combo.currentText()}")

            if hasattr(self, 'parameter_panel') and hasattr(self.parameter_panel, 'speed_slider'):
                voice_speed = self.parameter_panel.speed_slider.value() / 100.0
                print(f"获取到语速: {voice_speed}")

            # 解析引擎类型
            if "Edge TTS" in voice_channel:
                engine_name = "edge_tts"
            elif "Azure TTS" in voice_channel:
                engine_name = "azure_tts"
            else:
                engine_name = "edge_tts"

            print(f"配音设置解析结果:")
            print(f"  引擎: {engine_name}")
            print(f"  声音: {voice_role}")
            print(f"  语速: {voice_speed}")

            return {
                'engine_name': engine_name,
                'voice': voice_role,
                'speed': voice_speed
            }

        except Exception as e:
            print(f"获取配音设置失败: {e}")
            return None

    def parse_srt_time_to_seconds(self, time_str):
        """将SRT时间格式转换为秒数"""
        try:
            # 格式: HH:MM:SS,mmm
            time_parts = time_str.split(',')
            if len(time_parts) == 2:
                hms_part = time_parts[0]
                ms_part = time_parts[1]

                h, m, s = map(int, hms_part.split(':'))
                ms = int(ms_part)

                total_seconds = h * 3600 + m * 60 + s + ms / 1000.0
                return total_seconds
            else:
                return 0.0
        except Exception as e:
            print(f"解析时间格式失败: {time_str}, 错误: {e}")
            return 0.0

    def play_subtitle_audio(self, row):
        """播放指定行字幕的配音音频"""
        try:
            import os
            print(f"🔊 尝试播放第 {row + 1} 行字幕的配音音频")

            # 停止当前播放
            if hasattr(self, 'video_upload_area') and hasattr(self.video_upload_area, 'subtitle_audio_player'):
                self.video_upload_area.subtitle_audio_player.stop()

            # 获取配音音频文件路径
            audio_path = self.get_subtitle_audio_path(row)
            if not audio_path:
                QMessageBox.warning(self, "播放失败", f"第 {row + 1} 行字幕的配音音频不存在！\n\n请先完成字幕配音。")
                return

            # 检查文件是否存在
            if not os.path.exists(audio_path):
                QMessageBox.warning(self, "播放失败", f"配音音频文件不存在：\n{audio_path}\n\n请重新生成配音。")
                return

            # 更新当前播放行号
            if hasattr(self, 'video_upload_area'):
                self.video_upload_area.current_playing_row = row

            # 更新播放按钮状态
            self.update_play_button_states(row, "playing")

            # 使用测试功能播放音频
            success = self.test_audio_playback(audio_path)
            if success:
                print(f"✅ 开始播放配音音频: {os.path.basename(audio_path)}")
            else:
                print(f"❌ 播放配音音频失败: {os.path.basename(audio_path)}")
                QMessageBox.warning(self, "播放失败", f"无法播放音频文件:\n{os.path.basename(audio_path)}\n\n请检查音频设备设置。")

        except Exception as e:
            print(f"❌ 播放字幕音频失败: {e}")
            QMessageBox.critical(self, "播放错误", f"播放字幕音频时出错：\n{str(e)}")

    def get_subtitle_audio_path(self, row):
        """获取指定行字幕的配音音频文件路径"""
        try:
            import os
            # 获取输出目录
            output_dir = self.parameter_panel.output_path_edit.text().strip()
            if not output_dir:
                print("❌ 输出路径未设置")
                return None

            # 配音文件目录
            voiceover_dir = os.path.join(output_dir, "voiceover")
            if not os.path.exists(voiceover_dir):
                print(f"❌ 配音目录不存在: {voiceover_dir}")
                return None

            # 获取字幕索引（从表格中的第一列获取）
            index_item = self.subtitles_table.item(row, 0)
            if index_item:
                subtitle_index = int(index_item.text())
            else:
                subtitle_index = row + 1

            # 构建音频文件路径
            # 配音文件命名格式：subtitle_001_engine.wav 或 subtitle_001.wav
            possible_filenames = [
                f"subtitle_{subtitle_index:04d}_edge_tts.wav",
                f"subtitle_{subtitle_index:04d}_azure_tts.wav",
                f"subtitle_{subtitle_index:04d}_openai_tts.wav",
                f"subtitle_{subtitle_index:04d}.wav",
                f"subtitle_{subtitle_index:03d}.wav",
                f"subtitle_{row + 1:04d}.wav",  # 使用行号+1
                f"subtitle_{row + 1:03d}.wav",
                f"{subtitle_index}.wav",
                f"{row + 1}.wav"
            ]

            for filename in possible_filenames:
                audio_path = os.path.join(voiceover_dir, filename)
                if os.path.exists(audio_path):
                    print(f"✅ 找到配音文件: {filename}")
                    return audio_path

            # 如果还是找不到，尝试模糊匹配
            try:
                all_files = os.listdir(voiceover_dir)
                wav_files = [f for f in all_files if f.endswith('.wav')]

                # 尝试匹配包含行号的文件
                for pattern in [f"_{subtitle_index:04d}", f"_{subtitle_index:03d}", f"_{row + 1:04d}", f"_{row + 1:03d}"]:
                    for wav_file in wav_files:
                        if pattern in wav_file:
                            audio_path = os.path.join(voiceover_dir, wav_file)
                            print(f"✅ 模糊匹配找到配音文件: {wav_file}")
                            return audio_path

            except Exception as e:
                print(f"⚠️ 模糊匹配失败: {e}")

            print(f"❌ 未找到第 {subtitle_index} 行字幕的配音文件")
            print(f"   查找目录: {voiceover_dir}")
            print(f"   尝试的文件名: {possible_filenames}")

            return None

        except Exception as e:
            print(f"❌ 获取字幕音频路径失败: {e}")
            return None

    def retry_single_subtitle_voiceover(self, row_index):
        """重新为单个字幕配音"""
        try:
            print(f"🔄 开始重新配音字幕 {row_index + 1}")

            # 检查字幕数据是否存在
            if not hasattr(self, 'subtitle_segments') or row_index >= len(self.subtitle_segments):
                QMessageBox.warning(self, "错误", "字幕数据不存在，请先提取字幕")
                return

            # 检查TTS管理器是否存在
            if not hasattr(self, 'tts_manager') or not self.tts_manager:
                QMessageBox.warning(self, "错误", "TTS引擎未初始化，请先进行字幕配音")
                return

            # 获取字幕数据
            subtitle = self.subtitle_segments[row_index].copy()

            # 从表格获取最新的字幕文本（可能已被编辑）
            content_item = self.subtitles_table.item(row_index, 3)
            if content_item:
                # 获取原始多行文本
                latest_text = content_item.data(Qt.UserRole + 1)
                if not latest_text:
                    latest_text = content_item.text()
                subtitle['text'] = latest_text
                print(f"📝 使用最新字幕文本: {latest_text[:50]}...")

            # 检查是否存在重叠标记
            has_overlap = False
            if hasattr(self, 'overlap_detector') and self.overlap_detector:
                has_overlap = self.overlap_detector.is_subtitle_overlapping(row_index)

            # 确认重新配音
            message = f"确定要重新为第 {row_index + 1} 条字幕配音吗？\n\n"
            message += f"字幕内容: {subtitle['text'][:50]}{'...' if len(subtitle['text']) > 50 else ''}\n\n"
            message += "注意：这将覆盖原有的音频文件。"

            if has_overlap:
                message += "\n\n⚠️ 此字幕当前被标记为存在重叠风险。\n重新配音后将重新检测重叠状态。"

            reply = QMessageBox.question(
                self, "确认重新配音",
                message,
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply != QMessageBox.Yes:
                return

            # 更新状态为配音中
            self.update_subtitle_status(row_index, "配音中", QColor("#F39C12"))

            # 获取配音设置
            voice_id = getattr(self, 'current_voice_id', None)
            speed = getattr(self, 'current_speed', 1.0)
            engine_name = getattr(self, 'current_engine', 'edge_tts')

            if not voice_id:
                QMessageBox.warning(self, "错误", "未选择语音，请先在字幕配音功能中配置")
                return

            # 获取输出目录
            output_dir = self.parameter_panel.output_path_edit.text().strip()
            if not output_dir:
                QMessageBox.warning(self, "错误", "输出路径未设置")
                return

            # 创建音频输出目录
            audio_dir = os.path.join(output_dir, "voiceover")
            os.makedirs(audio_dir, exist_ok=True)

            # 生成音频文件名
            audio_filename = f"subtitle_{row_index + 1:04d}.wav"
            audio_path = os.path.join(audio_dir, audio_filename)

            # 备份现有音频文件
            if os.path.exists(audio_path):
                backup_path = audio_path + ".backup"
                if os.path.exists(backup_path):
                    os.remove(backup_path)
                os.rename(audio_path, backup_path)
                print(f"📦 已备份原音频文件")

            # 创建单个配音工作线程
            from ui.subtitle_voiceover_dialog import VoiceoverWorker

            self.single_retry_worker = VoiceoverWorker(
                self.tts_manager,
                [subtitle],  # 只配音一个字幕
                voice_id,
                speed,
                audio_dir,
                engine_name
            )

            # 连接信号
            self.single_retry_worker.voiceover_completed.connect(
                lambda worker_idx, sub, success, result: self.on_single_retry_voiceover_completed(row_index, sub, success, result)
            )
            self.single_retry_worker.error_occurred.connect(
                lambda error: self.on_single_retry_voiceover_error(row_index, error)
            )

            # 启动工作线程
            self.single_retry_worker.start()

            print(f"🎯 开始重新配音字幕 {row_index + 1}")

        except Exception as e:
            print(f"❌ 重新配音失败: {e}")
            QMessageBox.critical(self, "错误", f"重新配音失败: {e}")
            # 恢复状态
            if hasattr(self, 'subtitle_segments') and row_index < len(self.subtitle_segments):
                self.update_subtitle_status(row_index, "❌配音失败", QColor("#E74C3C"))

    def on_single_retry_voiceover_completed(self, row_index, subtitle_data, success, result):
        """单个字幕重新配音完成回调"""
        try:
            if success:
                audio_path = result
                print(f"✅ 字幕 {row_index + 1} 重新配音成功: {os.path.basename(audio_path)}")

                # 更新状态为已配音
                self.update_subtitle_status(row_index, "已配音", QColor("#9B59B6"))

                # 检测音频重叠和优化
                if hasattr(self, 'overlap_detector'):
                    self.check_single_subtitle_overlap(row_index, audio_path)

                # 清理备份文件
                backup_path = audio_path + ".backup"
                if os.path.exists(backup_path):
                    try:
                        os.remove(backup_path)
                        print(f"🗑️ 已删除备份文件")
                    except Exception as e:
                        print(f"⚠️ 删除备份文件失败: {e}")

                QMessageBox.information(self, "配音完成", f"第 {row_index + 1} 条字幕重新配音成功！")

            else:
                error_msg = result
                print(f"❌ 字幕 {row_index + 1} 重新配音失败: {error_msg}")
                self.update_subtitle_status(row_index, "❌配音失败", QColor("#E74C3C"))

                # 恢复备份文件
                audio_dir = os.path.join(self.parameter_panel.output_path_edit.text().strip(), "voiceover")
                audio_filename = f"subtitle_{row_index + 1:04d}.wav"
                audio_path = os.path.join(audio_dir, audio_filename)
                backup_path = audio_path + ".backup"

                if os.path.exists(backup_path):
                    try:
                        if os.path.exists(audio_path):
                            os.remove(audio_path)
                        os.rename(backup_path, audio_path)
                        print(f"🔄 已恢复备份文件")
                    except Exception as e:
                        print(f"⚠️ 恢复备份文件失败: {e}")

                QMessageBox.warning(self, "配音失败", f"第 {row_index + 1} 条字幕重新配音失败:\n{error_msg}")

        except Exception as e:
            print(f"❌ 处理重新配音完成回调失败: {e}")
            self.update_subtitle_status(row_index, "❌配音失败", QColor("#E74C3C"))

    def on_single_retry_voiceover_error(self, row_index, error_msg):
        """单个字幕重新配音错误回调"""
        try:
            print(f"❌ 字幕 {row_index + 1} 重新配音错误: {error_msg}")
            self.update_subtitle_status(row_index, "❌配音失败", QColor("#E74C3C"))
            QMessageBox.critical(self, "配音错误", f"第 {row_index + 1} 条字幕重新配音失败:\n{error_msg}")
        except Exception as e:
            print(f"❌ 处理重新配音错误回调失败: {e}")

    def check_single_subtitle_overlap(self, subtitle_index, audio_path):
        """检查单个字幕的重叠状态（重配音完成后调用）"""
        try:
            if subtitle_index < 0 or subtitle_index >= len(self.subtitle_segments):
                print(f"⚠️ 字幕索引超出范围，跳过重叠检查: {subtitle_index}")
                return

            if not audio_path or not os.path.exists(audio_path):
                print(f"⚠️ 音频文件不存在，跳过重叠检查: {audio_path}")
                return

            print(f"🔍 检查字幕 {subtitle_index + 1} 的重叠状态...")

            # 获取字幕数据
            subtitle_data = self.subtitle_segments[subtitle_index]

            # 1. 首先检查音频时长与字幕时长的比例（与正常配音逻辑一致）
            try:
                from pydub import AudioSegment

                # 获取音频时长
                audio = AudioSegment.from_wav(audio_path)
                audio_duration = len(audio) / 1000.0  # 转换为秒
                subtitle_duration = subtitle_data['duration']

                print(f"📊 音频时长: {audio_duration:.2f}秒, 字幕时长: {subtitle_duration:.2f}秒")

                # 计算所需加速比例
                if audio_duration > subtitle_duration:
                    required_speedup = audio_duration / subtitle_duration
                    print(f"⚡ 需要加速比例: {required_speedup:.3f}x")

                    MAX_SPEED_UP = 1.2  # 最大推荐加速比例（仅用于警告）

                    # 显示加速警告（如果超过推荐值）
                    if required_speedup > MAX_SPEED_UP:
                        print(f"⚠️ 字幕 {subtitle_index + 1} 重新配音后需要超速加速 {required_speedup:.2f}x > {MAX_SPEED_UP}x")
                        print(f"💡 建议：缩短字幕文本内容或调整字幕时间，但仍会应用加速")

                        # 应用加速并标记为橙色（表示超速但已处理）
                        try:
                            success = self.apply_single_audio_speedup(audio_path, required_speedup)
                            if success:
                                self.update_subtitle_status(subtitle_index, f"已配音({required_speedup:.2f}x)", QColor("#FF8C00"))  # 橙色
                                print(f"✅ 字幕 {subtitle_index + 1} 超速加速应用成功")
                            else:
                                self.update_subtitle_status(subtitle_index, "加速失败", QColor("#E74C3C"))
                                print(f"❌ 字幕 {subtitle_index + 1} 超速加速应用失败")
                        except Exception as e:
                            print(f"❌ 应用超速加速失败: {e}")
                            self.update_subtitle_status(subtitle_index, "加速失败", QColor("#E74C3C"))
                    else:
                        print(f"✅ 字幕 {subtitle_index + 1} 音频时长可接受，加速比例: {required_speedup:.3f}x")

            except ImportError:
                print("⚠️ 音频处理库不可用，跳过音频时长检测")
            except Exception as e:
                print(f"⚠️ 检测音频时长失败: {e}")

            # 2. 检查与相邻字幕的时间重叠（原有逻辑）
            if hasattr(self, 'overlap_detector') and self.overlap_detector:
                # 执行重叠检测
                overlapping_indices = self.overlap_detector.detect_overlaps(self.subtitle_segments, {subtitle_index: audio_path})

                # 检查结果并标记
                if subtitle_index in overlapping_indices:
                    # 字幕重新配音后仍然与相邻字幕重叠
                    self.overlap_detector.mark_subtitle_overlapping(subtitle_index)
                    self.mark_subtitle_row_red(subtitle_index)
                    print(f"🔴 字幕 {subtitle_index + 1} 重新配音后仍与相邻字幕重叠，已重新标记为红色")
                    print(f"💡 建议：缩短字幕文本内容或调整字幕时间")
                else:
                    # 字幕重新配音后不再与相邻字幕重叠
                    if self.overlap_detector.is_subtitle_overlapping(subtitle_index):
                        self.overlap_detector.unmark_subtitle_overlapping(subtitle_index)
                        self.unmark_subtitle_row_red(subtitle_index)
                        print(f"✅ 字幕 {subtitle_index + 1} 重新配音后不再重叠，红色标记已移除")
                    else:
                        print(f"✅ 字幕 {subtitle_index + 1} 重新配音完成，无重叠问题")

                    # 更新状态为已配音
                    self.update_subtitle_status(subtitle_index, "已配音", QColor("#9B59B6"))
            else:
                print("⚠️ 重叠检测器未初始化，跳过相邻字幕重叠检查")
                # 如果没有重叠检测器，但音频时长检查通过，则标记为已配音
                self.update_subtitle_status(subtitle_index, "已配音", QColor("#9B59B6"))

        except Exception as e:
            print(f"❌ 检查单个字幕重叠状态失败: {e}")
            import traceback
            traceback.print_exc()

    def update_subtitle_status(self, row_index, status_text, color):
        """更新字幕状态"""
        try:
            if row_index < self.subtitles_table.rowCount():
                status_item = self.subtitles_table.item(row_index, 4)
                if status_item:
                    status_item.setText(status_text)
                    status_item.setBackground(color)
                    status_item.setForeground(QColor("#FFFFFF"))
        except Exception as e:
            print(f"❌ 更新字幕状态失败: {e}")

    def mark_subtitle_row_red(self, row_index):
        """标记字幕行为红色（重叠）"""
        try:
            if row_index < self.subtitles_table.rowCount():
                # 标记状态列为红色
                status_item = self.subtitles_table.item(row_index, 4)
                if status_item:
                    status_item.setText("🔴需调整")
                    status_item.setBackground(QColor("#E74C3C"))  # 红色
                    status_item.setForeground(QColor("#FFFFFF"))

                # 标记整行背景为浅红色
                for col in range(self.subtitles_table.columnCount()):
                    item = self.subtitles_table.item(row_index, col)
                    if item:
                        item.setBackground(QColor("#FFEBEE"))  # 浅红色背景
        except Exception as e:
            print(f"❌ 标记字幕行红色失败: {e}")

    def unmark_subtitle_row_red(self, row_index):
        """移除字幕行的红色标记"""
        try:
            if row_index < self.subtitles_table.rowCount():
                # 恢复状态列
                status_item = self.subtitles_table.item(row_index, 4)
                if status_item:
                    status_item.setText("已配音")
                    status_item.setBackground(QColor("#9B59B6"))  # 紫色
                    status_item.setForeground(QColor("#FFFFFF"))

                # 恢复整行背景
                for col in range(self.subtitles_table.columnCount()):
                    item = self.subtitles_table.item(row_index, col)
                    if item:
                        item.setBackground(QColor("#FFFFFF"))  # 白色背景
        except Exception as e:
            print(f"❌ 移除字幕行红色标记失败: {e}")

    def update_play_button_states(self, playing_row, state):
        """更新播放按钮状态"""
        try:
            for row in range(self.subtitles_table.rowCount()):
                # 获取操作单元格
                operation_widget = self.subtitles_table.cellWidget(row, 5)
                if operation_widget:
                    # 查找播放按钮
                    play_btn = operation_widget.findChild(QPushButton)
                    if play_btn and (play_btn.toolTip() == "播放音频" or play_btn.toolTip() == "停止播放"):
                        if row == playing_row and state == "playing":
                            # 当前播放行，显示停止图标
                            play_btn.setText("🟥")
                            play_btn.setToolTip("停止播放")
                            play_btn.setStyleSheet("""
                                QPushButton {
                                    background-color: #3B82F6;
                                    color: white;
                                    border: none;
                                    border-radius: 13px;
                                    font-size: 10px;
                                    font-weight: bold;
                                }
                                QPushButton:hover {
                                    background-color: #60A5FA;
                                    font-size: 11px;
                                    font-weight: bold;
                                }
                                QPushButton:pressed {
                                    background-color: #1D4ED8;
                                }
                            """)
                            # 重新连接停止事件
                            play_btn.clicked.disconnect()
                            play_btn.clicked.connect(self.stop_subtitle_audio)
                        else:
                            # 其他行或停止状态，显示播放图标
                            play_btn.setText("🔊")
                            play_btn.setToolTip("播放音频")
                            play_btn.setStyleSheet("""
                                QPushButton {
                                    background-color: #3B82F6;
                                    color: white;
                                    border: none;
                                    border-radius: 13px;
                                    font-size: 12px;
                                    font-weight: bold;
                                }
                                QPushButton:hover {
                                    background-color:#60A5FA;
                                }
                                QPushButton:pressed {
                                    background-color: #1D4ED8;
                                }
                            """)
                            # 重新连接播放事件
                            play_btn.clicked.disconnect()
                            play_btn.clicked.connect(lambda checked, r=row: self.play_subtitle_audio(r))

        except Exception as e:
            print(f"❌ 更新播放按钮状态失败: {e}")

    def stop_subtitle_audio(self):
        """停止播放字幕音频"""
        try:
            if hasattr(self, 'video_upload_area') and hasattr(self.video_upload_area, 'subtitle_audio_player'):
                # 停止播放
                self.video_upload_area.subtitle_audio_player.stop()

            # 立即重置所有播放按钮状态
            self.reset_all_play_buttons()

            print("⏹ 停止播放字幕音频")

        except Exception as e:
            print(f"❌ 停止播放失败: {e}")

    def reset_all_play_buttons(self):
        """重置所有播放按钮状态"""
        try:
            print("🔄 开始重置所有播放按钮状态...")

            # 重置当前播放行号
            if hasattr(self, 'video_upload_area'):
                self.video_upload_area.current_playing_row = -1
                print("✅ 已重置当前播放行号")

            # 统计重置的按钮数量
            reset_count = 0

            # 更新所有播放按钮状态
            for row in range(self.subtitles_table.rowCount()):
                # 获取操作单元格
                operation_widget = self.subtitles_table.cellWidget(row, 5)
                if operation_widget:
                    # 查找播放按钮
                    play_btn = operation_widget.findChild(QPushButton)
                    if play_btn and (play_btn.toolTip() == "播放音频" or play_btn.toolTip() == "停止播放"):
                        # 重置为播放状态
                        play_btn.setText("🔊")
                        play_btn.setToolTip("播放音频")
                        play_btn.setStyleSheet("""
                            QPushButton {
                                background-color: #3B82F6;
                                color: white;
                                border: none;
                                border-radius: 13px;
                                font-size: 12px;
                                font-weight: bold;
                            }
                            QPushButton:hover {
                                background-color:#60A5FA;
                            }
                            QPushButton:pressed {
                                background-color: #1D4ED8;
                            }
                        """)

                        # 重新连接播放事件
                        try:
                            play_btn.clicked.disconnect()
                        except:
                            pass  # 如果没有连接的信号，忽略错误
                        play_btn.clicked.connect(lambda checked, r=row: self.play_subtitle_audio(r))
                        reset_count += 1

            print(f"✅ 已重置 {reset_count} 个播放按钮状态")

        except Exception as e:
            print(f"❌ 重置播放按钮状态失败: {e}")
            import traceback
            traceback.print_exc()

    def on_subtitle_audio_status_changed(self, status):
        """处理字幕音频播放状态变化"""
        try:
            print(f"🔄 主窗口接收到音频状态变化: {status}")

            # 检查状态类型，避免属性错误
            if hasattr(QMediaPlayer, 'MediaStatus'):
                if status == QMediaPlayer.MediaStatus.EndOfMedia:
                    # 播放结束，自动重置按钮状态
                    print("🎵 音频播放完成 - 自动重置播放按钮状态")
                    self.reset_all_play_buttons()

                    # 重置当前播放行号
                    if hasattr(self, 'video_upload_area'):
                        self.video_upload_area.current_playing_row = -1
                        print("🔄 已重置当前播放行号")

                elif status == QMediaPlayer.MediaStatus.InvalidMedia:
                    # 媒体无效，重置按钮状态
                    print("❌ 音频媒体无效 - 重置播放按钮状态")
                    self.reset_all_play_buttons()

                elif status == QMediaPlayer.MediaStatus.LoadingMedia:
                    print("⏳ 正在加载音频...")
                elif status == QMediaPlayer.MediaStatus.LoadedMedia:
                    print("✅ 音频加载完成")
                elif status == QMediaPlayer.MediaStatus.BufferingMedia:
                    print("🔄 音频缓冲中...")
                elif status == QMediaPlayer.MediaStatus.BufferedMedia:
                    print("✅ 音频缓冲完成")
            else:
                # 兼容性处理，如果没有MediaStatus枚举
                print(f"⚠️ 无法识别的音频状态: {status}")

        except Exception as e:
            print(f"❌ 处理音频状态变化失败: {e}")
            import traceback
            traceback.print_exc()
            # 发生错误时也重置按钮状态
            try:
                self.reset_all_play_buttons()
            except Exception as e2:
                print(f"❌ 重置按钮状态也失败: {e2}")

    def on_subtitle_audio_playback_state_changed(self, state):
        """处理字幕音频播放状态变化"""
        try:
            print(f"🔄 主窗口接收到播放状态变化: {state}")

            # 检查播放状态
            if hasattr(QMediaPlayer, 'PlaybackState'):
                if state == QMediaPlayer.PlaybackState.StoppedState:
                    # 播放停止，自动重置按钮状态
                    print("⏹ 音频播放已停止 - 自动重置播放按钮状态")
                    self.reset_all_play_buttons()

                    # 重置当前播放行号
                    if hasattr(self, 'video_upload_area'):
                        self.video_upload_area.current_playing_row = -1
                        print("🔄 已重置当前播放行号")

                elif state == QMediaPlayer.PlaybackState.PlayingState:
                    print("▶️ 字幕音频正在播放")
                elif state == QMediaPlayer.PlaybackState.PausedState:
                    print("⏸️ 字幕音频已暂停")
            else:
                print(f"⚠️ 无法识别的播放状态: {state}")

        except Exception as e:
            print(f"❌ 处理播放状态变化失败: {e}")
            import traceback
            traceback.print_exc()

    def on_subtitle_audio_error(self, error, error_string):
        """处理字幕音频播放错误"""
        try:
            print(f"❌ 字幕音频播放错误: {error_string}")

            # 重置按钮状态
            self.reset_all_play_buttons()

            # 显示错误消息
            QMessageBox.critical(self, "播放错误", f"音频播放出错：\n{error_string}")

        except Exception as e:
            print(f"❌ 处理音频错误失败: {e}")

    def update_subtitle_status(self, row_index, status_text, color):
        """更新字幕状态显示"""
        try:
            if row_index < 0 or row_index >= self.subtitles_table.rowCount():
                return

            # 更新状态列（第5列）
            status_item = self.subtitles_table.item(row_index, 4)
            if not status_item:
                status_item = QTableWidgetItem(status_text)
                self.subtitles_table.setItem(row_index, 4, status_item)
            else:
                status_item.setText(status_text)

            # 设置状态颜色
            status_item.setForeground(color)

            print(f"📊 更新第 {row_index + 1} 行状态: {status_text}")

        except Exception as e:
            print(f"❌ 更新字幕状态失败: {e}")

    def on_voiceover_progress(self, progress):
        """配音进度更新回调"""
        try:
            # 将配音进度映射到40-90%范围
            mapped_progress = 40 + int(progress * 0.5)
            self.update_progress_display(f"正在生成配音... {progress}%", mapped_progress)
        except Exception as e:
            print(f"❌ 更新配音进度失败: {e}")

    def on_voiceover_completed(self, index, subtitle_info, success, result):
        """单个配音完成回调"""
        try:
            if success:
                audio_path = result
                print(f"✅ 字幕 {index + 1} 配音完成: {audio_path}")

                # 修复音频文件格式（特别是Edge TTS生成的文件）
                fixed_audio_path = self.fix_audio_format(audio_path)
                if fixed_audio_path:
                    audio_path = fixed_audio_path

                # 检测音频时长和重叠
                self.check_audio_overlap(index, subtitle_info, audio_path)

            else:
                error_msg = result
                print(f"❌ 字幕 {index + 1} 配音失败: {error_msg}")

                # 更新状态为配音失败
                if index < self.subtitles_table.rowCount():
                    status_item = self.subtitles_table.item(index, 4)
                    if status_item:
                        status_item.setText("❌配音失败")
                        status_item.setBackground(QColor("#E74C3C"))  # 红色背景
                        status_item.setForeground(QColor("#FFFFFF"))

        except Exception as e:
            print(f"❌ 处理配音完成回调失败: {e}")

    def fix_audio_format(self, audio_path):
        """修复音频文件格式问题"""
        try:
            if not os.path.exists(audio_path):
                print(f"⚠️ 音频文件不存在: {audio_path}")
                return None

            print(f"🔧 检查音频文件格式: {os.path.basename(audio_path)}")

            # 检查文件头，判断实际格式
            with open(audio_path, 'rb') as f:
                header = f.read(12)

            is_wav_format = header.startswith(b'RIFF') and b'WAVE' in header
            is_mp3_format = header.startswith(b'ID3') or header[0:2] == b'\xff\xfb' or header[0:2] == b'\xff\xf3'

            print(f"🔍 格式检测结果: WAV={is_wav_format}, MP3={is_mp3_format}")

            # 如果文件格式正确，直接返回
            if is_wav_format and audio_path.lower().endswith('.wav'):
                print(f"✅ 音频格式正确，无需修复")
                return audio_path

            # 需要修复格式
            try:
                from pydub import AudioSegment

                if is_mp3_format:
                    print(f"🔄 修复MP3格式文件...")
                    audio = AudioSegment.from_mp3(audio_path)
                else:
                    print(f"🔄 使用通用格式加载...")
                    audio = AudioSegment.from_file(audio_path)

                # 创建备份
                backup_path = audio_path + ".backup"
                import shutil
                shutil.copy2(audio_path, backup_path)
                print(f"💾 已创建备份: {os.path.basename(backup_path)}")

                # 重新保存为标准WAV格式
                print(f"🔄 重新保存为标准WAV格式...")
                audio.export(audio_path, format="wav")

                # 验证修复结果
                if os.path.exists(audio_path) and os.path.getsize(audio_path) > 0:
                    # 再次检查格式
                    with open(audio_path, 'rb') as f:
                        new_header = f.read(12)

                    if new_header.startswith(b'RIFF') and b'WAVE' in new_header:
                        print(f"✅ 音频格式修复成功")
                        # 删除备份
                        try:
                            os.remove(backup_path)
                            print(f"🗑️ 已删除备份文件")
                        except:
                            pass
                        return audio_path
                    else:
                        print(f"❌ 格式修复失败，恢复备份")
                        shutil.move(backup_path, audio_path)
                        return audio_path
                else:
                    print(f"❌ 修复后文件无效，恢复备份")
                    shutil.move(backup_path, audio_path)
                    return audio_path

            except Exception as e:
                print(f"❌ 音频格式修复失败: {e}")
                return audio_path

        except Exception as e:
            print(f"❌ 检查音频格式失败: {e}")
            return audio_path

    def check_audio_overlap(self, index, subtitle_info, audio_path):
        """检测音频重叠并处理"""
        try:
            print(f"🔍 开始检测字幕 {index + 1} 的音频重叠...")

            # 导入音频处理库
            try:
                from pydub import AudioSegment
                print(f"✅ 音频处理库导入成功")
            except ImportError as e:
                print(f"⚠️ 音频处理库不可用: {e}")
                print("⚠️ 跳过重叠检测，直接标记为配音完成")
                self.mark_subtitle_voiceover_completed(index, False)
                return

            # 检查音频文件是否存在
            if not audio_path:
                print(f"⚠️ 音频文件路径为空")
                self.mark_subtitle_voiceover_completed(index, False)
                return

            if not os.path.exists(audio_path):
                print(f"⚠️ 音频文件不存在: {audio_path}")
                self.mark_subtitle_voiceover_completed(index, False)
                return

            # 检查文件大小
            file_size = os.path.getsize(audio_path)
            if file_size == 0:
                print(f"⚠️ 音频文件为空: {audio_path}")
                self.mark_subtitle_voiceover_completed(index, False)
                return

            print(f"📁 音频文件: {os.path.basename(audio_path)} ({file_size} bytes)")

            # 检测并修复音频文件格式
            try:
                # 首先检查文件头，判断实际格式
                with open(audio_path, 'rb') as f:
                    header = f.read(12)

                is_wav_format = header.startswith(b'RIFF') and b'WAVE' in header
                is_mp3_format = header.startswith(b'ID3') or header[0:2] == b'\xff\xfb' or header[0:2] == b'\xff\xf3'

                print(f"🔍 文件格式检测: WAV={is_wav_format}, MP3={is_mp3_format}")

                # 根据实际格式加载音频
                if is_wav_format:
                    try:
                        audio = AudioSegment.from_wav(audio_path)
                        print(f"✅ WAV格式加载成功，时长: {len(audio) / 1000:.2f}s")
                    except Exception as e:
                        print(f"⚠️ WAV格式加载失败: {e}")
                        # 尝试通用格式
                        audio = AudioSegment.from_file(audio_path)
                        print(f"✅ 通用格式加载成功，时长: {len(audio) / 1000:.2f}s")
                elif is_mp3_format:
                    # 文件实际是MP3格式，但扩展名是.wav
                    print(f"⚠️ 检测到MP3格式文件，但扩展名为.wav，正在修复...")
                    audio = AudioSegment.from_mp3(audio_path)
                    print(f"✅ MP3格式加载成功，时长: {len(audio) / 1000:.2f}s")

                    # 转换为真正的WAV格式
                    print(f"🔄 正在转换为WAV格式...")
                    audio.export(audio_path, format="wav")
                    print(f"✅ 已转换为WAV格式")
                else:
                    # 尝试通用格式加载
                    print(f"🔍 使用通用格式加载...")
                    audio = AudioSegment.from_file(audio_path)
                    print(f"✅ 通用格式加载成功，时长: {len(audio) / 1000:.2f}s")

                    # 确保保存为WAV格式
                    print(f"🔄 正在标准化为WAV格式...")
                    audio.export(audio_path, format="wav")
                    print(f"✅ 已标准化为WAV格式")

            except Exception as e:
                print(f"❌ 加载音频文件失败: {e}")
                print(f"🔄 尝试最后的通用格式加载...")
                try:
                    audio = AudioSegment.from_file(audio_path)
                    print(f"✅ 最终通用格式加载成功，时长: {len(audio) / 1000:.2f}s")

                    # 重新保存为标准WAV格式
                    print(f"🔄 重新保存为标准WAV格式...")
                    audio.export(audio_path, format="wav")
                    print(f"✅ 已重新保存为标准WAV格式")
                except Exception as e2:
                    print(f"❌ 完全无法加载音频文件: {e2}")
                    self.mark_subtitle_voiceover_completed(index, False)
                    return

            # 去除静音
            try:
                audio_trimmed = audio.strip_silence(silence_thresh=-40, silence_len=100)
                actual_duration = len(audio_trimmed) / 1000.0  # 转换为秒
                print(f"✅ 去除静音后时长: {actual_duration:.2f}s")
            except Exception as e:
                print(f"⚠️ 去除静音失败，使用原始音频: {e}")
                audio_trimmed = audio
                actual_duration = len(audio) / 1000.0

            # 获取字幕时长
            try:
                subtitle_duration = subtitle_info['duration']
                print(f"📝 字幕时长: {subtitle_duration:.2f}s")
            except (KeyError, TypeError) as e:
                print(f"❌ 获取字幕时长失败: {e}")
                print(f"   subtitle_info: {subtitle_info}")
                self.mark_subtitle_voiceover_completed(index, False)
                return

            print(f"🔍 字幕 {index + 1} 时长对比:")
            print(f"   字幕时长: {subtitle_duration:.2f}s")
            print(f"   音频时长: {actual_duration:.2f}s")
            print(f"   时长差异: {actual_duration - subtitle_duration:.2f}s")

            # 检查是否需要加速
            MIN_GAP_DURATION = 0.1  # 最小间隔时间
            MAX_SPEED_UP = 1.2  # 最大推荐加速比例（仅用于警告）
            MIN_SPEED_UP = 1.01

            if actual_duration + MIN_GAP_DURATION > subtitle_duration:
                # 需要加速
                required_speedup = (actual_duration + MIN_GAP_DURATION) / subtitle_duration

                print(f"⚠️ 字幕 {index + 1} 需要加速: {required_speedup:.3f}x")

                # 确保最小加速比例
                if required_speedup < MIN_SPEED_UP:
                    required_speedup = MIN_SPEED_UP
                    print(f"🟡 字幕 {index + 1} 使用最小加速比例: {required_speedup:.3f}x")

                # 显示超速警告（如果需要）
                if required_speedup > MAX_SPEED_UP:
                    print(f"⚠️ 字幕 {index + 1} 加速比例 {required_speedup:.3f}x 超过推荐值 {MAX_SPEED_UP}x，但仍会应用加速")

                # 无论加速比例多少，都尝试应用加速
                print(f"🟡 字幕 {index + 1} 应用加速: {required_speedup:.3f}x")
                success = self.apply_audio_speedup(audio_path, required_speedup)

                if success:
                    print(f"✅ 字幕 {index + 1} 音频加速成功")
                    # 根据是否超速决定标记状态
                    needs_adjustment = required_speedup > MAX_SPEED_UP
                    self.mark_subtitle_voiceover_completed(index, needs_adjustment, required_speedup)
                else:
                    print(f"❌ 字幕 {index + 1} 音频加速失败")
                    self.mark_subtitle_voiceover_completed(index, True, required_speedup)  # 加速失败标记为需要调整
            else:
                # 时长合适，无需加速
                print(f"✅ 字幕 {index + 1} 时长合适，无需调整")
                self.mark_subtitle_voiceover_completed(index, False, 1)

        except Exception as e:
            print(f"❌ 检测音频重叠失败: {e}")
            import traceback
            traceback.print_exc()
            print(f"⚠️ 字幕 {index + 1} 重叠检测失败，直接标记为配音完成")
            self.mark_subtitle_voiceover_completed(index, False)

    def apply_audio_speedup(self, audio_path, speedup_ratio):
        """应用音频加速（优化版，防止卡死）"""
        import subprocess
        import time
        import tempfile

        temp_file = None
        try:
            print(f"🔄 开始音频加速: {os.path.basename(audio_path)}, 比例: {speedup_ratio:.3f}x")

            # 检查FFmpeg是否可用
            try:
                ffmpeg_result = subprocess.run(["ffmpeg", "-version"],
                                               capture_output=True, text=True, timeout=10)
                if ffmpeg_result.returncode != 0:
                    raise Exception("FFmpeg不可用")
                print("✅ FFmpeg可用")
            except (subprocess.TimeoutExpired, FileNotFoundError, Exception) as e:
                print(f"❌ FFmpeg不可用: {e}")
                print("💡 请确保已安装FFmpeg并将其添加到系统PATH中")
                return False

            # 导入音频处理库
            try:
                from pydub import AudioSegment
            except ImportError as e:
                print(f"❌ 无法导入音频处理库: {e}")
                return False

            # 检查文件是否存在
            if not os.path.exists(audio_path):
                print(f"❌ 音频文件不存在: {audio_path}")
                return False

            # 备份原始文件
            backup_path = audio_path + ".backup"
            try:
                import shutil
                shutil.copy2(audio_path, backup_path)
                print(f"💾 已备份原始文件: {os.path.basename(backup_path)}")
            except Exception as e:
                print(f"⚠️ 备份文件失败: {e}")

            # 加载音频文件（支持多种格式）
            try:
                # 首先检查文件格式
                with open(audio_path, 'rb') as f:
                    header = f.read(12)

                is_wav_format = header.startswith(b'RIFF') and b'WAVE' in header
                is_mp3_format = header.startswith(b'ID3') or header[0:2] == b'\xff\xfb' or header[0:2] == b'\xff\xf3'

                print(f"🔍 加速处理 - 文件格式检测: WAV={is_wav_format}, MP3={is_mp3_format}")

                if is_wav_format:
                    audio = AudioSegment.from_wav(audio_path)
                elif is_mp3_format:
                    print(f"⚠️ 检测到MP3格式文件，扩展名为.wav")
                    audio = AudioSegment.from_mp3(audio_path)
                else:
                    # 使用通用格式加载
                    audio = AudioSegment.from_file(audio_path)

                original_duration = len(audio) / 1000.0
                print(f"✅ 音频文件加载成功，原始时长: {original_duration:.2f}s")

            except Exception as e:
                print(f"❌ 加载音频文件失败: {e}")
                # 尝试通用格式作为最后手段
                try:
                    audio = AudioSegment.from_file(audio_path)
                    original_duration = len(audio) / 1000.0
                    print(f"✅ 通用格式加载成功，原始时长: {original_duration:.2f}s")
                except Exception as e2:
                    print(f"❌ 完全无法加载音频文件: {e2}")
                    return False

            # 去除静音
            print(f"🔇 正在去除静音...")
            audio = audio.strip_silence(silence_thresh=-40, silence_len=100)
            print(f"✅ 静音去除完成，处理后时长: {len(audio) / 1000.0:.2f}s")

            # 使用临时文件，确保文件名唯一
            temp_dir = os.path.dirname(audio_path)
            temp_file = os.path.join(temp_dir, f"temp_nosilence_{int(time.time())}.wav")

            try:
                print(f"💾 保存临时文件: {os.path.basename(temp_file)}")
                audio.export(temp_file, format="wav")

                # 确保文件写入完成
                time.sleep(0.1)

                # 验证临时文件
                if not os.path.exists(temp_file) or os.path.getsize(temp_file) == 0:
                    raise Exception("临时文件创建失败或为空")

                print(f"✅ 临时文件创建成功，大小: {os.path.getsize(temp_file)} bytes")

            except Exception as e:
                print(f"❌ 创建临时文件失败: {e}")
                return False

            # 应用FFmpeg加速处理
            try:
                print(f"⚡ 开始FFmpeg音频加速处理...")

                # 构建FFmpeg命令，添加更多参数防止卡死
                command = [
                    "ffmpeg",
                    "-y",  # 覆盖输出文件
                    "-i", temp_file,
                    "-af", f"atempo={speedup_ratio}",
                    "-vn",  # 不处理视频
                    "-c:a", "pcm_s16le",  # 音频编码
                    "-ar", "44100",  # 采样率
                    "-ac", "2",  # 声道数
                    audio_path
                ]

                print(f"🔧 FFmpeg命令: {' '.join(command)}")

                # 执行FFmpeg，设置超时和输出重定向
                result = subprocess.run(
                    command,
                    check=True,
                    timeout=30,  # 30秒超时
                    capture_output=True,  # 捕获输出
                    text=True,
                    cwd=temp_dir  # 设置工作目录
                )

                print(f"✅ FFmpeg处理完成")
                if result.stderr:
                    print(f"📝 FFmpeg输出: {result.stderr[:200]}...")  # 只显示前200字符

            except subprocess.TimeoutExpired:
                print(f"❌ FFmpeg处理超时（30秒）")
                return False
            except subprocess.CalledProcessError as e:
                print(f"❌ FFmpeg处理失败: {e}")
                if e.stderr:
                    print(f"📝 错误详情: {e.stderr[:500]}...")
                return False
            except FileNotFoundError:
                print(f"❌ 找不到FFmpeg命令，请确保已正确安装并添加到系统PATH中")
                return False
            except Exception as e:
                print(f"❌ 音频加速处理失败: {e}")
                return False

            # 验证保存的文件
            try:
                # 验证保存的文件
                if os.path.exists(audio_path) and os.path.getsize(audio_path) > 0:
                    print(f"✅ 文件验证成功，大小: {os.path.getsize(audio_path)} bytes")

                    # 删除备份文件
                    try:
                        if os.path.exists(backup_path):
                            os.remove(backup_path)
                            print(f"🗑️ 已删除备份文件")
                    except Exception as e:
                        print(f"⚠️ 删除备份文件失败: {e}")

                    # 删除临时文件
                    try:
                        if os.path.exists(temp_file):
                            os.remove(temp_file)
                            print(f"🗑️ 已删除临时文件")
                    except Exception as e:
                        print(f"⚠️ 删除临时文件失败: {e}")

                    return True
                else:
                    print(f"❌ 保存的文件无效")
                    # 恢复备份文件
                    if os.path.exists(backup_path):
                        shutil.move(backup_path, audio_path)
                        print(f"🔄 已恢复备份文件")
                    return False

            except Exception as e:
                print(f"❌ 保存加速音频失败: {e}")
                # 恢复备份文件
                try:
                    if os.path.exists(backup_path):
                        import shutil
                        shutil.move(backup_path, audio_path)
                        print(f"🔄 已恢复备份文件")
                except Exception as e2:
                    print(f"❌ 恢复备份文件失败: {e2}")
                return False

        except Exception as e:
            print(f"❌ 音频加速失败: {e}")
            import traceback
            traceback.print_exc()
            return False
        finally:
            # 清理临时文件
            if temp_file and os.path.exists(temp_file):
                try:
                    os.remove(temp_file)
                    print(f"🗑️ 已清理临时文件")
                except Exception as e:
                    print(f"⚠️ 清理临时文件失败: {e}")


    def mark_subtitle_voiceover_completed(self, index, needs_adjustment, speedup_ratio):
        """标记字幕配音完成"""
        try:
            if index < self.subtitles_table.rowCount():
                status_item = self.subtitles_table.item(index, 4)
                if status_item:
                    if needs_adjustment:
                        status_item.setText("🔴需调整")
                        status_item.setBackground(QColor("#E74C3C"))  # 红色背景
                        status_item.setToolTip(
                            f"音频时长超出字幕时长过多，需要加速{speedup_ratio:.2f}x，建议手动调整字幕内容")
                    else:
                        status_item.setText("已配音")
                        status_item.setBackground(QColor("#9B59B6"))  # 紫色背景
                    status_item.setForeground(QColor("#FFFFFF"))
        except Exception as e:
            print(f"❌ 标记配音完成状态失败: {e}")

    def on_voiceover_error(self, error_msg):
        """配音错误回调"""
        try:
            print(f"❌ 配音错误: {error_msg}")
        except Exception as e:
            print(f"❌ 处理配音错误失败: {e}")

    def on_voiceover_finished(self):
        """配音完成回调"""
        try:
            self.update_progress_display("字幕配音完成！", 100)

            # 统计配音结果
            completed_count = 0
            needs_adjustment_count = 0
            failed_count = 0
            total_count = self.subtitles_table.rowCount()

            for row in range(total_count):
                status_item = self.subtitles_table.item(row, 4)
                if status_item:
                    status_text = status_item.text()
                    if status_text == "已配音":
                        completed_count += 1
                    elif "需调整" in status_text:
                        needs_adjustment_count += 1
                    elif "配音失败" in status_text:
                        failed_count += 1

            print(f"字幕配音完成:")
            print(f"  总字幕数: {total_count}")
            print(f"  配音成功: {completed_count}")
            print(f"  需要调整: {needs_adjustment_count}")
            print(f"  配音失败: {failed_count}")

            # 标记配音完成
            self.voiceover_completed = True

            # 启用视频合成按钮
            if hasattr(self, 'video_synthesis_btn'):
                self.video_synthesis_btn.setEnabled(True)
                print("✅ 视频合成按钮已启用")

            # 延迟显示结果
            def show_result():
                if self.is_auto_processing:
                    # 自动化模式：配音完成后停止自动化
                    self.is_auto_processing = False
                    self.update_progress_display("自动化流程完成！字幕配音已完成", 100)

                    print(f"✅ 自动化流程完成！配音统计：成功{completed_count}，需调整{needs_adjustment_count}，失败{failed_count}")

                    # 显示自动化完成的总结
                    message = f"""视频翻译自动化流程已完成！

已完成步骤：
✅ 视频转音频
✅ 人声分离（如果启用）
✅ 字幕提取
✅ 字幕翻译
✅ 字幕配音

配音统计：
总字幕数: {total_count}
配音成功: {completed_count}
需要调整: {needs_adjustment_count}
配音失败: {failed_count}

您现在可以点击"视频合成"按钮生成最终视频。"""
                    QMessageBox.information(self, "自动化流程完成", message)

                    # 延迟重置进度显示
                    QTimer.singleShot(3000, self.reset_progress_display)
                else:
                    # 非自动化模式：显示详细结果
                    QTimer.singleShot(2000, self.reset_progress_display)
                    self.show_voiceover_result(completed_count, needs_adjustment_count, failed_count, total_count)

            QTimer.singleShot(1000, show_result)

        except Exception as e:
            print(f"❌ 处理配音完成失败: {e}")

    def show_voiceover_result(self, completed_count, needs_adjustment_count, failed_count, total_count):
        """显示配音结果"""
        try:
            success_rate = completed_count / total_count * 100 if total_count > 0 else 0

            if needs_adjustment_count == 0 and failed_count == 0:
                # 全部配音成功，询问是否自动开始视频合成
                reply = QMessageBox.question(
                    self,
                    "字幕配音完成",
                    f"字幕配音全部完成！\n\n"
                    f"配音统计:\n"
                    f"总字幕数: {total_count}\n"
                    f"配音成功: {completed_count}\n"
                    f"成功率: {success_rate:.1f}%\n\n"
                    f"所有字幕配音已生成。\n\n"
                    f"是否立即开始视频合成（生成最终翻译视频）？",
                    QMessageBox.Yes | QMessageBox.No,
                    QMessageBox.Yes
                )

                if reply == QMessageBox.Yes:
                    # 用户选择自动开始视频合成
                    print("🎬 用户确认，即将自动开始视频合成...")
                    QTimer.singleShot(1000, lambda: self.start_video_synthesis(auto_start=True))  # 延迟1秒开始
            elif needs_adjustment_count > 0:
                # 有字幕需要调整
                QMessageBox.warning(
                    self,
                    "字幕配音部分完成",
                    f"字幕配音部分完成！\n\n"
                    f"配音统计:\n"
                    f"总字幕数: {total_count}\n"
                    f"配音成功: {completed_count}\n"
                    f"需要调整: {needs_adjustment_count}\n"
                    f"配音失败: {failed_count}\n"
                    f"成功率: {success_rate:.1f}%\n\n"
                    f"标记为红色的字幕需要人工调整：\n"
                    f"• 音频时长超出字幕时长过多\n"
                    f"• 需要加速超过1.2倍\n"
                    f"• 建议缩短字幕内容或分割长句\n\n"
                    f"调整完成后可重新生成配音。"
                )
            else:
                # 配音失败较多
                QMessageBox.critical(
                    self,
                    "字幕配音失败",
                    f"字幕配音失败！\n\n"
                    f"配音统计:\n"
                    f"总字幕数: {total_count}\n"
                    f"配音成功: {completed_count}\n"
                    f"配音失败: {failed_count}\n\n"
                    f"可能的原因:\n"
                    f"1. TTS服务连接问题\n"
                    f"2. 字幕内容格式错误\n"
                    f"3. 输出目录权限问题\n\n"
                    f"请检查设置后重试。"
                )

        except Exception as e:
            print(f"显示配音结果时出错: {e}")
            QMessageBox.warning(self, "提示", f"字幕配音完成，但显示结果时出错: {str(e)}")

    def validate_background_music_requirements(self):
        """验证保留背景音乐的要求"""
        try:
            # 检查是否启用了人声分离
            voice_separation_enabled = self.check_voice_separation_enabled()
            if not voice_separation_enabled:
                return {
                    'valid': False,
                    'message': (
                        "要保留背景音乐，需要先启用人声分离功能。\n\n"
                        "请在首页选项设置中勾选\"人声分离\"，\n"
                        "并完成音频的人声分离处理。"
                    )
                }

            # 检查背景音乐文件是否存在
            background_music_path = self.get_background_music_path()
            if not background_music_path or not os.path.exists(background_music_path):
                return {
                    'valid': False,
                    'message': (
                        "未找到背景音乐文件。\n\n"
                        "请先执行人声分离操作，生成背景音乐文件后\n"
                        "再尝试保留背景音乐。"
                    )
                }

            # 验证背景音乐文件的有效性
            if not self.validate_background_music_file(background_music_path):
                return {
                    'valid': False,
                    'message': (
                        "背景音乐文件损坏或无效。\n\n"
                        "请重新执行人声分离操作。"
                    )
                }

            return {
                'valid': True,
                'message': f"背景音乐文件已准备就绪: {os.path.basename(background_music_path)}"
            }

        except Exception as e:
            print(f"❌ 验证背景音乐要求失败: {e}")
            return {
                'valid': False,
                'message': f"验证过程中出现错误: {str(e)}"
            }

    def check_voice_separation_enabled(self):
        """检查是否启用了人声分离功能"""
        try:
            # 检查参数设置面板中的人声分离选项
            if hasattr(self, 'parameter_panel') and hasattr(self.parameter_panel, 'voice_separation_checkbox'):
                return self.parameter_panel.voice_separation_checkbox.isChecked()

            # 如果没有找到复选框，检查是否有人声分离的输出文件
            voice_separation_dir = self.get_voice_separation_output_dir()
            if voice_separation_dir and os.path.exists(voice_separation_dir):
                # 检查目录中是否有分离后的文件
                files = os.listdir(voice_separation_dir)
                background_files = [f for f in files if 'background' in f.lower() or 'other' in f.lower()]
                return len(background_files) > 0

            return False

        except Exception as e:
            print(f"❌ 检查人声分离状态失败: {e}")
            return False

    def get_voice_separation_output_dir(self):
        """获取人声分离输出目录"""
        try:
            if hasattr(self, 'parameter_panel') and hasattr(self.parameter_panel, 'output_path_edit'):
                output_dir = self.parameter_panel.output_path_edit.text().strip()
                if output_dir:
                    return os.path.join(output_dir, "voice_separation")

            # 默认输出目录
            return os.path.join("output", "voice_separation")

        except Exception as e:
            print(f"❌ 获取人声分离输出目录失败: {e}")
            return None

    def get_background_music_path(self):
        """获取背景音乐文件路径"""
        try:
            voice_separation_dir = self.get_voice_separation_output_dir()
            if not voice_separation_dir or not os.path.exists(voice_separation_dir):
                return None

            # 查找背景音乐文件（可能的命名模式）
            possible_patterns = [
                "*background*.wav",
                "*background*.mp3",
                "*other*.wav",
                "*other*.mp3",
                "*instrumental*.wav",
                "*instrumental*.mp3"
            ]

            import glob
            for pattern in possible_patterns:
                files = glob.glob(os.path.join(voice_separation_dir, pattern))
                if files:
                    # 返回最新的文件
                    latest_file = max(files, key=os.path.getmtime)
                    print(f"🎵 找到背景音乐文件: {latest_file}")
                    return latest_file

            print("❌ 未找到背景音乐文件")
            return None

        except Exception as e:
            print(f"❌ 获取背景音乐文件路径失败: {e}")
            return None

    def validate_background_music_file(self, file_path):
        """验证背景音乐文件的有效性"""
        try:
            if not os.path.exists(file_path):
                return False

            # 检查文件大小
            file_size = os.path.getsize(file_path)
            if file_size < 1024:  # 小于1KB认为无效
                print(f"❌ 背景音乐文件太小: {file_size} bytes")
                return False

            # 尝试使用pydub验证音频文件
            try:
                from pydub import AudioSegment
                audio = AudioSegment.from_file(file_path)

                # 检查音频长度
                if len(audio) < 1000:  # 小于1秒认为无效
                    print(f"❌ 背景音乐文件太短: {len(audio)}ms")
                    return False

                # 检查是否为静音
                if audio.dBFS == float('-inf'):
                    print("❌ 背景音乐文件为静音")
                    return False

                print(f"✅ 背景音乐文件验证通过: 长度={len(audio)/1000:.2f}s, 音量={audio.dBFS:.1f}dB")
                return True

            except Exception as e:
                print(f"❌ 音频文件格式验证失败: {e}")
                return False

        except Exception as e:
            print(f"❌ 验证背景音乐文件失败: {e}")
            return False





class FunctionArea(QWidget):
    """
    功能区域
    显示各种功能卡片：视频提取音频、音频提取字幕、字幕翻译、字幕配音等
    """
    # 添加信号用于通知主窗口切换到音频提取界面
    switch_to_audio_extraction = Signal()

    def __init__(self):
        super().__init__()
        self.setFixedSize(1359, 776)  # 与左侧导航栏保持一致的高度
        self.setup_ui()

    def setup_ui(self):
        """初始化功能区域UI"""
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)

        # 创建背景Frame
        background_frame = QFrame()
        background_frame.setFixedSize(1359, 776)
        background_frame.setStyleSheet(StyleManager.get_frame_style())

        # Frame内部布局
        frame_layout = QVBoxLayout(background_frame)
        frame_layout.setContentsMargins(40, 30, 40, 30)
        frame_layout.setSpacing(20)

        # 标题区域
        title_container = self.create_title_section()
        frame_layout.addWidget(title_container)

        # 创建滚动区域来包装功能卡片
        scroll_area = QScrollArea()
        scroll_area.setFixedSize(1279, 536)  # 减小滚动区域高度为底部提示留出空间
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAlwaysOff)  # 隐藏垂直滚动条
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)  # 隐藏水平滚动条
        scroll_area.setWidgetResizable(True)  # 允许内容自动调整大小
        scroll_area.setStyleSheet("""
            QScrollArea {
                background-color: transparent;
                border: none;
            }
            QScrollArea > QWidget > QWidget {
                background-color: transparent;
            }
        """)

        # 功能卡片区域（只包含卡片，不包含底部提示）
        cards_container = self.create_function_cards()
        scroll_area.setWidget(cards_container)

        frame_layout.addWidget(scroll_area)

        # 添加间隔空间，防止滚动内容与底部提示重叠
        frame_layout.addSpacing(15)

        # 底部提示信息（固定在最下面，不在滚动区域内）
        bottom_container = self.create_bottom_tips()
        frame_layout.addWidget(bottom_container)

        # 将背景Frame添加到主布局
        main_layout.addWidget(background_frame)

    def create_title_section(self):
        """创建标题区域"""
        title_container = QWidget()
        title_container.setFixedHeight(120)  # 增加高度以容纳更多内容

        # 创建背景装饰Frame
        background_frame = QFrame(title_container)
        background_frame.setFixedSize(1279, 120)  # 设置背景frame尺寸
        background_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(43, 157, 124, 0.1),
                    stop:0.3 rgba(30, 120, 90, 0.08),
                    stop:0.7 rgba(20, 80, 60, 0.06),
                    stop:1 rgba(15, 40, 30, 0.04));
                border: 1px solid rgba(43, 157, 124, 0.2);
                border-radius: 25px;
            }
        """)

        title_layout = QHBoxLayout(title_container)
        title_layout.setContentsMargins(40, 20, 40, 20)
        title_layout.setSpacing(30)

        # 左侧内容区域
        left_content = QWidget()
        left_layout = QVBoxLayout(left_content)
        left_layout.setContentsMargins(0, 0, 0, 0)
        left_layout.setSpacing(8)

        # 主标题区域
        title_header = QHBoxLayout()
        title_header.setSpacing(15)

        # 装饰图标
        icon_label = QLabel("⚡")
        icon_label.setStyleSheet(StyleManager.get_label_style(color='#2B9D7C', font_size=32))
        icon_label.setFixedSize(40, 40)
        icon_label.setAlignment(Qt.AlignCenter)
        title_header.addWidget(icon_label)

        # 主标题
        main_title = QLabel("功能中心")
        main_title.setStyleSheet(f"""
            QLabel {{
                color: #FFFFFF;
                font-size: 28px;
                font-weight: bold;
                background-color: transparent;
                border: none;
            }}
        """)
        title_header.addWidget(main_title)

        # 状态徽章
        status_badge = QLabel("AI加速")
        status_badge.setStyleSheet("""
            QLabel {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #2B9D7C, stop:1 #00CC55);
                color: #FFFFFF;
                font-size: 12px;
                font-weight: bold;
                border-radius: 10px;
                padding: 4px 8px;
            }
        """)
        status_badge.setFixedHeight(20)
        status_badge.setAlignment(Qt.AlignCenter)
        title_header.addWidget(status_badge)
        title_header.addStretch()

        left_layout.addLayout(title_header)

        # 副标题
        sub_title = QLabel("选择您需要的AI视频处理功能模块，体验智能化工作流程")
        sub_title.setStyleSheet(StyleManager.get_label_style(color=StyleManager.COLORS['text_gray'], font_size=16))
        left_layout.addWidget(sub_title)

        title_layout.addWidget(left_content)

        # 右侧统计信息区域
        stats_container = QWidget()
        stats_container.setFixedWidth(300)
        stats_layout = QHBoxLayout(stats_container)
        stats_layout.setContentsMargins(0, 0, 0, 0)
        stats_layout.setSpacing(20)

        # 可用功能统计
        available_stat = self.create_stat_card("6", "可用功能", "#4ECDC4")
        stats_layout.addWidget(available_stat)

        # AI模型统计
        ai_stat = self.create_stat_card("3", "AI模型", "#45B7D1")
        stats_layout.addWidget(ai_stat)

        # 处理速度统计
        speed_stat = self.create_stat_card("10x", "处理加速", "#96CEB4")
        stats_layout.addWidget(speed_stat)

        title_layout.addWidget(stats_container)

        return title_container

    def create_stat_card(self, value, label, color):
        """创建统计卡片 - 美化版本"""
        card = QFrame()
        card.setFixedSize(85, 65)  # 稍微增大尺寸
        card.setStyleSheet(f"""
            QFrame {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba({StyleManager.hex_to_rgb(color)}, 0.2),
                    stop:0.5 rgba({StyleManager.hex_to_rgb(color)}, 0.12),
                    stop:1 rgba({StyleManager.hex_to_rgb(color)}, 0.08));
                border: 2px solid rgba({StyleManager.hex_to_rgb(color)}, 0.4);
                border-radius: 16px;
                box-shadow: 0px 4px 16px rgba({StyleManager.hex_to_rgb(color)}, 0.15);
            }}
            QFrame:hover {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba({StyleManager.hex_to_rgb(color)}, 0.3),
                    stop:0.5 rgba({StyleManager.hex_to_rgb(color)}, 0.18),
                    stop:1 rgba({StyleManager.hex_to_rgb(color)}, 0.12));
                border: 2px solid rgba({StyleManager.hex_to_rgb(color)}, 0.6);
                transform: translateY(-2px);
            }}
        """)

        card_layout = QVBoxLayout(card)
        card_layout.setContentsMargins(8, 8, 8, 8)
        card_layout.setSpacing(2)

        # 数值
        value_label = QLabel(value)
        value_label.setStyleSheet(f"""
            QLabel {{
                color: {color};
                font-size: 18px;
                font-weight: bold;
                background-color: transparent;
                border: none;
            }}
        """)
        value_label.setAlignment(Qt.AlignCenter)
        card_layout.addWidget(value_label)

        # 标签
        label_widget = QLabel(label)
        label_widget.setStyleSheet(StyleManager.get_label_style(color=StyleManager.COLORS['text_gray'], font_size=10))
        label_widget.setAlignment(Qt.AlignCenter)
        label_widget.setWordWrap(True)
        card_layout.addWidget(label_widget)

        return card

    def create_function_cards(self):
        """创建功能卡片区域"""
        cards_container = QWidget()

        main_layout = QVBoxLayout(cards_container)
        main_layout.setContentsMargins(0, 20, 0, 30)  # 增加底部边距，为底部提示留出空间
        main_layout.setSpacing(25)

        # 功能分类标题
        category_header = QHBoxLayout()
        category_header.setSpacing(15)

        # 分类图标
        category_icon = QLabel("🚀")
        category_icon.setStyleSheet(StyleManager.get_label_style(color='#2B9D7C', font_size=24))
        category_icon.setFixedSize(30, 30)
        category_icon.setAlignment(Qt.AlignCenter)
        category_header.addWidget(category_icon)

        # 分类标题
        category_title = QLabel("核心功能模块")
        category_title.setStyleSheet(StyleManager.get_label_style(color='#FFFFFF', font_size=20))
        category_title.setProperty("font-weight", "bold")
        category_header.addWidget(category_title)

        # 装饰线 - 美化版本
        decorator_line = QFrame()
        decorator_line.setFixedHeight(3)
        decorator_line.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 transparent,
                    stop:0.05 rgba(43, 157, 124, 0.2),
                    stop:0.1 rgba(43, 157, 124, 0.5),
                    stop:0.5 rgba(43, 157, 124, 0.8),
                    stop:0.9 rgba(43, 157, 124, 0.5),
                    stop:0.95 rgba(43, 157, 124, 0.2),
                    stop:1 transparent);
                border: none;
                border-radius: 1.5px;
            }
        """)
        category_header.addWidget(decorator_line)

        main_layout.addLayout(category_header)

        # 创建网格布局容器
        grid_container = QWidget()
        grid_layout = QGridLayout(grid_container)
        grid_layout.setContentsMargins(0, 0, 0, 0)
        grid_layout.setSpacing(18)  # 减小间距从25到18

        # 功能卡片数据
        functions = [
            {
                'icon': '📥',
                'title': '视频下载',
                'description': '下载YouTube等平台视频\n支持多种清晰度和格式选择',
                'color': '#6C5CE7',
                'status': '可用',
                'category': 'tool'
            },
            {
                'icon': '🎵',
                'title': '视频提取音频',
                'description': '从视频文件中提取高质量音频\n支持多种视频格式转换',
                'color': '#FF6B6B',
                'status': '可用',
                'category': 'media'
            },
            {
                'icon': '🎤',
                'title': '人声分离',
                'description': '基于Facebook Demucs技术\n智能分离人声和背景音乐',
                'color': '#FF7043',
                'status': '可用',
                'category': 'ai'
            },
            {
                'icon': '📝',
                'title': '音频提取字幕',
                'description': '使用AI语音识别技术\n自动生成准确字幕文件',
                'color': '#4ECDC4',
                'status': '可用',
                'category': 'ai'
            },
            {
                'icon': '🌐',
                'title': '字幕翻译',
                'description': '支持拖拽文件加载\n多翻译服务智能切换',
                'color': '#45B7D1',
                'status': '可用',
                'category': 'ai'
            },
            {
                'icon': '🎙️',
                'title': '字幕配音',
                'description': 'AI智能语音合成\n生成自然流畅的配音',
                'color': '#96CEB4',
                'status': '可用',
                'category': 'ai'
            },
            {
                'icon': '🎬',
                'title': '视频合成',
                'description': '将音频和字幕合并到视频\n一键生成最终作品',
                'color': '#FFEAA7',
                'status': '可用',
                'category': 'media'
            },
            {
                'icon': '⚙️',
                'title': '批量处理',
                'description': '支持批量文件处理\n提高工作效率',
                'color': '#DDA0DD',
                'status': '可用',
                'category': 'tool'
            },
            {
                'icon': '✏️',
                'title': '字幕编辑器',
                'description': '支持字幕文件编辑\n提升字幕处理效率',
                'color': '#9B59B6',
                'status': '可用',
                'category': 'tool'
            }
        ]

        # 创建功能卡片 (3行3列布局，支持8个功能)
        for i, func_data in enumerate(functions):
            row = i // 3
            col = i % 3
            card = self.create_function_card(func_data)
            grid_layout.addWidget(card, row, col)

        main_layout.addWidget(grid_container)

        return cards_container

    def create_bottom_tips(self):
        """创建底部提示信息区域"""
        bottom_container = QFrame()
        bottom_container.setFixedHeight(60)
        bottom_container.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(27, 30, 36, 0.95),
                    stop:0.3 rgba(30, 35, 41, 0.9),
                    stop:0.7 rgba(35, 40, 46, 0.9),
                    stop:1 rgba(32, 37, 43, 0.95));
                border: 1px solid rgba(43, 157, 124, 0.3);
                border-radius: 15px;
            }
        """)

        bottom_info = QHBoxLayout(bottom_container)
        bottom_info.setContentsMargins(20, 15, 20, 15)
        bottom_info.setSpacing(15)

        # 提示图标容器
        tip_icon_container = QFrame()
        tip_icon_container.setFixedSize(30, 30)
        tip_icon_container.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(43, 157, 124, 0.2),
                    stop:1 rgba(43, 157, 124, 0.1));
                border: 1px solid rgba(43, 157, 124, 0.3);
                border-radius: 15px;
            }
        """)

        tip_icon_layout = QVBoxLayout(tip_icon_container)
        tip_icon_layout.setContentsMargins(0, 0, 0, 0)

        tip_icon = QLabel("💡")
        tip_icon.setStyleSheet(StyleManager.get_label_style(font_size=14))
        tip_icon.setAlignment(Qt.AlignCenter)
        tip_icon_layout.addWidget(tip_icon)

        bottom_info.addWidget(tip_icon_container)

        # 提示文字
        tip_text = QLabel("点击任意功能卡片开始您的AI视频处理之旅")
        tip_text.setStyleSheet(StyleManager.get_label_style(color='#FFFFFF', font_size=14))
        tip_text.setProperty("font-weight", "500")
        bottom_info.addWidget(tip_text)

        bottom_info.addStretch()

        # 更多功能提示
        more_text = QLabel("✨ 更多功能即将上线...")
        more_text.setStyleSheet(StyleManager.get_label_style(color='#2B9D7C', font_size=13))
        more_text.setProperty("font-style", "italic")
        more_text.setProperty("font-weight", "500")
        bottom_info.addWidget(more_text)

        return bottom_container

    def create_function_card(self, func_data):
        """创建单个功能卡片"""
        card = QFrame()
        card.setFixedSize(340, 220)  # 减小卡片尺寸以适应3行布局
        card.setCursor(Qt.PointingHandCursor)

        # 卡片样式 - 美化版本
        card.setStyleSheet(f"""
            QFrame {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(26, 30, 36, 0.95),
                    stop:0.3 rgba(30, 35, 41, 0.9),
                    stop:0.7 rgba(35, 40, 46, 0.9),
                    stop:1 rgba(32, 37, 43, 0.95));
                border: 2px solid rgba({StyleManager.hex_to_rgb(func_data['color'])}, 0.4);
                border-radius: 24px;
                box-shadow: 0px 8px 32px rgba(0, 0, 0, 0.3);
            }}
            QFrame:hover {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(35, 40, 46, 1.0),
                    stop:0.3 rgba(40, 45, 51, 0.95),
                    stop:0.7 rgba(45, 50, 56, 0.95),
                    stop:1 rgba(42, 47, 53, 1.0));
                border: 2px solid rgba({StyleManager.hex_to_rgb(func_data['color'])}, 0.8);
                transform: translateY(-4px);
                box-shadow: 0px 12px 40px rgba({StyleManager.hex_to_rgb(func_data['color'])}, 0.2);
            }}
        """)

        # 卡片布局
        card_layout = QVBoxLayout(card)
        card_layout.setContentsMargins(20, 20, 20, 20)
        card_layout.setSpacing(12)  # 减小整体间距，更紧凑

        # 顶部区域：图标和状态
        top_layout = QHBoxLayout()
        top_layout.setSpacing(10)

        # 图标容器 - 添加背景装饰
        icon_container = QFrame()
        icon_container.setFixedSize(56, 56)
        icon_container.setStyleSheet(f"""
            QFrame {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba({StyleManager.hex_to_rgb(func_data['color'])}, 0.15),
                    stop:1 rgba({StyleManager.hex_to_rgb(func_data['color'])}, 0.08));
                border: 2px solid rgba({StyleManager.hex_to_rgb(func_data['color'])}, 0.3);
                border-radius: 28px;
            }}
        """)

        icon_layout = QVBoxLayout(icon_container)
        icon_layout.setContentsMargins(0, 0, 0, 0)

        # 图标
        icon_label = QLabel(func_data['icon'])
        icon_label.setStyleSheet(StyleManager.get_label_style(font_size=28))
        icon_label.setAlignment(Qt.AlignCenter)
        icon_layout.addWidget(icon_label)

        top_layout.addWidget(icon_container)

        top_layout.addStretch()

        # 状态标签 - 美化版本
        status_label = QLabel(func_data['status'])
        status_label.setStyleSheet(f"""
            QLabel {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba({StyleManager.hex_to_rgb(func_data['color'])}, 0.25),
                    stop:1 rgba({StyleManager.hex_to_rgb(func_data['color'])}, 0.15));
                color: {func_data['color']};
                font-size: 10px;
                font-weight: bold;
                border: 1px solid rgba({StyleManager.hex_to_rgb(func_data['color'])}, 0.5);
                border-radius: 10px;
                padding: 4px 8px;
                text-transform: uppercase;
                letter-spacing: 0.5px;
            }}
        """)
        status_label.setAlignment(Qt.AlignCenter)
        status_label.setFixedHeight(20)
        top_layout.addWidget(status_label)

        card_layout.addLayout(top_layout)

        # 标题
        title_label = QLabel(func_data['title'])
        title_label.setStyleSheet(StyleManager.get_label_style(color='#FFFFFF', font_size=16))  # 减小标题字体
        title_label.setProperty("font-weight", "bold")
        title_label.setFixedHeight(20)  # 固定标题高度
        card_layout.addWidget(title_label)

        # 描述区域 - 使用固定高度容器
        desc_container = QWidget()
        desc_container.setFixedHeight(60)  # 固定描述区域高度
        desc_layout = QVBoxLayout(desc_container)
        desc_layout.setContentsMargins(0, 0, 0, 0)
        desc_layout.setSpacing(0)

        desc_label = QLabel(func_data['description'])
        desc_label.setStyleSheet(
            StyleManager.get_label_style(color=StyleManager.COLORS['text_gray'], font_size=12))  # 减小描述字体
        desc_label.setWordWrap(True)
        desc_label.setAlignment(Qt.AlignTop)
        desc_layout.addWidget(desc_label)

        card_layout.addWidget(desc_container)

        # 添加弹性空间，将按钮推到底部
        card_layout.addStretch()

        # 底部操作按钮
        button_layout = QHBoxLayout()
        button_layout.setContentsMargins(0, 0, 0, 0)
        button_layout.addStretch()

        action_btn = QPushButton("立即使用")
        action_btn.setFixedSize(100, 36)  # 稍微增大按钮尺寸
        action_btn.setStyleSheet(f"""
            QPushButton {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 {func_data['color']},
                    stop:0.5 rgba({StyleManager.hex_to_rgb(func_data['color'])}, 0.9),
                    stop:1 rgba({StyleManager.hex_to_rgb(func_data['color'])}, 0.8));
                color: #FFFFFF;
                font-size: 12px;
                font-weight: bold;
                border: 2px solid rgba({StyleManager.hex_to_rgb(func_data['color'])}, 0.6);
                border-radius: 18px;
                padding: 8px 16px;
                text-transform: uppercase;
                letter-spacing: 0.5px;
            }}
            QPushButton:hover {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba({StyleManager.hex_to_rgb(func_data['color'])}, 1.1),
                    stop:1 {func_data['color']});
                border: 2px solid {func_data['color']};
                transform: translateY(-2px);
                box-shadow: 0px 6px 20px rgba({StyleManager.hex_to_rgb(func_data['color'])}, 0.4);
            }}
            QPushButton:pressed {{
                background: rgba({StyleManager.hex_to_rgb(func_data['color'])}, 0.7);
                transform: translateY(0px);
            }}
        """)

        # 绑定点击事件
        action_btn.clicked.connect(lambda checked, title=func_data['title']: self.on_function_clicked(title))

        button_layout.addWidget(action_btn)
        card_layout.addLayout(button_layout)

        return card

    def on_function_clicked(self, function_title):
        """功能卡片点击事件"""
        print(f"点击了功能: {function_title}")

        if function_title == "视频下载":
            # 弹出视频下载对话框
            self.show_video_download_dialog()
        elif function_title == "视频提取音频":
            # 直接弹出批量音频提取对话框
            self.show_batch_audio_extraction_dialog()
        elif function_title == "人声分离":
            # 弹出人声分离对话框
            self.show_voice_separation_dialog()
        elif function_title == "音频提取字幕":
            # 弹出音频字幕提取对话框
            self.show_subtitle_extraction_dialog()
        elif function_title == "字幕编辑器":
            # 弹出字幕编辑器对话框
            self.show_subtitle_editor_dialog()
        elif function_title == "字幕翻译":
            # 弹出字幕翻译对话框
            self.show_subtitle_translation_dialog()
        elif function_title == "字幕配音":
            # 弹出字幕配音对话框
            self.show_subtitle_voiceover_dialog()
        else:
            # 其他功能暂时显示提示信息
            print(f"功能 '{function_title}' 正在开发中...")
            # TODO: 这里可以添加其他功能的具体实现逻辑

    def show_batch_audio_extraction_dialog(self):
        """显示批量音频提取对话框"""
        try:
            from .batch_audio_extraction_dialog import BatchAudioExtractionDialog
            # 找到主窗口
            main_window = self
            while main_window.parent() is not None:
                main_window = main_window.parent()

            dialog = BatchAudioExtractionDialog(main_window)
            dialog.exec()
        except ImportError as e:
            print(f"无法导入批量音频提取对话框: {e}")
            # 如果导入失败，显示提示消息
            from PySide6.QtWidgets import QMessageBox

            # 找到主窗口
            main_window = self
            while main_window.parent() is not None:
                main_window = main_window.parent()

            QMessageBox.information(
                main_window,
                "提示",
                "批量音频提取对话框正在开发中，请使用主界面的拖拽功能上传视频文件。"
            )

    def show_voice_separation_dialog(self):
        """显示人声分离对话框"""
        try:
            from .voice_separation_dialog import VoiceSeparationDialog
            # 找到主窗口
            main_window = self
            while main_window.parent() is not None:
                main_window = main_window.parent()

            dialog = VoiceSeparationDialog(main_window)
            dialog.exec()
        except ImportError as e:
            print(f"无法导入人声分离对话框: {e}")
            # 如果导入失败，显示提示消息
            from PySide6.QtWidgets import QMessageBox

            # 找到主窗口
            main_window = self
            while main_window.parent() is not None:
                main_window = main_window.parent()

            QMessageBox.information(
                main_window,
                "提示",
                "人声分离功能正在开发中，将使用AI技术智能分离音频中的人声和背景音乐。"
            )

    def show_video_download_dialog(self):
        """显示视频下载对话框"""
        try:
            from .video_download_dialog import VideoDownloadDialog

            # 找到主窗口
            main_window = self
            while main_window.parent() is not None:
                main_window = main_window.parent()

            # 创建并显示视频下载对话框
            dialog = VideoDownloadDialog(main_window)
            dialog.exec()

        except ImportError as e:
            # 如果导入失败，显示错误信息
            from PySide6.QtWidgets import QMessageBox

            msg_box = QMessageBox(self)
            msg_box.setWindowTitle("视频下载功能")
            msg_box.setText("视频下载功能启动失败！")
            msg_box.setInformativeText(
                f"错误信息：{str(e)}\n\n"
                "请确认以下依赖已正确安装：\n"
                "• yt-dlp: pip install yt-dlp\n"
                "• 其他相关依赖库\n\n"
                "如需帮助，请查看安装文档。"
            )
            msg_box.setIcon(QMessageBox.Critical)
            msg_box.setStyleSheet("""
                QMessageBox {
                    background-color: #1B1E24;
                    color: #FFFFFF;
                    font-size: 14px;
                }
                QMessageBox QLabel {
                    color: #FFFFFF;
                    font-size: 14px;
                }
                QMessageBox QPushButton {
                    background-color: #6C5CE7;
                    color: #FFFFFF;
                    border: none;
                    border-radius: 8px;
                    padding: 8px 16px;
                    font-size: 12px;
                    font-weight: bold;
                    min-width: 80px;
                }
                QMessageBox QPushButton:hover {
                    background-color: #5A48D1;
                }
                QMessageBox QPushButton:pressed {
                    background-color: #4C3FC7;
                }
            """)
            msg_box.exec()

    def show_subtitle_editor_dialog(self):
        """显示字幕编辑器对话框"""
        try:
            try:
                from .subtitle_editor_dialog import SubtitleEditorDialog
            except ImportError:
                from subtitle_editor_dialog import SubtitleEditorDialog

            # 找到主窗口
            main_window = self
            while main_window.parent() is not None:
                main_window = main_window.parent()

            # 创建并显示字幕编辑器对话框
            dialog = SubtitleEditorDialog(main_window)
            dialog.exec()

        except ImportError as e:
            # 如果导入失败，显示错误信息
            from PySide6.QtWidgets import QMessageBox

            msg_box = QMessageBox(self)
            msg_box.setWindowTitle("字幕编辑器功能")
            msg_box.setText("字幕编辑器功能启动失败！")
            msg_box.setInformativeText(
                f"错误信息：{str(e)}\n\n"
                "字幕编辑器功能包含以下特性：\n"
                "• 支持SRT、VTT等多种字幕格式\n"
                "• 实时预览和编辑功能\n"
                "• 时间轴调整和同步\n"
                "• 批量编辑和替换\n\n"
                "如需帮助，请查看相关文档。"
            )
            msg_box.setIcon(QMessageBox.Information)
            msg_box.setStyleSheet("""
                QMessageBox {
                    background-color: #1B1E24;
                    color: #FFFFFF;
                    font-size: 14px;
                }
                QMessageBox QLabel {
                    color: #FFFFFF;
                    font-size: 14px;
                }
                QMessageBox QPushButton {
                    background-color: #9B59B6;
                    color: #FFFFFF;
                    border: none;
                    border-radius: 8px;
                    padding: 8px 16px;
                    font-size: 12px;
                    font-weight: bold;
                    min-width: 80px;
                }
                QMessageBox QPushButton:hover {
                    background-color: #8E44AD;
                }
                QMessageBox QPushButton:pressed {
                    background-color: #7D3C98;
                }
            """)
            msg_box.exec()

    def show_subtitle_voiceover_dialog(self):
        """显示字幕配音对话框（优化启动模式）"""
        try:
            print("🚀 正在启动字幕配音功能...")

            # 直接使用简单启动方式，避免复杂的进程管理
            QTimer.singleShot(100, self._launch_subtitle_voiceover_simple)

        except Exception as e:
            print(f"❌ 字幕配音功能启动失败: {e}")
            import traceback
            traceback.print_exc()

    def _launch_subtitle_voiceover_independently(self):
        """独立启动字幕配音功能（完全模拟测试文件的成功模式）"""
        try:
            print("🎯 正在独立启动字幕配音模块...")

            # 使用完全独立的启动方式
            import subprocess
            import sys
            import os

            # 获取当前工作目录
            current_dir = os.getcwd()

            # 创建独立启动脚本
            startup_script = os.path.join(current_dir, "launch_subtitle_voiceover.py")

            script_content = '''import sys
import os
from PySide6.QtWidgets import QApplication
from PySide6.QtCore import Qt

# 添加当前目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def main():
    """启动字幕配音功能"""
    # 创建独立的Qt应用
    app = QApplication.instance()
    if app is None:
        app = QApplication(sys.argv)
        app.setApplicationName("FlipTalk AI - 字幕配音")
        app.setQuitOnLastWindowClosed(False)  # 不自动退出

        try:
            # 导入字幕配音对话框
        from ui.subtitle_voiceover_dialog import SubtitleVoiceoverDialog

        # 创建对话框（无父窗口）
        dialog = SubtitleVoiceoverDialog(parent=None)

        # 设置为独立窗口
        dialog.setWindowTitle("FlipTalk AI - 字幕配音")
        dialog.setWindowFlags(Qt.Window | Qt.WindowTitleHint | Qt.WindowCloseButtonHint | Qt.WindowMinMaxButtonsHint)
        dialog.setAttribute(Qt.WA_QuitOnClose, True)  # 关闭时退出子应用
        dialog.resize(1400, 800)

        # 显示对话框
        dialog.show()
        dialog.raise_()
        dialog.activateWindow()

        print("✅ 字幕配音窗口已独立启动")

        # 只有在没有其他应用实例时才启动事件循环
        if app.instance() and len(app.allWindows()) == 1:
            return app.exec()
        else:
            return 0

    except Exception as e:
        print(f"❌ 字幕配音启动失败: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
'''

            # 写入启动脚本
            with open(startup_script, 'w', encoding='utf-8') as f:
                f.write(script_content)

            print(f"📝 已创建独立启动脚本: {startup_script}")

            # 使用新进程启动
            python_executable = sys.executable
            cmd = [python_executable, startup_script]

            print(f"🚀 正在启动独立进程: {' '.join(cmd)}")

            # 创建独立进程（无窗口模式）
            if sys.platform == "win32":
                # Windows下使用无窗口启动
                startupinfo = subprocess.STARTUPINFO()
                startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
                startupinfo.wShowWindow = subprocess.SW_HIDE
                creationflags = subprocess.CREATE_NO_WINDOW
            else:
                startupinfo = None
                creationflags = 0

            process = subprocess.Popen(
                cmd,
                cwd=current_dir,
                startupinfo=startupinfo,
                creationflags=creationflags,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )

            print(f"✅ 字幕配音功能已在独立进程中启动 (PID: {process.pid})")
            print("📌 字幕配音窗口现在作为完全独立的应用程序运行")
            print("📌 您可以在主程序和字幕配音窗口之间自由切换")

            # 清理启动脚本（延迟删除）
            def cleanup_script():
                try:
                    if os.path.exists(startup_script):
                        os.remove(startup_script)
                        print(f"🧹 已清理启动脚本: {startup_script}")
                except:
                    pass

            QTimer.singleShot(5000, cleanup_script)  # 5秒后清理

        except Exception as e:
            print(f"❌ 独立启动失败: {e}")
            import traceback
            traceback.print_exc()

            # 回退到简单启动方式
            self._launch_subtitle_voiceover_simple()


class SystemSettingsArea(QWidget):
    """
    系统设置区域
    提供软件的各种系统级配置选项，包含界面、处理、性能等设置
    """

    def __init__(self):
        super().__init__()
        self.setFixedSize(1359, 776)  # 使用全宽尺寸
        self.current_category = "界面设置"  # 当前选中的设置分类
        self.setup_ui()

    def setup_ui(self):
        """初始化系统设置区域UI"""
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)

        # 创建背景Frame
        background_frame = QFrame()
        background_frame.setFixedSize(1359, 776)
        background_frame.setStyleSheet(StyleManager.get_frame_style())

        # Frame内部布局
        frame_layout = QVBoxLayout(background_frame)
        frame_layout.setContentsMargins(40, 30, 40, 30)
        frame_layout.setSpacing(20)

        # 标题区域
        header_section = self.create_header_section()
        frame_layout.addWidget(header_section)

        # 主内容区域（左侧导航 + 右侧设置）
        content_section = self.create_content_section()
        frame_layout.addWidget(content_section)

        # 底部操作区域
        bottom_section = self.create_bottom_section()
        frame_layout.addWidget(bottom_section)

        # 将背景Frame添加到主布局
        main_layout.addWidget(background_frame)

    def create_header_section(self):
        """创建标题区域"""
        header_container = QWidget()
        header_container.setFixedHeight(120)

        # 创建背景装饰Frame
        background_frame = QFrame(header_container)
        background_frame.setFixedSize(1279, 120)
        background_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(150, 206, 180, 0.1),
                    stop:0.3 rgba(120, 170, 150, 0.08),
                    stop:0.7 rgba(90, 130, 115, 0.06),
                    stop:1 rgba(60, 90, 80, 0.04));
                border: 1px solid rgba(150, 206, 180, 0.2);
                border-radius: 25px;
            }
        """)

        header_layout = QHBoxLayout(header_container)
        header_layout.setContentsMargins(40, 20, 40, 20)
        header_layout.setSpacing(30)

        # 左侧标题区域
        left_content = QWidget()
        left_layout = QVBoxLayout(left_content)
        left_layout.setContentsMargins(0, 0, 0, 0)
        left_layout.setSpacing(8)

        # 标题区域
        title_header = QHBoxLayout()
        title_header.setSpacing(15)

        # 装饰图标
        icon_label = QLabel("🔨")
        icon_label.setStyleSheet(StyleManager.get_label_style(color='#96CEB4', font_size=32))
        icon_label.setFixedSize(40, 40)
        icon_label.setAlignment(Qt.AlignCenter)
        title_header.addWidget(icon_label)

        # 主标题
        main_title = QLabel("系统设置")
        main_title.setStyleSheet(StyleManager.get_label_style(color='#FFFFFF', font_size=28))
        main_title.setProperty("font-weight", "bold")
        title_header.addWidget(main_title)

        # 配置状态
        status_badge = QLabel("配置管理")
        status_badge.setStyleSheet("""
            QLabel {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #96CEB4, stop:1 #4ECDC4);
                color: #FFFFFF;
                font-size: 12px;
                font-weight: bold;
                border-radius: 10px;
                padding: 4px 8px;
            }
        """)
        status_badge.setFixedHeight(20)
        status_badge.setAlignment(Qt.AlignCenter)
        title_header.addWidget(status_badge)
        title_header.addStretch()

        left_layout.addLayout(title_header)

        # 副标题
        sub_title = QLabel("自定义软件行为，优化处理性能，配置系统偏好设置")
        sub_title.setStyleSheet(StyleManager.get_label_style(color=StyleManager.COLORS['text_gray'], font_size=16))
        left_layout.addWidget(sub_title)

        header_layout.addWidget(left_content)

        # 右侧快速状态
        status_container = QWidget()
        status_container.setFixedWidth(300)
        status_layout = QVBoxLayout(status_container)
        status_layout.setContentsMargins(0, 0, 0, 0)
        status_layout.setSpacing(8)

        # 系统状态信息
        system_info = QLabel("💻 系统版本：v1.0.0 | GPU：可用 | 内存：8GB")
        system_info.setStyleSheet(StyleManager.get_label_style(color=StyleManager.COLORS['text_gray'], font_size=12))
        status_layout.addWidget(system_info)

        # 配置状态
        config_info = QLabel("⚙️ 已保存配置：12项 | 最后修改：今天 14:30")
        config_info.setStyleSheet(StyleManager.get_label_style(color=StyleManager.COLORS['text_gray'], font_size=12))
        status_layout.addWidget(config_info)

        header_layout.addWidget(status_container)

        return header_container

    def create_content_section(self):
        """创建主内容区域"""
        content_container = QWidget()
        content_layout = QHBoxLayout(content_container)
        content_layout.setContentsMargins(0, 0, 0, 0)
        content_layout.setSpacing(20)

        # 左侧分类导航
        navigation_panel = self.create_navigation_panel()
        content_layout.addWidget(navigation_panel)

        # 右侧设置内容
        settings_panel = self.create_settings_panel()
        content_layout.addWidget(settings_panel)

        return content_container

    def create_navigation_panel(self):
        """创建左侧分类导航面板"""
        nav_container = QFrame()
        nav_container.setFixedWidth(280)
        nav_container.setStyleSheet("""
            QFrame {
                background-color: #14161A;
                border-radius: 20px;
                border: 1px solid rgba(255, 255, 255, 0.1);
            }
        """)

        nav_layout = QVBoxLayout(nav_container)
        nav_layout.setContentsMargins(20, 25, 20, 25)
        nav_layout.setSpacing(8)

        # 导航标题
        nav_title = QLabel("设置分类")
        nav_title.setStyleSheet(StyleManager.get_label_style(color='#FFFFFF', font_size=16))
        nav_title.setProperty("font-weight", "bold")
        nav_layout.addWidget(nav_title)

        # 分隔线
        separator = QFrame()
        separator.setFixedHeight(1)
        separator.setStyleSheet("""
            QFrame {
                background-color: rgba(255, 255, 255, 0.1);
                border: none;
            }
        """)
        nav_layout.addWidget(separator)
        nav_layout.addSpacing(10)

        # 设置分类列表
        categories = [
            ("🎨", "界面设置", "主题、语言、显示选项"),
            ("⚡", "性能设置", "GPU、内存、并发数"),
            ("📁", "文件设置", "路径、格式、清理策略"),
            ("🔊", "音视频设置", "质量、编码、输出格式"),
            ("🛠️", "高级设置", "调试、日志、实验功能"),
            ("🔐", "安全设置", "隐私、数据保护")
        ]

        for icon, title, desc in categories:
            nav_btn = self.create_nav_category_button(icon, title, desc)
            nav_layout.addWidget(nav_btn)

        nav_layout.addStretch()

        return nav_container

    def create_nav_category_button(self, icon, title, description):
        """创建分类导航按钮"""
        btn_container = QFrame()
        btn_container.setFixedHeight(70)
        btn_container.setCursor(Qt.PointingHandCursor)

        # 设置选中状态样式
        if title == self.current_category:
            btn_container.setStyleSheet("""
                QFrame {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 rgba(150, 206, 180, 0.3),
                        stop:1 rgba(76, 205, 196, 0.2));
                    border: 1px solid rgba(150, 206, 180, 0.5);
                    border-radius: 12px;
                    margin: 2px;
                }
                QFrame:hover {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 rgba(150, 206, 180, 0.4),
                        stop:1 rgba(76, 205, 196, 0.3));
                }
            """)
        else:
            btn_container.setStyleSheet("""
                QFrame {
                    background-color: transparent;
                    border: 1px solid transparent;
                    border-radius: 12px;
                    margin: 2px;
                }
                QFrame:hover {
                    background-color: rgba(255, 255, 255, 0.05);
                    border: 1px solid rgba(255, 255, 255, 0.1);
                }
            """)

        btn_layout = QHBoxLayout(btn_container)
        btn_layout.setContentsMargins(15, 10, 15, 10)
        btn_layout.setSpacing(12)

        # 图标
        icon_label = QLabel(icon)
        icon_label.setStyleSheet(StyleManager.get_label_style(font_size=20))
        icon_label.setFixedSize(30, 30)
        icon_label.setAlignment(Qt.AlignCenter)
        btn_layout.addWidget(icon_label)

        # 文字信息
        text_container = QWidget()
        text_layout = QVBoxLayout(text_container)
        text_layout.setContentsMargins(0, 0, 0, 0)
        text_layout.setSpacing(2)

        # 标题
        title_label = QLabel(title)
        title_color = '#96CEB4' if title == self.current_category else '#FFFFFF'
        title_label.setStyleSheet(StyleManager.get_label_style(color=title_color, font_size=14))
        title_label.setProperty("font-weight", "bold")
        text_layout.addWidget(title_label)

        # 描述
        desc_label = QLabel(description)
        desc_label.setStyleSheet(StyleManager.get_label_style(color=StyleManager.COLORS['text_gray'], font_size=11))
        desc_label.setWordWrap(True)
        text_layout.addWidget(desc_label)

        btn_layout.addWidget(text_container)

        # 绑定点击事件
        btn_container.mousePressEvent = lambda event, cat=title: self.switch_category(cat)

        return btn_container

    def create_settings_panel(self):
        """创建右侧设置面板"""
        settings_container = QFrame()
        settings_container.setFixedWidth(837)
        settings_container.setStyleSheet("""
            QFrame {
                background-color: #14161A;
                border-radius: 20px;
                border: 1px solid rgba(255, 255, 255, 0.1);
            }
        """)

        # 主布局
        main_layout = QVBoxLayout(settings_container)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)

        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setStyleSheet("""
            QScrollArea {
                background-color: transparent;
                border: none;
                border-radius: 20px;
            }
            QScrollBar:vertical {
                background-color: #1B1E24;
                width: 8px;
                border-radius: 4px;
                margin: 5px;
            }
            QScrollBar::handle:vertical {
                background-color: #96CEB4;
                border-radius: 4px;
                min-height: 20px;
            }
            QScrollBar::handle:vertical:hover {
                background-color: #4ECDC4;
            }
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
                height: 0px;
            }
            QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {
                background: transparent;
            }
        """)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setWidgetResizable(True)

        # 根据当前分类显示不同的设置内容
        if self.current_category == "界面设置":
            content = self.create_ui_settings()
        elif self.current_category == "性能设置":
            content = self.create_performance_settings()
        elif self.current_category == "文件设置":
            content = self.create_file_settings()
        elif self.current_category == "音视频设置":
            content = self.create_media_settings()
        elif self.current_category == "高级设置":
            content = self.create_advanced_settings()
        elif self.current_category == "安全设置":
            content = self.create_security_settings()
        else:
            content = self.create_ui_settings()  # 默认显示界面设置

        # 设置滚动内容
        scroll_area.setWidget(content)
        main_layout.addWidget(scroll_area)

        return settings_container

    def create_ui_settings(self):
        """创建界面设置内容"""
        content = QWidget()
        content.setStyleSheet("""
            QWidget {
                background-color: #14161A;
                border: none;
            }
        """)
        layout = QVBoxLayout(content)
        layout.setContentsMargins(30, 25, 30, 25)
        layout.setSpacing(20)

        # 分类标题
        title = QLabel("🎨 界面设置")
        title.setStyleSheet(StyleManager.get_label_style(color='#96CEB4', font_size=18))
        title.setProperty("font-weight", "bold")
        layout.addWidget(title)

        # 设置项目
        settings_items = [
            self.create_setting_group("主题设置", [
                ("界面主题", "ComboBox", ["深色主题", "浅色主题", "自动"], "深色主题"),
                ("主色调", "ComboBox", ["翠绿色", "蓝色", "紫色", "橙色"], "翠绿色"),
                ("透明效果", "CheckBox", None, True)
            ]),

            self.create_setting_group("语言与区域", [
                ("界面语言", "ComboBox", ["简体中文", "English", "日本語"], "简体中文"),
                ("时间格式", "ComboBox", ["24小时制", "12小时制"], "24小时制"),
                ("数字格式", "ComboBox", ["中文数字", "阿拉伯数字"], "阿拉伯数字")
            ]),

            self.create_setting_group("显示选项", [
                ("字体大小", "ComboBox", ["小", "中", "大", "特大"], "中"),
                ("窗口透明度", "Slider", (50, 100), 95),
                ("显示动画", "CheckBox", None, True),
                ("启动时最大化", "CheckBox", None, False)
            ])
        ]

        for setting_group in settings_items:
            layout.addWidget(setting_group)

        layout.addStretch()
        return content

    def create_performance_settings(self):
        """创建性能设置内容"""
        content = QWidget()
        content.setStyleSheet("""
            QWidget {
                background-color: #14161A;
                border: none;
            }
        """)
        layout = QVBoxLayout(content)
        layout.setContentsMargins(30, 25, 30, 25)
        layout.setSpacing(20)

        # 分类标题
        title = QLabel("⚡ 性能设置")
        title.setStyleSheet(StyleManager.get_label_style(color='#96CEB4', font_size=18))
        title.setProperty("font-weight", "bold")
        layout.addWidget(title)

        # 设置项目
        settings_items = [
            self.create_setting_group("处理器设置", [
                ("GPU加速", "CheckBox", None, True),
                ("CPU核心数", "ComboBox", ["自动", "1", "2", "4", "8"], "自动"),
                ("并发处理数", "Slider", (1, 10), 4)
            ]),

            self.create_setting_group("内存管理", [
                ("最大内存使用", "ComboBox", ["2GB", "4GB", "8GB", "不限制"], "8GB"),
                ("缓存大小", "ComboBox", ["512MB", "1GB", "2GB", "4GB"], "2GB"),
                ("自动清理缓存", "CheckBox", None, True)
            ]),

            self.create_setting_group("处理优化", [
                ("处理质量", "ComboBox", ["快速", "平衡", "高质量"], "平衡"),
                ("预处理优化", "CheckBox", None, True),
                ("后台处理", "CheckBox", None, True)
            ])
        ]

        for setting_group in settings_items:
            layout.addWidget(setting_group)

        layout.addStretch()
        return content

    def create_file_settings(self):
        """创建文件设置内容"""
        content = QWidget()
        content.setStyleSheet("""
            QWidget {
                background-color: #14161A;
                border: none;
            }
        """)
        layout = QVBoxLayout(content)
        layout.setContentsMargins(30, 25, 30, 25)
        layout.setSpacing(20)

        # 分类标题
        title = QLabel("📁 文件设置")
        title.setStyleSheet(StyleManager.get_label_style(color='#96CEB4', font_size=18))
        title.setProperty("font-weight", "bold")
        layout.addWidget(title)

        # 设置项目
        settings_items = [
            self.create_setting_group("目录设置", [
                ("输出目录", "PathSelector", None, "C:/FlipTalk/Output"),
                ("临时目录", "PathSelector", None, "C:/FlipTalk/Temp"),
                ("自动创建子文件夹", "CheckBox", None, True)
            ]),

            self.create_setting_group("文件管理", [
                ("保留原始文件", "CheckBox", None, True),
                ("自动删除临时文件", "CheckBox", None, True),
                ("文件命名规则", "ComboBox", ["原文件名", "时间戳", "自定义"], "原文件名"),
                ("文件覆盖策略", "ComboBox", ["询问", "覆盖", "重命名"], "询问")
            ]),

            self.create_setting_group("存储优化", [
                ("压缩临时文件", "CheckBox", None, False),
                ("定期清理", "ComboBox", ["从不", "每天", "每周", "每月"], "每周"),
                ("存储空间警告", "Slider", (1, 20), 5)
            ])
        ]

        for setting_group in settings_items:
            layout.addWidget(setting_group)

        layout.addStretch()
        return content

    def create_media_settings(self):
        """创建音视频设置内容"""
        content = QWidget()
        content.setStyleSheet("""
            QWidget {
                background-color: #14161A;
                border: none;
            }
        """)
        layout = QVBoxLayout(content)
        layout.setContentsMargins(30, 25, 30, 25)
        layout.setSpacing(20)

        # 分类标题
        title = QLabel("🔊 音视频设置")
        title.setStyleSheet(StyleManager.get_label_style(color='#96CEB4', font_size=18))
        title.setProperty("font-weight", "bold")
        layout.addWidget(title)

        # 设置项目
        settings_items = [
            self.create_setting_group("视频设置", [
                ("输出格式", "ComboBox", ["MP4", "MOV", "AVI", "MKV"], "MP4"),
                ("视频质量", "ComboBox", ["720p", "1080p", "1440p", "4K"], "1080p"),
                ("编码器", "ComboBox", ["H.264", "H.265", "VP9"], "H.264"),
                ("帧率", "ComboBox", ["24fps", "30fps", "60fps"], "30fps")
            ]),

            self.create_setting_group("音频设置", [
                ("音频格式", "ComboBox", ["MP3", "WAV", "AAC", "FLAC"], "MP3"),
                ("音频质量", "ComboBox", ["128kbps", "192kbps", "320kbps"], "192kbps"),
                ("采样率", "ComboBox", ["44.1kHz", "48kHz", "96kHz"], "48kHz"),
                ("声道", "ComboBox", ["单声道", "立体声", "5.1环绕"], "立体声")
            ]),

            self.create_setting_group("字幕设置", [
                ("字幕格式", "ComboBox", ["SRT", "ASS", "VTT"], "SRT"),
                ("字幕编码", "ComboBox", ["UTF-8", "GBK", "BIG5"], "UTF-8"),
                ("默认字体", "ComboBox", ["微软雅黑", "宋体", "Arial"], "微软雅黑"),
                ("字幕位置", "ComboBox", ["底部", "顶部", "中间"], "底部")
            ])
        ]

        for setting_group in settings_items:
            layout.addWidget(setting_group)

        layout.addStretch()
        return content

    def create_advanced_settings(self):
        """创建高级设置内容"""
        content = QWidget()
        content.setStyleSheet("""
            QWidget {
                background-color: #14161A;
                border: none;
            }
        """)
        layout = QVBoxLayout(content)
        layout.setContentsMargins(30, 25, 30, 25)
        layout.setSpacing(20)

        # 分类标题
        title = QLabel("🛠️ 高级设置")
        title.setStyleSheet(StyleManager.get_label_style(color='#96CEB4', font_size=18))
        title.setProperty("font-weight", "bold")
        layout.addWidget(title)

        # 设置项目
        settings_items = [
            self.create_setting_group("调试选项", [
                ("调试模式", "CheckBox", None, False),
                ("详细日志", "CheckBox", None, False),
                ("性能监控", "CheckBox", None, True),
                ("错误报告", "CheckBox", None, True)
            ]),

            self.create_setting_group("实验功能", [
                ("Beta功能", "CheckBox", None, False),
                ("AI增强", "CheckBox", None, True),
                ("实时预览", "CheckBox", None, False),
                ("云端处理", "CheckBox", None, False)
            ]),

            self.create_setting_group("开发者选项", [
                ("API调试", "CheckBox", None, False),
                ("网络日志", "CheckBox", None, False),
                ("导出配置", "Button", None, "导出"),
                ("重置所有设置", "Button", None, "重置")
            ])
        ]

        for setting_group in settings_items:
            layout.addWidget(setting_group)

        layout.addStretch()
        return content

    def create_security_settings(self):
        """创建安全设置内容"""
        content = QWidget()
        content.setStyleSheet("""
            QWidget {
                background-color: #14161A;
                border: none;
            }
        """)
        layout = QVBoxLayout(content)
        layout.setContentsMargins(30, 25, 30, 25)
        layout.setSpacing(20)

        # 分类标题
        title = QLabel("🔐 安全设置")
        title.setStyleSheet(StyleManager.get_label_style(color='#96CEB4', font_size=18))
        title.setProperty("font-weight", "bold")
        layout.addWidget(title)

        # 设置项目
        settings_items = [
            self.create_setting_group("隐私保护", [
                ("本地处理模式", "CheckBox", None, True),
                ("数据加密", "CheckBox", None, True),
                ("使用统计", "CheckBox", None, False),
                ("错误报告包含个人信息", "CheckBox", None, False)
            ]),

            self.create_setting_group("网络安全", [
                ("验证SSL证书", "CheckBox", None, True),
                ("使用代理", "CheckBox", None, False),
                ("代理地址", "LineEdit", None, ""),
                ("超时时间", "ComboBox", ["30秒", "60秒", "120秒"], "60秒")
            ]),

            self.create_setting_group("数据管理", [
                ("自动备份配置", "CheckBox", None, True),
                ("备份频率", "ComboBox", ["每天", "每周", "每月"], "每周"),
                ("清除历史记录", "Button", None, "立即清除"),
                ("数据导出", "Button", None, "导出数据")
            ])
        ]

        for setting_group in settings_items:
            layout.addWidget(setting_group)

        layout.addStretch()
        return content

    def create_setting_group(self, title, settings):
        """创建设置组"""
        group_container = QFrame()
        group_container.setStyleSheet("""
            QFrame {
                background-color: rgba(255, 255, 255, 0.02);
                border: 1px solid rgba(255, 255, 255, 0.05);
                border-radius: 15px;
                margin: 5px;
            }
        """)

        group_layout = QVBoxLayout(group_container)
        group_layout.setContentsMargins(20, 15, 20, 15)
        group_layout.setSpacing(12)

        # 组标题
        title_label = QLabel(title)
        title_label.setStyleSheet(StyleManager.get_label_style(color='#FFFFFF', font_size=15))
        title_label.setProperty("font-weight", "bold")
        group_layout.addWidget(title_label)

        # 设置项目
        for setting_name, setting_type, options, default_value in settings:
            setting_item = self.create_setting_item(setting_name, setting_type, options, default_value)
            group_layout.addWidget(setting_item)

        return group_container

    def create_setting_item(self, name, item_type, options, default_value):
        """创建单个设置项"""
        item_container = QWidget()
        item_layout = QHBoxLayout(item_container)
        item_layout.setContentsMargins(0, 5, 0, 5)
        item_layout.setSpacing(15)

        # 设置名称
        name_label = QLabel(name)
        name_label.setFixedWidth(120)
        name_label.setStyleSheet(StyleManager.get_label_style(color=StyleManager.COLORS['text_white'], font_size=13))
        item_layout.addWidget(name_label)

        # 根据类型创建控件
        if item_type == "ComboBox":
            widget = QComboBox()
            widget.addItems(options)
            widget.setCurrentText(default_value)
            widget.setStyleSheet("""
                QComboBox {
                    background-color: #1B1E24;
                    color: #FFFFFF;
                    font-size: 12px;
                    border: 1px solid rgba(255, 255, 255, 0.2);
                    border-radius: 8px;
                    padding: 5px 10px;
                    min-width: 120px;
                }
                QComboBox::drop-down {
                    border: none;
                    width: 20px;
                }
                QComboBox::down-arrow {
                    image: none;
                    border: 2px solid #96CEB4;
                    width: 6px;
                    height: 6px;
                    border-top: none;
                    border-right: none;
                    transform: rotate(-45deg);
                }
                QComboBox QAbstractItemView {
                    background-color: #1B1E24;
                    color: #FFFFFF;
                    selection-background-color: #96CEB4;
                    border: 1px solid rgba(255, 255, 255, 0.2);
                }
            """)

        elif item_type == "CheckBox":
            widget = QCheckBox()
            widget.setChecked(default_value)
            widget.setStyleSheet("""
                QCheckBox {
                    color: #FFFFFF;
                    font-size: 12px;
                }
                QCheckBox::indicator {
                    width: 18px;
                    height: 18px;
                    background-color: #1B1E24;
                    border: 1px solid rgba(255, 255, 255, 0.3);
                    border-radius: 4px;
                }
                QCheckBox::indicator:checked {
                    background-color: #96CEB4;
                    border: 1px solid #96CEB4;
                    image: none;
                }
                QCheckBox::indicator:checked:after {
                    content: "✓";
                    color: white;
                    font-weight: bold;
                }
            """)

        elif item_type == "Slider":
            widget = QWidget()
            slider_layout = QHBoxLayout(widget)
            slider_layout.setContentsMargins(0, 0, 0, 0)
            slider_layout.setSpacing(10)

            from PySide6.QtWidgets import QSlider
            slider = QSlider(Qt.Horizontal)
            slider.setMinimum(options[0])
            slider.setMaximum(options[1])
            slider.setValue(default_value)

            # 为语速滑块添加objectName
            if name == "语速":
                slider.setObjectName("voice_speed_slider")
            slider.setStyleSheet("""
                QSlider::groove:horizontal {
                    height: 6px;
                    background-color: #1B1E24;
                    border-radius: 3px;
                }
                QSlider::handle:horizontal {
                    background-color: #96CEB4;
                    border: 2px solid #96CEB4;
                    width: 16px;
                    height: 16px;
                    border-radius: 8px;
                    margin: -5px 0;
                }
                QSlider::sub-page:horizontal {
                    background-color: #96CEB4;
                    border-radius: 3px;
                }
            """)

            value_label = QLabel(str(default_value))
            value_label.setFixedWidth(30)
            value_label.setStyleSheet(StyleManager.get_label_style(color='#96CEB4', font_size=12))
            value_label.setProperty("font-weight", "bold")

            slider.valueChanged.connect(lambda v, label=value_label: label.setText(str(v)))

            slider_layout.addWidget(slider)
            slider_layout.addWidget(value_label)

        elif item_type == "LineEdit":
            widget = QLineEdit(default_value)
            widget.setStyleSheet(StyleManager.get_input_style())

        elif item_type == "PathSelector":
            widget = QWidget()
            path_layout = QHBoxLayout(widget)
            path_layout.setContentsMargins(0, 0, 0, 0)
            path_layout.setSpacing(8)

            path_edit = QLineEdit(default_value)
            path_edit.setStyleSheet(StyleManager.get_input_style())

            browse_btn = QPushButton("浏览")
            browse_btn.setFixedSize(60, 30)
            browse_btn.setStyleSheet(StyleManager.get_button_style(font_size=12, border_radius=15))
            browse_btn.clicked.connect(lambda: self.browse_folder(path_edit))

            path_layout.addWidget(path_edit)
            path_layout.addWidget(browse_btn)

        elif item_type == "Button":
            widget = QPushButton(default_value)
            widget.setFixedSize(80, 30)
            if "重置" in default_value or "清除" in default_value:
                widget.setStyleSheet(StyleManager.get_button_style(bg_color='#FF6B6B', font_size=12, border_radius=15))
            else:
                widget.setStyleSheet(StyleManager.get_button_style(bg_color='#96CEB4', font_size=12, border_radius=15))
            widget.clicked.connect(lambda: self.handle_button_click(name, default_value))

        else:
            widget = QLabel("未知类型")

        item_layout.addWidget(widget)
        item_layout.addStretch()

        return item_container

    def create_bottom_section(self):
        """创建底部操作区域"""
        bottom_container = QWidget()
        bottom_container.setFixedHeight(60)

        bottom_layout = QHBoxLayout(bottom_container)
        bottom_layout.setContentsMargins(0, 10, 0, 10)
        bottom_layout.setSpacing(15)

        # 左侧状态信息
        status_info = QLabel("💾 设置会自动保存，部分选项需要重启生效")
        status_info.setStyleSheet(StyleManager.get_label_style(color=StyleManager.COLORS['text_gray'], font_size=13))
        bottom_layout.addWidget(status_info)

        bottom_layout.addStretch()

        # 右侧操作按钮
        # 重置按钮
        reset_btn = QPushButton("恢复默认")
        reset_btn.setFixedSize(100, 40)
        reset_btn.setStyleSheet(StyleManager.get_button_style(bg_color='#FF6B6B', font_size=14, border_radius=20))
        reset_btn.clicked.connect(self.reset_settings)
        bottom_layout.addWidget(reset_btn)

        # 保存按钮
        save_btn = QPushButton("保存设置")
        save_btn.setFixedSize(100, 40)
        save_btn.setStyleSheet(StyleManager.get_button_style(bg_color='#96CEB4', font_size=14, border_radius=20))
        save_btn.clicked.connect(self.save_settings)
        bottom_layout.addWidget(save_btn)

        return bottom_container

    def switch_category(self, category):
        """切换设置分类"""
        if self.current_category != category:
            self.current_category = category
            print(f"切换到设置分类: {category}")
            # 重新构建整个内容区域
            self.refresh_content()

    def refresh_content(self):
        """刷新内容区域，重新构建导航和设置面板"""
        # 获取主内容区域布局
        content_layout = self.layout().itemAt(0).widget().layout().itemAt(1).widget().layout()

        # 清除现有内容
        while content_layout.count():
            child = content_layout.takeAt(0)
            if child.widget():
                child.widget().deleteLater()

        # 重新创建左侧导航面板
        navigation_panel = self.create_navigation_panel()
        content_layout.addWidget(navigation_panel)

        # 重新创建右侧设置面板
        settings_panel = self.create_settings_panel()
        content_layout.addWidget(settings_panel)

    def browse_folder(self, line_edit):
        """浏览文件夹"""
        folder_path = QFileDialog.getExistingDirectory(self, "选择文件夹")
        if folder_path:
            line_edit.setText(folder_path)

    def handle_button_click(self, setting_name, button_text):
        """处理按钮点击事件"""
        print(f"点击了设置按钮: {setting_name} - {button_text}")
        # 这里可以添加具体的按钮处理逻辑

    def reset_settings(self):
        """重置设置"""
        print("恢复默认设置")
        # 这里可以添加实际的重置逻辑

    def save_settings(self):
        """保存设置"""
        print("保存所有设置")
        # 这里可以添加实际的保存逻辑


class SettingsArea(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()

    def setup_ui(self):
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # API设置
        api_group = QGroupBox("API设置")
        api_group.setStyleSheet("""
            QGroupBox {
                color: white;
                font-size: 14px;
                border: 1px solid #2B9D7C;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px;
            }
        """)
        api_layout = QFormLayout(api_group)
        api_layout.setSpacing(10)

        # OpenAI设置
        self.openai_key = QLineEdit()
        self.openai_key.setPlaceholderText("输入OpenAI API Key")
        self.openai_base_url = QLineEdit()
        self.openai_base_url.setPlaceholderText("https://api.openai.com")

        # Azure TTS设置
        self.azure_key = QLineEdit()
        self.azure_key.setPlaceholderText("输入Azure TTS Key")
        self.azure_region = QLineEdit()
        self.azure_region.setPlaceholderText("eastus")

        # DeepL设置
        self.deepl_key = QLineEdit()
        self.deepl_key.setPlaceholderText("输入DeepL API Key")

        # Google设置
        self.google_key = QLineEdit()
        self.google_key.setPlaceholderText("输入Google API Key")

        api_layout.addRow("OpenAI Key:", self.openai_key)
        api_layout.addRow("OpenAI Base URL:", self.openai_base_url)
        api_layout.addRow("Azure TTS Key:", self.azure_key)
        api_layout.addRow("Azure Region:", self.azure_region)
        api_layout.addRow("DeepL Key:", self.deepl_key)
        api_layout.addRow("Google Key:", self.google_key)

        # TTS设置
        tts_group = QGroupBox("TTS设置")
        tts_group.setStyleSheet("""
            QGroupBox {
                color: white;
                font-size: 14px;
                border: 1px solid #2B9D7C;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px;
            }
        """)
        tts_layout = QFormLayout(tts_group)
        tts_layout.setSpacing(10)

        self.engine_combo = QComboBox()
        self.engine_combo.addItems(["edge_tts", "azure_tts"])

        self.language_combo = QComboBox()
        self.language_combo.addItems(["zh-CN", "en-US", "ja-JP"])

        self.voice_combo = QComboBox()
        self.voice_combo.addItems(["zh-CN-XiaoxiaoNeural", "en-US-JennyNeural"])

        self.speed_spin = QDoubleSpinBox()
        self.speed_spin.setRange(0.5, 2.0)
        self.speed_spin.setValue(1.0)
        self.speed_spin.setSingleStep(0.1)

        self.cache_check = QCheckBox("启用缓存")
        self.cache_check.setChecked(True)

        self.format_combo = QComboBox()
        self.format_combo.addItems(["wav", "mp3"])

        tts_layout.addRow("默认引擎:", self.engine_combo)
        tts_layout.addRow("默认语言:", self.language_combo)
        tts_layout.addRow("默认声音:", self.voice_combo)
        tts_layout.addRow("语速:", self.speed_spin)
        tts_layout.addRow("缓存:", self.cache_check)
        tts_layout.addRow("输出格式:", self.format_combo)

        # UI设置
        ui_group = QGroupBox("UI设置")
        ui_group.setStyleSheet("""
            QGroupBox {
                color: white;
                font-size: 14px;
                border: 1px solid #2B9D7C;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px;
            }
        """)
        ui_layout = QFormLayout(ui_group)
        ui_layout.setSpacing(10)

        self.theme_combo = QComboBox()
        self.theme_combo.addItems(["dark", "light"])

        self.ui_language_combo = QComboBox()
        self.ui_language_combo.addItems(["zh-CN", "en-US"])

        self.auto_save_check = QCheckBox("启用自动保存")
        self.auto_save_check.setChecked(True)

        ui_layout.addRow("主题:", self.theme_combo)
        ui_layout.addRow("语言:", self.ui_language_combo)
        ui_layout.addRow("自动保存:", self.auto_save_check)

        # 应用设置
        app_group = QGroupBox("应用设置")
        app_group.setStyleSheet("""
            QGroupBox {
                color: white;
                font-size: 14px;
                border: 1px solid #2B9D7C;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px;
            }
        """)
        app_layout = QFormLayout(app_group)
        app_layout.setSpacing(10)

        self.output_dir = QLineEdit()
        self.output_dir.setPlaceholderText("选择输出目录")
        self.output_dir_btn = QPushButton("浏览")
        output_layout = QHBoxLayout()
        output_layout.addWidget(self.output_dir)
        output_layout.addWidget(self.output_dir_btn)

        self.temp_dir = QLineEdit()
        self.temp_dir.setPlaceholderText("选择临时目录")
        self.temp_dir_btn = QPushButton("浏览")
        temp_layout = QHBoxLayout()
        temp_layout.addWidget(self.temp_dir)
        temp_layout.addWidget(self.temp_dir_btn)

        self.max_tasks_spin = QSpinBox()
        self.max_tasks_spin.setRange(1, 16)
        self.max_tasks_spin.setValue(4)

        app_layout.addRow("输出目录:", output_layout)
        app_layout.addRow("临时目录:", temp_layout)
        app_layout.addRow("最大并发任务:", self.max_tasks_spin)

        # 添加所有组到主布局
        layout.addWidget(api_group)
        layout.addWidget(tts_group)
        layout.addWidget(ui_group)
        layout.addWidget(app_group)
        layout.addStretch()

        # 设置统一的样式
        self.setStyleSheet("""
            QLineEdit, QComboBox, QSpinBox, QDoubleSpinBox {
                background-color: #1F1F1F;
                color: white;
                border: 1px solid #2B9D7C;
                border-radius: 4px;
                padding: 5px;
                min-height: 25px;
            }

            QPushButton {
                background-color: #2B9D7C;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 5px 10px;
                min-width: 60px;
                min-height: 25px;
            }

            QPushButton:hover {
                background-color: #3CAD8C;
            }

            QCheckBox {
                color: white;
            }

            QLabel {
                color: white;
            }
        """)

        # 连接信号
        self.output_dir_btn.clicked.connect(self.choose_output_dir)
        self.temp_dir_btn.clicked.connect(self.choose_temp_dir)

    def choose_output_dir(self):
        dir_path = QFileDialog.getExistingDirectory(self, "选择输出目录")
        if dir_path:
            self.output_dir.setText(dir_path)

    def choose_temp_dir(self):
        dir_path = QFileDialog.getExistingDirectory(self, "选择临时目录")
        if dir_path:
            self.temp_dir.setText(dir_path)


class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("FlipTalk AI")
        self.setup_ui()

    def setup_ui(self):
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 创建主布局
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)

        # 创建左侧导航栏
        nav_bar = QFrame()
        nav_bar.setFixedWidth(50)
        nav_bar.setStyleSheet("background-color: #1B1E24;")
        nav_layout = QVBoxLayout(nav_bar)
        nav_layout.setContentsMargins(0, 0, 0, 0)
        nav_layout.setSpacing(0)

        # 添加导航按钮
        home_btn = QPushButton("🏠")
        functions_btn = QPushButton("⚙️")
        api_settings_btn = QPushButton("🔧")
        system_settings_btn = QPushButton("🔨")
        logs_btn = QPushButton("📋")

        # 设置按钮样式
        nav_button_style = """
            QPushButton {
                background-color: transparent;
                border: none;
                color: white;
                font-size: 20px;
                padding: 10px;
                text-align: center;
            }
            QPushButton:hover {
                background-color: #2B9D7C;
            }
            QPushButton:checked {
                background-color: #2B9D7C;
            }
        """

        for btn in [home_btn, functions_btn, api_settings_btn, system_settings_btn, logs_btn]:
            btn.setFixedHeight(50)
            btn.setStyleSheet(nav_button_style)
            btn.setCheckable(True)
            nav_layout.addWidget(btn)

        nav_layout.addStretch()

        # 创建右侧内容区域
        content_area = QStackedWidget()
        content_area.setStyleSheet("background-color: #1F1F1F;")

        # 创建各个页面
        home_page = QWidget()
        functions_page = QWidget()
        api_settings_page = SettingsArea()  # 使用新的SettingsArea
        system_settings_page = QWidget()
        logs_page = QWidget()

        # 添加页面到堆叠窗口
        content_area.addWidget(home_page)
        content_area.addWidget(functions_page)
        content_area.addWidget(api_settings_page)
        content_area.addWidget(system_settings_page)
        content_area.addWidget(logs_page)

        # 添加导航栏和内容区域到主布局
        main_layout.addWidget(nav_bar)
        main_layout.addWidget(content_area)
        # 连接导航按钮信号
        home_btn.clicked.connect(lambda: self.switch_page(0, home_btn))
        functions_btn.clicked.connect(lambda: self.switch_page(1, functions_btn))
        api_settings_btn.clicked.connect(lambda: self.switch_page(2, api_settings_btn))
        system_settings_btn.clicked.connect(lambda: self.switch_page(3, system_settings_btn))
        logs_btn.clicked.connect(lambda: self.switch_page(4, logs_btn))

        # 设置初始页面
        home_btn.setChecked(True)
        content_area.setCurrentIndex(0)

        # 设置窗口大小
        self.resize(1200, 800)

    def switch_page(self, index, button):
        # 取消其他按钮的选中状态
        for btn in self.findChildren(QPushButton):
            if btn != button and btn.isCheckable():
                btn.setChecked(False)

        # 切换到选中的页面
        self.centralWidget().findChild(QStackedWidget).setCurrentIndex(index)


class SubtitleTextEditor(QPlainTextEdit):
    """简洁的内联字幕编辑器"""
    text_saved = Signal(str, int)  # 新文本, 行索引
    edit_cancelled = Signal()  # 编辑取消

    def __init__(self, original_text, row_index, parent=None):
        super().__init__(parent)
        self.original_text = original_text
        self.row_index = row_index
        self.setPlainText(original_text)

        # 设置样式
        self.setStyleSheet("""
            QPlainTextEdit {
                background-color: #14161A;
                color: #FFFFFF;
                border: 2px solid #2B9D7C;
                border-radius: 6px;
                padding: 8px;
                font-family: "Microsoft YaHei";
                font-size: 13px;
                selection-background-color: #2B9D7C;
                selection-color: #1B1E24;
            }
        """)

        # 设置最小大小
        self.setMinimumHeight(80)
        self.setMaximumHeight(150)

        # 选中所有文本
        self.selectAll()

    def keyPressEvent(self, event):
        """处理键盘事件"""
        if event.key() == Qt.Key_Return and not (event.modifiers() & Qt.ShiftModifier):
            # Enter键保存（Shift+Enter换行）
            self.save_text()
        elif event.key() == Qt.Key_Escape:
            # Esc键取消
            self.cancel_edit()
        else:
            super().keyPressEvent(event)

    def focusOutEvent(self, event):
        """失去焦点时自动保存"""
        super().focusOutEvent(event)
        self.save_text()

    def save_text(self):
        """保存文本"""
        new_text = self.toPlainText().strip()
        if new_text and new_text != self.original_text:
            self.text_saved.emit(new_text, self.row_index)
        else:
            self.edit_cancelled.emit()

    def cancel_edit(self):
        """取消编辑"""
        self.edit_cancelled.emit()


class SubtitleTextDelegate(QStyledItemDelegate):
    """字幕文本列的自定义委托"""

    def __init__(self, parent_window):
        super().__init__()
        self.parent_window = parent_window

    def createEditor(self, parent, option, index):
        """创建简洁的内联编辑器"""
        if index.column() == 3:  # 字幕内容列
            original_text = index.data(Qt.UserRole + 1) or index.data(Qt.DisplayRole)
            row = index.row()

            editor = SubtitleTextEditor(original_text, row, parent)
            editor.text_saved.connect(self.parent_window.update_subtitle_text)
            # 获取表格项
            item = self.parent_window.subtitles_table.item(row, 3)
            editor.edit_cancelled.connect(lambda: self.parent_window.subtitles_table.closePersistentEditor(item))

            print(f"📝 为字幕 {row + 1} 创建编辑器，原始文本: {original_text[:30]}...")

            return editor
        return super().createEditor(parent, option, index)

    def setEditorData(self, editor, index):
        """设置编辑器数据"""
        if isinstance(editor, SubtitleTextEditor):
            # 数据已在构造函数中设置
            pass
        else:
            super().setEditorData(editor, index)

    def setModelData(self, editor, model, index):
        """设置模型数据 - 由text_saved信号处理，这里不需要做任何事"""
        if isinstance(editor, SubtitleTextEditor):
            # 数据保存由信号处理
            pass
        else:
            super().setModelData(editor, model, index)


def main():
    """
    主函数：创建并运行FlipTalk AI应用
    """
    app = QApplication(sys.argv)

    # 设置应用程序属性
    app.setApplicationName("FlipTalk AI")
    app.setApplicationVersion("1.0")

    # 创建主窗口
    window = FlipTalkMainWindow()
    window.show()

    # 运行应用程序
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
