#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FlipTalk AI - 视频翻译软件主界面
功能：提供视频上传、处理监控、字幕编辑等完整UI界面
"""

import sys
# 添加动画相关导入
import random
from PySide6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QPushButton, QScrollArea, QProgressBar, QLineEdit,
    QCheckBox, QFrame, QFileDialog, QGridLayout, QTextEdit, QComboBox,
    QDialog, QMessageBox, QGroupBox, QFormLayout, QDoubleSpinBox, QSpinBox,
    QTableWidget, QTableWidgetItem, QHeaderView, QListWidget, QSlider,
    QStackedWidget
)
from PySide6.QtCore import Qt, QMimeData, QThread, Signal, QPropertyAnimation, QEasingCurve, QRect, QTimer, QUrl, QPoint, Slot
from PySide6.QtGui import QFont, QDragEnterEvent, QDropEvent, QPainter, QBrush, QPen, QPixmap, QColor, QImage, QIcon
from PySide6.QtMultimedia import QMediaPlayer, QAudioOutput
from PySide6.QtMultimediaWidgets import QVideoWidget
import time
import os

# 导入WhisperX字幕提取插件（用于模型检查）
try:
    from plugins.subtitle_extractor.plugin import WhisperXSubtitleExtractor
    WHISPERX_AVAILABLE = True
except ImportError as e:
    print(f"警告：无法导入WhisperX插件 - {e}")
    WHISPERX_AVAILABLE = False
    # 创建占位符类
    class WhisperXSubtitleExtractor:
        def __init__(self): pass
        def initialize(self, config): return False

# 导入OpenCV
try:
    import cv2
    import numpy as np
    OPENCV_AVAILABLE = True
    print("OpenCV已加载，版本:", cv2.__version__)
except ImportError:
    print("警告: OpenCV 模块不可用，将使用默认视频预览")
    OPENCV_AVAILABLE = False

# 导入多媒体模块
try:
    from PySide6.QtMultimedia import QMediaPlayer, QAudioOutput
    from PySide6.QtMultimediaWidgets import QVideoWidget
    MULTIMEDIA_AVAILABLE = True
except ImportError:
    print("警告: QtMultimedia 模块不可用，视频预览功能将被禁用")
    MULTIMEDIA_AVAILABLE = False

# 导入参数设置窗口
try:
    from .config_window import ConfigWindow
except ImportError:
    try:
        from config_window import ConfigWindow
    except ImportError:
        print("警告：无法导入ConfigWindow，某些功能可能不可用")
        class ConfigWindow:
            def __init__(self, *args, **kwargs): pass

# 导入核心服务
try:
    from core.services import core_service
    CORE_SERVICE_AVAILABLE = True
except ImportError as e:
    print(f"警告：无法导入核心服务 - {e}")
    CORE_SERVICE_AVAILABLE = False


class StyleManager:
    """
    统一的样式管理类
    避免重复定义相同的样式
    """
    # 颜色常量
    COLORS = {
        'primary': '#2B9D7C',
        'primary_hover': '#00CC55',
        'background_dark': '#1B1E24',
        'background_light': '#14161A',
        'text_white': '#FFFFFF',
        'text_gray': 'rgba(255, 255, 255, 0.7)',
        'text_light_gray': 'rgba(255, 255, 255, 0.4)',
        'border': '#444444',
        'transparent': 'transparent',
        'marked': '#FF0000'
    }
    
    @staticmethod
    def get_label_style(color='#FFFFFF', font_size=14, bg_color='transparent'):
        """获取标签通用样式"""
        return f"""
            QLabel {{
                color: {color};
                font-size: {font_size}px;
                background-color: {bg_color};
                border: none;
            }}
        """
    
    @staticmethod
    def get_button_style(bg_color='#2B9D7C', text_color='#1B1E24', font_size=20, border_radius=19):
        """获取按钮通用样式"""
        return f"""
            QPushButton {{
                background-color: {bg_color};
                color: {text_color};
                font-size: {font_size}px;
                border: none;
                border-radius: {border_radius}px;
                outline: none;
            }}
            QPushButton:hover {{
                background-color: #00CC55;
                outline: none;
            }}
            QPushButton:focus {{
                outline: none;
                border: 1px solid rgba(43, 157, 124, 0.5);
            }}
        """
    
    @staticmethod
    def get_input_style(bg_color='#14161A', border_radius=17):
        """获取输入框通用样式"""
        return f"""
            QLineEdit {{
                background-color: {bg_color};
                color: #FFFFFF;
                font-size: 14px;
                border: 1px solid #444444;
                border-radius: {border_radius}px;
                padding: 5px;
                text-align: center;
            }}
        """
    
    @staticmethod
    def get_frame_style(bg_color='#1B1E24', border_radius=30):
        """获取Frame通用样式"""
        return f"""
            QFrame {{
                background-color: {bg_color};
                border-radius: {border_radius}px;
                border: none;
            }}
        """
    
    @staticmethod
    def hex_to_rgb(hex_color):
        """将十六进制颜色转换为RGB格式"""
        hex_color = hex_color.lstrip('#')
        return ', '.join(str(int(hex_color[i:i+2], 16)) for i in (0, 2, 4))


class RoundedWidget(QWidget):
    """
    圆角矩形组件基类
    提供统一的圆角矩形样式
    """
    def __init__(self, bg_color="#1B1E24", border_radius=30):
        super().__init__()
        self.bg_color = bg_color  # 背景颜色
        self.border_radius = border_radius  # 圆角半径
        self.setup_style()
    
    def setup_style(self):
        """设置组件样式"""
        self.setStyleSheet(f"""
            QWidget {{
                background-color: {self.bg_color};
                border-radius: {self.border_radius}px;
            }}
        """)


class NavigationBar(QWidget):
    """
    左侧导航栏组件
    使用QFrame作为背景容器，包含logo和功能按钮
    """
    # 添加信号用于通知主窗口切换界面
    page_changed = Signal(str)
    
    def __init__(self):
        super().__init__()
        self.setFixedSize(50, 776)  # 设置固定尺寸：50*776
        self.current_page = "首页"  # 当前选中页面
        self.last_click_time = 0  # 上次点击时间，用于防抖
        self.setup_ui()
    
    def setup_ui(self):
        """初始化导航栏UI - 极简图标版"""
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # 创建背景Frame - 极简设计
        background_frame = QFrame()
        background_frame.setFixedSize(50, 776)
        background_frame.setStyleSheet("""
            QFrame {
                background: #14161A;
                border-radius: 15px;
                border: 1px solid rgba(43, 157, 124, 0.1);
            }
        """)
        
        # Frame内部布局
        frame_layout = QVBoxLayout(background_frame)
        frame_layout.setContentsMargins(5, 20, 5, 20)
        frame_layout.setSpacing(8)
        
        # 顶部简化Logo/图标
        logo_btn = self.create_icon_button("≡", "菜单", is_logo=True)
        frame_layout.addWidget(logo_btn)
        
        # 添加间距
        frame_layout.addSpacing(15)
        
        # 导航图标按钮列表
        nav_items = [
            ("🏠", "首页"),
            ("⚙️", "功能"), 
            ("🔧", "API设置"),
            ("🔨", "系统设置"),
            ("📋", "运行日志")
        ]
        
        for icon, nav_text in nav_items:
            btn = self.create_icon_button(icon, nav_text)
            frame_layout.addWidget(btn)
            frame_layout.addSpacing(5)
        
        # 添加弹性空间
        frame_layout.addStretch()
        
        # 将背景Frame添加到主布局
        main_layout.addWidget(background_frame)
    
    def create_icon_button(self, icon, nav_text, is_logo=False):
        """创建图标按钮"""
        btn = QPushButton(icon)
        btn.setFixedSize(40, 40)
        
        # 保存导航文本用于事件处理
        btn.nav_text = nav_text
        
        # 添加tooltip
        btn.setToolTip(nav_text)
        
        if is_logo:
            # Logo样式
            btn.setStyleSheet("""
                QPushButton {
                    background: rgba(43, 157, 124, 0.15);
                color: #2B9D7C;
                font-size: 18px;
                font-weight: bold;
                    border: 1px solid rgba(43, 157, 124, 0.3);
                    border-radius: 20px;
                    outline: none;
                }
                QPushButton:hover {
                    background-color: #00CC55;
                }
                QToolTip {
                    background-color: #1B1E24;
                    color: white;
                    border: 1px solid #2B9D7C;
                    border-radius: 4px;
                    padding: 5px;
            }
        """)
        else:
            # 根据是否选中设置不同样式
            if nav_text == self.current_page:
                btn.setStyleSheet(self.get_icon_button_style(selected=True))
            else:
                btn.setStyleSheet(self.get_icon_button_style(selected=False))
        
        # 绑定点击事件
        btn.clicked.connect(lambda checked, t=nav_text: self.on_nav_clicked(t))
        
        return btn
    
    def get_icon_button_style(self, selected=False):
        """获取图标按钮样式"""
        if selected:
            return """
                QPushButton {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 #2B9D7C, stop:1 #25876A);
                    color: #FFFFFF;
                    font-size: 16px;
                    font-weight: bold;
                    border: none;
                    border-radius: 20px;
                    outline: none;
                }
                QPushButton:hover {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 #32B189, stop:1 #2B9D7C);
                    outline: none;
                }
                QToolTip {
                    background-color: #1B1E24;
                    color: white;
                    border: 1px solid #2B9D7C;
                    border-radius: 4px;
                    padding: 5px;
                }
            """
        else:
            return """
                QPushButton {
                    background: #1B1E24;
                    color: rgba(255, 255, 255, 0.7);
                    font-size: 16px;
                    font-weight: normal;
                    border: none;
                    border-radius: 20px;
                    outline: none;
                }
                QPushButton:hover {
                    background: rgba(43, 157, 124, 0.2);
                    color: rgba(255, 255, 255, 0.9);
                    border: none;
                    outline: none;
                }
                QToolTip {
                    background-color: #1B1E24;
                    color: white;
                    border: 1px solid #2B9D7C;
                    border-radius: 4px;
                    padding: 5px;
            }
            """
    
    def on_nav_clicked(self, text):
        """导航按钮点击事件处理"""
        # 防抖：如果距离上次点击时间小于300毫秒，则忽略
        current_time = time.time() * 1000  # 转换为毫秒
        if current_time - self.last_click_time < 300:
            return
        
        self.last_click_time = current_time
        
        # 如果点击的是当前页面，则不处理
        if self.current_page == text:
            return
        
        self.current_page = text
        # 重新设置所有按钮样式
        for i in range(self.layout().itemAt(0).widget().layout().count()):
            item = self.layout().itemAt(0).widget().layout().itemAt(i)
            if item and item.widget() and isinstance(item.widget(), QPushButton):
                button = item.widget()
                # 检查按钮是否有nav_text属性
                if hasattr(button, 'nav_text'):
                    button_text = button.nav_text
                    if button_text == text:
                        button.setStyleSheet(self.get_icon_button_style(selected=True))
                    else:
                        button.setStyleSheet(self.get_icon_button_style(selected=False))
        
        # 发射信号通知主窗口切换界面
        self.page_changed.emit(text)


class ProcessListItem(RoundedWidget):
    """
    处理列表中的单个视频项 - 简洁设计
    显示文件名、处理状态和进度
    """
    def __init__(self, filename, status, progress):
        super().__init__(bg_color="transparent", border_radius=0)
        self.setFixedSize(470, 50)  # 减小尺寸
        self.filename = filename
        self.status = status
        self.progress = progress
        self.setup_ui()
    
    def setup_ui(self):
        """初始化处理项UI - 简洁设计"""
        # 主布局
        main_layout = QHBoxLayout(self)
        main_layout.setContentsMargins(8, 8, 8, 8)
        main_layout.setSpacing(10)
        
        # 简洁的状态指示器
        status_dot = QLabel("●")
        if self.progress == 100:
            status_color = "#2B9D7C"  # 绿色 - 完成
        elif self.progress == 0:
            status_color = "#888888"  # 灰色 - 等待
        else:
            status_color = "#FFA726"  # 橙色 - 处理中
        
        status_dot.setStyleSheet(f"""
            QLabel {{
                color: {status_color};
                font-size: 12px;
                background-color: transparent;
                border: none;
            }}
        """)
        status_dot.setFixedWidth(12)
        main_layout.addWidget(status_dot)
        
        # 文件名
        filename_label = QLabel(self.filename)
        filename_label.setStyleSheet("""
            QLabel {
                color: #FFFFFF;
                font-size: 13px;
                font-weight: normal;
                background-color: transparent;
                border: none;
            }
        """)
        main_layout.addWidget(filename_label)
        
        # 弹性空间
        main_layout.addStretch()
        
        # 状态文字
        status_label = QLabel(self.status)
        status_label.setStyleSheet(f"""
            QLabel {{
                color: {status_color};
                font-size: 11px;
                background-color: transparent;
                border: none;
            }}
        """)
        status_label.setFixedWidth(60)
        main_layout.addWidget(status_label)
        
        # 简洁的进度条
        progress_container = QFrame()
        progress_container.setFixedSize(80, 4)
        progress_container.setStyleSheet("""
            QFrame {
                background-color: rgba(255, 255, 255, 0.2);
                border-radius: 2px;
            }
        """)
        
        # 进度填充
        progress_fill = QFrame(progress_container)
        fill_width = int((self.progress / 100) * 80)
        progress_fill.setFixedSize(fill_width, 4)
        progress_fill.move(0, 0)
        progress_fill.setStyleSheet(f"""
            QFrame {{
                background-color: {status_color};
                border-radius: 2px;
            }}
        """)
        
        progress_widget = QWidget()
        progress_layout = QVBoxLayout(progress_widget)
        progress_layout.setContentsMargins(0, 0, 0, 0)
        progress_layout.setAlignment(Qt.AlignCenter)
        progress_layout.addWidget(progress_container)
        progress_widget.setFixedWidth(80)
        main_layout.addWidget(progress_widget)
        
        # 简洁的进度百分比
        progress_text = QLabel(f"{self.progress}%")
        progress_text.setStyleSheet("""
            QLabel {
                color: rgba(255, 255, 255, 0.7);
                font-size: 10px;
                background-color: transparent;
                border: none;
            }
        """)
        progress_text.setFixedWidth(30)
        progress_text.setAlignment(Qt.AlignRight)
        main_layout.addWidget(progress_text)


class SubtitleEditItem(QWidget):
    """
    字幕编辑列表中的单个字幕项
    包含时间戳、操作按钮和字幕文本编辑，使用QFrame作为背景容器
    """
    def __init__(self, start_time, end_time, subtitle_text, is_marked=False):
        super().__init__()
        self.setFixedSize(581, 92)
        self.start_time = start_time  # 开始时间
        self.end_time = end_time  # 结束时间
        self.subtitle_text = subtitle_text  # 字幕文本
        self.is_marked = is_marked  # 是否被标记
        self.setup_ui()
    
    def setup_ui(self):
        """初始化字幕编辑项UI"""
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # 创建背景Frame - 根据是否被标记设置不同背景色
        bg_color = "#FF0000" if self.is_marked else "#1B1E24"
        background_frame = QFrame()
        background_frame.setFixedSize(581, 92)
        background_frame.setStyleSheet(f"""
            QFrame {{
                background-color: {bg_color};
                border-radius: 15px;
                border: none;
            }}
        """)
        
        # Frame内部布局
        frame_layout = QHBoxLayout(background_frame)
        frame_layout.setContentsMargins(10, 8, 10, 8)
        frame_layout.setSpacing(15)
        
        # 时间戳编辑区域
        time_layout = QVBoxLayout()
        time_layout.setSpacing(8)
        
        # 开始时间输入框
        self.start_time_edit = QLineEdit(self.start_time)
        self.start_time_edit.setFixedSize(134, 34)
        self.start_time_edit.setStyleSheet(StyleManager.get_input_style())
        time_layout.addWidget(self.start_time_edit)
        
        # 结束时间输入框
        self.end_time_edit = QLineEdit(self.end_time)
        self.end_time_edit.setFixedSize(134, 34)
        self.end_time_edit.setStyleSheet(StyleManager.get_input_style())
        time_layout.addWidget(self.end_time_edit)
        
        frame_layout.addLayout(time_layout)
        
        # 操作按钮
        action_btn = QPushButton("重生成音频")
        action_btn.setFixedSize(96, 34)
        action_btn.setStyleSheet(StyleManager.get_button_style(font_size=15, border_radius=17))
        frame_layout.addWidget(action_btn)
        
        # 字幕文本编辑框
        self.subtitle_edit = QTextEdit(self.subtitle_text)
        self.subtitle_edit.setFixedSize(300, 76)
        self.subtitle_edit.setStyleSheet("""
            QTextEdit {
                background-color: #14161A;
                color: #FFFFFF;
                border: 1px solid #444444;
                border-radius: 15px;
                padding: 8px;
            }
        """)
        frame_layout.addWidget(self.subtitle_edit)
        
        # 将背景Frame添加到主布局
        main_layout.addWidget(background_frame)


class VideoUploadArea(RoundedWidget):
    """
    视频文件上传区域
    支持拖拽和点击上传视频文件，上传成功后显示视频预览
    """

    # 定义信号
    video_uploaded = Signal(str)  # 视频上传成功信号，传递文件路径
    video_cleared = Signal()  # 视频清除信号

    def __init__(self):
        super().__init__(bg_color="#1B1E24", border_radius=30)
        self.uploaded_video_path = None
        self.is_preview_mode = False
        self.setAcceptDrops(True)
        self.setup_ui()

    def setup_ui(self):
        """初始化视频上传区域UI"""
        # 主布局
        self.main_layout = QVBoxLayout(self)
        self.main_layout.setContentsMargins(10, 10, 10, 10)  # 减少边距
        self.main_layout.setSpacing(8)  # 减少间距

        # 创建上传提示区域（初始状态）
        self.upload_prompt_area = self.create_upload_prompt()
        self.main_layout.addWidget(self.upload_prompt_area)

        # 创建视频预览区域（上传后显示）
        self.video_preview_area = self.create_video_preview()
        self.video_preview_area.hide()  # 初始隐藏
        self.main_layout.addWidget(self.video_preview_area)


    def create_upload_prompt(self):
        """创建上传提示区域"""
        prompt_widget = QWidget()
        prompt_widget.setStyleSheet("""
            QWidget {
                background-color:#1B1E24;
                border: 2px dashed #2B9D7C;
                border-radius: 20px;
            }
            QWidget:hover {
                border-color: #00CC55;
                background-color: rgba(43, 157, 124, 0.05);
            }
        """)

        layout = QVBoxLayout(prompt_widget)
        layout.setContentsMargins(20, 25, 20, 25)
        layout.setSpacing(12)
        layout.setAlignment(Qt.AlignCenter)

        # 视频图标
        icon_label = QLabel("🎬")
        icon_label.setAlignment(Qt.AlignCenter)
        icon_label.setStyleSheet("""
            QLabel {
                font-size: 32px;
                color: #2B9D7C;
                background: transparent;
                border: none;
            }
        """)
        layout.addWidget(icon_label)

        # 主要提示文字
        main_text = QLabel("拖拽视频文件到此处")
        main_text.setAlignment(Qt.AlignCenter)
        main_text.setStyleSheet("""
            QLabel {
                color: #FFFFFF;
                font-size: 16px;
                font-weight: bold;
                background: transparent;
                border: none;
            }
        """)
        layout.addWidget(main_text)

        # 副提示文字
        sub_text = QLabel("或点击选择文件")
        sub_text.setAlignment(Qt.AlignCenter)
        sub_text.setStyleSheet("""
            QLabel {
                color: #AAAAAA;
                font-size: 12px;
                background: transparent;
                border: none;
            }
        """)
        layout.addWidget(sub_text)

        # 支持格式提示
        format_text = QLabel("支持 MP4, AVI, MOV, MKV 格式")
        format_text.setAlignment(Qt.AlignCenter)
        format_text.setStyleSheet("""
            QLabel {
                color: #666666;
                font-size: 10px;
                background: transparent;
                border: none;
            }
        """)
        layout.addWidget(format_text)

        # 点击事件
        prompt_widget.mousePressEvent = self.on_click_upload

        return prompt_widget

    def create_video_preview(self):
        """创建视频预览区域"""
        preview_widget = QWidget()
        preview_widget.setStyleSheet("""
            QWidget {
                background-color: #14161A;
                border: 2px solid #2B9D7C;
                border-radius: 20px;
            }
        """)

        layout = QVBoxLayout(preview_widget)
        layout.setContentsMargins(10, 10, 10, 10)  # 适当边距，为居中留出空间
        layout.setSpacing(0)  # 无间距

        # 创建视频预览容器，支持叠加播放按钮
        self.video_container = QWidget()

        # 重新计算，让视频预览区域在容器中居中显示
        # 容器总尺寸：450px宽 × 260px高
        # 边距：10px × 4 = 40px
        # 可用空间：430px宽 × 240px高

        container_width = 410
        container_height = 240
        margin = 10

        available_width = container_width - (margin * 2)  # 410 - 20 = 390px
        available_height = container_height - (margin * 2)  # 240 - 20 = 220px

        # 根据9:16比例计算最佳尺寸
        width_based_height = int(available_width * 9 / 16)  # 390 * 9/16 = 219px
        height_based_width = int(available_height * 16 / 9)  # 220 * 16/9 = 391px

        # 选择能完全适应的尺寸
        if width_based_height <= available_height:
            # 宽度优先：使用全部宽度
            preview_width = available_width  # 390px
            preview_height = width_based_height  # 219px
        else:
            # 高度优先：使用全部高度
            preview_width = height_based_width  # 391px
            preview_height = available_height  # 220px

        # 219px < 220px，所以使用宽度优先方案
        preview_width = available_width  # 390px
        preview_height = width_based_height  # 219px

        # 设置容器尺寸
        self.video_container.setFixedSize(preview_width, preview_height)

        # 创建视频预览标签
        self.thumbnail_label = QLabel(self.video_container)
        self.thumbnail_label.setFixedSize(preview_width, preview_height)
        self.thumbnail_label.setAlignment(Qt.AlignCenter)
        self.thumbnail_label.setStyleSheet("""
            QLabel {
                background-color: #1B1E24;
                border: 1px solid #333333;
                border-radius: 15px;
                color: #AAAAAA;
                font-size: 12px;
            }
        """)
        self.thumbnail_label.setText("视频预览加载中...")

        # 创建视频播放组件（初始隐藏）
        self.video_widget = QVideoWidget(self.video_container)
        self.video_widget.setFixedSize(preview_width, preview_height)
        self.video_widget.setStyleSheet("""
            QVideoWidget {
                background-color: #000000;
                border: 1px solid #333333;
                border-radius: 15px;
            }
        """)
        self.video_widget.hide()  # 初始隐藏

        # 创建媒体播放器
        self.media_player = QMediaPlayer()
        self.audio_output = QAudioOutput()
        self.media_player.setAudioOutput(self.audio_output)
        self.media_player.setVideoOutput(self.video_widget)

        # 创建叠加的播放按钮
        self.overlay_play_button = QPushButton("▶", self.video_container)
        self.overlay_play_button.setFixedSize(60, 60)

        # 计算播放按钮的居中位置
        button_x = (preview_width - 60) // 2
        button_y = (preview_height - 60) // 2
        self.overlay_play_button.move(button_x, button_y)

        self.overlay_play_button.setStyleSheet("""
            QPushButton {
                background: rgba(0, 0, 0, 120);
                color: #FFFFFF;
                font-size: 24px;
                font-weight: bold;
                border: 2px solid rgba(255, 255, 255, 150);
                border-radius: 30px;
            }
            QPushButton:hover {
                background: rgba(0, 0, 0, 160);
                border: 2px solid rgba(255, 255, 255, 200);
                transform: scale(1.1);
            }
            QPushButton:pressed {
                background: rgba(0, 0, 0, 200);
                transform: scale(0.95);
            }
        """)

        # 连接播放按钮点击事件
        self.overlay_play_button.clicked.connect(self.play_preview_video)

        # 连接播放状态变化信号
        self.media_player.playbackStateChanged.connect(self.on_playback_state_changed)

        # 添加双击停止功能
        self.video_widget.mouseDoubleClickEvent = self.stop_video_playback

        # 初始隐藏播放按钮（只在有视频时显示）
        self.overlay_play_button.hide()

        layout.addWidget(self.video_container, 0, Qt.AlignCenter)  # 居中对齐


        return preview_widget

    def play_preview_video(self):
        """在预览区域直接播放视频"""
        if self.uploaded_video_path:
            try:
                import os
                # 停止当前播放并完全清除媒体播放器状态
                self.media_player.stop()
                self.media_player.setSource(QUrl())  # 清除媒体源

                # 立即隐藏缩略图，防止显示旧内容
                if hasattr(self, 'thumbnail_label'):
                    self.thumbnail_label.hide()

                # 强制刷新视频输出，清除之前的帧
                if hasattr(self, 'video_widget'):
                    self.video_widget.update()
                    self.video_widget.repaint()
                    # 显示视频播放器
                    self.video_widget.show()

                # 短暂延迟确保清除完成
                from PySide6.QtCore import QTimer
                QTimer.singleShot(100, lambda: self._load_and_play_video(self.uploaded_video_path))

                print(f"准备播放视频: {os.path.basename(self.uploaded_video_path)}")
            except Exception as e:
                print(f"播放视频失败: {e}")
        else:
            print("没有可播放的视频文件")

    def _load_and_play_video(self, video_path):
        """延迟加载并播放视频"""
        try:
            import os

            # 再次确保视频输出清除
            if hasattr(self, 'video_widget'):
                self.video_widget.update()
                self.video_widget.repaint()

            # 切换到视频播放模式
            self.switch_to_video_mode()

            # 设置新的视频文件
            video_url = QUrl.fromLocalFile(os.path.abspath(video_path))
            self.media_player.setSource(video_url)

            # 开始播放
            self.media_player.play()

            print(f"正在播放视频: {os.path.basename(video_path)}")
        except Exception as e:
            print(f"延迟播放视频失败: {e}")

    def switch_to_video_mode(self):
        """切换到视频播放模式"""
        # 强制刷新视频输出，清除之前的帧
        if hasattr(self, 'video_widget'):
            self.video_widget.update()
            self.video_widget.repaint()

        # 隐藏缩略图，显示视频播放器
        self.thumbnail_label.hide()
        self.video_widget.show()

        # 更新播放按钮样式和功能
        self.overlay_play_button.setText("⏸")
        self.overlay_play_button.clicked.disconnect()
        self.overlay_play_button.clicked.connect(self.toggle_play_pause)

    def switch_to_thumbnail_mode(self):
        """切换回缩略图预览模式"""
        # 停止播放并清除媒体源
        if hasattr(self, 'media_player'):
            self.media_player.stop()
            # 不在这里清除媒体源，因为可能还需要播放同一个视频

        # 强制刷新视频输出，清除之前的帧
        if hasattr(self, 'video_widget'):
            self.video_widget.update()
            self.video_widget.repaint()

        # 显示缩略图，隐藏视频播放器
        self.video_widget.hide()
        self.thumbnail_label.show()

        # 恢复播放按钮
        self.overlay_play_button.setText("▶")
        self.overlay_play_button.clicked.disconnect()
        self.overlay_play_button.clicked.connect(self.play_preview_video)

    def stop_video_playback(self, event):
        """停止视频播放（双击触发）"""
        if hasattr(self, 'media_player'):
            self.media_player.stop()

    def toggle_play_pause(self):
        """切换播放/暂停状态"""
        if hasattr(self, 'media_player'):
            if self.media_player.playbackState() == QMediaPlayer.PlaybackState.PlayingState:
                self.media_player.pause()
            else:
                self.media_player.play()



    def on_playback_state_changed(self, state):
        """播放状态变化时更新按钮文本"""
        if hasattr(self, 'overlay_play_button'):
            if state == QMediaPlayer.PlaybackState.PlayingState:
                self.overlay_play_button.setText("⏸")
            elif state == QMediaPlayer.PlaybackState.PausedState:
                self.overlay_play_button.setText("▶")
            elif state == QMediaPlayer.PlaybackState.StoppedState:
                # 视频停止时切换回缩略图模式
                self.switch_to_thumbnail_mode()

    def on_click_upload(self, event):
        """点击上传事件"""
        self.select_video_file()

    def select_video_file(self):
        """选择视频文件"""
        file_dialog = QFileDialog()
        file_path, _ = file_dialog.getOpenFileName(
            self,
            "选择视频文件",
            "",
            "视频文件 (*.mp4 *.avi *.mov *.mkv *.flv *.wmv *.webm);;所有文件 (*)"
        )

        if file_path:
            self.load_video(file_path)

    def load_video(self, file_path):
        """加载视频文件"""
        try:
            import os
            if not os.path.exists(file_path):
                print(f"视频文件不存在: {file_path}")
                return

            self.uploaded_video_path = file_path
            file_name = os.path.basename(file_path)
            file_size = os.path.getsize(file_path)



            # 更新视频信息
            self.update_video_info(file_path, file_size)

            # 切换到预览模式
            self.switch_to_preview_mode()

            # 发送信号
            self.video_uploaded.emit(file_path)

            print(f"视频文件已加载: {file_name}")

        except Exception as e:
            print(f"加载视频文件失败: {e}")

    def update_video_info(self, file_path, file_size):
        """更新视频信息显示"""
        try:
            # 格式化文件大小
            if file_size < 1024 * 1024:
                size_str = f"{file_size / 1024:.1f} KB"
            elif file_size < 1024 * 1024 * 1024:
                size_str = f"{file_size / (1024 * 1024):.1f} MB"
            else:
                size_str = f"{file_size / (1024 * 1024 * 1024):.1f} GB"

            # 尝试获取视频信息（需要OpenCV或其他库）
            try:
                import cv2
                cap = cv2.VideoCapture(file_path)
                if cap.isOpened():
                    fps = cap.get(cv2.CAP_PROP_FPS)
                    frame_count = cap.get(cv2.CAP_PROP_FRAME_COUNT)
                    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
                    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))

                    if fps > 0:
                        duration = frame_count / fps
                        duration_str = f"{int(duration // 60):02d}:{int(duration % 60):02d}"
                    else:
                        duration_str = "未知"

                    resolution_str = f"{width}x{height}"
                    cap.release()
                else:
                    duration_str = "未知"
                    resolution_str = "未知"
            except ImportError:
                duration_str = "未知"
                resolution_str = "未知"
            except Exception:
                duration_str = "未知"
                resolution_str = "未知"



            # 更新缩略图
            self.update_thumbnail(file_path)

        except Exception as e:
            print(f"更新视频信息失败: {e}")

    def update_thumbnail(self, file_path):
        """更新视频缩略图"""
        try:
            import cv2

            # 立即清除之前的缩略图，防止显示旧内容
            if hasattr(self, 'thumbnail_label'):
                self.thumbnail_label.clear()
                self.thumbnail_label.setText("正在生成预览...")
                self.thumbnail_label.update()
                self.thumbnail_label.repaint()

            # 确保切换到缩略图模式
            self.switch_to_thumbnail_mode()

            cap = cv2.VideoCapture(file_path)
            if cap.isOpened():
                # 读取第一帧作为缩略图
                ret, frame = cap.read()
                if ret:
                    # 转换为Qt格式并显示
                    height, width, channel = frame.shape
                    bytes_per_line = 3 * width
                    q_image = QImage(frame.data, width, height, bytes_per_line, QImage.Format_RGB888).rgbSwapped()

                    # 缩放到合适大小，优化9:16比例视频的显示
                    pixmap = QPixmap.fromImage(q_image)
                    label_size = self.thumbnail_label.size()

                    # 为9:16比例视频优化缩放
                    scaled_pixmap = pixmap.scaled(
                        label_size.width() - 20,  # 留出边距
                        label_size.height() - 20,  # 留出边距
                        Qt.KeepAspectRatio,
                        Qt.SmoothTransformation
                    )

                    # 清除之前的内容并设置新的缩略图
                    self.thumbnail_label.clear()
                    self.thumbnail_label.setPixmap(scaled_pixmap)

                    print(f"缩略图已更新: {file_path}")
                else:
                    self.thumbnail_label.setText("无法生成预览")
                cap.release()
            else:
                self.thumbnail_label.setText("无法打开视频")
        except ImportError:
            self.thumbnail_label.setText("视频预览\n(需要OpenCV)")
        except Exception as e:
            print(f"生成缩略图失败: {e}")
            self.thumbnail_label.setText("预览生成失败")

    def switch_to_preview_mode(self):
        """切换到预览模式"""
        self.upload_prompt_area.hide()
        self.video_preview_area.show()
        self.overlay_play_button.show()  # 显示叠加的播放按钮
        self.is_preview_mode = True

    def switch_to_upload_mode(self):
        """切换到上传模式"""
        self.video_preview_area.hide()
        self.upload_prompt_area.show()
        self.overlay_play_button.hide()  # 隐藏叠加的播放按钮
        self.is_preview_mode = False
        self.uploaded_video_path = None

        # 重置媒体播放器状态
        if hasattr(self, 'media_player'):
            self.media_player.stop()
            self.media_player.setSource(QUrl())  # 清除媒体源

            # 强制刷新视频输出，清除之前的帧
            if hasattr(self, 'video_widget'):
                self.video_widget.update()
                # 强制重绘，确保清除所有视频帧
                self.video_widget.repaint()

        # 立即清除缩略图显示，防止显示上一个视频的内容
        if hasattr(self, 'thumbnail_label'):
            self.thumbnail_label.clear()
            self.thumbnail_label.setText("视频预览")
            # 强制立即重绘缩略图标签
            self.thumbnail_label.update()
            self.thumbnail_label.repaint()

        # 确保切换回缩略图模式
        self.switch_to_thumbnail_mode()

        # 发送视频清除信号
        self.video_cleared.emit()





    def dragEnterEvent(self, event):
        """拖拽进入事件"""
        if event.mimeData().hasUrls():
            urls = event.mimeData().urls()
            if urls and urls[0].isLocalFile():
                file_path = urls[0].toLocalFile()
                # 检查是否为视频文件
                video_extensions = ['.mp4', '.avi', '.mov', '.mkv', '.flv', '.wmv', '.webm']
                if any(file_path.lower().endswith(ext) for ext in video_extensions):
                    event.acceptProposedAction()
                    # 添加视觉反馈
                    if not self.is_preview_mode:
                        self.upload_prompt_area.setStyleSheet("""
                            QWidget {
                                background-color: rgba(43, 157, 124, 0.1);
                                border: 2px dashed #00CC55;
                                border-radius: 20px;
                            }
                        """)
                else:
                    event.ignore()
            else:
                event.ignore()
        else:
            event.ignore()

    def dragLeaveEvent(self, event):
        """拖拽离开事件"""
        if not self.is_preview_mode:
            self.upload_prompt_area.setStyleSheet("""
                QWidget {
                    background-color: #14161A;
                    border: 2px dashed #2B9D7C;
                    border-radius: 20px;
                }
                QWidget:hover {
                    border-color: #00CC55;
                    background-color: rgba(43, 157, 124, 0.05);
                }
            """)

    def dropEvent(self, event):
        """拖拽放下事件"""
        if event.mimeData().hasUrls():
            urls = event.mimeData().urls()
            if urls and urls[0].isLocalFile():
                file_path = urls[0].toLocalFile()
                # 检查是否为视频文件
                video_extensions = ['.mp4', '.avi', '.mov', '.mkv', '.flv', '.wmv', '.webm']
                if any(file_path.lower().endswith(ext) for ext in video_extensions):
                    self.load_video(file_path)
                    event.acceptProposedAction()
                else:
                    print("不支持的文件格式")
                    event.ignore()
            else:
                event.ignore()
        else:
            event.ignore()

        # 恢复样式
        if not self.is_preview_mode:
            self.dragLeaveEvent(event)

    def get_uploaded_video_path(self):
        """获取已上传的视频文件路径"""
        return self.uploaded_video_path


class ParameterSettingsPanel(RoundedWidget):
    """
    参数设置栏组件
    显示主要的参数设置选项，以紧凑的形式展示在主界面上
    包含滚动区域，支持更多设置项
    """
    def __init__(self):
        super().__init__(bg_color="#252525", border_radius=16)
        # 固定宽度会在主窗口中设置
        
        # 初始化字幕提取器插件（用于检测模型状态）
        self.extractor = None
        if WHISPERX_AVAILABLE:
            try:
                self.extractor = WhisperXSubtitleExtractor()
                
                # 默认配置：使用auto模式，支持GPU和CPU自动切换
                default_config = {
                    "device": "auto",  # 支持GPU/CPU自动选择
                    "default_model": "medium",  # 使用更小的模型提高稳定性
                    "enable_uvr5": False,  # 默认禁用UVR5
                    "models_dir": "models/whisperx_subtitle/weights"  # 指向weights目录
                }
                
                self.extractor.initialize(default_config)
                print("参数设置面板：成功初始化WhisperX插件")
            except Exception as e:
                print(f"参数设置面板：初始化WhisperX插件失败 - {e}")
                self.extractor = None
        
        # 初始化TTS管理器
        self.tts_manager = None
        try:
            from plugins.tts.tts_manager_plugin import TtsManagerPlugin
            self.tts_manager = TtsManagerPlugin()
            print("参数设置面板：正在初始化TTS管理器")
            
            # 默认配置
            tts_config = {
                "fast_mode": False,  # 禁用快速模式以确保加载声音列表
                "skip_voice_list": False  # 确保加载声音列表
            }
            
            # 尝试加载已保存的配置
            try:
                from core.config_manager import get_config_manager
                config_manager = get_config_manager()
                if config_manager:
                    tts_config["azure_api_key"] = config_manager.get("api_keys.azure_tts_key", "")
                    tts_config["azure_region"] = config_manager.get("api_keys.azure_tts_region", "eastus")
                    print(f"已加载Azure TTS配置 - 区域: {tts_config['azure_region']}")
            except Exception as config_err:
                print(f"加载TTS配置失败: {config_err}")
            
            self.tts_manager.initialize(tts_config)
            print("参数设置面板：TTS管理器初始化成功")
            
            # 预览音频状态
            self._preview_audio_path = None
            self._preview_in_progress = False
            self._preview_media_player = None
            self._preview_audio_output = None
        except ImportError as e:
            print(f"参数设置面板：无法导入TTS管理器 - {e}")
            self.tts_manager = None
        
        self.setup_ui()
        
        # 加载模型状态
        self.load_model_combo()
        
        # 如果TTS管理器成功初始化，则设置信号连接
        if self.tts_manager:
            # 引擎切换时更新语音和声音列表
            self.engine_combo.currentIndexChanged.connect(self.on_engine_changed)
            # 语言切换时更新声音列表
            self.language_combo.currentIndexChanged.connect(self.update_voice_list)
            # 声音切换时的回调
            self.voice_combo.currentIndexChanged.connect(self.on_voice_changed)
            # 实现速度滑块值变化时的回调
            self.speed_slider.valueChanged.connect(self.on_speed_changed)
    
    def setup_ui(self):
        """初始化参数设置栏UI"""
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(16, 16, 16, 16)
        main_layout.setSpacing(15)
        
        # 标题
        title_label = QLabel("参数设置")
        title_label.setStyleSheet("""
            QLabel {
                color: #FFFFFF;
                font-size: 18px;
                font-weight: bold;
                background-color: transparent;
            }
        """)
        main_layout.addWidget(title_label)
        
        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setFrameShape(QFrame.NoFrame)  # 无边框
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)  # 禁用水平滚动条
        scroll_area.setStyleSheet("""
            QScrollArea {
                background-color: transparent;
                border: none;
            }
            QScrollBar:vertical {
                background-color: #353535;
                width: 8px;
                margin: 0px;
                border-radius: 4px;
            }
            QScrollBar::handle:vertical {
                background-color: #555555;
                min-height: 20px;
                border-radius: 4px;
            }
            QScrollBar::handle:vertical:hover {
                background-color: #666666;
            }
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
                height: 0px;
            }
            QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {
                background-color: transparent;
            }
        """)
        
        # 创建内容容器
        content_widget = QWidget()
        content_widget.setStyleSheet("background-color: transparent;")
        content_layout = QVBoxLayout(content_widget)
        content_layout.setContentsMargins(0, 0, 8, 0)  # 右侧留出滚动条空间
        content_layout.setSpacing(15)
        
        # 语音识别设置 - 替换为增强版
        sr_frame = self.create_speech_recognition_section()
        content_layout.addWidget(sr_frame)
        
        # 字幕翻译设置
        trans_frame = self.create_settings_section("字幕翻译", [
            ("翻译渠道", ["谷歌翻译 (免费)", "微软翻译 (需要API Key)", "DeepL Pro (需要API Key)"], "谷歌翻译 (免费)"),
            ("目标语言", ["中文 (zh)", "英语 (en)", "日语 (ja)", "韩语 (ko)", "法语 (fr)", "德语 (de)", "西班牙语 (es)", "俄语 (ru)", "阿拉伯语 (ar)", "泰语 (th)"], "中文 (zh)")
        ])
        content_layout.addWidget(trans_frame)
        
        # 配音设置
        voice_frame = QFrame()
        voice_frame.setStyleSheet("""
            QFrame {
                background-color: #353535;
                border-radius: 12px;
                border: none;
            }
        """)
        
        voice_layout = QVBoxLayout(voice_frame)
        voice_layout.setContentsMargins(12, 12, 12, 12)
        voice_layout.setSpacing(8)
        
        # 标题
        title_label = QLabel("配音设置")
        title_label.setStyleSheet("""
            QLabel {
                color: #FFFFFF;
                font-size: 15px;
                font-weight: bold;
                background-color: transparent;
            }
        """)
        voice_layout.addWidget(title_label)
        
        # TTS引擎选择
        engine_layout = QHBoxLayout()
        engine_layout.setContentsMargins(0, 4, 0, 4)
        
        engine_label = QLabel("TTS引擎:")
        engine_label.setStyleSheet("""
            QLabel {
                color: #E0E0E0;
                font-size: 13px;
                background-color: transparent;
            }
        """)
        engine_label.setFixedWidth(70)
        
        # TTS引擎下拉框
        self.engine_combo = QComboBox()
        self.engine_combo.setFixedHeight(26)
        self.engine_combo.setStyleSheet("""
            QComboBox {
                background-color: #252525;
                color: #FFFFFF;
                font-size: 13px;
                border: none;
                border-radius: 6px;
                padding: 2px 8px;
            }
            QComboBox::drop-down {
                border: none;
                width: 20px;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 4px solid transparent;
                border-right: 4px solid transparent;
                border-top: 6px solid #CCCCCC;
                margin-right: 6px;
            }
            QComboBox QAbstractItemView {
                background-color: #252525;
                color: #FFFFFF;
                selection-background-color: #0076FF;
                border: none;
                border-radius: 6px;
                padding: 5px;
            }
        """)
        
        # 添加引擎选项
        if self.tts_manager:
            engines = self.tts_manager.get_available_engines()
            for engine_name in engines:
                if engine_name == "edge_tts":
                    self.engine_combo.addItem("Edge TTS (免费)", engine_name)
                elif engine_name == "azure_tts":
                    self.engine_combo.addItem("Azure TTS (高级)", engine_name)
                else:
                    self.engine_combo.addItem(engine_name, engine_name)
            
            # 默认选择Edge TTS
            for i in range(self.engine_combo.count()):
                if self.engine_combo.itemData(i) == "edge_tts":
                    self.engine_combo.setCurrentIndex(i)
                    break
        else:
            self.engine_combo.addItem("Edge TTS (免费)", "edge_tts")
            self.engine_combo.addItem("Azure TTS (高级)", "azure_tts")
        
        engine_layout.addWidget(engine_label)
        engine_layout.addWidget(self.engine_combo)
        voice_layout.addLayout(engine_layout)
        
        # 添加分割线
        line1 = QFrame()
        line1.setFrameShape(QFrame.HLine)
        line1.setStyleSheet("background-color: #454545;")
        line1.setFixedHeight(1)
        voice_layout.addWidget(line1)
        
        # 语言选择
        language_layout = QHBoxLayout()
        language_layout.setContentsMargins(0, 4, 0, 4)
        
        language_label = QLabel("语言:")
        language_label.setStyleSheet("""
            QLabel {
                color: #E0E0E0;
                font-size: 13px;
                background-color: transparent;
            }
        """)
        language_label.setFixedWidth(70)
        
        # 语言下拉框
        self.language_combo = QComboBox()
        self.language_combo.setFixedHeight(26)
        self.language_combo.setStyleSheet("""
            QComboBox {
                background-color: #252525;
                color: #FFFFFF;
                font-size: 13px;
                border: none;
                border-radius: 6px;
                padding: 2px 8px;
            }
            QComboBox::drop-down {
                border: none;
                width: 20px;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 4px solid transparent;
                border-right: 4px solid transparent;
                border-top: 6px solid #CCCCCC;
                margin-right: 6px;
            }
            QComboBox QAbstractItemView {
                background-color: #252525;
                color: #FFFFFF;
                selection-background-color: #0076FF;
                border: none;
                border-radius: 6px;
                padding: 5px;
            }
        """)
        language_layout.addWidget(language_label)
        language_layout.addWidget(self.language_combo)
        voice_layout.addLayout(language_layout)
        
        # 添加分割线
        line2 = QFrame()
        line2.setFrameShape(QFrame.HLine)
        line2.setStyleSheet("background-color: #454545;")
        line2.setFixedHeight(1)
        voice_layout.addWidget(line2)
        
        # 声音选择
        voice_choice_layout = QHBoxLayout()
        voice_choice_layout.setContentsMargins(0, 4, 0, 4)
        
        voice_label = QLabel("声音:")
        voice_label.setStyleSheet("""
            QLabel {
                color: #E0E0E0;
                font-size: 13px;
                background-color: transparent;
            }
        """)
        voice_label.setFixedWidth(70)
        
        # 声音下拉框
        self.voice_combo = QComboBox()
        self.voice_combo.setFixedHeight(26)
        self.voice_combo.setStyleSheet("""
            QComboBox {
                background-color: #252525;
                color: #FFFFFF;
                font-size: 13px;
                border: none;
                border-radius: 6px;
                padding: 2px 8px;
            }
            QComboBox::drop-down {
                border: none;
                width: 20px;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 4px solid transparent;
                border-right: 4px solid transparent;
                border-top: 6px solid #CCCCCC;
                margin-right: 6px;
            }
            QComboBox QAbstractItemView {
                background-color: #252525;
                color: #FFFFFF;
                selection-background-color: #0076FF;
                border: none;
                border-radius: 6px;
                padding: 5px;
            }
        """)
        voice_choice_layout.addWidget(voice_label)
        voice_choice_layout.addWidget(self.voice_combo)
        voice_layout.addLayout(voice_choice_layout)
        
        # 添加分割线
        line3 = QFrame()
        line3.setFrameShape(QFrame.HLine)
        line3.setStyleSheet("background-color: #454545;")
        line3.setFixedHeight(1)
        voice_layout.addWidget(line3)
        
        # 语速设置
        speed_option_layout = QHBoxLayout()
        speed_option_layout.setContentsMargins(0, 4, 0, 4)
        
        # 语速标签
        speed_label_main = QLabel("语速:")
        speed_label_main.setStyleSheet("""
            QLabel {
                color: #E0E0E0;
                font-size: 13px;
                background-color: transparent;
            }
        """)
        speed_label_main.setFixedWidth(70)
        
        # 语速数值标签
        self.speed_label = QLabel("1.0x")
        self.speed_label.setStyleSheet("""
            QLabel {
                color: #00CC55;
                font-size: 13px;
                font-weight: bold;
                background-color: transparent;
                padding-right: 8px;
            }
        """)
        self.speed_label.setFixedWidth(40)
        self.speed_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        
        # 语速滑块
        self.speed_slider = QSlider(Qt.Horizontal)
        self.speed_slider.setRange(50, 200)  # 0.5x to 2.0x
        self.speed_slider.setValue(100)  # 1.0x
        self.speed_slider.setFixedHeight(20)
        self.speed_slider.setStyleSheet("""
            QSlider::groove:horizontal {
                border: 1px solid #444444;
                height: 4px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #333333, stop:1 #555555);
                border-radius: 2px;
            }
            QSlider::handle:horizontal {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #2B9D7C, stop:1 #00CC55);
                border: 1px solid #1a7a5c;
                width: 12px;
                height: 12px;
                margin: -4px 0;
                border-radius: 6px;
            }
            QSlider::handle:horizontal:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y1:1,
                    stop:0 #00CC55, stop:1 #2B9D7C);
            }
            QSlider::sub-page:horizontal {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #2B9D7C, stop:1 #00CC55);
                border-radius: 2px;
            }
        """)
        
        # 将滑动条值变化连接到更新标签
        self.speed_slider.valueChanged.connect(self.on_speed_changed)
        
        # 添加到布局
        speed_layout = QHBoxLayout()
        speed_layout.setContentsMargins(0, 0, 0, 0)
        speed_layout.addWidget(speed_label_main)
        speed_layout.addWidget(self.speed_slider)
        speed_layout.addWidget(self.speed_label)
        voice_layout.addLayout(speed_layout)
        
        # 添加试听按钮
        listen_layout = QHBoxLayout()
        listen_layout.setContentsMargins(0, 8, 0, 0)
        
        self.voice_test_btn = QPushButton("试听")
        self.voice_test_btn.setFixedSize(100, 30)
        self.voice_test_btn.setCheckable(True)  # 可切换状态
        self.voice_test_btn.setStyleSheet("""
            QPushButton {
                background-color: #2B9D7C;
                color: #FFFFFF;
                font-size: 14px;
                border: none;
                border-radius: 15px;
                outline: none;
            }
            QPushButton:hover {
                background-color: #00CC55;
            }
            QPushButton:pressed {
                background-color: #1B8A69;
            }
            QPushButton:checked {
                background-color: #D32F2F;
            }
            QPushButton:checked:hover {
                background-color: #F44336;
            }
        """)
        self.voice_test_btn.clicked.connect(self.toggle_voice_preview)
        
        listen_layout.addStretch()
        listen_layout.addWidget(self.voice_test_btn)
        listen_layout.addStretch()
        
        voice_layout.addLayout(listen_layout)
        
        # 语速设置
        speed_option_layout = QHBoxLayout()
        speed_option_layout.setContentsMargins(0, 4, 0, 4)
        
        # 语速标签
        speed_label_main = QLabel("语速:")
        speed_label_main.setStyleSheet("""
            QLabel {
                color: #E0E0E0;
                font-size: 13px;
                background-color: transparent;
            }
        """)
        speed_label_main.setFixedWidth(70)
        
        # 语速数值标签
        self.speed_label = QLabel("1.0x")
        self.speed_label.setStyleSheet("""
            QLabel {
                color: #00CC55;
                font-size: 13px;
                font-weight: bold;
                background-color: transparent;
                padding-right: 8px;
            }
        """)
        self.speed_label.setFixedWidth(40)
        self.speed_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        
        # 语速滑块
        self.speed_slider = QSlider(Qt.Horizontal)
        self.speed_slider.setRange(50, 200)  # 0.5x to 2.0x
        self.speed_slider.setValue(100)  # 1.0x
        self.speed_slider.setFixedHeight(20)
        self.speed_slider.setStyleSheet("""
            QSlider::groove:horizontal {
                border: 1px solid #444444;
                height: 4px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #333333, stop:1 #555555);
                border-radius: 2px;
            }
            QSlider::handle:horizontal {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #2B9D7C, stop:1 #00CC55);
                border: 1px solid #1a7a5c;
                width: 12px;
                height: 12px;
                margin: -4px 0;
                border-radius: 6px;
            }
            QSlider::handle:horizontal:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y1:1,
                    stop:0 #00CC55, stop:1 #2B9D7C);
            }
            QSlider::sub-page:horizontal {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #2B9D7C, stop:1 #00CC55);
                border-radius: 2px;
            }
        """)
        
        # 将滑动条值变化连接到更新标签
        self.speed_slider.valueChanged.connect(lambda value: self.speed_label.setText(f"{value/100:.1f}x"))
        
        # 添加到布局
        speed_layout = QHBoxLayout()
        speed_layout.setContentsMargins(0, 0, 0, 0)
        speed_layout.addWidget(speed_label_main)
        speed_layout.addWidget(self.speed_slider)
        speed_layout.addWidget(self.speed_label)
        voice_layout.addLayout(speed_layout)
        
        # 添加分割线
        line = QFrame()
        line.setFrameShape(QFrame.HLine)
        line.setStyleSheet("background-color: #454545;")
        line.setFixedHeight(1)
        voice_layout.addWidget(line)
        
        # 语速设置
        speed_option_layout = QHBoxLayout()
        speed_option_layout.setContentsMargins(0, 4, 0, 4)
        
        # 语速标签
        speed_label_main = QLabel("语速:")
        speed_label_main.setStyleSheet("""
            QLabel {
                color: #E0E0E0;
                font-size: 13px;
                background-color: transparent;
            }
        """)
        speed_label_main.setFixedWidth(70)
        
        # 语速数值标签
        self.speed_label = QLabel("1.0x")
        self.speed_label.setStyleSheet("""
            QLabel {
                color: #00CC55;
                font-size: 13px;
                font-weight: bold;
                background-color: transparent;
                padding-right: 8px;
            }
        """)
        self.speed_label.setFixedWidth(40)
        self.speed_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        
        # 语速滑块
        self.speed_slider = QSlider(Qt.Horizontal)
        self.speed_slider.setRange(50, 200)  # 0.5x to 2.0x
        self.speed_slider.setValue(100)  # 1.0x
        self.speed_slider.setFixedHeight(20)
        self.speed_slider.setStyleSheet("""
            QSlider::groove:horizontal {
                border: 1px solid #444444;
                height: 4px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #333333, stop:1 #555555);
                border-radius: 2px;
            }
            QSlider::handle:horizontal {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #2B9D7C, stop:1 #00CC55);
                border: 1px solid #1a7a5c;
                width: 12px;
                height: 12px;
                margin: -4px 0;
                border-radius: 6px;
            }
            QSlider::handle:horizontal:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y1:1,
                    stop:0 #00CC55, stop:1 #2B9D7C);
            }
            QSlider::sub-page:horizontal {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #2B9D7C, stop:1 #00CC55);
                border-radius: 2px;
            }
        """)
        
        # 将滑动条值变化连接到更新标签
        self.speed_slider.valueChanged.connect(lambda value: self.speed_label.setText(f"{value/100:.1f}x"))
        
        # 添加到布局
        speed_layout = QHBoxLayout()
        speed_layout.setContentsMargins(0, 0, 0, 0)
        speed_layout.addWidget(speed_label_main)
        speed_layout.addWidget(self.speed_slider)
        speed_layout.addWidget(self.speed_label)
        voice_layout.addLayout(speed_layout)
        
        content_layout.addWidget(voice_frame)
        
        # 选项设置
        options_frame = self.create_options_section()
        content_layout.addWidget(options_frame)
        
        # 添加弹性空间
        content_layout.addStretch()
        
        # 将内容部件设置为滚动区域的部件
        scroll_area.setWidget(content_widget)
        
        # 将滚动区域添加到主布局
        main_layout.addWidget(scroll_area, 1)  # 1表示拉伸因子，使其填充剩余空间
        
        # 底部按钮
        button_layout = QHBoxLayout()
        
        # 详细设置按钮
        detail_btn = QPushButton("详细设置")
        detail_btn.setFixedSize(100, 32)
        detail_btn.setStyleSheet("""
            QPushButton {
                background-color: #3A3A3A;
                color: #FFFFFF;
                font-size: 14px;
                border: none;
                border-radius: 16px;
            }
            QPushButton:hover {
                background-color: #444444;
            }
            QPushButton:pressed {
                background-color: #2A2A2A;
            }
        """)
        detail_btn.clicked.connect(self.open_detailed_settings)
        
        # 应用按钮
        apply_btn = QPushButton("应用")
        apply_btn.setFixedSize(80, 32)
        apply_btn.setStyleSheet("""
            QPushButton {
                background-color: #0076FF;
                color: #FFFFFF;
                font-size: 14px;
                border: none;
                border-radius: 16px;
            }
            QPushButton:hover {
                background-color: #0A84FF;
            }
            QPushButton:pressed {
                background-color: #0062D8;
            }
        """)
        apply_btn.clicked.connect(self.apply_settings)
        
        button_layout.addWidget(detail_btn)
        button_layout.addStretch()
        button_layout.addWidget(apply_btn)
        
        main_layout.addLayout(button_layout)
    
    def create_settings_section(self, title, options):
        """创建设置区域"""
        section = QFrame()
        section.setStyleSheet("""
            QFrame {
                background-color: #353535;
                border-radius: 12px;
                border: none;
            }
        """)
        
        layout = QVBoxLayout(section)
        layout.setContentsMargins(12, 12, 12, 12)
        layout.setSpacing(8)
        
        # 标题
        title_label = QLabel(title)
        title_label.setStyleSheet("""
            QLabel {
                color: #FFFFFF;
                font-size: 15px;
                font-weight: bold;
                background-color: transparent;
            }
        """)
        layout.addWidget(title_label)
        
        # 选项
        for i, (label_text, items, default_value) in enumerate(options):
            option_layout = QHBoxLayout()
            option_layout.setContentsMargins(0, 4, 0, 4)
            
            # 标签
            label = QLabel(label_text + ":")
            label.setStyleSheet("""
                QLabel {
                    color: #E0E0E0;
                    font-size: 13px;
                    background-color: transparent;
                }
            """)
            label.setFixedWidth(70)
            
            # 下拉框
            combo = QComboBox()
            combo.addItems(items)
            combo.setCurrentText(default_value)
            combo.setFixedHeight(26)
            combo.setStyleSheet("""
                QComboBox {
                    background-color: #252525;
                    color: #FFFFFF;
                    font-size: 13px;
                    border: none;
                    border-radius: 6px;
                    padding: 2px 8px;
                }
                QComboBox::drop-down {
                    border: none;
                    width: 20px;
                }
                QComboBox::down-arrow {
                    image: none;
                    border-left: 4px solid transparent;
                    border-right: 4px solid transparent;
                    border-top: 6px solid #CCCCCC;
                    margin-right: 6px;
                }
                QComboBox QAbstractItemView {
                    background-color: #252525;
                    color: #FFFFFF;
                    selection-background-color: #0076FF;
                    border: none;
                    border-radius: 6px;
                    padding: 5px;
                }
            """)
            
            option_layout.addWidget(label)
            option_layout.addWidget(combo)
            layout.addLayout(option_layout)
            
            # 添加分割线，除了最后一项
            if i < len(options) - 1:
                line = QFrame()
                line.setFrameShape(QFrame.HLine)
                line.setStyleSheet("background-color: #454545;")
                line.setFixedHeight(1)
                layout.addWidget(line)
        
        return section
    
    def create_speech_recognition_section(self):
        """创建增强版语音识别设置区域（包含模型检查和下载按钮）"""
        section = QFrame()
        section.setStyleSheet("""
            QFrame {
                background-color: #353535;
                border-radius: 12px;
                border: none;
            }
        """)
        
        layout = QVBoxLayout(section)
        layout.setContentsMargins(12, 12, 12, 12)
        layout.setSpacing(8)
        
        # 标题区域
        title_layout = QHBoxLayout()
        
        # 标题
        title_label = QLabel("语音识别")
        title_label.setStyleSheet("""
            QLabel {
                color: #FFFFFF;
                font-size: 15px;
                font-weight: bold;
                background-color: transparent;
            }
        """)
        title_layout.addWidget(title_label)
        
        # 模型状态标签
        self.model_status_label = QLabel("")
        self.model_status_label.setStyleSheet("""
            QLabel {
                font-size: 11px;
                color: rgba(255, 255, 255, 0.7);
                background: transparent;
                border: none;
            }
        """)
        title_layout.addWidget(self.model_status_label)
        title_layout.addStretch()
        
        # 下载模型按钮
        self.download_model_button = QPushButton("⬇️")
        self.download_model_button.setToolTip("下载选中的模型")
        self.download_model_button.setFixedSize(24, 24)
        self.download_model_button.setStyleSheet("""
            QPushButton {
                background-color: #555555;
                color: #FFFFFF;
                font-size: 12px;
                border: none;
                border-radius: 12px;
            }
            QPushButton:hover {
                background-color: #666666;
            }
            QPushButton:pressed {
                background-color: #444444;
            }
            QPushButton:disabled {
                background-color: #333333;
                color: #666666;
            }
        """)
        self.download_model_button.clicked.connect(self.download_selected_model)
        
        title_layout.addWidget(self.download_model_button)
        
        layout.addLayout(title_layout)
        
        # 识别引擎
        engine_layout = QHBoxLayout()
        engine_layout.setContentsMargins(0, 4, 0, 4)
        
        engine_label = QLabel("识别引擎:")
        engine_label.setStyleSheet("""
            QLabel {
                color: #E0E0E0;
                font-size: 13px;
                background-color: transparent;
            }
        """)
        engine_label.setFixedWidth(70)
        
        self.engine_combo = QComboBox()
        self.engine_combo.addItems(["whisperX 本地", "whisperX API"])
        self.engine_combo.setCurrentText("whisperX 本地")
        self.engine_combo.setFixedHeight(26)
        self.engine_combo.setStyleSheet("""
            QComboBox {
                background-color: #252525;
                color: #FFFFFF;
                font-size: 13px;
                border: none;
                border-radius: 6px;
                padding: 2px 8px;
            }
            QComboBox::drop-down {
                border: none;
                width: 20px;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 4px solid transparent;
                border-right: 4px solid transparent;
                border-top: 6px solid #CCCCCC;
                margin-right: 6px;
            }
            QComboBox QAbstractItemView {
                background-color: #252525;
                color: #FFFFFF;
                selection-background-color: #0076FF;
                border: none;
                border-radius: 6px;
                padding: 5px;
            }
        """)
        
        engine_layout.addWidget(engine_label)
        engine_layout.addWidget(self.engine_combo)
        layout.addLayout(engine_layout)
        
        # 添加分割线
        line1 = QFrame()
        line1.setFrameShape(QFrame.HLine)
        line1.setStyleSheet("background-color: #454545;")
        line1.setFixedHeight(1)
        layout.addWidget(line1)
        
        # 识别模型
        model_layout = QHBoxLayout()
        model_layout.setContentsMargins(0, 4, 0, 4)
        
        model_label = QLabel("识别模型:")
        model_label.setStyleSheet("""
            QLabel {
                color: #E0E0E0;
                font-size: 13px;
                background-color: transparent;
            }
        """)
        model_label.setFixedWidth(70)
        
        self.model_combo = QComboBox()
        # 模型列表将在load_model_combo方法中加载
        self.model_combo.setFixedHeight(26)
        self.model_combo.setStyleSheet("""
            QComboBox {
                background-color: #252525;
                color: #FFFFFF;
                font-size: 13px;
                border: none;
                border-radius: 6px;
                padding: 2px 8px;
            }
            QComboBox::drop-down {
                border: none;
                width: 20px;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 4px solid transparent;
                border-right: 4px solid transparent;
                border-top: 6px solid #CCCCCC;
                margin-right: 6px;
            }
            QComboBox QAbstractItemView {
                background-color: #252525;
                color: #FFFFFF;
                selection-background-color: #0076FF;
                border: none;
                border-radius: 6px;
                padding: 5px;
            }
        """)
        # 暂时禁用模型状态更新
        # self.model_combo.currentIndexChanged.connect(self.update_model_status)
        
        model_layout.addWidget(model_label)
        model_layout.addWidget(self.model_combo)
        layout.addLayout(model_layout)
        
        # 添加分割线
        line2 = QFrame()
        line2.setFrameShape(QFrame.HLine)
        line2.setStyleSheet("background-color: #454545;")
        line2.setFixedHeight(1)
        layout.addWidget(line2)
        
        # 源语言
        lang_layout = QHBoxLayout()
        lang_layout.setContentsMargins(0, 4, 0, 4)
        
        lang_label = QLabel("源语言:")
        lang_label.setStyleSheet("""
            QLabel {
                color: #E0E0E0;
                font-size: 13px;
                background-color: transparent;
            }
        """)
        lang_label.setFixedWidth(70)
        
        self.lang_combo = QComboBox()
        self.lang_combo.addItems(["自动检测", "中文", "英语", "日语", "韩语", "西班牙语", "法语", "德语", "意大利语", "葡萄牙语", "俄语"])
        self.lang_combo.setCurrentText("自动检测")
        self.lang_combo.setFixedHeight(26)
        self.lang_combo.setStyleSheet("""
            QComboBox {
                background-color: #252525;
                color: #FFFFFF;
                font-size: 13px;
                border: none;
                border-radius: 6px;
                padding: 2px 8px;
            }
            QComboBox::drop-down {
                border: none;
                width: 20px;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 4px solid transparent;
                border-right: 4px solid transparent;
                border-top: 6px solid #CCCCCC;
                margin-right: 6px;
            }
            QComboBox QAbstractItemView {
                background-color: #252525;
                color: #FFFFFF;
                selection-background-color: #0076FF;
                border: none;
                border-radius: 6px;
                padding: 5px;
            }
        """)
        
        lang_layout.addWidget(lang_label)
        lang_layout.addWidget(self.lang_combo)
        layout.addLayout(lang_layout)
        
        return section
    
    def create_options_section(self):
        """创建选项设置区域"""
        section = QFrame()
        section.setStyleSheet("""
            QFrame {
                background-color: #353535;
                border-radius: 12px;
                border: none;
            }
        """)
        
        layout = QVBoxLayout(section)
        layout.setContentsMargins(12, 12, 12, 12)
        layout.setSpacing(8)
        
        # 标题
        title_label = QLabel("选项设置")
        title_label.setStyleSheet("""
            QLabel {
                color: #FFFFFF;
                font-size: 15px;
                font-weight: bold;
                background-color: transparent;
            }
        """)
        layout.addWidget(title_label)
        
        # 选项复选框
        options = [
            ("启用GPU加速", True),
            ("保留视频原声", False),
            ("保留背景音乐", False)
        ]
        
        for i, (option_text, default_checked) in enumerate(options):
            option_layout = QHBoxLayout()
            option_layout.setContentsMargins(0, 4, 0, 4)
            
            # 标签
            label = QLabel(option_text)
            label.setStyleSheet("""
                QLabel {
                    color: #E0E0E0;
                    font-size: 13px;
                    background-color: transparent;
                }
            """)
            
            # 复选框
            checkbox = QCheckBox()
            checkbox.setChecked(default_checked)
            checkbox.setStyleSheet("""
                QCheckBox {
                    background-color: transparent;
                }
                QCheckBox::indicator {
                    width: 18px;
                    height: 18px;
                    background-color: #252525;
                    border: none;
                    border-radius: 4px;
                }
                QCheckBox::indicator:checked {
                    background-color: #0076FF;
                    image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iMTAiIHZpZXdCb3g9IjAgMCAxMiAxMCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTQgNy4zMzMzM0wxLjMzMzM0IDQuNjY2NjZMMC4zMzMzMzcgNS42NjY2Nkw0IDkuMzMzMzNMMTEuMzMzMyAyTDEwLjMzMzMgMUw0IDcuMzMzMzNaIiBmaWxsPSJ3aGl0ZSIvPgo8L3N2Zz4K);
                    background-repeat: no-repeat;
                    background-position: center;
                }
            """)
            
            option_layout.addWidget(label)
            option_layout.addStretch()
            option_layout.addWidget(checkbox)
            layout.addLayout(option_layout)
            
            # 添加分割线，除了最后一项
            if i < len(options) - 1:
                line = QFrame()
                line.setFrameShape(QFrame.HLine)
                line.setStyleSheet("background-color: #454545;")
                line.setFixedHeight(1)
                layout.addWidget(line)
        
        return section
    
    def open_detailed_settings(self):
        """打开详细设置窗口"""
        try:
            config_window = ConfigWindow()
            config_window.exec()
        except Exception as e:
            print(f"打开详细设置窗口时出错: {e}")
    
    def apply_settings(self):
        """应用当前参数设置"""
        # 这里可以添加应用设置的逻辑
        print("应用参数设置")
        
    def on_engine_changed(self):
        """当TTS引擎改变时"""
        if not self.tts_manager:
            return
            
        try:
            # 清理预览
            if hasattr(self, '_cleanup_preview_file'):
                self._cleanup_preview_file()
            if hasattr(self, '_reset_preview_state'):
                self._reset_preview_state()
            
            engine_name = self.engine_combo.currentData()
            print(f"🔄 正在切换到TTS引擎: {engine_name}")
            
            if not engine_name:
                print("⚠️ 未选择TTS引擎")
                return
            
            # 如果是Azure TTS，先检查API密钥
            if engine_name == "azure_tts":
                try:
                    from core.config_manager import get_config_manager
                    config_manager = get_config_manager()
                    azure_key = config_manager.get("api_keys.azure_tts_key", "")
                    if not azure_key:
                        from PySide6.QtWidgets import QMessageBox
                        QMessageBox.warning(self, "Azure TTS未配置", 
                                          "请先在API设置中配置Azure TTS的API密钥")
                        # 切换回Edge TTS
                        for i in range(self.engine_combo.count()):
                            if self.engine_combo.itemData(i) == "edge_tts":
                                self.engine_combo.setCurrentIndex(i)
                                break
                        return
                except Exception as e:
                    print(f"获取Azure配置失败: {e}")
            
            # 切换TTS引擎
            if self.tts_manager.set_current_engine(engine_name):
                print(f"✅ 切换到TTS引擎成功: {engine_name}")
                # 更新语言和声音列表
                self.update_language_list()
                self.update_voice_list()
            else:
                print(f"❌ 切换TTS引擎失败: {engine_name}")
                # 如果切换失败，回退到Edge TTS
                for i in range(self.engine_combo.count()):
                    if self.engine_combo.itemData(i) == "edge_tts":
                        self.engine_combo.setCurrentIndex(i)
                        break
                
        except Exception as e:
            print(f"❌ 切换TTS引擎时出错: {e}")
            import traceback
            traceback.print_exc()
    
    def update_language_list(self):
        """更新语言列表"""
        if not self.tts_manager:
            return
            
        try:
            # 保存当前选择
            current_lang_code = self.language_combo.currentData()
            
            # 清空列表
            self.language_combo.clear()
            
            # 获取当前引擎支持的语言
            supported_languages = self.tts_manager.get_supported_languages()
            
            if not supported_languages:
                print("⚠️ 没有找到支持的语言")
                return
            
            # 添加语言到下拉框
            for lang_code, lang_name in supported_languages.items():
                self.language_combo.addItem(lang_name, lang_code)
            
            # 恢复之前的选择或默认选择中文
            if current_lang_code:
                # 先尝试恢复之前的选择
                for i in range(self.language_combo.count()):
                    if self.language_combo.itemData(i) == current_lang_code:
                        self.language_combo.setCurrentIndex(i)
                        break
            
            # 如果没有恢复成功，尝试选择中文
            if self.language_combo.currentData() is None:
                for i in range(self.language_combo.count()):
                    lang_code = self.language_combo.itemData(i)
                    if lang_code and ("zh" in str(lang_code).lower() or "chinese" in str(lang_code).lower()):
                        self.language_combo.setCurrentIndex(i)
                        break
            
            # 如果还是没有选择，选择第一个
            if self.language_combo.currentData() is None and self.language_combo.count() > 0:
                self.language_combo.setCurrentIndex(0)
                
        except Exception as e:
            print(f"❌ 更新语言列表失败: {e}")
    
    def update_voice_list(self):
        """更新声音列表"""
        if not self.tts_manager:
            return
        
        try:
            print("🔄 正在更新声音列表...")
            
            # 保存当前选择
            current_voice_id = self.voice_combo.currentData()
            
            # 清空列表
            self.voice_combo.clear()
            
            # 获取当前语言代码
            current_lang_code = self.language_combo.currentData()
            if not current_lang_code:
                print("⚠️ 未选择语言，无法更新声音列表")
                return
            
            # 获取可用声音列表
            available_voices = self.tts_manager.get_available_voices(current_lang_code)
            
            if not available_voices:
                print(f"⚠️ 语言 {current_lang_code} 没有找到可用声音")
                return
                        
            # 添加声音到下拉框
            for voice_id, voice_name in available_voices.items():
                self.voice_combo.addItem(voice_name, voice_id)
            
            # 恢复之前的选择
            if current_voice_id:
                for i in range(self.voice_combo.count()):
                    if self.voice_combo.itemData(i) == current_voice_id:
                        self.voice_combo.setCurrentIndex(i)
                        break
            
            # 如果没有恢复成功，选择第一个
            if self.voice_combo.currentData() is None and self.voice_combo.count() > 0:
                self.voice_combo.setCurrentIndex(0)
                
        except Exception as e:
            print(f"❌ 更新声音列表失败: {e}")
    
    def on_voice_changed(self):
        """当声音选择改变时"""
        # 清理预览（如果正在进行）
        if hasattr(self, '_cleanup_preview_file'):
            self._cleanup_preview_file()
        if hasattr(self, '_reset_preview_state'):
            self._reset_preview_state()
    
    def on_speed_changed(self):
        """当语速改变时"""
        speed_value = self.speed_slider.value() / 100.0
        self.speed_label.setText(f"{speed_value:.1f}x")
        
        # 清理预览（如果正在进行）
        if hasattr(self, '_cleanup_preview_file'):
            self._cleanup_preview_file()
        if hasattr(self, '_reset_preview_state'):
            self._reset_preview_state()
            
    def toggle_voice_preview(self):
        """切换语音预览状态"""
        if not hasattr(self, '_preview_in_progress') or not self._preview_in_progress:
            self.start_voice_preview()
        else:
            self.stop_voice_preview()
    
    def start_voice_preview(self):
        """开始语音预览"""
        if not self.tts_manager:
            from PySide6.QtWidgets import QMessageBox
            QMessageBox.warning(self, "错误", "TTS引擎未初始化")
            return
        
        if not hasattr(self, 'voice_test_btn') or not self.voice_test_btn:
            print("警告：voice_test_btn 不存在，可能未正确初始化")
            return
        
        # 设置预览状态为进行中
        self._preview_in_progress = True
        self.voice_test_btn.setEnabled(False)
        self.voice_test_btn.setText("生成中...")
        
        # 获取当前设置
        voice = self.voice_combo.currentData()
        if not voice:
            from PySide6.QtWidgets import QMessageBox
            QMessageBox.warning(self, "错误", "请先选择声音")
            self._reset_preview_state()
            return
            
        speed = self.speed_slider.value() / 100.0
        
        # 使用默认试听文本
        text = "欢迎使用FlipTalk Ai，支持多种语音设置。"
            
        # 创建异步任务
        from threading import Thread
        def run_async_task():
            try:
                # 使用TTS管理器的preview_voice方法
                audio_path = self.tts_manager.preview_voice(text, voice, speed)
                
                # 在主线程中播放音频
                from PySide6.QtCore import QMetaObject, Qt, Q_ARG
                QMetaObject.invokeMethod(
                    self, 
                    "_play_preview_audio_slot",
                    Qt.QueuedConnection,
                    Q_ARG(str, audio_path)
                )
                
            except Exception as e:
                # 在主线程中显示错误
                from PySide6.QtCore import QMetaObject, Qt, Q_ARG
                QMetaObject.invokeMethod(
                    self,
                    "on_preview_failed",
                    Qt.QueuedConnection,
                    Q_ARG(str, str(e))
                )
            
        preview_thread = Thread(target=run_async_task)
        preview_thread.daemon = True
        preview_thread.start()
    
    @Slot(str)
    def _play_preview_audio_slot(self, audio_path: str):
        """播放预览音频的槽方法"""
        self._play_preview_audio(audio_path)
    
    def _play_preview_audio(self, audio_path: str):
        """播放预览音频"""
        try:
            # 清理之前的音频
            self._cleanup_preview_file()
            
            # 保存新的音频路径
            self._preview_audio_path = audio_path
            
            # 初始化播放器
            if not self._preview_media_player:
                self._preview_media_player = QMediaPlayer()
                self._preview_audio_output = QAudioOutput()
                self._preview_media_player.setAudioOutput(self._preview_audio_output)
                
                # 连接信号
                self._preview_media_player.mediaStatusChanged.connect(self._on_preview_status_changed)
                self._preview_media_player.errorOccurred.connect(self._on_preview_error)
            
            # 设置音频文件
            self._preview_media_player.setSource(QUrl.fromLocalFile(audio_path))
            
            # 开始播放
            self._preview_media_player.play()
            
            # 更新按钮状态
            self.voice_test_btn.setText("停止")
            self.voice_test_btn.setEnabled(True)
            self.voice_test_btn.setChecked(True)  # 设置为选中状态，显示红色背景
            
        except Exception as e:
            self.on_preview_failed(f"播放预览失败: {e}")
    
    def _on_preview_status_changed(self, status):
        """处理预览状态变化"""
        from PySide6.QtMultimedia import QMediaPlayer
        if status == QMediaPlayer.MediaStatus.EndOfMedia:
            self._reset_preview_state()
            
    def _on_preview_error(self, error, error_string):
        """处理预览错误"""
        self.on_preview_failed(f"预览播放错误: {error_string}")
    
    def stop_voice_preview(self):
        """停止语音预览"""
        print("🛑 停止语音预览")
        
        # 立即停止媒体播放器
        if hasattr(self, '_preview_media_player') and self._preview_media_player:
            try:
                self._preview_media_player.stop()
                from PySide6.QtCore import QUrl
                self._preview_media_player.setSource(QUrl())  # 清空媒体源
                print("✅ 媒体播放器已停止")
            except Exception as e:
                print(f"⚠️ 停止媒体播放器时出错: {e}")
        
        # 重置状态
        self._reset_preview_state()
        
        # 清理预览文件（延迟清理以避免文件占用冲突）
        from PySide6.QtCore import QTimer
        QTimer.singleShot(500, self._cleanup_preview_file)
    
    def _reset_preview_state(self):
        """重置预览状态"""
        self._preview_in_progress = False
        if hasattr(self, 'voice_test_btn'):
            self.voice_test_btn.setText("试听")
            self.voice_test_btn.setEnabled(True)
            self.voice_test_btn.setChecked(False)  # 重置按钮选中状态，恢复默认背景色
    
    def _cleanup_preview_file(self):
        """清理预览音频文件"""
        if hasattr(self, '_preview_audio_path') and self._preview_audio_path:
            try:
                import os
                if os.path.exists(self._preview_audio_path):
                    os.unlink(self._preview_audio_path)
            except Exception as e:
                print(f"清理预览文件失败: {e}")
            finally:
                self._preview_audio_path = None
    
    def on_preview_failed(self, error_message: str):
        """处理预览失败"""
        self._reset_preview_state()
        self.show_error_message(f"预览失败: {error_message}")
    
    def show_error_message(self, message: str):
        """显示错误消息"""
        from PySide6.QtWidgets import QMessageBox
        QMessageBox.warning(self, "错误", message)
    
    def load_model_combo(self):
        """加载可用的语音识别模型列表"""
        # 默认模型列表
        default_models = [
            "tiny", 
            "base", 
            "small", 
            "medium", 
            "large-v1",
            "large-v2", 
            "large-v3"
        ]
        
        try:
            # 尝试获取可用模型
            if self.extractor:
                available_models = self.extractor.get_available_models()
                if available_models:
                    self.model_combo.clear()
                    
                    # 添加模型到下拉列表
                    for model_name in available_models:
                        self.model_combo.addItem(model_name, model_name)
                        
                    # 检查是否有已下载的模型并更新状态
                    downloaded_models = self.extractor.get_downloaded_models()
                    if downloaded_models:
                        # 设置为第一个已下载的模型
                        for i in range(self.model_combo.count()):
                            if self.model_combo.itemText(i) in downloaded_models:
                                self.model_combo.setCurrentIndex(i)
                                break
            
            # 设置默认模型为large-v3
            default_model = "large-v3"
            for i in range(self.model_combo.count()):
                if self.model_combo.itemData(i) == default_model:
                    self.model_combo.setCurrentIndex(i)
                    break
            
            # 暂时禁用模型状态更新
            # self.update_model_status()
            
        except Exception as e:
            print(f"加载模型列表失败: {e}")
            
            # 回退到默认列表
            self.model_combo.clear()
            for model_name in default_models:
                self.model_combo.addItem(model_name, model_name)
            
            # 设置默认为large-v3
            for i in range(self.model_combo.count()):
                if self.model_combo.itemText(i) == "large-v3":
                    self.model_combo.setCurrentIndex(i)
                    break
            
            # 更新状态标签 - 提示错误
            if hasattr(self, 'model_status_label'):
                self.model_status_label.setText(f"(加载失败: {str(e)[:20]})")
                self.model_status_label.setStyleSheet("""
                    QLabel {
                        font-size: 11px;
                        color: #FF4444;
                        background: transparent;
                        border: none;
                    }
                """)
    

    
    def download_selected_model(self):
        """下载选中的模型"""
        if not self.extractor:
            QMessageBox.warning(self, "警告", "WhisperX插件未初始化，无法下载模型")
            return
            
        selected_model = self.model_combo.currentData()
        if not selected_model:
            return
                
        # 提示下载确认
        reply = QMessageBox.question(
            self, 
            "下载模型",
            f"确定要下载 {selected_model} 模型吗？这可能需要一段时间。",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            # 显示下载中状态
            self.model_status_label.setText("⏳ 模型下载中...")
            self.model_status_label.setStyleSheet("""
                QLabel {
                    font-size: 11px;
                    color: #0076FF;
                    background: transparent;
                    border: none;
                }
            """)
            self.download_model_button.setEnabled(False)
            QApplication.processEvents()
            
            try:
                # 定义进度回调函数
                def progress_callback(message, percentage):
                    self.model_status_label.setText(f"⏳ 下载中: {percentage}%")
                    QApplication.processEvents()
                
                # 下载模型
                success = self.extractor.download_model(selected_model, progress_callback)
                
                if success:
                    self.model_status_label.setText("✅ 下载完成")
                    self.model_status_label.setStyleSheet("""
                        QLabel {
                            font-size: 11px;
                            color: #44FF44;
                            background: transparent;
                            border: none;
                        }
                    """)
                    QMessageBox.information(self, "下载成功", f"模型 {selected_model} 下载完成！")
                    
                    # 刷新模型列表
                    self.load_model_combo()
                else:
                    self.model_status_label.setText("❌ 下载失败")
                    self.model_status_label.setStyleSheet("""
                        QLabel {
                            font-size: 11px;
                            color: #FF4444;
                            background: transparent;
                            border: none;
                        }
                    """)
                    QMessageBox.critical(self, "下载失败", f"模型 {selected_model} 下载失败！")
                    self.download_model_button.setEnabled(True)
                
            except Exception as e:
                self.model_status_label.setText("❌ 下载出错")
                self.model_status_label.setStyleSheet("""
                    QLabel {
                        font-size: 11px;
                        color: #FF4444;
                        background: transparent;
                        border: none;
                    }
                """)
                QMessageBox.critical(self, "下载错误", f"下载模型时出错: {e}")
                self.download_model_button.setEnabled(True)


class SubtitleEditArea(QWidget):
    """
    字幕编辑区域
    包含字幕编辑表格和操作按钮
    """
    def __init__(self):
        super().__init__()
        self.gpu_detector = None
        self.gpu_is_available = False
        self.gpu_name = "检测中..."
        self.setup_ui()
        self.setup_gpu_detector()
    
    def setup_ui(self):
        """初始化字幕编辑区域UI"""
        # 主布局
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)
        
        # 创建主容器
        self.main_container = self.create_subtitle_edit()
        layout.addWidget(self.main_container)
    
    def setup_gpu_detector(self):
        """设置GPU检测器"""
        # 尝试导入GPU检测器
        try:
            from core.gpu_detector import GPUDetector
            self.gpu_detector = GPUDetector()
            self.gpu_detector.gpu_status_updated.connect(self.on_gpu_status_updated)
            # 触发立即检测
            self.detect_and_update_gpu_immediately()
        except ImportError as e:
            print(f"无法导入GPU检测器: {e}")
    
    def on_gpu_status_updated(self, is_available: bool, gpu_name: str, status_text: str):
        """GPU状态更新回调"""
        self.gpu_is_available = is_available
        self.gpu_name = gpu_name
        
        # 更新GPU状态显示
        if hasattr(self, 'gpu_status_label'):
            # 设置状态文本和颜色
            color = "#00CC55" if is_available else "#FF5555"
            status_icon = "✓ " if is_available else "✗ "
            self.gpu_status_label.setText(f"GPU: {status_icon}{status_text}")
            self.gpu_status_label.setStyleSheet(f"""
                QLabel {{
                    color: {color};
                    font-size: 12px;
                    background-color: transparent;
                    border: none;
                }}
            """)
            
            # 更新提示文本
            if hasattr(self, 'gpu_usage_label'):
                if is_available:
                    self.gpu_usage_label.setText(f"{gpu_name} - 点击帮助了解如何获得最佳性能")
                else:
                    self.gpu_usage_label.setText("未检测到可用GPU，点击查看配置帮助")
    
    def refresh_gpu_status(self):
        """刷新GPU状态"""
        try:
            if self.gpu_detector:
                self.gpu_detector.check_gpu_async()
        except Exception as e:
            print(f"刷新GPU状态出错: {e}")
    
    def detect_and_update_gpu_immediately(self):
        """立即检测GPU并更新状态"""
        try:
            if self.gpu_detector:
                is_available, gpu_name = self.gpu_detector.check_gpu_sync()
                status = "可用" if is_available else "不可用"
                self.on_gpu_status_updated(is_available, gpu_name, status)
        except Exception as e:
            print(f"GPU检测失败: {e}")
            self.on_gpu_status_updated(False, "检测失败", "检测失败")
     
    def show_gpu_help(self):
        """显示GPU配置帮助窗口"""
        try:
            # 创建帮助对话框
            help_dialog = QDialog(self)
            help_dialog.setWindowTitle("GPU配置指南")
            help_dialog.setFixedSize(500, 700)
            help_dialog.setStyleSheet("""
                QDialog {
                    background-color: #1B1E24;
                    color: #FFFFFF;
                }
                QScrollArea {
                    border: none;
                    background-color: #1B1E24;
                }
                QTextEdit {
                    background-color: #14161A;
                    color: #FFFFFF;
                    border: 1px solid #333333;
                    border-radius: 8px;
                    padding: 10px;
                    font-size: 14px;
                }
                QPushButton {
                    background-color: #2B9D7C;
                    color: #FFFFFF;
                    border: none;
                    border-radius: 8px;
                    padding: 8px 12px;
                    font-size: 14px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #00CC55;
                }
            """)
            
            # 帮助对话框布局
            dialog_layout = QVBoxLayout(help_dialog)
            dialog_layout.setContentsMargins(20, 20, 20, 20)
            dialog_layout.setSpacing(15)
            
            # 标题
            title_label = QLabel("GPU加速配置指南")
            title_label.setStyleSheet("""
                QLabel {
                    color: #FFFFFF;
                    font-size: 18px;
                    font-weight: bold;
                    background-color: transparent;
                    border: none;
                }
            """)
            title_label.setAlignment(Qt.AlignCenter)
            dialog_layout.addWidget(title_label)
            
            # 内容滚动区域
            scroll_area = QScrollArea()
            scroll_area.setWidgetResizable(True)
            scroll_area.setStyleSheet("background-color: transparent;")
            
            # 内容编辑器
            help_text = QTextEdit()
            help_text.setReadOnly(True)
            
            # 获取帮助内容
            content = self.get_gpu_help_content()
            help_text.setHtml(content)
            
            scroll_area.setWidget(help_text)
            dialog_layout.addWidget(scroll_area)
            
            # 按钮布局
            buttons_layout = QHBoxLayout()
            buttons_layout.setSpacing(10)
            
            # 复制按钮
            copy_btn = QPushButton("复制指南")
            copy_btn.setFixedSize(100, 36)
            copy_btn.clicked.connect(lambda: self.copy_guide_to_clipboard(content))
            
            # 查看完整文档按钮
            doc_btn = QPushButton("完整文档")
            doc_btn.setFixedSize(100, 36)
            doc_btn.clicked.connect(self.open_full_documentation)
            
            # 关闭按钮
            close_btn = QPushButton("关闭")
            close_btn.setFixedSize(100, 36)
            close_btn.clicked.connect(help_dialog.accept)
            
            buttons_layout.addWidget(copy_btn)
            buttons_layout.addWidget(doc_btn)
            buttons_layout.addStretch()
            buttons_layout.addWidget(close_btn)
            
            dialog_layout.addLayout(buttons_layout)
            
            # 显示对话框
            help_dialog.exec()
            
        except Exception as e:
            print(f"显示GPU帮助窗口出错: {e}")
            # 如果出错，显示简单的消息框
            QMessageBox.information(self, "GPU配置指南", self._get_fallback_help_content())
    
    def get_gpu_help_content(self):
        """获取GPU帮助内容"""
        try:
            # 尝试从专门的帮助指南模块获取内容
            from core.gpu_help_guide import GPUHelpGuide
            return GPUHelpGuide.get_help_content_html(self.gpu_is_available, self.gpu_name)
        except ImportError:
            # 如果模块不可用，使用后备内容
            return self._get_fallback_help_content()
    
    def _get_fallback_help_content(self):
        """获取后备的GPU帮助内容"""
        if self.gpu_is_available:
            return f"""
                <h3>GPU已成功配置!</h3>
                <p>检测到: {self.gpu_name}</p>
                <p>您的系统已经正确配置了GPU加速。</p>
                <p>以下是一些提示，帮助您获得最佳性能:</p>
                <ul>
                    <li>确保在处理大型视频时，电脑有足够的散热</li>
                    <li>关闭其他使用GPU的应用程序以提供更多资源</li>
                    <li>定期更新显卡驱动以获得最佳性能和兼容性</li>
        </ul>
            """
        else:
            return f"""
                <h3>GPU配置指南</h3>
                <p>当前状态: 未检测到可用GPU或配置不正确</p>
                <p>要启用GPU加速，请按照以下步骤操作:</p>
                <ol>
                    <li>确保您的电脑有支持CUDA的NVIDIA显卡</li>
                    <li>安装最新的NVIDIA显卡驱动</li>
                    <li>安装CUDA Toolkit 11.7或更新版本</li>
                    <li>确保系统环境变量正确设置</li>
                    <li>重新启动应用程序</li>
                </ol>
                <p>如果您使用的是AMD或Intel显卡，请参考完整文档了解替代加速方法。</p>
        """
    
    def open_full_documentation(self):
        """打开完整文档"""
        try:
            # 尝试使用系统默认浏览器打开本地文档文件
            # 这里优先检查几个可能的文档路径
            doc_paths = [
                os.path.join(os.path.dirname(__file__), "..", "docs", "gpu_configuration_guide.md"),
                os.path.join(os.path.dirname(__file__), "..", "docs", "GPU_CONFIGURATION_GUIDE.md"),
                os.path.join(os.path.dirname(__file__), "..", "docs", "gpu_guide.md"),
                os.path.join(os.path.dirname(__file__), "..", "docs", "GPU_GUIDE.md")
            ]
            
            found = False
            for path in doc_paths:
                if os.path.exists(path):
                    # 使用系统默认程序打开文档
                    import subprocess
                    import platform
                    
                    if platform.system() == 'Windows':
                        os.startfile(path)
                    elif platform.system() == 'Darwin':  # macOS
                        subprocess.run(['open', path])
                    else:  # Linux
                        subprocess.run(['xdg-open', path])
                    
                    found = True
                    break
            
            if not found:
                # 如果找不到文档文件，显示一个消息框
                QMessageBox.information(
                    self,
                    "文档不可用",
                    "无法找到GPU配置指南文档文件。请联系开发者获取帮助。"
                )
        except Exception as e:
            print(f"打开文档出错: {e}")
            QMessageBox.warning(
                self,
                "打开文档失败",
                f"无法打开文档: {e}"
            )
    
    def copy_guide_to_clipboard(self, content):
        """复制指南内容到剪贴板"""
        try:
            clipboard = QApplication.clipboard()
            clipboard.setText(content)
            QMessageBox.information(self, "复制成功", "GPU配置指南已复制到剪贴板")
        except Exception as e:
            print(f"复制到剪贴板出错: {e}")
    
    def create_subtitle_edit(self):
        """创建字幕编辑区域 - 使用QFrame作为背景容器"""
        # 创建容器组件
        edit_container = QWidget()
        edit_container.setFixedHeight(745)  # 增加高度，以容纳底部的GPU状态区域
        
        # 容器主布局
        container_layout = QVBoxLayout(edit_container)
        container_layout.setContentsMargins(0, 0, 0, 0)
        container_layout.setSpacing(0)
        
        # 创建背景Frame - 黑色背景，圆角30px
        background_frame = QFrame()
        background_frame.setFixedHeight(745)  # 与容器同高
        background_frame.setStyleSheet("""
            QFrame {
                background-color: #14161A;
                border-radius: 30px;
                border: none;
            }
        """)
        
        # Frame内部布局
        frame_layout = QVBoxLayout(background_frame)
        frame_layout.setContentsMargins(15, 15, 15, 15)
        frame_layout.setSpacing(10)
        
        # 字幕编辑区标题
        title_layout = QHBoxLayout()
        title_layout.setContentsMargins(5, 0, 5, 0)
        title_layout.setSpacing(10)
        
        # 左侧标题
        title_label = QLabel("字幕编辑")
        title_label.setStyleSheet("""
            QLabel {
                color: #FFFFFF;
                font-size: 18px;
                font-weight: bold;
                background-color: transparent;
                border: none;
            }
        """)
        title_layout.addWidget(title_label)
        
        title_layout.addStretch()
        
        # 右侧字幕计数
        subtitle_count_label = QLabel("# 开始时间 结束时间 原文")
        subtitle_count_label.setStyleSheet("""
            QLabel {
                color: #9CA3AF;
                font-size: 12px;
                background-color: transparent;
                border: none;
            }
        """)
        title_layout.addWidget(subtitle_count_label)
        
        frame_layout.addLayout(title_layout)
        
        # 创建选项卡布局
        tab_layout = QHBoxLayout()
        tab_layout.setContentsMargins(0, 0, 0, 0)
        tab_layout.setSpacing(0)
        
        # 添加选项卡按钮
        tab_buttons = [
            ("添加字幕", "#3498db"),
            ("删除字幕", "#e74c3c"),
            ("合并字幕", "#f39c12")
        ]
        
        for text, color in tab_buttons:
            btn = QPushButton(text)
            btn.setFixedHeight(36)
            btn.setCursor(Qt.PointingHandCursor)
            btn.setStyleSheet(f"""
                QPushButton {{
                    background-color: {color};
                    color: #FFFFFF;
                    font-size: 14px;
                    font-weight: bold;
                    border: none;
                    border-top-left-radius: 8px;
                    border-top-right-radius: 8px;
                    padding: 8px 15px;
                }}
                QPushButton:hover {{
                    background-color: {color}99;
                }}
            """)
            tab_layout.addWidget(btn)
        
        tab_layout.addStretch(1)
        frame_layout.addLayout(tab_layout)
        
        # 创建字幕内容表格
        subtitle_table = QTableWidget()
        subtitle_table.setColumnCount(4)
        subtitle_table.setHorizontalHeaderLabels(["#", "开始时间", "结束时间", "原文"])
        
        # 样式设置
        subtitle_table.setStyleSheet("""
            QTableWidget {
                background-color: #1B1E24;
                border: 1px solid #333333;
                color: #FFFFFF;
                gridline-color: #333333;
                outline: none;
            }
            QTableWidget::item {
                padding: 5px;
                border-bottom: 1px solid #333333;
            }
            QTableWidget::item:selected {
                background-color: #2B9D7C;
                color: #FFFFFF;
            }
            QHeaderView::section {
                background-color: #2A2A2A;
                color: #FFFFFF;
                padding: 5px;
                border: 1px solid #333333;
                font-weight: bold;
            }
            QScrollBar:vertical {
                background: #1B1E24;
                width: 10px;
                margin: 0px;
            }
            QScrollBar::handle:vertical {
                background: #2B9D7C;
                min-height: 20px;
                border-radius: 5px;
            }
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
                height: 0px;
            }
        """)
        
        # 设置列宽
        subtitle_table.setColumnWidth(0, 50)   # #列
        subtitle_table.setColumnWidth(1, 100)  # 开始时间列
        subtitle_table.setColumnWidth(2, 100)  # 结束时间列
        subtitle_table.setColumnWidth(3, 650)  # 原文列
        
        # 添加示例数据
        for i in range(10):
            subtitle_table.insertRow(i)
            subtitle_table.setItem(i, 0, QTableWidgetItem(str(i+1)))
            subtitle_table.setItem(i, 1, QTableWidgetItem(f"00:0{i}:00"))
            subtitle_table.setItem(i, 2, QTableWidgetItem(f"00:0{i}:05"))
            subtitle_table.setItem(i, 3, QTableWidgetItem(f"这是第{i+1}条示例字幕内容"))
        
        frame_layout.addWidget(subtitle_table, 1)  # 1是拉伸因子，使表格占用所有可用空间
        
        # GPU状态区域 - 放在底部
        gpu_layout = QHBoxLayout()
        gpu_layout.setContentsMargins(5, 5, 5, 5)
        gpu_layout.setSpacing(10)
        
        # GPU状态标签
        self.gpu_status_label = QLabel("GPU: 检测中...")
        self.gpu_status_label.setStyleSheet("""
            QLabel {
                color: #AAAAAA;
                font-size: 12px;
                background-color: transparent;
                border: none;
            }
        """)
        gpu_layout.addWidget(self.gpu_status_label)
        
        # GPU使用情况标签
        self.gpu_usage_label = QLabel("正在检测GPU...")
        self.gpu_usage_label.setStyleSheet("""
            QLabel {
                color: #AAAAAA;
                font-size: 12px;
                background-color: transparent;
                border: none;
            }
        """)
        gpu_layout.addWidget(self.gpu_usage_label)
        
        gpu_layout.addStretch()
        
        # GPU帮助按钮
        gpu_help_btn = QPushButton("配置帮助")
        gpu_help_btn.setFixedSize(80, 28)
        gpu_help_btn.setStyleSheet("""
            QPushButton {
                background-color: #555555;
                color: #FFFFFF;
                font-size: 12px;
                border: none;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #666666;
            }
            QPushButton:pressed {
                background-color: #444444;
            }
        """)
        # gpu_help_btn.clicked.connect(self.show_gpu_help)
        gpu_layout.addWidget(gpu_help_btn)
        
        frame_layout.addLayout(gpu_layout)
        
        # 将背景Frame添加到容器布局中，并水平居中
        container_layout.addWidget(background_frame)
        
        # 返回容器组件
        return edit_container


class APISettingsArea(QWidget):
    """
    API设置区域
    支持不同宽度的API配置界面，可用于右侧区域或全宽显示
    """
    def __init__(self, width=612):
        super().__init__()
        self.area_width = width  # 支持自定义宽度
        self.setFixedSize(width, 776)
        self.setup_ui()
    
    def setup_ui(self):
        """初始化API设置区域UI"""
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # 创建背景Frame - 深灰色背景，圆角30px
        background_frame = QFrame()
        background_frame.setFixedSize(self.area_width, 776)
        background_frame.setStyleSheet(StyleManager.get_frame_style())
        
        # Frame内部布局
        frame_layout = QVBoxLayout(background_frame)
        # 根据宽度调整内边距
        margin = 40 if self.area_width > 800 else 20
        frame_layout.setContentsMargins(margin, 20, margin, 20)
        frame_layout.setSpacing(15)
        
        # 顶部状态显示区域
        status_area = self.create_status_area()
        frame_layout.addWidget(status_area)
        
        # API设置区域
        api_settings = self.create_api_settings()
        frame_layout.addWidget(api_settings)
        
        # 将背景Frame添加到主布局
        main_layout.addWidget(background_frame)
    
    def create_status_area(self):
        """创建顶部状态显示区域"""
        # 根据宽度决定是否显示状态区域
        if self.area_width <= 800:
            # 窄版本：显示简化的状态区域
            status_container = QWidget()
            status_container.setFixedHeight(50)
            
            status_layout = QVBoxLayout(status_container)
            status_layout.setContentsMargins(30, 30, 30, 30)
            status_layout.setSpacing(10) #减小间距
            
            # 当前选择标题
            title_label = QLabel("当前选择：")
            title_label.setStyleSheet(StyleManager.get_label_style(font_size=20, color='#FFFFFF'))
            title_label.setProperty("font-weight", "bold")
            status_layout.addWidget(title_label)
            
            # 状态信息布局
            status_info_layout = QVBoxLayout()
            status_info_layout.setSpacing(8)
            
            # 语音识别状态
            speech_layout = QHBoxLayout()
            speech_label = QLabel("语音识别：")
            speech_label.setFixedWidth(100)
            speech_label.setStyleSheet(StyleManager.get_label_style(font_size=16))
            speech_value = QLabel("WhisperX（本地）")
            speech_value.setStyleSheet(StyleManager.get_label_style(color='#2B9D7C', font_size=16))
            speech_layout.addWidget(speech_label)
            speech_layout.addWidget(speech_value)
            speech_layout.addStretch()
            
            # 翻译渠道状态
            translate_layout = QHBoxLayout()
            translate_label = QLabel("翻译渠道：")
            translate_label.setFixedWidth(100)
            translate_label.setStyleSheet(StyleManager.get_label_style(font_size=16))
            translate_value = QLabel("deepl pro")
            translate_value.setStyleSheet(StyleManager.get_label_style(color='#2B9D7C', font_size=16))
            translate_layout.addWidget(translate_label)
            translate_layout.addWidget(translate_value)
            translate_layout.addStretch()
            
            # TTS配音状态
            tts_layout = QHBoxLayout()
            tts_label = QLabel("TTS配音：")
            tts_label.setFixedWidth(100)
            tts_label.setStyleSheet(StyleManager.get_label_style(font_size=16))
            tts_value = QLabel("Azure TTS")
            tts_value.setStyleSheet(StyleManager.get_label_style(color='#2B9D7C', font_size=16))
            tts_layout.addWidget(tts_label)
            tts_layout.addWidget(tts_value)
            tts_layout.addStretch()
            
            status_info_layout.addLayout(speech_layout)
            status_info_layout.addLayout(translate_layout)
            status_info_layout.addLayout(tts_layout)
            
            status_layout.addLayout(status_info_layout)
            
            return status_container
        else:
            # 宽版本：显示带黑色Frame的状态区域
            # 创建状态区域容器
            status_container = QWidget()
            status_container.setFixedHeight(210)
            
            # 状态区域主布局
            container_layout = QVBoxLayout(status_container)
            container_layout.setContentsMargins(0, 0, 0, 0)
            container_layout.setSpacing(0)
            
            # 创建黑色背景Frame
            status_frame = QFrame()
            status_frame.setFixedSize(self.area_width - 80, 210)  # 减去左右边距
            status_frame.setStyleSheet("""
                QFrame {
                    background-color: #14161A;
                    border-radius: 30px;
                    border: none;
                }
            """)
            
            # Frame内部布局
            frame_layout = QVBoxLayout(status_frame)
            frame_layout.setContentsMargins(40, 0, 40,25)  # 调整边距
            frame_layout.setSpacing(10)
            
            # 顶部标题区域
            title_container = QWidget()
            title_layout = QHBoxLayout(title_container)
            title_layout.setContentsMargins(0, 0, 0, 0)
            
            # 主标题
            title_label = QLabel("当前配置状态")
            title_label.setStyleSheet("""
                QLabel {
                    color: #FFFFFF;
                    font-size: 22px;
                    font-weight: bold;
                    background-color: transparent;
                    border: none;
                }
            """)
            
            # 状态指示器
            status_indicator = QLabel("●")
            status_indicator.setStyleSheet("""
                QLabel {
                    color: #2B9D7C;
                    font-size: 20px;
                    background-color: transparent;
                    border: none;
                }
            """)
            
            title_layout.addWidget(title_label)
            title_layout.addWidget(status_indicator)
            title_layout.addStretch()
            
            frame_layout.addWidget(title_container)
            
            # 状态卡片容器
            cards_container = QWidget()
            cards_layout = QHBoxLayout(cards_container)
            cards_layout.setContentsMargins(0, 0, 0, 0)
            cards_layout.setSpacing(20)
            
            # 语音识别卡片
            speech_card = self.create_status_card("🎤", "语音识别", "WhisperX（本地）", "#FF6B6B")
            cards_layout.addWidget(speech_card)
            
            # 翻译渠道卡片
            translate_card = self.create_status_card("🌐", "翻译渠道", "DeepL Pro", "#4ECDC4")
            cards_layout.addWidget(translate_card)
            
            # TTS配音卡片
            tts_card = self.create_status_card("🔊", "TTS配音", "Azure TTS", "#45B7D1")
            cards_layout.addWidget(tts_card)
            
            frame_layout.addWidget(cards_container)
            
            # 将Frame添加到容器布局中，并水平居中
            container_layout.addWidget(status_frame, 0, Qt.AlignCenter)
            
            return status_container
    
    def create_status_card(self, icon, title, value, accent_color):
        """创建美观的状态卡片"""
        card = QFrame()
        card.setFixedSize(320, 100)
        card.setStyleSheet(f"""
            QFrame {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(42, 42, 42, 0.9), 
                    stop:0.5 rgba(35, 35, 35, 0.8), 
                    stop:1 rgba(27, 30, 36, 0.9));
                border: 0px solid rgba(255, 255, 255, 0.1);
                border-radius: 15px;
            }}
        """)
        
        card_layout = QHBoxLayout(card)
        card_layout.setContentsMargins(15, 12, 15, 12)
        card_layout.setSpacing(12)
        
        # 左侧图标区域
        icon_container = QFrame()
        icon_container.setFixedSize(50, 50)
        icon_container.setStyleSheet(f"""
            QFrame {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {accent_color}, 
                    stop:1 rgba({StyleManager.hex_to_rgb(accent_color)}, 0.7));
                border-radius: 25px;
                border: none;
            }}
        """)
        
        icon_layout = QVBoxLayout(icon_container)
        icon_layout.setContentsMargins(0, 0, 0, 0)
        
        icon_label = QLabel(icon)
        icon_label.setStyleSheet(StyleManager.get_label_style(color='#FFFFFF', font_size=24))
        icon_label.setAlignment(Qt.AlignCenter)
        icon_layout.addWidget(icon_label)
        
        card_layout.addWidget(icon_container)
        
        # 右侧文本区域
        text_container = QWidget()
        text_layout = QVBoxLayout(text_container)
        text_layout.setContentsMargins(0, 0, 0, 0)
        text_layout.setSpacing(2)
        
        # 标题
        title_label = QLabel(title)
        title_label.setStyleSheet(StyleManager.get_label_style(color='rgba(255, 255, 255, 0.7)', font_size=14))
        
        # 值
        value_label = QLabel(value)
        value_label.setStyleSheet(StyleManager.get_label_style(color=accent_color, font_size=16))
        value_label.setProperty("font-weight", "bold")
        
        text_layout.addWidget(title_label)
        text_layout.addWidget(value_label)
        
        card_layout.addWidget(text_container)
        
        return card

    def create_api_settings(self):
        """创建API设置区域"""
        settings_container = QWidget()
        
        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setStyleSheet("""
            QScrollArea {
                background-color: #1B1E24;
                border: none;
            }
            QScrollBar:vertical {
                background-color: #14161A; /* 设置垂直滚动条的背景颜色为 #14161A (一种深灰色) */
                width: 0px;                /* 将垂直滚动条的宽度设置为0像素，使其在视觉上不可见，达到隐藏滚动条的效果 */
                /* background-color: #14161A; */ /* 此为重复设置，通常在同一规则中，属性的最后一次声明生效，这条可以移除 */
            }
            QScrollBar::handle:vertical {
                background-color: #14161A;
            }
            QScrollBar::add-line:vertical {
                background-color: #14161A; /* 设置滚动条的添加线（即滚动条的末端）的背景颜色为 #14161A */
            }
            QScrollBar::sub-line:vertical {
                background-color: #14161A;
            }
        """)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        
        # 滚动内容
        scroll_content = QWidget()
        scroll_content.setStyleSheet("""
            QWidget {
                background-color: #1B1E24;
            }
        """)
        scroll_layout = QVBoxLayout(scroll_content)
        scroll_layout.setSpacing(15)  # 减小卡片间距
        scroll_layout.setAlignment(Qt.AlignTop | Qt.AlignHCenter)  # 顶部和水平居中对齐
        scroll_layout.setContentsMargins(0, 10, 0, 20)  # 不设置左右边距，让卡片自己居中
        
        # OpenAI API设置卡片
        openai_section = self.create_modern_setting_section(
            "🤖 OpenAI API设置",
            "配置OpenAI API密钥和服务地址",
            [
                ("API密钥", "sk-...", "请输入您的OpenAI API密钥"),
                ("API基础URL", "https://api.openai.com", "可选，自定义API服务地址")
            ],
            "#FF6B6B"
        )
        scroll_layout.addWidget(openai_section)
        
        # Azure TTS设置卡片
        azure_section = self.create_azure_tts_setting_section()
        scroll_layout.addWidget(azure_section)
        
        # DeepL API设置卡片
        deepl_section = self.create_modern_setting_section(
            "🌐 DeepL API设置",
            "配置DeepL翻译API密钥",
            [
                ("API密钥", "", "请输入DeepL API密钥")
            ],
            "#4ECDC4"
        )
        scroll_layout.addWidget(deepl_section)
        
        # Google API设置卡片
        google_section = self.create_modern_setting_section(
            "📱 Google API设置",
            "配置Google翻译API密钥",
            [
                ("API密钥", "", "请输入Google API密钥")
            ],
            "#FFA726"
        )
        scroll_layout.addWidget(google_section)
        
        scroll_area.setWidget(scroll_content)
        
        # 设置滚动区域布局
        container_layout = QVBoxLayout(settings_container)
        container_layout.setContentsMargins(0, 0, 0, 0)
        container_layout.addWidget(scroll_area)
        
        return settings_container
    
    def create_azure_tts_setting_section(self):
        """创建Azure TTS设置卡片（支持自动保存）"""
        try:
            from core.config_manager import get_config_manager
            config_manager = get_config_manager()
        except ImportError:
            config_manager = None
        
        # 从配置中加载当前值
        current_key = config_manager.get("api_keys.azure_tts_key", "") if config_manager else ""
        current_region = config_manager.get("api_keys.azure_tts_region", "eastus") if config_manager else "eastus"
        
        accent_color = "#45B7D1"
        
        # 主卡片容器
        section_card = QFrame()
        card_width = self.area_width - 80
        section_card.setFixedWidth(card_width)
        section_card.setStyleSheet(f"""
            QFrame {{
                background-color: #14161A;
                border: 0px solid rgba(255, 255, 255, 0.1);
                border-radius: 30px;
                margin: 0px;
            }}
            QFrame:hover {{
                border: 0px solid rgba({StyleManager.hex_to_rgb(accent_color)}, 0.4);
            }}
        """)
        
        card_layout = QVBoxLayout(section_card)
        card_layout.setContentsMargins(25, 20, 25, 20)
        card_layout.setSpacing(15)
        
        # 头部区域
        header_container = QWidget()
        header_layout = QHBoxLayout(header_container)
        header_layout.setContentsMargins(0, 0, 0, 0)
        header_layout.setSpacing(15)
        
        # 左侧标题区域
        title_container = QWidget()
        header_container.setStyleSheet("background-color: #14161A;")
        title_layout = QVBoxLayout(title_container)
        title_layout.setContentsMargins(0, 0, 0, 0)
        title_layout.setSpacing(4)
        
        # 主标题
        title_label = QLabel("🔊 Azure TTS设置")
        title_label.setStyleSheet(f"""
            QLabel {{
                color: {accent_color};
                font-size: 18px;
                font-weight: bold;
                background-color: transparent;
                border: none;
            }}
        """)
        
        # 描述文字
        desc_label = QLabel("配置Azure TTS服务密钥和区域")
        desc_label.setStyleSheet("""
            QLabel {
                color: rgba(255, 255, 255, 0.6);
                font-size: 13px;
                background-color: #14161A;
                border: none;
            }
        """)
        desc_label.setWordWrap(False)
        
        title_layout.addWidget(title_label)
        title_layout.addWidget(desc_label)
        
        header_layout.addWidget(title_container)
        header_layout.addStretch()
        
        card_layout.addWidget(header_container)
        
        # 分隔线
        separator = QFrame()
        separator.setFixedHeight(1)
        separator.setStyleSheet(f"""
            QFrame {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 transparent, 
                    stop:0.2 rgba({StyleManager.hex_to_rgb(accent_color)}, 0.4), 
                    stop:0.8 rgba({StyleManager.hex_to_rgb(accent_color)}, 0.4), 
                    stop:1 transparent);
                border: none;
                border-radius: 1px;
            }}
        """)
        card_layout.addWidget(separator)
        
        # 设置项容器
        settings_container = QWidget()
        settings_container.setStyleSheet("background-color: #14161A;")
        settings_layout = QVBoxLayout(settings_container)
        settings_layout.setContentsMargins(0, 0, 0, 0)
        settings_layout.setSpacing(12)
        
        # Azure TTS密钥输入
        key_field = self.create_smart_input_field(
            "Azure TTS密钥", 
            current_key, 
            "请输入Azure TTS服务密钥", 
            accent_color,
            lambda value: self.save_azure_config("key", value) if config_manager else None
        )
        settings_layout.addWidget(key_field)
        
        # Azure 区域输入
        region_field = self.create_smart_input_field(
            "Azure 区域", 
            current_region, 
            "请输入Azure服务区域", 
            accent_color,
            lambda value: self.save_azure_config("region", value) if config_manager else None
        )
        settings_layout.addWidget(region_field)
        
        card_layout.addWidget(settings_container)
        
        return section_card
    
    def create_smart_input_field(self, label_text, default_value, placeholder, accent_color, save_callback):
        """创建支持自动保存的智能输入字段"""
        field_container = QWidget()
        
        field_layout = QVBoxLayout(field_container)
        field_layout.setContentsMargins(0, 0, 0, 0)
        field_layout.setSpacing(6)
        
        # 字段标签
        label = QLabel(label_text)
        label.setStyleSheet(StyleManager.get_label_style(color='rgba(255, 255, 255, 0.9)', bg_color='#14161A'))
        label.setProperty("font-weight", "500")
        field_layout.addWidget(label)
        
        # 输入框容器
        input_container = QFrame()
        input_container.setFixedHeight(45)
        input_container.setStyleSheet(f"""
            QFrame {{
                background: #1B1E24;
                border: 1px solid rgba(255, 255, 255, 0.1);
                border-radius: 12px;
            }}
            QFrame:hover {{
                border: 2px solid rgba({StyleManager.hex_to_rgb(accent_color)}, 0.4);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(25, 25, 25, 0.9), 
                    stop:1 rgba(30, 30, 30, 0.9));
            }}
        """)
        
        input_layout = QHBoxLayout(input_container)
        input_layout.setContentsMargins(12, 0, 12, 0)
        
        # 输入框
        input_field = QLineEdit(default_value)
        input_field.setPlaceholderText(placeholder)
        input_field.setStyleSheet(f"""
            QLineEdit {{
                background-color: #1B1E24;
                color: #FFFFFF;
                font-size: 14px;
                border: none;
                padding: 6px 0px;
            }}
            QLineEdit::placeholder {{
                color: rgba(255, 255, 255, 0.4);
            }}
            QLineEdit:focus {{
                color: {accent_color};
            }}
        """)
        
        # 连接自动保存信号
        if save_callback:
            input_field.textChanged.connect(save_callback)
        
        input_layout.addWidget(input_field)
        field_layout.addWidget(input_container)
        
        return field_container
    
    def save_azure_config(self, key_type, value):
        """保存Azure TTS配置"""
        try:
            from core.config_manager import get_config_manager
            config_manager = get_config_manager()
            success = config_manager.update_api_key("azure_tts", key_type, value)
            if success:
                print(f"Azure TTS {key_type} 配置已保存: {'***' if key_type == 'key' and value else value}")
        except ImportError:
            print("配置管理器不可用，无法保存配置")

    def create_modern_setting_section(self, title, description, settings, accent_color):
        """创建现代化的设置卡片"""
        # 主卡片容器
        section_card = QFrame()
        # 设置固定宽度，与状态区域保持一致
        card_width = self.area_width - 80  # 与状态Frame宽度一致
        section_card.setFixedWidth(card_width)
        section_card.setStyleSheet(f"""
            QFrame {{
                background-color: #14161A;
                border: 0px solid rgba(255, 255, 255, 0.1);
                border-radius: 30px;
                margin: 0px;
            }}
            QFrame:hover {{
                border: 0px solid rgba({StyleManager.hex_to_rgb(accent_color)}, 0.4);
            }}
        """)
        
        card_layout = QVBoxLayout(section_card)
        card_layout.setContentsMargins(25, 20, 25, 20)  # 减小内边距
        card_layout.setSpacing(15)  # 减小间距
        
        # 头部区域
        header_container = QWidget()
        header_layout = QHBoxLayout(header_container)
        header_layout.setContentsMargins(0, 0, 0, 0)
        header_layout.setSpacing(15)
        
        # 左侧标题区域
        title_container = QWidget()
        header_container.setStyleSheet("background-color: #14161A;")
        title_layout = QVBoxLayout(title_container)
        title_layout.setContentsMargins(0, 0, 0, 0)
        title_layout.setSpacing(4)  # 减小标题和描述间距
        
        # 主标题
        title_label = QLabel(title)
        title_label.setStyleSheet(f"""
            QLabel {{
                color: {accent_color};
                font-size: 18px;
                font-weight: bold;
                background-color: transparent;
                border: none;
            }}
        """)
        
        # 描述文字
        desc_label = QLabel(description)
        desc_label.setStyleSheet("""
            QLabel {
                color: rgba(255, 255, 255, 0.6);
                font-size: 13px;
                background-color: #14161A;
                border: none;
            }
        """)
        desc_label.setWordWrap(False)  # 禁用自动换行，保持一行显示
        
        title_layout.addWidget(title_label)
        title_layout.addWidget(desc_label)
        
        header_layout.addWidget(title_container)
        header_layout.addStretch()
        
        card_layout.addWidget(header_container)
        
        # 分隔线
        separator = QFrame()
        separator.setFixedHeight(1)  # 减小分隔线高度
        separator.setStyleSheet(f"""
            QFrame {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 transparent, 
                    stop:0.2 rgba({StyleManager.hex_to_rgb(accent_color)}, 0.4), 
                    stop:0.8 rgba({StyleManager.hex_to_rgb(accent_color)}, 0.4), 
                    stop:1 transparent);
                border: none;
                border-radius: 1px;
            }}
        """)
        card_layout.addWidget(separator)
        
        # 设置项容器
        settings_container = QWidget()
        settings_container.setStyleSheet("background-color: #14161A;")
        settings_layout = QVBoxLayout(settings_container)
        settings_layout.setContentsMargins(0, 0, 0, 0)
        settings_layout.setSpacing(12)  # 减小设置项间距
        
        # 创建设置项
        for field_name, default_value, placeholder in settings:
            field_container = self.create_modern_input_field(field_name, default_value, placeholder, accent_color)
            settings_layout.addWidget(field_container)
        
        card_layout.addWidget(settings_container)
        
        return section_card
    
    def create_modern_input_field(self, label_text, default_value, placeholder, accent_color):
        """创建现代化的输入字段"""
        field_container = QWidget()
        
        field_layout = QVBoxLayout(field_container)
        field_layout.setContentsMargins(0, 0, 0, 0)
        field_layout.setSpacing(6)  # 减小标签和输入框间距
        
        # 字段标签
        label = QLabel(label_text)
        label.setStyleSheet(StyleManager.get_label_style(color='rgba(255, 255, 255, 0.9)', bg_color='#14161A'))
        label.setProperty("font-weight", "500")
        field_layout.addWidget(label)
        
        # 输入框容器
        input_container = QFrame()
        input_container.setFixedHeight(45)  # 减小输入框高度
        input_container.setStyleSheet(f"""
            QFrame {{
                background: #1B1E24; /* 设置输入框的背景颜色为 #14161A */
                border: 1px solid rgba(255, 255, 255, 0.1);
                border-radius: 12px;
            }}
            QFrame:hover {{
                border: 2px solid rgba({StyleManager.hex_to_rgb(accent_color)}, 0.4);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(25, 25, 25, 0.9), 
                    stop:1 rgba(30, 30, 30, 0.9));
            }}
        """)
        
        input_layout = QHBoxLayout(input_container)
        input_layout.setContentsMargins(12, 0, 12, 0)  # 减小内边距
        
        # 输入框
        input_field = QLineEdit(default_value)
        
        input_field.setPlaceholderText(placeholder)
        input_field.setStyleSheet(f"""
            QLineEdit {{
                background-color: #1B1E24;
                color: #FFFFFF;
                font-size: 14px;
                border: none;
                padding: 6px 0px;
            }}
            QLineEdit::placeholder {{
                color: rgba(255, 255, 255, 0.4);
            }}
            QLineEdit:focus {{
                color: {accent_color};/* 设置输入框聚焦时的文字颜色为 accent_color */
            }}
        """)
        
        input_layout.addWidget(input_field)
        
        field_layout.addWidget(input_container)
        
        return field_container


class FlipTalkMainWindow(QMainWindow):
    """
    FlipTalk AI主窗口
    整合所有UI组件，提供完整的应用界面
    """
    def __init__(self):
        super().__init__()
        self.setWindowTitle("FlipTalk AI - 视频翻译软件")
        self.setFixedSize(1440, 800)  # 设置窗口固定尺寸
        
        # 初始化动画对象和状态
        self.slide_animation = None
        self.is_animating = False  # 动画状态锁
        self.current_page = "首页"  # 当前页面状态
        
        # 初始化GPU检测器
        self.gpu_is_available = False
        self.gpu_name = "检测中..."
        try:
            from core.gpu_detector import GPUDetector
            self.gpu_detector = GPUDetector()
            self.gpu_detector.gpu_status_updated.connect(self.on_gpu_status_updated)
        except Exception as e:
            print(f"GPU检测器初始化失败: {e}")
        
        self.setup_ui()
        self.setup_style()
        
    def on_gpu_status_updated(self, is_available: bool, gpu_name: str, status_text: str):
        """GPU状态更新回调"""
        self.gpu_is_available = is_available
        self.gpu_name = gpu_name
        # 可以在此处添加更多GPU状态更新相关的逻辑
        print(f"GPU状态更新: 可用={is_available}, 名称={gpu_name}, 状态={status_text}")
    
    def create_slide_animation(self, widget, direction):
        """
        创建滑入动画
        widget: 要执行动画的组件
        direction: 滑入方向 ('left', 'right', 'top', 'bottom')
        """
        # 设置动画状态锁
        self.is_animating = True
        
        if self.slide_animation:
            self.slide_animation.stop()
            self.slide_animation.deleteLater()
        
        # 获取组件的最终位置
        final_geometry = widget.geometry()
        
        # 根据滑入方向设置起始位置
        start_geometry = QRect(final_geometry)
        
        if direction == 'left':
            # 从左侧滑入
            start_geometry.moveLeft(-final_geometry.width())
        elif direction == 'right':
            # 从右侧滑入
            start_geometry.moveLeft(self.width())
        elif direction == 'top':
            # 从顶部滑入
            start_geometry.moveTop(-final_geometry.height())
        elif direction == 'bottom':
            # 从底部滑入
            start_geometry.moveTop(self.height())
        
        # 设置起始位置
        widget.setGeometry(start_geometry)
        widget.show()
        
        # 创建动画
        self.slide_animation = QPropertyAnimation(widget, b"geometry")
        self.slide_animation.setDuration(400)  # 动画持续时间400毫秒，从600ms减少到400ms
        self.slide_animation.setStartValue(start_geometry)
        self.slide_animation.setEndValue(final_geometry)
        
        # 设置缓动曲线，让动画更自然
        self.slide_animation.setEasingCurve(QEasingCurve.OutCubic)
        
        # 添加动画完成回调
        self.slide_animation.finished.connect(self.on_animation_finished)
        
        # 启动动画
        self.slide_animation.start()
    
    def on_animation_finished(self):
        """动画完成回调，重置动画状态"""
        self.is_animating = False
        if self.slide_animation:
            self.slide_animation.deleteLater()
            self.slide_animation = None
    
    def get_random_slide_direction(self):
        """
        随机获取滑入方向
        返回值: 'left', 'right', 'top', 'bottom' 中的一个
        """
        directions = ['left', 'right', 'top', 'bottom']
        return random.choice(directions)
    
    def setup_ui(self):
        """初始化主窗口UI"""
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QHBoxLayout(central_widget)
        # 修改：减小主布局的内边距，从(12, 12, 12, 12)改为(8, 8, 8, 8)
        main_layout.setContentsMargins(3, 3, 3, 3)
        # 修改：减小组件之间的间距，从10改为5
        main_layout.setSpacing(5)
        
        # 左侧导航栏
        self.nav_bar = NavigationBar()
        main_layout.addWidget(self.nav_bar)
        
        # 右侧内容区域容器
        self.right_area_container = QWidget()
        self.right_area_container.setFixedSize(1324, 776)
        
        # 内容区域布局
        self.right_area_layout = QHBoxLayout(self.right_area_container)
        # 设置右侧区域内边距
        self.right_area_layout.setContentsMargins(0, 0, 0, 0)
        # 增加组件之间的间距，为视频区域留出足够空间
        self.right_area_layout.setSpacing(10)
        
        # 添加右侧区域容器到主布局
        main_layout.addWidget(self.right_area_container)
        
        # 连接导航栏的页面切换信号
        self.nav_bar.page_changed.connect(self.on_page_changed)
        
        # 首页三个主要区域组件
        self.create_main_areas()
        
        # 其他全宽区域组件（API设置、功能、系统设置、日志等）
        self.create_full_width_areas()
        
        # 显示首页
        self.show_home_page()
        
        # 设置窗口样式
        self.setStyleSheet("""
            QMainWindow {
                background-color: #121419;
            }
        """)

    def create_main_areas(self):
        """创建主要区域：参数设置、字幕编辑"""
        print("创建主要区域...")
        
        # 创建主内容区域
        self.main_content = QWidget()
        main_layout = QHBoxLayout(self.main_content)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(15)  # 增加左侧区域和右侧字幕编辑区的间距
        
        # 创建左侧区域容器
        left_container = QWidget()
        left_layout = QVBoxLayout(left_container)
        left_layout.setContentsMargins(0, 0, 0, 0)
        left_layout.setSpacing(5)  # 减少间距，使布局更紧凑

        # 创建视频上传区域
        self.video_upload_area = VideoUploadArea()
        self.video_upload_area.setFixedWidth(450)  # 与参数设置面板保持相同宽度
        self.video_upload_area.setFixedHeight(260)  # 删除播放按钮和视频信息后进一步减少高度
        # 连接视频上传和清除信号
        self.video_upload_area.video_uploaded.connect(self.on_video_uploaded)
        self.video_upload_area.video_cleared.connect(self.on_video_cleared)
        left_layout.addWidget(self.video_upload_area)

        # 创建视频状态提示区域（位于视频上传区和参数设置区之间）
        status_container = QWidget()
        status_container.setFixedHeight(28)  # 减少容器高度，使布局更紧凑
        status_layout = QHBoxLayout(status_container)
        status_layout.setContentsMargins(0, 0, 0, 0)
        status_layout.setSpacing(8)  # 减少间距

        # 状态提示标签
        self.video_status_label = QLabel("📁 未选择文件")
        self.video_status_label.setAlignment(Qt.AlignCenter)
        self.video_status_label.setWordWrap(True)  # 允许文字换行
        self.video_status_label.setStyleSheet("""
            QLabel {
                color: #888888;
                font-size: 11px;
                background-color: transparent;
                border: none;
                padding: 2px 10px;
                margin: 0px 0px;
            }
        """)
        status_layout.addWidget(self.video_status_label, 1)  # 拉伸占据大部分空间

        # 重新上传按钮
        self.reupload_button = QPushButton("重新上传")
        self.reupload_button.setFixedSize(80, 22)  # 减少按钮高度，与紧凑布局匹配
        self.reupload_button.setEnabled(False)  # 初始状态禁用
        self.reupload_button.setStyleSheet("""
            QPushButton {
                background-color: #3A3A3A;
                color: #CCCCCC;
                border: 1px solid #555555;
                border-radius: 4px;
                font-size: 10px;
                padding: 2px 8px;
            }
            QPushButton:enabled {
                background-color: #2B9D7C;
                color: white;
                border: 1px solid #2B9D7C;
            }
            QPushButton:enabled:hover {
                background-color: #239B75;
            }
            QPushButton:enabled:pressed {
                background-color: #1E8A68;
            }
            QPushButton:disabled {
                background-color: #2A2A2A;
                color: #666666;
                border: 1px solid #444444;
            }
        """)
        self.reupload_button.clicked.connect(self.on_reupload_clicked)
        status_layout.addWidget(self.reupload_button, 0)  # 不拉伸，固定大小

        left_layout.addWidget(status_container)

        # 创建参数设置区域
        self.parameter_panel = ParameterSettingsPanel()
        self.parameter_panel.setFixedWidth(450)  # 设置固定宽度
        left_layout.addWidget(self.parameter_panel)
        
        # 创建右侧区域 - 字幕编辑
        self.subtitle_area = self.create_subtitle_edit()
        
        # 将左侧容器和字幕编辑区添加到主布局
        main_layout.addWidget(left_container)
        main_layout.addWidget(self.subtitle_area, 1)  # 添加拉伸因子，使字幕编辑区占据更多空间
        
        return self.main_content

    def on_video_uploaded(self, file_path):
        """视频上传成功回调"""
        print(f"视频文件已上传: {file_path}")

        # 示例：可以在这里触发视频分析或预处理
        try:
            import os
            file_name = os.path.basename(file_path)
            file_size = os.path.getsize(file_path)
            print(f"文件名: {file_name}")
            print(f"文件大小: {file_size / (1024*1024):.2f} MB")

            # 更新视频状态提示
            if hasattr(self, 'video_status_label'):
                # 格式化文件大小
                if file_size < 1024 * 1024:
                    size_str = f"{file_size / 1024:.1f} KB"
                elif file_size < 1024 * 1024 * 1024:
                    size_str = f"{file_size / (1024 * 1024):.1f} MB"
                else:
                    size_str = f"{file_size / (1024 * 1024 * 1024):.1f} GB"

                # 截断过长的文件名，确保能在一行显示
                display_name = file_name if len(file_name) <= 20 else file_name[:17] + "..."
                self.video_status_label.setText(f"✅ {display_name} ({size_str})")
                self.video_status_label.setStyleSheet("""
                    QLabel {
                        color: #2B9D7C;
                        font-size: 11px;
                        background-color: transparent;
                        border: none;
                        padding: 2px 10px;
                        margin: 0px 0px;
                    }
                """)

                # 激活重新上传按钮
                if hasattr(self, 'reupload_button'):
                    self.reupload_button.setEnabled(True)

            # 可以在这里添加更多逻辑，比如：
            # - 检查视频格式兼容性
            # - 预估处理时间
            # - 更新参数设置的默认值
            # - 启用开始处理按钮等

        except Exception as e:
            print(f"处理上传的视频文件时出错: {e}")
            # 如果出错，显示错误状态
            if hasattr(self, 'video_status_label'):
                self.video_status_label.setText("❌ 文件处理出错")
                self.video_status_label.setStyleSheet("""
                    QLabel {
                        color: #FF5555;
                        font-size: 12px;
                        background-color: transparent;
                        border: none;
                        padding: 3px 10px;
                        margin: 5px 0px;
                    }
                """)

    def on_video_cleared(self):
        """视频清除回调"""
        print("视频已清除")
        # 重置视频状态提示
        if hasattr(self, 'video_status_label'):
            self.video_status_label.setText("📁 未选择文件")
            self.video_status_label.setStyleSheet("""
                QLabel {
                    color: #888888;
                    font-size: 11px;
                    background-color: transparent;
                    border: none;
                    padding: 2px 10px;
                    margin: 0px 0px;
                }
            """)

            # 禁用重新上传按钮
            if hasattr(self, 'reupload_button'):
                self.reupload_button.setEnabled(False)

    def on_reupload_clicked(self):
        """重新上传按钮点击处理"""
        print("重新上传按钮被点击")
        # 立即清除当前视频预览，防止显示旧内容
        if hasattr(self, 'video_upload_area') and hasattr(self.video_upload_area, 'thumbnail_label'):
            self.video_upload_area.thumbnail_label.clear()
            self.video_upload_area.thumbnail_label.setText("选择新视频...")
            self.video_upload_area.thumbnail_label.update()
            self.video_upload_area.thumbnail_label.repaint()

        # 触发视频上传区域的重新选择
        if hasattr(self, 'video_upload_area'):
            self.video_upload_area.switch_to_upload_mode()
            # 可以选择性地触发文件选择对话框
            self.video_upload_area.select_video_file()

    def create_full_width_areas(self):
        """创建API设置、功能、系统设置和日志等全宽区域组件"""
        # API设置区域
        self.api_settings_area = APISettingsArea(width=1324)  # 使用全宽版本
        self.api_settings_area.hide()  # 初始隐藏
        
        # 功能区域
        self.function_area = FunctionArea()
        self.function_area.hide()  # 初始隐藏
        self.function_area.switch_to_audio_extraction.connect(self.switch_to_audio_extraction_mode)
        
        # 日志区域
        self.log_area = LogArea()
        self.log_area.hide()  # 初始隐藏
        
        # 系统设置区域
        self.system_settings_area = SystemSettingsArea()
        self.system_settings_area.hide()  # 初始隐藏
    
    def on_page_changed(self, page_name):
        """处理页面切换事件"""
        # 防止动画期间重复点击
        if self.is_animating:
            return
        
        # 如果是相同页面，不需要切换
        if self.current_page == page_name:
            return
        
        # 首先停止任何正在进行的动画并清理状态
        self.stop_current_animation()
        
        # 隐藏所有全宽区域
        self.hide_all_full_width_areas()
        
        if page_name == "API设置":
            # 切换到API设置界面
            self.show_api_settings_page()
        elif page_name == "功能":
            # 切换到功能界面
            self.show_function_page()
        elif page_name == "运行日志":
            # 切换到运行日志界面
            self.show_log_page()
        elif page_name == "系统设置":
            # 切换到系统设置界面
            self.show_system_settings_page()
        else:
            # 切换回首页
            self.show_home_page()
        
        # 更新当前页面状态
        self.current_page = page_name
    
    def stop_current_animation(self):
        """停止当前动画并重置状态"""
        if self.slide_animation:
            self.slide_animation.stop()
            self.slide_animation.finished.disconnect()  # 断开信号连接
            self.slide_animation.deleteLater()
            self.slide_animation = None
        self.is_animating = False
    
    def hide_all_full_width_areas(self):
        """隐藏所有全宽区域"""
        self.api_settings_area.hide()
        self.function_area.hide()
        self.log_area.hide()
        self.system_settings_area.hide()
    
    def show_api_settings_page(self):
        """显示API设置页面"""
        # 隐藏参数设置栏（非首页不显示）
        self.parameter_panel.hide()
        
        # 清空右侧区域布局中的所有组件
        for i in reversed(range(self.right_area_layout.count())): 
            widget = self.right_area_layout.itemAt(i).widget()
            if widget:
                widget.hide()
                
        # 添加API设置区域到右侧区域布局
        self.right_area_layout.addWidget(self.api_settings_area)
        self.api_settings_area.show()
        
        # 应用动画效果
        random_direction = self.get_random_slide_direction()
        self.create_slide_animation(self.api_settings_area, random_direction)
    
    def show_function_page(self):
        """显示功能页面"""
        # 隐藏参数设置栏（非首页不显示）
        self.parameter_panel.hide()
        
        # 清空右侧区域布局中的所有组件
        for i in reversed(range(self.right_area_layout.count())): 
            widget = self.right_area_layout.itemAt(i).widget()
            if widget:
                widget.hide()
                
        # 添加功能区域到右侧区域布局
        self.right_area_layout.addWidget(self.function_area)
        self.function_area.show()
        
        # 应用动画效果
        random_direction = self.get_random_slide_direction()
        self.create_slide_animation(self.function_area, random_direction)
    
    def show_home_page(self):
        """显示首页"""
        try:
            # 清空右侧区域布局中的所有组件
            for i in reversed(range(self.right_area_layout.count())): 
                widget = self.right_area_layout.itemAt(i).widget()
                if widget:
                    widget.hide()
                    self.right_area_layout.removeWidget(widget)
            
            # 添加主内容区域到右侧布局（包含了左侧容器和字幕编辑区域）
            if hasattr(self, 'main_content') and self.main_content:
                self.right_area_layout.addWidget(self.main_content)
                self.main_content.show()
                
                # 确保内部组件也显示
                if hasattr(self, 'parameter_panel') and self.parameter_panel:
                                self.parameter_panel.show()
                if hasattr(self, 'subtitle_area') and self.subtitle_area:
                                self.subtitle_area.show()
                
                print("首页组件已添加并显示")
            else:
                print("错误：主内容区域不存在")
        except Exception as e:
            print(f"显示首页出错: {e}")

    def show_log_page(self):
        """显示运行日志页面"""
        # 隐藏参数设置栏（非首页不显示）
        self.parameter_panel.hide()
        
        # 清空右侧区域布局中的所有组件
        for i in reversed(range(self.right_area_layout.count())): 
            widget = self.right_area_layout.itemAt(i).widget()
            if widget:
                widget.hide()
                
        # 添加日志区域到右侧区域布局
        self.right_area_layout.addWidget(self.log_area)
        self.log_area.show()
        
        # 应用动画效果
        random_direction = self.get_random_slide_direction()
        self.create_slide_animation(self.log_area, random_direction)
    
    def show_system_settings_page(self):
        """显示系统设置页面"""
        # 隐藏参数设置栏（非首页不显示）
        self.parameter_panel.hide()
        
        # 清空右侧区域布局中的所有组件
        for i in reversed(range(self.right_area_layout.count())): 
            widget = self.right_area_layout.itemAt(i).widget()
            if widget:
                widget.hide()
                
        # 添加系统设置区域到右侧区域布局
        self.right_area_layout.addWidget(self.system_settings_area)
        self.system_settings_area.show()
        
        # 应用动画效果
        random_direction = self.get_random_slide_direction()
        self.create_slide_animation(self.system_settings_area, random_direction)
    
    def reset_widget_geometry(self, widget):
        """重置组件几何位置到正确的布局位置"""
        # 确保组件在正确的位置
        widget.setGeometry(0, 0, widget.width(), widget.height())
    
    def reset_right_area_layout(self):
        """重置右侧区域布局"""
        try:
            # 清空右侧区域布局中的所有组件
            for i in reversed(range(self.right_area_layout.count())): 
                widget = self.right_area_layout.itemAt(i).widget()
                if widget:
                    widget.hide()
                    self.right_area_layout.removeWidget(widget)
            
            # 添加主内容区域到右侧布局（包含了左侧容器和字幕编辑区域）
            if hasattr(self, 'main_content') and self.main_content:
                self.right_area_layout.addWidget(self.main_content)
                self.main_content.show()
                
                # 确保内部组件也显示
                if hasattr(self, 'parameter_panel') and self.parameter_panel:
                                self.parameter_panel.show()
                if hasattr(self, 'subtitle_area') and self.subtitle_area:
                                self.subtitle_area.show()
                
                print("首页布局已重置")
            else:
                print("错误：主内容区域不存在")
        except Exception as e:
            print(f"重置右侧区域布局出错: {e}")
    
    def switch_to_audio_extraction_mode(self):
        """切换到音频提取模式"""
        # 如果当前不在功能页面，先切换到功能页面
        if self.current_page != "功能":
            self.nav_bar.on_nav_clicked("功能")
        
        # 模拟点击音频提取功能
        self.function_area.on_function_clicked("音频提取")
    
    def closeEvent(self, event):
        """窗口关闭事件处理，确保所有资源被释放"""
        print("窗口正在关闭，清理资源...")
        
        # 调用父类的关闭事件处理
        super().closeEvent(event)
    
    def setup_style(self):
        """设置窗口样式"""
        self.setStyleSheet("""
            QMainWindow {
                background-color: #121419;
            }
        """)
        
    def create_subtitle_edit(self):
        """创建字幕编辑区域 - 使用QFrame作为背景容器"""
        # 创建容器组件
        edit_container = QWidget()
        edit_container.setFixedHeight(745)  # 增加高度，以容纳底部的GPU状态区域
        
        # 容器主布局
        container_layout = QVBoxLayout(edit_container)
        container_layout.setContentsMargins(0, 0, 0, 0)
        container_layout.setSpacing(0)
        
        # 创建背景Frame - 黑色背景，圆角30px
        background_frame = QFrame()
        background_frame.setFixedHeight(745)  # 与容器同高
        background_frame.setStyleSheet("""
            QFrame {
                background-color: #14161A;
                border-radius: 30px;
                border: none;
            }
        """)
        
        # Frame内部布局
        frame_layout = QVBoxLayout(background_frame)
        frame_layout.setContentsMargins(15, 15, 15, 15)
        frame_layout.setSpacing(10)
        
        # 字幕编辑区标题
        title_layout = QHBoxLayout()
        title_layout.setContentsMargins(8, 0, 8, 0)
        title_layout.setSpacing(10)
        
        # 左侧标题
        title_label = QLabel("字幕编辑")
        title_label.setStyleSheet("""
            QLabel {
                color: #FFFFFF;
                font-size: 18px;
                font-weight: bold;
                background-color: transparent;
                border: none;
            }
        """)
        title_layout.addWidget(title_label)
        
        title_layout.addStretch()
        
        # 右侧字幕计数
        subtitle_count_label = QLabel("字幕条数: 0 | 双击内容编辑")
        subtitle_count_label.setStyleSheet("""
            QLabel {
                color: #9CA3AF;
                font-size: 12px;
                background-color: transparent;
                border: none;
            }
        """)
        title_layout.addWidget(subtitle_count_label)
        
        frame_layout.addLayout(title_layout)
        
        # 创建统一的字幕表格 - 六列设计（序号+开始时间+结束时间+内容+状态+操作）
        subtitles_table = QTableWidget()
        subtitles_table.setColumnCount(6)
        subtitles_table.setHorizontalHeaderLabels(["#", "开始时间", "结束时间", "原文"])
        
        # 设置表格样式 - 参照字幕配音窗口的深色主题
        subtitles_table.setStyleSheet("""
            QTableWidget {
                background-color: #1B1E24;
                border: 1px solid #333333;
                gridline-color: #333333;
                selection-background-color: #2B9D7C;
                selection-color: #FFFFFF;
                color: #FFFFFF;
                font-size: 12px;
                border-radius: 4px;
            }
            QTableWidget::item {
                padding: 6px;
                border-bottom: 1px solid #333333;
            }
            QTableWidget::item:selected {
                background-color: #2B9D7C;
                color: #FFFFFF;
            }
            QTableWidget::item:hover {
                background-color: rgba(43, 157, 124, 0.3);
            }
            QTableWidget::item:focus {
                background-color: rgba(43, 157, 124, 0.5);
                border: 1px solid #2B9D7C;
            }
            QHeaderView::section {
                background-color: #2B9D7C;
                color: #FFFFFF;
                padding: 6px;
                border: none;
                border-right: 1px solid #1B1E24;
                font-weight: bold;
                font-size: 11px;
            }
            QHeaderView::section:last {
                border-right: none;
            }
            QScrollBar:vertical {
                background-color: #1B1E24;
                width: 12px;
                border-radius: 6px;
            }
            QScrollBar::handle:vertical {
                background-color: #2B9D7C;
                border-radius: 6px;
                min-height: 20px;
            }
            QScrollBar::handle:vertical:hover {
                background-color: #00CC55;
            }
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
                height: 0px;
            }
            QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {
                background: transparent;
            }
        """)
        
        # 设置列宽 - 优化六列布局
        subtitles_header = subtitles_table.horizontalHeader()
        subtitles_header.setSectionResizeMode(0, QHeaderView.Fixed)  # 序号
        subtitles_header.setSectionResizeMode(1, QHeaderView.Fixed)  # 开始时间
        subtitles_header.setSectionResizeMode(2, QHeaderView.Fixed)  # 结束时间
        subtitles_header.setSectionResizeMode(3, QHeaderView.Stretch)  # 字幕内容
        subtitles_header.setSectionResizeMode(4, QHeaderView.Fixed)  # 状态
        subtitles_header.setSectionResizeMode(5, QHeaderView.Fixed)  # 操作
        
        subtitles_table.setColumnWidth(0, 50)   # 序号列宽
        subtitles_table.setColumnWidth(1, 90)   # 开始时间列宽
        subtitles_table.setColumnWidth(2, 90)   # 结束时间列宽
        subtitles_table.setColumnWidth(4, 80)   # 状态列宽
        subtitles_table.setColumnWidth(5, 100)  # 操作列宽
        
        # 设置行高和选择模式
        subtitles_table.setSelectionBehavior(QTableWidget.SelectRows)
        subtitles_table.setSelectionMode(QTableWidget.SingleSelection)
        subtitles_table.verticalHeader().setVisible(False)
        subtitles_table.setShowGrid(True)
        subtitles_table.verticalHeader().setDefaultSectionSize(40)
        
        # 设置垂直滚动条策略为始终显示，确保可滚动
        subtitles_table.setVerticalScrollBarPolicy(Qt.ScrollBarAlwaysOn)
        
        frame_layout.addWidget(subtitles_table)
        
        # 底部区域 - 包含GPU状态
        bottom_layout = QHBoxLayout()
        
        # 左侧操作按钮
        button_layout = QHBoxLayout()
        button_layout.setSpacing(10)
        
        edit_btn = QPushButton("编辑选中")
        edit_btn.setFixedSize(90, 36)
        edit_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #3B82F6, stop:1 #1E40AF);
                color: #FFFFFF;
                font-size: 13px;
                font-weight: bold;
                border: none;
                border-radius: 18px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #60A5FA, stop:1 #1D4ED8);
            }
        """)
        button_layout.addWidget(edit_btn)
        
        add_btn = QPushButton("添加字幕")
        add_btn.setFixedSize(90, 36)
        add_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #2B9D7C, stop:1 #00A86B);
                color: #FFFFFF;
                font-size: 13px;
                font-weight: bold;
                border: none;
                border-radius: 18px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #00CC55, stop:1 #26A65B);
            }
        """)
        button_layout.addWidget(add_btn)
        
        delete_btn = QPushButton("删除选中")
        delete_btn.setFixedSize(90, 36)
        delete_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #E74C3C, stop:1 #C0392B);
                color: #FFFFFF;
                font-size: 13px;
                font-weight: bold;
                border: none;
                border-radius: 18px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #F5593D, stop:1 #E74C3C);
            }
        """)
        button_layout.addWidget(delete_btn)
        
        # 添加底部左侧操作按钮
        bottom_layout.addLayout(button_layout)
        
        # 右侧添加GPU状态区域
        bottom_layout.addStretch()
        
        # GPU状态显示区域
        gpu_status_container = QWidget()
        gpu_layout = QVBoxLayout(gpu_status_container)
        gpu_layout.setContentsMargins(0, 0, 0, 0)
        gpu_layout.setSpacing(3)
        
        # GPU主状态标签
        self.gpu_status_label = QLabel("🔍 GPU检测中...")
        self.gpu_status_label.setStyleSheet("""
            QLabel {
                color: #9CA3AF;
                font-size: 16px;
                font-weight: bold;
                background-color: transparent;
                border: none;
            }
        """)
        self.gpu_status_label.setAlignment(Qt.AlignRight)
        gpu_layout.addWidget(self.gpu_status_label)
        
        # GPU详细信息标签
        self.gpu_detail_label = QLabel("检测中，请稍候...")
        self.gpu_detail_label.setStyleSheet("""
            QLabel {
                color: #9CA3AF;
                font-size: 11px;
                background-color: transparent;
                border: none;
            }
        """)
        self.gpu_detail_label.setAlignment(Qt.AlignRight)
        gpu_layout.addWidget(self.gpu_detail_label)
        
        # 帮助按钮
        gpu_help_btn = QPushButton("GPU帮助")
        gpu_help_btn.setFixedSize(80, 30)
        gpu_help_btn.setStyleSheet("""
            QPushButton {
                background-color: #333333;
                color: #FFFFFF;
                font-size: 12px;
                border: none;
                border-radius: 15px;
            }
            QPushButton:hover {
                background-color: #444444;
            }
        """)
        gpu_layout.addWidget(gpu_help_btn)
        
        bottom_layout.addLayout(gpu_layout)
        
        frame_layout.addLayout(bottom_layout)
        
        # 添加背景Frame到主布局
        container_layout.addWidget(background_frame)
        
        # 连接信号
        # 开始GPU检测 - 只在gpu_detector存在时执行
        try:
            if hasattr(self, 'gpu_detector') and self.gpu_detector:
                self.gpu_detector.check_gpu_async()
            else:
                # 如果GPU检测器初始化失败，更新状态标签显示
                if hasattr(self, 'gpu_status_label'):
                    self.gpu_status_label.setText("🔍 GPU检测器不可用")
                    self.gpu_status_label.setStyleSheet("""
                        QLabel {
                            color: #FF5555;
                            font-size: 16px;
                            font-weight: bold;
                            background-color: transparent;
                            border: none;
                        }
                    """)
                if hasattr(self, 'gpu_detail_label'):
                    self.gpu_detail_label.setText("GPU检测器无法初始化，请确保core模块可用")
        except Exception as e:
            print(f"GPU检测失败: {e}")
        
        # 为示例添加一些示例字幕
        for i in range(5):
            subtitles_table.insertRow(i)
            # 序号
            item = QTableWidgetItem(str(i+1))
            item.setTextAlignment(Qt.AlignCenter)
            subtitles_table.setItem(i, 0, item)
            
            # 开始时间
            start_time = QTableWidgetItem(f"00:0{i}:00,000")
            start_time.setTextAlignment(Qt.AlignCenter)
            subtitles_table.setItem(i, 1, start_time)
            
            # 结束时间
            end_time = QTableWidgetItem(f"00:0{i}:05,000")
            end_time.setTextAlignment(Qt.AlignCenter)
            subtitles_table.setItem(i, 2, end_time)
            
            # 字幕内容
            content = QTableWidgetItem(f"这是第{i+1}条示例字幕内容，用于演示字幕编辑区的外观。")
            subtitles_table.setItem(i, 3, content)
            
            # 状态
            status = QTableWidgetItem("就绪")
            status.setTextAlignment(Qt.AlignCenter)
            status.setBackground(QColor("#2B9D7C"))
            status.setForeground(QColor("#FFFFFF"))
            subtitles_table.setItem(i, 4, status)
            
            # 操作按钮单元格
            operation_cell = QWidget()
            operation_layout = QHBoxLayout(operation_cell)
            operation_layout.setContentsMargins(2, 2, 2, 2)
            operation_layout.setSpacing(8)
            
            # 播放按钮
            play_btn = QPushButton("▶")
            play_btn.setFixedSize(26, 26)
            play_btn.setStyleSheet("""
                QPushButton {
                    background-color: #2B9D7C;
                    color: white;
                    border: none;
                    border-radius: 13px;
                    font-size: 12px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #00CC55;
                }
            """)
            operation_layout.addWidget(play_btn)
            
            # 编辑按钮
            edit_item_btn = QPushButton("✎")
            edit_item_btn.setFixedSize(26, 26)
            edit_item_btn.setStyleSheet("""
                QPushButton {
                    background-color: #3B82F6;
                    color: white;
                    border: none;
                    border-radius: 13px;
                    font-size: 12px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #60A5FA;
                }
            """)
            operation_layout.addWidget(edit_item_btn)
            
            # 删除按钮
            delete_item_btn = QPushButton("✕")
            delete_item_btn.setFixedSize(26, 26)
            delete_item_btn.setStyleSheet("""
                QPushButton {
                    background-color: #E74C3C;
                    color: white;
                    border: none;
                    border-radius: 13px;
                    font-size: 12px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #F5593D;
                }
            """)
            operation_layout.addWidget(delete_item_btn)
            
            # 使布局居中
            operation_layout.setAlignment(Qt.AlignCenter)
            
            # 将操作单元格添加到表格
            subtitles_table.setCellWidget(i, 5, operation_cell)
        
        # 更新字幕计数
        subtitle_count_label.setText(f"字幕条数: {subtitles_table.rowCount()} | 双击内容编辑")
        
        return edit_container


class FunctionArea(QWidget):
    """
    功能区域
    显示各种功能卡片：视频提取音频、音频提取字幕、字幕翻译、字幕配音等
    """
    # 添加信号用于通知主窗口切换到音频提取界面
    switch_to_audio_extraction = Signal()
    
    def __init__(self):
        super().__init__()
        self.setFixedSize(1359, 776)  # 与左侧导航栏保持一致的高度
        self.setup_ui()
    
    def setup_ui(self):
        """初始化功能区域UI"""
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # 创建背景Frame
        background_frame = QFrame()
        background_frame.setFixedSize(1359, 776)
        background_frame.setStyleSheet(StyleManager.get_frame_style())
        
        # Frame内部布局
        frame_layout = QVBoxLayout(background_frame)
        frame_layout.setContentsMargins(40, 30, 40, 30)
        frame_layout.setSpacing(20)
        
        # 标题区域
        title_container = self.create_title_section()
        frame_layout.addWidget(title_container)
        
        # 创建滚动区域来包装功能卡片
        scroll_area = QScrollArea()
        scroll_area.setFixedSize(1279, 536)  # 减小滚动区域高度为底部提示留出空间
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAlwaysOff)  # 隐藏垂直滚动条
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)  # 隐藏水平滚动条
        scroll_area.setWidgetResizable(True)  # 允许内容自动调整大小
        scroll_area.setStyleSheet("""
            QScrollArea {
                background-color: transparent;
                border: none;
            }
            QScrollArea > QWidget > QWidget {
                background-color: transparent;
            }
        """)
        
        # 功能卡片区域（只包含卡片，不包含底部提示）
        cards_container = self.create_function_cards()
        scroll_area.setWidget(cards_container)
        
        frame_layout.addWidget(scroll_area)
        
        # 添加间隔空间，防止滚动内容与底部提示重叠
        frame_layout.addSpacing(15)
        
        # 底部提示信息（固定在最下面，不在滚动区域内）
        bottom_container = self.create_bottom_tips()
        frame_layout.addWidget(bottom_container)
        
        # 将背景Frame添加到主布局
        main_layout.addWidget(background_frame)
    
    def create_title_section(self):
        """创建标题区域"""
        title_container = QWidget()
        title_container.setFixedHeight(120)  # 增加高度以容纳更多内容
        
        # 创建背景装饰Frame
        background_frame = QFrame(title_container)
        background_frame.setFixedSize(1279, 120)  # 设置背景frame尺寸
        background_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(43, 157, 124, 0.1), 
                    stop:0.3 rgba(30, 120, 90, 0.08), 
                    stop:0.7 rgba(20, 80, 60, 0.06), 
                    stop:1 rgba(15, 40, 30, 0.04));
                border: 1px solid rgba(43, 157, 124, 0.2);
                border-radius: 25px;
            }
        """)
        
        title_layout = QHBoxLayout(title_container)
        title_layout.setContentsMargins(40, 20, 40, 20)
        title_layout.setSpacing(30)
        
        # 左侧内容区域
        left_content = QWidget()
        left_layout = QVBoxLayout(left_content)
        left_layout.setContentsMargins(0, 0, 0, 0)
        left_layout.setSpacing(8)
        
        # 主标题区域
        title_header = QHBoxLayout()
        title_header.setSpacing(15)
        
        # 装饰图标
        icon_label = QLabel("⚡")
        icon_label.setStyleSheet(StyleManager.get_label_style(color='#2B9D7C', font_size=32))
        icon_label.setFixedSize(40, 40)
        icon_label.setAlignment(Qt.AlignCenter)
        title_header.addWidget(icon_label)
        
        # 主标题
        main_title = QLabel("功能中心")
        main_title.setStyleSheet(f"""
            QLabel {{
                color: #FFFFFF;
                font-size: 28px;
                font-weight: bold;
                background-color: transparent;
                border: none;
            }}
        """)
        title_header.addWidget(main_title)
        
        # 状态徽章
        status_badge = QLabel("AI加速")
        status_badge.setStyleSheet("""
            QLabel {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #2B9D7C, stop:1 #00CC55);
                color: #FFFFFF;
                font-size: 12px;
                font-weight: bold;
                border-radius: 10px;
                padding: 4px 8px;
            }
        """)
        status_badge.setFixedHeight(20)
        status_badge.setAlignment(Qt.AlignCenter)
        title_header.addWidget(status_badge)
        title_header.addStretch()
        
        left_layout.addLayout(title_header)
        
        # 副标题
        sub_title = QLabel("选择您需要的AI视频处理功能模块，体验智能化工作流程")
        sub_title.setStyleSheet(StyleManager.get_label_style(color=StyleManager.COLORS['text_gray'], font_size=16))
        left_layout.addWidget(sub_title)
        
        title_layout.addWidget(left_content)
        
        # 右侧统计信息区域
        stats_container = QWidget()
        stats_container.setFixedWidth(300)
        stats_layout = QHBoxLayout(stats_container)
        stats_layout.setContentsMargins(0, 0, 0, 0)
        stats_layout.setSpacing(20)
        
        # 可用功能统计
        available_stat = self.create_stat_card("6", "可用功能", "#4ECDC4")
        stats_layout.addWidget(available_stat)
        
        # AI模型统计
        ai_stat = self.create_stat_card("3", "AI模型", "#45B7D1")
        stats_layout.addWidget(ai_stat)
        
        # 处理速度统计
        speed_stat = self.create_stat_card("10x", "处理加速", "#96CEB4")
        stats_layout.addWidget(speed_stat)
        
        title_layout.addWidget(stats_container)
        
        return title_container
    
    def create_stat_card(self, value, label, color):
        """创建统计卡片 - 美化版本"""
        card = QFrame()
        card.setFixedSize(85, 65)  # 稍微增大尺寸
        card.setStyleSheet(f"""
            QFrame {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba({StyleManager.hex_to_rgb(color)}, 0.2), 
                    stop:0.5 rgba({StyleManager.hex_to_rgb(color)}, 0.12), 
                    stop:1 rgba({StyleManager.hex_to_rgb(color)}, 0.08));
                border: 2px solid rgba({StyleManager.hex_to_rgb(color)}, 0.4);
                border-radius: 16px;
                box-shadow: 0px 4px 16px rgba({StyleManager.hex_to_rgb(color)}, 0.15);
            }}
            QFrame:hover {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba({StyleManager.hex_to_rgb(color)}, 0.3), 
                    stop:0.5 rgba({StyleManager.hex_to_rgb(color)}, 0.18), 
                    stop:1 rgba({StyleManager.hex_to_rgb(color)}, 0.12));
                border: 2px solid rgba({StyleManager.hex_to_rgb(color)}, 0.6);
                transform: translateY(-2px);
            }}
        """)
        
        card_layout = QVBoxLayout(card)
        card_layout.setContentsMargins(8, 8, 8, 8)
        card_layout.setSpacing(2)
        
        # 数值
        value_label = QLabel(value)
        value_label.setStyleSheet(f"""
            QLabel {{
                color: {color};
                font-size: 18px;
                font-weight: bold;
                background-color: transparent;
                border: none;
            }}
        """)
        value_label.setAlignment(Qt.AlignCenter)
        card_layout.addWidget(value_label)
        
        # 标签
        label_widget = QLabel(label)
        label_widget.setStyleSheet(StyleManager.get_label_style(color=StyleManager.COLORS['text_gray'], font_size=10))
        label_widget.setAlignment(Qt.AlignCenter)
        label_widget.setWordWrap(True)
        card_layout.addWidget(label_widget)
        
        return card
    
    def create_function_cards(self):
        """创建功能卡片区域"""
        cards_container = QWidget()
        
        main_layout = QVBoxLayout(cards_container)
        main_layout.setContentsMargins(0, 20, 0, 30)  # 增加底部边距，为底部提示留出空间
        main_layout.setSpacing(25)
        
        # 功能分类标题
        category_header = QHBoxLayout()
        category_header.setSpacing(15)
        
        # 分类图标
        category_icon = QLabel("🚀")
        category_icon.setStyleSheet(StyleManager.get_label_style(color='#2B9D7C', font_size=24))
        category_icon.setFixedSize(30, 30)
        category_icon.setAlignment(Qt.AlignCenter)
        category_header.addWidget(category_icon)
        
        # 分类标题
        category_title = QLabel("核心功能模块")
        category_title.setStyleSheet(StyleManager.get_label_style(color='#FFFFFF', font_size=20))
        category_title.setProperty("font-weight", "bold")
        category_header.addWidget(category_title)
        
        # 装饰线 - 美化版本
        decorator_line = QFrame()
        decorator_line.setFixedHeight(3)
        decorator_line.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 transparent, 
                    stop:0.05 rgba(43, 157, 124, 0.2),
                    stop:0.1 rgba(43, 157, 124, 0.5), 
                    stop:0.5 rgba(43, 157, 124, 0.8),
                    stop:0.9 rgba(43, 157, 124, 0.5), 
                    stop:0.95 rgba(43, 157, 124, 0.2),
                    stop:1 transparent);
                border: none;
                border-radius: 1.5px;
            }
        """)
        category_header.addWidget(decorator_line)
        
        main_layout.addLayout(category_header)
        
        # 创建网格布局容器
        grid_container = QWidget()
        grid_layout = QGridLayout(grid_container)
        grid_layout.setContentsMargins(0, 0, 0, 0)
        grid_layout.setSpacing(18)  # 减小间距从25到18
        
        # 功能卡片数据
        functions = [
            {
                'icon': '📥',
                'title': '视频下载',
                'description': '下载YouTube等平台视频\n支持多种清晰度和格式选择',
                'color': '#6C5CE7',
                'status': '可用',
                'category': 'tool'
            },
            {
                'icon': '🎵',
                'title': '视频提取音频',
                'description': '从视频文件中提取高质量音频\n支持多种视频格式转换',
                'color': '#FF6B6B',
                'status': '可用',
                'category': 'media'
            },
            {
                'icon': '🎤',
                'title': '人声分离',
                'description': '基于Facebook Demucs技术\n智能分离人声和背景音乐',
                'color': '#FF7043',
                'status': '可用',
                'category': 'ai'
            },
            {
                'icon': '📝',
                'title': '音频提取字幕',
                'description': '使用AI语音识别技术\n自动生成准确字幕文件',
                'color': '#4ECDC4',
                'status': '可用',
                'category': 'ai'
            },
            {
                'icon': '🌐',
                'title': '字幕翻译',
                'description': '支持拖拽文件加载\n多翻译服务智能切换',
                'color': '#45B7D1',
                'status': '可用',
                'category': 'ai'
            },
            {
                'icon': '🎙️',
                'title': '字幕配音',
                'description': 'AI智能语音合成\n生成自然流畅的配音',
                'color': '#96CEB4',
                'status': '可用',
                'category': 'ai'
            },
            {
                'icon': '🎬',
                'title': '视频合成',
                'description': '将音频和字幕合并到视频\n一键生成最终作品',
                'color': '#FFEAA7',
                'status': '可用',
                'category': 'media'
            },
            {
                'icon': '⚙️',
                'title': '批量处理',
                'description': '支持批量文件处理\n提高工作效率',
                'color': '#DDA0DD',
                'status': '可用',
                'category': 'tool'
            },
            {
                'icon': '✏️',
                'title': '字幕编辑器',
                'description': '支持字幕文件编辑\n提升字幕处理效率',
                'color': '#9B59B6',
                'status': '可用',
                'category': 'tool'
            }
        ]
        
        # 创建功能卡片 (3行3列布局，支持8个功能)
        for i, func_data in enumerate(functions):
            row = i // 3
            col = i % 3
            card = self.create_function_card(func_data)
            grid_layout.addWidget(card, row, col)
        
        main_layout.addWidget(grid_container)
        
        return cards_container
    
    def create_bottom_tips(self):
        """创建底部提示信息区域"""
        bottom_container = QFrame()
        bottom_container.setFixedHeight(60)
        bottom_container.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(27, 30, 36, 0.95), 
                    stop:0.3 rgba(30, 35, 41, 0.9), 
                    stop:0.7 rgba(35, 40, 46, 0.9), 
                    stop:1 rgba(32, 37, 43, 0.95));
                border: 1px solid rgba(43, 157, 124, 0.3);
                border-radius: 15px;
            }
        """)
        
        bottom_info = QHBoxLayout(bottom_container)
        bottom_info.setContentsMargins(20, 15, 20, 15)
        bottom_info.setSpacing(15)
        
        # 提示图标容器
        tip_icon_container = QFrame()
        tip_icon_container.setFixedSize(30, 30)
        tip_icon_container.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(43, 157, 124, 0.2), 
                    stop:1 rgba(43, 157, 124, 0.1));
                border: 1px solid rgba(43, 157, 124, 0.3);
                border-radius: 15px;
            }
        """)
        
        tip_icon_layout = QVBoxLayout(tip_icon_container)
        tip_icon_layout.setContentsMargins(0, 0, 0, 0)
        
        tip_icon = QLabel("💡")
        tip_icon.setStyleSheet(StyleManager.get_label_style(font_size=14))
        tip_icon.setAlignment(Qt.AlignCenter)
        tip_icon_layout.addWidget(tip_icon)
        
        bottom_info.addWidget(tip_icon_container)
        
        # 提示文字
        tip_text = QLabel("点击任意功能卡片开始您的AI视频处理之旅")
        tip_text.setStyleSheet(StyleManager.get_label_style(color='#FFFFFF', font_size=14))
        tip_text.setProperty("font-weight", "500")
        bottom_info.addWidget(tip_text)
        
        bottom_info.addStretch()
        
        # 更多功能提示
        more_text = QLabel("✨ 更多功能即将上线...")
        more_text.setStyleSheet(StyleManager.get_label_style(color='#2B9D7C', font_size=13))
        more_text.setProperty("font-style", "italic")
        more_text.setProperty("font-weight", "500")
        bottom_info.addWidget(more_text)
        
        return bottom_container
    
    def create_function_card(self, func_data):
        """创建单个功能卡片"""
        card = QFrame()
        card.setFixedSize(340, 220)  # 减小卡片尺寸以适应3行布局
        card.setCursor(Qt.PointingHandCursor)
        
        # 卡片样式 - 美化版本
        card.setStyleSheet(f"""
            QFrame {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(26, 30, 36, 0.95), 
                    stop:0.3 rgba(30, 35, 41, 0.9), 
                    stop:0.7 rgba(35, 40, 46, 0.9), 
                    stop:1 rgba(32, 37, 43, 0.95));
                border: 2px solid rgba({StyleManager.hex_to_rgb(func_data['color'])}, 0.4);
                border-radius: 24px;
                box-shadow: 0px 8px 32px rgba(0, 0, 0, 0.3);
            }}
            QFrame:hover {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(35, 40, 46, 1.0), 
                    stop:0.3 rgba(40, 45, 51, 0.95), 
                    stop:0.7 rgba(45, 50, 56, 0.95), 
                    stop:1 rgba(42, 47, 53, 1.0));
                border: 2px solid rgba({StyleManager.hex_to_rgb(func_data['color'])}, 0.8);
                transform: translateY(-4px);
                box-shadow: 0px 12px 40px rgba({StyleManager.hex_to_rgb(func_data['color'])}, 0.2);
            }}
        """)
        
        # 卡片布局
        card_layout = QVBoxLayout(card)
        card_layout.setContentsMargins(20, 20, 20, 20)
        card_layout.setSpacing(12)  # 减小整体间距，更紧凑
        
        # 顶部区域：图标和状态
        top_layout = QHBoxLayout()
        top_layout.setSpacing(10)
        
        # 图标容器 - 添加背景装饰
        icon_container = QFrame()
        icon_container.setFixedSize(56, 56)
        icon_container.setStyleSheet(f"""
            QFrame {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba({StyleManager.hex_to_rgb(func_data['color'])}, 0.15), 
                    stop:1 rgba({StyleManager.hex_to_rgb(func_data['color'])}, 0.08));
                border: 2px solid rgba({StyleManager.hex_to_rgb(func_data['color'])}, 0.3);
                border-radius: 28px;
            }}
        """)
        
        icon_layout = QVBoxLayout(icon_container)
        icon_layout.setContentsMargins(0, 0, 0, 0)
        
        # 图标
        icon_label = QLabel(func_data['icon'])
        icon_label.setStyleSheet(StyleManager.get_label_style(font_size=28))
        icon_label.setAlignment(Qt.AlignCenter)
        icon_layout.addWidget(icon_label)
        
        top_layout.addWidget(icon_container)
        
        top_layout.addStretch()
        
        # 状态标签 - 美化版本
        status_label = QLabel(func_data['status'])
        status_label.setStyleSheet(f"""
            QLabel {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba({StyleManager.hex_to_rgb(func_data['color'])}, 0.25), 
                    stop:1 rgba({StyleManager.hex_to_rgb(func_data['color'])}, 0.15));
                color: {func_data['color']};
                font-size: 10px;
                font-weight: bold;
                border: 1px solid rgba({StyleManager.hex_to_rgb(func_data['color'])}, 0.5);
                border-radius: 10px;
                padding: 4px 8px;
                text-transform: uppercase;
                letter-spacing: 0.5px;
            }}
        """)
        status_label.setAlignment(Qt.AlignCenter)
        status_label.setFixedHeight(20)
        top_layout.addWidget(status_label)
        
        card_layout.addLayout(top_layout)
        
        # 标题
        title_label = QLabel(func_data['title'])
        title_label.setStyleSheet(StyleManager.get_label_style(color='#FFFFFF', font_size=16))  # 减小标题字体
        title_label.setProperty("font-weight", "bold")
        title_label.setFixedHeight(20)  # 固定标题高度
        card_layout.addWidget(title_label)
        
        # 描述区域 - 使用固定高度容器
        desc_container = QWidget()
        desc_container.setFixedHeight(60)  # 固定描述区域高度
        desc_layout = QVBoxLayout(desc_container)
        desc_layout.setContentsMargins(0, 0, 0, 0)
        desc_layout.setSpacing(0)
        
        desc_label = QLabel(func_data['description'])
        desc_label.setStyleSheet(StyleManager.get_label_style(color=StyleManager.COLORS['text_gray'], font_size=12))  # 减小描述字体
        desc_label.setWordWrap(True)
        desc_label.setAlignment(Qt.AlignTop)
        desc_layout.addWidget(desc_label)
        
        card_layout.addWidget(desc_container)
        
        # 添加弹性空间，将按钮推到底部
        card_layout.addStretch()
        
        # 底部操作按钮
        button_layout = QHBoxLayout()
        button_layout.setContentsMargins(0, 0, 0, 0)
        button_layout.addStretch()
        
        action_btn = QPushButton("立即使用")
        action_btn.setFixedSize(100, 36)  # 稍微增大按钮尺寸
        action_btn.setStyleSheet(f"""
            QPushButton {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 {func_data['color']}, 
                    stop:0.5 rgba({StyleManager.hex_to_rgb(func_data['color'])}, 0.9),
                    stop:1 rgba({StyleManager.hex_to_rgb(func_data['color'])}, 0.8));
                color: #FFFFFF;
                font-size: 12px;
                font-weight: bold;
                border: 2px solid rgba({StyleManager.hex_to_rgb(func_data['color'])}, 0.6);
                border-radius: 18px;
                padding: 8px 16px;
                text-transform: uppercase;
                letter-spacing: 0.5px;
            }}
            QPushButton:hover {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba({StyleManager.hex_to_rgb(func_data['color'])}, 1.1), 
                    stop:1 {func_data['color']});
                border: 2px solid {func_data['color']};
                transform: translateY(-2px);
                box-shadow: 0px 6px 20px rgba({StyleManager.hex_to_rgb(func_data['color'])}, 0.4);
            }}
            QPushButton:pressed {{
                background: rgba({StyleManager.hex_to_rgb(func_data['color'])}, 0.7);
                transform: translateY(0px);
            }}
        """)
        
        # 绑定点击事件
        action_btn.clicked.connect(lambda checked, title=func_data['title']: self.on_function_clicked(title))
        
        button_layout.addWidget(action_btn)
        card_layout.addLayout(button_layout)
        
        return card
    
    def on_function_clicked(self, function_title):
        """功能卡片点击事件"""
        print(f"点击了功能: {function_title}")
        
        if function_title == "视频下载":
            # 弹出视频下载对话框
            self.show_video_download_dialog()
        elif function_title == "视频提取音频":
            # 直接弹出批量音频提取对话框
            self.show_batch_audio_extraction_dialog()
        elif function_title == "人声分离":
            # 弹出人声分离对话框
            self.show_voice_separation_dialog()
        elif function_title == "音频提取字幕":
            # 弹出音频字幕提取对话框
            self.show_subtitle_extraction_dialog()
        elif function_title == "字幕编辑器":
            # 弹出字幕编辑器对话框
            self.show_subtitle_editor_dialog()
        elif function_title == "字幕翻译":
            # 弹出字幕翻译对话框
            self.show_subtitle_translation_dialog()
        elif function_title == "字幕配音":
            # 弹出字幕配音对话框
            self.show_subtitle_voiceover_dialog()
        else:
            # 其他功能暂时显示提示信息
            print(f"功能 '{function_title}' 正在开发中...")
            # TODO: 这里可以添加其他功能的具体实现逻辑

    def show_subtitle_editor_dialog(self):
        """显示字幕编辑器对话框"""
        try:
            try:
                from .subtitle_editor_dialog import SubtitleEditorDialog
            except ImportError:
                from subtitle_editor_dialog import SubtitleEditorDialog
            
            # 找到主窗口
            main_window = self
            while main_window.parent() is not None:
                main_window = main_window.parent()
            
            # 创建并显示字幕编辑器对话框
            dialog = SubtitleEditorDialog(main_window)
            dialog.exec()
            
        except ImportError as e:
            # 如果导入失败，显示错误信息
            from PySide6.QtWidgets import QMessageBox
            
            msg_box = QMessageBox(self)
            msg_box.setWindowTitle("字幕编辑器功能")
            msg_box.setText("字幕编辑器功能启动失败！")
            msg_box.setInformativeText(
                f"错误信息：{str(e)}\n\n"
                "字幕编辑器功能包含以下特性：\n"
                "• 支持SRT、VTT等多种字幕格式\n"
                "• 实时预览和编辑功能\n"
                "• 时间轴调整和同步\n"
                "• 批量编辑和替换\n\n"
                "如需帮助，请查看相关文档。"
            )
            msg_box.setIcon(QMessageBox.Information)
            msg_box.setStyleSheet("""
                QMessageBox {
                    background-color: #1B1E24;
                    color: #FFFFFF;
                    font-size: 14px;
                }
                QMessageBox QLabel {
                    color: #FFFFFF;
                    font-size: 14px;
                }
                QMessageBox QPushButton {
                    background-color: #9B59B6;
                    color: #FFFFFF;
                    border: none;
                    border-radius: 8px;
                    padding: 8px 16px;
                    font-size: 12px;
                    font-weight: bold;
                    min-width: 80px;
                }
                QMessageBox QPushButton:hover {
                    background-color: #8E44AD;
                }
                QMessageBox QPushButton:pressed {
                    background-color: #7D3C98;
                }
            """)
            msg_box.exec()

    def show_subtitle_translation_dialog(self):
        """显示字幕翻译对话框"""
        try:
            # 使用基于插件系统的字幕翻译对话框
            try:
                from .subtitle_translation_dialog import SubtitleTranslationDialog
            except ImportError:
                from subtitle_translation_dialog import SubtitleTranslationDialog
            
            # 找到主窗口
            main_window = self
            while main_window.parent() is not None:
                main_window = main_window.parent()
            
            # 创建并显示字幕翻译对话框
            dialog = SubtitleTranslationDialog(main_window)
            dialog.exec()
            
        except ImportError as e:
            # 如果导入失败，显示错误信息
            from PySide6.QtWidgets import QMessageBox
            
            msg_box = QMessageBox(self)
            msg_box.setWindowTitle("字幕翻译功能")
            msg_box.setText("字幕翻译功能启动失败！")
            msg_box.setInformativeText(
                f"错误信息：{str(e)}\n\n"
                "字幕翻译功能包含以下特性：\n"
                "• 支持微软、谷歌、DeepL翻译服务\n"
                "• 多语言自动翻译\n"
                "• 保持字幕时间轴同步\n"
                "• 批量翻译处理\n"
                "• 支持拖拽文件加载\n\n"
                "请确认以下依赖已正确安装：\n"
                "• requests: pip install requests\n"
                "如需帮助，请查看相关文档。"
            )
            msg_box.setIcon(QMessageBox.Information)
            msg_box.setStyleSheet("""
                QMessageBox {
                    background-color: #1B1E24;
                    color: #FFFFFF;
                    font-size: 14px;
                }
                QMessageBox QLabel {
                    color: #FFFFFF;
                    font-size: 14px;
                }
                QMessageBox QPushButton {
                    background-color: #45B7D1;
                    color: #FFFFFF;
                    border: none;
                    border-radius: 8px;
                    padding: 8px 16px;
                    font-size: 12px;
                    font-weight: bold;
                    min-width: 80px;
                }
                QMessageBox QPushButton:hover {
                    background-color: #3498DB;
                }
                QMessageBox QPushButton:pressed {
                    background-color: #2980B9;
                }
            """)
            msg_box.exec()

    def show_subtitle_voiceover_dialog(self):
        """显示字幕配音对话框（优化启动模式）"""
        try:
            print("🚀 正在启动字幕配音功能...")
            
            # 直接使用简单启动方式，避免复杂的进程管理
            QTimer.singleShot(100, self._launch_subtitle_voiceover_simple)
            
        except Exception as e:
            print(f"❌ 字幕配音功能启动失败: {e}")
            import traceback
            traceback.print_exc()
    
    def _launch_subtitle_voiceover_independently(self):
        """独立启动字幕配音功能（完全模拟测试文件的成功模式）"""
        try:
            print("🎯 正在独立启动字幕配音模块...")
            
            # 使用完全独立的启动方式
            import subprocess
            import sys
            import os
            
            # 获取当前工作目录
            current_dir = os.getcwd()
            
            # 创建独立启动脚本
            startup_script = os.path.join(current_dir, "launch_subtitle_voiceover.py")
            
            script_content = '''import sys
import os
from PySide6.QtWidgets import QApplication
from PySide6.QtCore import Qt

# 添加当前目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def main():
    """启动字幕配音功能"""
    # 创建独立的Qt应用
    app = QApplication.instance()
    if app is None:
        app = QApplication(sys.argv)
        app.setApplicationName("FlipTalk AI - 字幕配音")
        app.setQuitOnLastWindowClosed(False)  # 不自动退出
    
        try:
            # 导入字幕配音对话框
        from ui.subtitle_voiceover_dialog import SubtitleVoiceoverDialog
        
        # 创建对话框（无父窗口）
        dialog = SubtitleVoiceoverDialog(parent=None)
        
        # 设置为独立窗口
        dialog.setWindowTitle("FlipTalk AI - 字幕配音")
        dialog.setWindowFlags(Qt.Window | Qt.WindowTitleHint | Qt.WindowCloseButtonHint | Qt.WindowMinMaxButtonsHint)
        dialog.setAttribute(Qt.WA_QuitOnClose, True)  # 关闭时退出子应用
        dialog.resize(1400, 800)
        
        # 显示对话框
        dialog.show()
        dialog.raise_()
        dialog.activateWindow()
        
        print("✅ 字幕配音窗口已独立启动")
        
        # 只有在没有其他应用实例时才启动事件循环
        if app.instance() and len(app.allWindows()) == 1:
            return app.exec()
        else:
            return 0
            
    except Exception as e:
        print(f"❌ 字幕配音启动失败: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())


class SystemSettingsArea(QWidget):
    """
    系统设置区域
    提供软件的各种系统级配置选项，包含界面、处理、性能等设置
    """
    def __init__(self):
        super().__init__()
        self.setFixedSize(1359, 776)  # 使用全宽尺寸
        self.current_category = "界面设置"  # 当前选中的设置分类
        self.setup_ui()
    
    def setup_ui(self):
        """初始化系统设置区域UI"""
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # 创建背景Frame
        background_frame = QFrame()
        background_frame.setFixedSize(1359, 776)
        background_frame.setStyleSheet(StyleManager.get_frame_style())
        
        # Frame内部布局
        frame_layout = QVBoxLayout(background_frame)
        frame_layout.setContentsMargins(40, 30, 40, 30)
        frame_layout.setSpacing(20)
        
        # 标题区域
        header_section = self.create_header_section()
        frame_layout.addWidget(header_section)
        
        # 主内容区域（左侧导航 + 右侧设置）
        content_section = self.create_content_section()
        frame_layout.addWidget(content_section)
        
        # 底部操作区域
        bottom_section = self.create_bottom_section()
        frame_layout.addWidget(bottom_section)
        
        # 将背景Frame添加到主布局
        main_layout.addWidget(background_frame)
    
    def create_header_section(self):
        """创建标题区域"""
        header_container = QWidget()
        header_container.setFixedHeight(120)
        
        # 创建背景装饰Frame
        background_frame = QFrame(header_container)
        background_frame.setFixedSize(1279, 120)
        background_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(150, 206, 180, 0.1), 
                    stop:0.3 rgba(120, 170, 150, 0.08), 
                    stop:0.7 rgba(90, 130, 115, 0.06), 
                    stop:1 rgba(60, 90, 80, 0.04));
                border: 1px solid rgba(150, 206, 180, 0.2);
                border-radius: 25px;
            }
        """)
        
        header_layout = QHBoxLayout(header_container)
        header_layout.setContentsMargins(40, 20, 40, 20)
        header_layout.setSpacing(30)
        
        # 左侧标题区域
        left_content = QWidget()
        left_layout = QVBoxLayout(left_content)
        left_layout.setContentsMargins(0, 0, 0, 0)
        left_layout.setSpacing(8)
        
        # 标题区域
        title_header = QHBoxLayout()
        title_header.setSpacing(15)
        
        # 装饰图标
        icon_label = QLabel("🔨")
        icon_label.setStyleSheet(StyleManager.get_label_style(color='#96CEB4', font_size=32))
        icon_label.setFixedSize(40, 40)
        icon_label.setAlignment(Qt.AlignCenter)
        title_header.addWidget(icon_label)
        
        # 主标题
        main_title = QLabel("系统设置")
        main_title.setStyleSheet(StyleManager.get_label_style(color='#FFFFFF', font_size=28))
        main_title.setProperty("font-weight", "bold")
        title_header.addWidget(main_title)
        
        # 配置状态
        status_badge = QLabel("配置管理")
        status_badge.setStyleSheet("""
            QLabel {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #96CEB4, stop:1 #4ECDC4);
                color: #FFFFFF;
                font-size: 12px;
                font-weight: bold;
                border-radius: 10px;
                padding: 4px 8px;
            }
        """)
        status_badge.setFixedHeight(20)
        status_badge.setAlignment(Qt.AlignCenter)
        title_header.addWidget(status_badge)
        title_header.addStretch()
        
        left_layout.addLayout(title_header)
        
        # 副标题
        sub_title = QLabel("自定义软件行为，优化处理性能，配置系统偏好设置")
        sub_title.setStyleSheet(StyleManager.get_label_style(color=StyleManager.COLORS['text_gray'], font_size=16))
        left_layout.addWidget(sub_title)
        
        header_layout.addWidget(left_content)
        
        # 右侧快速状态
        status_container = QWidget()
        status_container.setFixedWidth(300)
        status_layout = QVBoxLayout(status_container)
        status_layout.setContentsMargins(0, 0, 0, 0)
        status_layout.setSpacing(8)
        
        # 系统状态信息
        system_info = QLabel("💻 系统版本：v1.0.0 | GPU：可用 | 内存：8GB")
        system_info.setStyleSheet(StyleManager.get_label_style(color=StyleManager.COLORS['text_gray'], font_size=12))
        status_layout.addWidget(system_info)
        
        # 配置状态
        config_info = QLabel("⚙️ 已保存配置：12项 | 最后修改：今天 14:30")
        config_info.setStyleSheet(StyleManager.get_label_style(color=StyleManager.COLORS['text_gray'], font_size=12))
        status_layout.addWidget(config_info)
        
        header_layout.addWidget(status_container)
        
        return header_container
    
    def create_content_section(self):
        """创建主内容区域"""
        content_container = QWidget()
        content_layout = QHBoxLayout(content_container)
        content_layout.setContentsMargins(0, 0, 0, 0)
        content_layout.setSpacing(20)
        
        # 左侧分类导航
        navigation_panel = self.create_navigation_panel()
        content_layout.addWidget(navigation_panel)
        
        # 右侧设置内容
        settings_panel = self.create_settings_panel()
        content_layout.addWidget(settings_panel)
        
        return content_container
    
    def create_navigation_panel(self):
        """创建左侧分类导航面板"""
        nav_container = QFrame()
        nav_container.setFixedWidth(280)
        nav_container.setStyleSheet("""
            QFrame {
                background-color: #14161A;
                border-radius: 20px;
                border: 1px solid rgba(255, 255, 255, 0.1);
            }
        """)
        
        nav_layout = QVBoxLayout(nav_container)
        nav_layout.setContentsMargins(20, 25, 20, 25)
        nav_layout.setSpacing(8)
        
        # 导航标题
        nav_title = QLabel("设置分类")
        nav_title.setStyleSheet(StyleManager.get_label_style(color='#FFFFFF', font_size=16))
        nav_title.setProperty("font-weight", "bold")
        nav_layout.addWidget(nav_title)
        
        # 分隔线
        separator = QFrame()
        separator.setFixedHeight(1)
        separator.setStyleSheet("""
            QFrame {
                background-color: rgba(255, 255, 255, 0.1);
                border: none;
            }
        """)
        nav_layout.addWidget(separator)
        nav_layout.addSpacing(10)
        
        # 设置分类列表
        categories = [
            ("🎨", "界面设置", "主题、语言、显示选项"),
            ("⚡", "性能设置", "GPU、内存、并发数"),
            ("📁", "文件设置", "路径、格式、清理策略"),
            ("🔊", "音视频设置", "质量、编码、输出格式"),
            ("🛠️", "高级设置", "调试、日志、实验功能"),
            ("🔐", "安全设置", "隐私、数据保护")
        ]
        
        for icon, title, desc in categories:
            nav_btn = self.create_nav_category_button(icon, title, desc)
            nav_layout.addWidget(nav_btn)
        
        nav_layout.addStretch()
        
        return nav_container
    
    def create_nav_category_button(self, icon, title, description):
        """创建分类导航按钮"""
        btn_container = QFrame()
        btn_container.setFixedHeight(70)
        btn_container.setCursor(Qt.PointingHandCursor)
        
        # 设置选中状态样式
        if title == self.current_category:
            btn_container.setStyleSheet("""
                QFrame {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 rgba(150, 206, 180, 0.3), 
                        stop:1 rgba(76, 205, 196, 0.2));
                    border: 1px solid rgba(150, 206, 180, 0.5);
                    border-radius: 12px;
                    margin: 2px;
                }
                QFrame:hover {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 rgba(150, 206, 180, 0.4), 
                        stop:1 rgba(76, 205, 196, 0.3));
                }
            """)
        else:
            btn_container.setStyleSheet("""
                QFrame {
                    background-color: transparent;
                    border: 1px solid transparent;
                    border-radius: 12px;
                    margin: 2px;
                }
                QFrame:hover {
                    background-color: rgba(255, 255, 255, 0.05);
                    border: 1px solid rgba(255, 255, 255, 0.1);
                }
            """)
        
        btn_layout = QHBoxLayout(btn_container)
        btn_layout.setContentsMargins(15, 10, 15, 10)
        btn_layout.setSpacing(12)
        
        # 图标
        icon_label = QLabel(icon)
        icon_label.setStyleSheet(StyleManager.get_label_style(font_size=20))
        icon_label.setFixedSize(30, 30)
        icon_label.setAlignment(Qt.AlignCenter)
        btn_layout.addWidget(icon_label)
        
        # 文字信息
        text_container = QWidget()
        text_layout = QVBoxLayout(text_container)
        text_layout.setContentsMargins(0, 0, 0, 0)
        text_layout.setSpacing(2)
        
        # 标题
        title_label = QLabel(title)
        title_color = '#96CEB4' if title == self.current_category else '#FFFFFF'
        title_label.setStyleSheet(StyleManager.get_label_style(color=title_color, font_size=14))
        title_label.setProperty("font-weight", "bold")
        text_layout.addWidget(title_label)
        
        # 描述
        desc_label = QLabel(description)
        desc_label.setStyleSheet(StyleManager.get_label_style(color=StyleManager.COLORS['text_gray'], font_size=11))
        desc_label.setWordWrap(True)
        text_layout.addWidget(desc_label)
        
        btn_layout.addWidget(text_container)
        
        # 绑定点击事件
        btn_container.mousePressEvent = lambda event, cat=title: self.switch_category(cat)
        
        return btn_container
    
    def create_settings_panel(self):
        """创建右侧设置面板"""
        settings_container = QFrame()
        settings_container.setFixedWidth(837)
        settings_container.setStyleSheet("""
            QFrame {
                background-color: #14161A;
                border-radius: 20px;
                border: 1px solid rgba(255, 255, 255, 0.1);
            }
        """)
        
        # 主布局
        main_layout = QVBoxLayout(settings_container)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setStyleSheet("""
            QScrollArea {
                background-color: transparent;
                border: none;
                border-radius: 20px;
            }
            QScrollBar:vertical {
                background-color: #1B1E24;
                width: 8px;
                border-radius: 4px;
                margin: 5px;
            }
            QScrollBar::handle:vertical {
                background-color: #96CEB4;
                border-radius: 4px;
                min-height: 20px;
            }
            QScrollBar::handle:vertical:hover {
                background-color: #4ECDC4;
            }
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
                height: 0px;
            }
            QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {
                background: transparent;
            }
        """)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setWidgetResizable(True)
        
        # 根据当前分类显示不同的设置内容
        if self.current_category == "界面设置":
            content = self.create_ui_settings()
        elif self.current_category == "性能设置":
            content = self.create_performance_settings()
        elif self.current_category == "文件设置":
            content = self.create_file_settings()
        elif self.current_category == "音视频设置":
            content = self.create_media_settings()
        elif self.current_category == "高级设置":
            content = self.create_advanced_settings()
        elif self.current_category == "安全设置":
            content = self.create_security_settings()
        else:
            content = self.create_ui_settings()  # 默认显示界面设置
        
        # 设置滚动内容
        scroll_area.setWidget(content)
        main_layout.addWidget(scroll_area)
        
        return settings_container
    
    def create_ui_settings(self):
        """创建界面设置内容"""
        content = QWidget()
        content.setStyleSheet("""
            QWidget {
                background-color: #14161A;
                border: none;
            }
        """)
        layout = QVBoxLayout(content)
        layout.setContentsMargins(30, 25, 30, 25)
        layout.setSpacing(20)
        
        # 分类标题
        title = QLabel("🎨 界面设置")
        title.setStyleSheet(StyleManager.get_label_style(color='#96CEB4', font_size=18))
        title.setProperty("font-weight", "bold")
        layout.addWidget(title)
        
        # 设置项目
        settings_items = [
            self.create_setting_group("主题设置", [
                ("界面主题", "ComboBox", ["深色主题", "浅色主题", "自动"], "深色主题"),
                ("主色调", "ComboBox", ["翠绿色", "蓝色", "紫色", "橙色"], "翠绿色"),
                ("透明效果", "CheckBox", None, True)
            ]),
            
            self.create_setting_group("语言与区域", [
                ("界面语言", "ComboBox", ["简体中文", "English", "日本語"], "简体中文"),
                ("时间格式", "ComboBox", ["24小时制", "12小时制"], "24小时制"),
                ("数字格式", "ComboBox", ["中文数字", "阿拉伯数字"], "阿拉伯数字")
            ]),
            
            self.create_setting_group("显示选项", [
                ("字体大小", "ComboBox", ["小", "中", "大", "特大"], "中"),
                ("窗口透明度", "Slider", (50, 100), 95),
                ("显示动画", "CheckBox", None, True),
                ("启动时最大化", "CheckBox", None, False)
            ])
        ]
        
        for setting_group in settings_items:
            layout.addWidget(setting_group)
        
        layout.addStretch()
        return content
    
    def create_performance_settings(self):
        """创建性能设置内容"""
        content = QWidget()
        content.setStyleSheet("""
            QWidget {
                background-color: #14161A;
                border: none;
            }
        """)
        layout = QVBoxLayout(content)
        layout.setContentsMargins(30, 25, 30, 25)
        layout.setSpacing(20)
        
        # 分类标题
        title = QLabel("⚡ 性能设置")
        title.setStyleSheet(StyleManager.get_label_style(color='#96CEB4', font_size=18))
        title.setProperty("font-weight", "bold")
        layout.addWidget(title)
        
        # 设置项目
        settings_items = [
            self.create_setting_group("处理器设置", [
                ("GPU加速", "CheckBox", None, True),
                ("CPU核心数", "ComboBox", ["自动", "1", "2", "4", "8"], "自动"),
                ("并发处理数", "Slider", (1, 10), 4)
            ]),
            
            self.create_setting_group("内存管理", [
                ("最大内存使用", "ComboBox", ["2GB", "4GB", "8GB", "不限制"], "8GB"),
                ("缓存大小", "ComboBox", ["512MB", "1GB", "2GB", "4GB"], "2GB"),
                ("自动清理缓存", "CheckBox", None, True)
            ]),
            
            self.create_setting_group("处理优化", [
                ("处理质量", "ComboBox", ["快速", "平衡", "高质量"], "平衡"),
                ("预处理优化", "CheckBox", None, True),
                ("后台处理", "CheckBox", None, True)
            ])
        ]
        
        for setting_group in settings_items:
            layout.addWidget(setting_group)
        
        layout.addStretch()
        return content
    
    def create_file_settings(self):
        """创建文件设置内容"""
        content = QWidget()
        content.setStyleSheet("""
            QWidget {
                background-color: #14161A;
                border: none;
            }
        """)
        layout = QVBoxLayout(content)
        layout.setContentsMargins(30, 25, 30, 25)
        layout.setSpacing(20)
        
        # 分类标题
        title = QLabel("📁 文件设置")
        title.setStyleSheet(StyleManager.get_label_style(color='#96CEB4', font_size=18))
        title.setProperty("font-weight", "bold")
        layout.addWidget(title)
        
        # 设置项目
        settings_items = [
            self.create_setting_group("目录设置", [
                ("输出目录", "PathSelector", None, "C:/FlipTalk/Output"),
                ("临时目录", "PathSelector", None, "C:/FlipTalk/Temp"),
                ("自动创建子文件夹", "CheckBox", None, True)
            ]),
            
            self.create_setting_group("文件管理", [
                ("保留原始文件", "CheckBox", None, True),
                ("自动删除临时文件", "CheckBox", None, True),
                ("文件命名规则", "ComboBox", ["原文件名", "时间戳", "自定义"], "原文件名"),
                ("文件覆盖策略", "ComboBox", ["询问", "覆盖", "重命名"], "询问")
            ]),
            
            self.create_setting_group("存储优化", [
                ("压缩临时文件", "CheckBox", None, False),
                ("定期清理", "ComboBox", ["从不", "每天", "每周", "每月"], "每周"),
                ("存储空间警告", "Slider", (1, 20), 5)
            ])
        ]
        
        for setting_group in settings_items:
            layout.addWidget(setting_group)
        
        layout.addStretch()
        return content
    
    def create_media_settings(self):
        """创建音视频设置内容"""
        content = QWidget()
        content.setStyleSheet("""
            QWidget {
                background-color: #14161A;
                border: none;
            }
        """)
        layout = QVBoxLayout(content)
        layout.setContentsMargins(30, 25, 30, 25)
        layout.setSpacing(20)
        
        # 分类标题
        title = QLabel("🔊 音视频设置")
        title.setStyleSheet(StyleManager.get_label_style(color='#96CEB4', font_size=18))
        title.setProperty("font-weight", "bold")
        layout.addWidget(title)
        
        # 设置项目
        settings_items = [
            self.create_setting_group("视频设置", [
                ("输出格式", "ComboBox", ["MP4", "MOV", "AVI", "MKV"], "MP4"),
                ("视频质量", "ComboBox", ["720p", "1080p", "1440p", "4K"], "1080p"),
                ("编码器", "ComboBox", ["H.264", "H.265", "VP9"], "H.264"),
                ("帧率", "ComboBox", ["24fps", "30fps", "60fps"], "30fps")
            ]),
            
            self.create_setting_group("音频设置", [
                ("音频格式", "ComboBox", ["MP3", "WAV", "AAC", "FLAC"], "MP3"),
                ("音频质量", "ComboBox", ["128kbps", "192kbps", "320kbps"], "192kbps"),
                ("采样率", "ComboBox", ["44.1kHz", "48kHz", "96kHz"], "48kHz"),
                ("声道", "ComboBox", ["单声道", "立体声", "5.1环绕"], "立体声")
            ]),
            
            self.create_setting_group("字幕设置", [
                ("字幕格式", "ComboBox", ["SRT", "ASS", "VTT"], "SRT"),
                ("字幕编码", "ComboBox", ["UTF-8", "GBK", "BIG5"], "UTF-8"),
                ("默认字体", "ComboBox", ["微软雅黑", "宋体", "Arial"], "微软雅黑"),
                ("字幕位置", "ComboBox", ["底部", "顶部", "中间"], "底部")
            ])
        ]
        
        for setting_group in settings_items:
            layout.addWidget(setting_group)
        
        layout.addStretch()
        return content
    
    def create_advanced_settings(self):
        """创建高级设置内容"""
        content = QWidget()
        content.setStyleSheet("""
            QWidget {
                background-color: #14161A;
                border: none;
            }
        """)
        layout = QVBoxLayout(content)
        layout.setContentsMargins(30, 25, 30, 25)
        layout.setSpacing(20)
        
        # 分类标题
        title = QLabel("🛠️ 高级设置")
        title.setStyleSheet(StyleManager.get_label_style(color='#96CEB4', font_size=18))
        title.setProperty("font-weight", "bold")
        layout.addWidget(title)
        
        # 设置项目
        settings_items = [
            self.create_setting_group("调试选项", [
                ("调试模式", "CheckBox", None, False),
                ("详细日志", "CheckBox", None, False),
                ("性能监控", "CheckBox", None, True),
                ("错误报告", "CheckBox", None, True)
            ]),
            
            self.create_setting_group("实验功能", [
                ("Beta功能", "CheckBox", None, False),
                ("AI增强", "CheckBox", None, True),
                ("实时预览", "CheckBox", None, False),
                ("云端处理", "CheckBox", None, False)
            ]),
            
            self.create_setting_group("开发者选项", [
                ("API调试", "CheckBox", None, False),
                ("网络日志", "CheckBox", None, False),
                ("导出配置", "Button", None, "导出"),
                ("重置所有设置", "Button", None, "重置")
            ])
        ]
        
        for setting_group in settings_items:
            layout.addWidget(setting_group)
        
        layout.addStretch()
        return content
    
    def create_security_settings(self):
        """创建安全设置内容"""
        content = QWidget()
        content.setStyleSheet("""
            QWidget {
                background-color: #14161A;
                border: none;
            }
        """)
        layout = QVBoxLayout(content)
        layout.setContentsMargins(30, 25, 30, 25)
        layout.setSpacing(20)
        
        # 分类标题
        title = QLabel("🔐 安全设置")
        title.setStyleSheet(StyleManager.get_label_style(color='#96CEB4', font_size=18))
        title.setProperty("font-weight", "bold")
        layout.addWidget(title)
        
        # 设置项目
        settings_items = [
            self.create_setting_group("隐私保护", [
                ("本地处理模式", "CheckBox", None, True),
                ("数据加密", "CheckBox", None, True),
                ("使用统计", "CheckBox", None, False),
                ("错误报告包含个人信息", "CheckBox", None, False)
            ]),
            
            self.create_setting_group("网络安全", [
                ("验证SSL证书", "CheckBox", None, True),
                ("使用代理", "CheckBox", None, False),
                ("代理地址", "LineEdit", None, ""),
                ("超时时间", "ComboBox", ["30秒", "60秒", "120秒"], "60秒")
            ]),
            
            self.create_setting_group("数据管理", [
                ("自动备份配置", "CheckBox", None, True),
                ("备份频率", "ComboBox", ["每天", "每周", "每月"], "每周"),
                ("清除历史记录", "Button", None, "立即清除"),
                ("数据导出", "Button", None, "导出数据")
            ])
        ]
        
        for setting_group in settings_items:
            layout.addWidget(setting_group)
        
        layout.addStretch()
        return content
    
    def create_setting_group(self, title, settings):
        """创建设置组"""
        group_container = QFrame()
        group_container.setStyleSheet("""
            QFrame {
                background-color: rgba(255, 255, 255, 0.02);
                border: 1px solid rgba(255, 255, 255, 0.05);
                border-radius: 15px;
                margin: 5px;
            }
        """)
        
        group_layout = QVBoxLayout(group_container)
        group_layout.setContentsMargins(20, 15, 20, 15)
        group_layout.setSpacing(12)
        
        # 组标题
        title_label = QLabel(title)
        title_label.setStyleSheet(StyleManager.get_label_style(color='#FFFFFF', font_size=15))
        title_label.setProperty("font-weight", "bold")
        group_layout.addWidget(title_label)
        
        # 设置项目
        for setting_name, setting_type, options, default_value in settings:
            setting_item = self.create_setting_item(setting_name, setting_type, options, default_value)
            group_layout.addWidget(setting_item)
        
        return group_container
