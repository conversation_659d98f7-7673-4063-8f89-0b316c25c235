try:
    from .base import RoundedWidget, RoundedFrame
except ImportError:
    # 如果导入失败，提供一个简单的替代实现
    from PySide6.QtWidgets import QWidget, QFrame
    class RoundedWidget(QWidget):
        def __init__(self, bg_color="#1B1E24", border_radius=30):
            super().__init__()
            self.bg_color = bg_color
            self.border_radius = border_radius
            self.setup_style()
        
        def setup_style(self):
            self.setStyleSheet(f"QWidget {{ background-color: {self.bg_color}; border-radius: {self.border_radius}px; }}")
    
    class RoundedFrame(QFrame):
        def __init__(self, bg_color="#1B1E24", border_radius=30):
            super().__init__()
            self.bg_color = bg_color
            self.border_radius = border_radius
            self.setup_style()
        
        def setup_style(self):
            self.setStyleSheet(f"QFrame {{ background-color: {self.bg_color}; border-radius: {self.border_radius}px; border: none; }}")
from .avatar import ClickableAvatar
from .dropzone import DropZone
from .process_list import ProcessListItem
from .subtitle_edit import SubtitleEditItem
from .video_processing import VideoProcessingArea
from .api_settings import APISettingsArea
from .system_settings import SystemSettingsArea
from .function_area import FunctionArea
from .log_area import LogArea
from .subtitle_edit_area import SubtitleEditArea

__all__ = [
    'RoundedWidget',
    'RoundedFrame',
    'ClickableAvatar',
    'DropZone',
    'ProcessListItem',
    'SubtitleEditItem',
    'VideoProcessingArea',
    'APISettingsArea',
    'SystemSettingsArea',
    'FunctionArea',
    'LogArea',
    'SubtitleEditArea'
] 