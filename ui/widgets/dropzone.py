from PySide6.QtWidgets import <PERSON>Widget, QVBoxLayout, QLabel, QPushButton, QFileDialog
from PySide6.QtCore import Qt
from PySide6.QtGui import QDragEnterEvent, QDropEvent
from ..style_manager import StyleManager
from .base import RoundedFrame

class DropZone(QWidget):
    """
    拖放区域组件
    支持拖放文件和点击选择文件
    """
    def __init__(self):
        super().__init__()
        self.uploaded_files = []  # 已上传的文件列表
        self.setup_ui()
    
    def setup_ui(self):
        """初始化拖放区域UI"""
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # 创建背景Frame
        background_frame = RoundedFrame()
        background_frame.setFixedHeight(200)
        background_frame.setStyleSheet("""
            QFrame {
                background-color: rgba(255, 255, 255, 0.05);
                border: 2px dashed rgba(255, 255, 255, 0.2);
                border-radius: 20px;
            }
        """)
        
        # Frame内部布局
        frame_layout = QVBoxLayout(background_frame)
        frame_layout.setContentsMargins(20, 20, 20, 20)
        frame_layout.setSpacing(10)
        frame_layout.setAlignment(Qt.AlignCenter)
        
        # 上传图标
        upload_icon = QLabel("📁")
        upload_icon.setStyleSheet(StyleManager.get_label_style(color='#96CEB4', font_size=48))
        upload_icon.setAlignment(Qt.AlignCenter)
        frame_layout.addWidget(upload_icon)
        
        # 提示文本
        hint_text = QLabel("拖放文件到这里，或")
        hint_text.setStyleSheet(StyleManager.get_label_style(color='#FFFFFF', font_size=16))
        hint_text.setAlignment(Qt.AlignCenter)
        frame_layout.addWidget(hint_text)
        
        # 选择文件按钮
        select_btn = QPushButton("选择文件")
        select_btn.setFixedSize(120, 40)
        select_btn.setStyleSheet(StyleManager.get_button_style(bg_color='#96CEB4', font_size=14, border_radius=20))
        select_btn.clicked.connect(self.on_select_clicked)
        frame_layout.addWidget(select_btn)
        
        # 支持格式提示
        format_hint = QLabel("支持格式：MP4, AVI, MOV, MKV")
        format_hint.setStyleSheet(StyleManager.get_label_style(color=StyleManager.COLORS['text_gray'], font_size=12))
        format_hint.setAlignment(Qt.AlignCenter)
        frame_layout.addWidget(format_hint)
        
        # 将背景Frame添加到主布局
        main_layout.addWidget(background_frame)
        
        # 设置接受拖放
        self.setAcceptDrops(True)
    
    def dragEnterEvent(self, event: QDragEnterEvent):
        """拖放进入事件"""
        if event.mimeData().hasUrls():
            event.acceptProposedAction()
    
    def dropEvent(self, event: QDropEvent):
        """拖放事件"""
        urls = event.mimeData().urls()
        for url in urls:
            file_path = url.toLocalFile()
            if file_path.lower().endswith(('.mp4', '.avi', '.mov', '.mkv')):
                self.uploaded_files.append(file_path)
                print(f"文件已添加: {file_path}")
            else:
                print(f"不支持的文件格式: {file_path}")
    
    def mousePressEvent(self, event):
        """鼠标点击事件"""
        self.on_select_clicked()
    
    def on_select_clicked(self):
        """选择文件按钮点击事件"""
        files, _ = QFileDialog.getOpenFileNames(
            self,
            "选择视频文件",
            "",
            "视频文件 (*.mp4 *.avi *.mov *.mkv)"
        )
        
        for file_path in files:
            self.uploaded_files.append(file_path)
            print(f"文件已添加: {file_path}")
    
    def get_uploaded_files(self):
        """获取已上传的文件列表"""
        return self.uploaded_files
    
    def clear_uploaded_files(self):
        """清空已上传的文件列表"""
        self.uploaded_files = [] 