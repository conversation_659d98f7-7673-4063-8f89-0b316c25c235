from PySide6.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QLineEdit
from PySide6.QtCore import Qt
from ..style_manager import StyleManager
from .base import RoundedFrame

class APISettingsArea(QWidget):
    """
    API设置区域组件
    管理各种API密钥和配置
    """
    def __init__(self, width=612):
        super().__init__()
        self.setFixedWidth(width)
        self.setup_ui()
    
    def setup_ui(self):
        """初始化API设置区域UI"""
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(20)
        
        # 状态区域
        status_area = self.create_status_area()
        main_layout.addWidget(status_area)
        
        # API设置区域
        api_settings = self.create_api_settings()
        main_layout.addWidget(api_settings)
    
    def create_status_area(self):
        """创建状态区域"""
        status_area = RoundedFrame()
        status_area.setFixedHeight(120)
        
        # 创建背景装饰Frame
        background_frame = RoundedFrame(status_area)
        background_frame.setFixedSize(612, 120)
        background_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(150, 206, 180, 0.1), 
                    stop:0.3 rgba(120, 165, 144, 0.08), 
                    stop:0.7 rgba(90, 124, 108, 0.06), 
                    stop:1 rgba(60, 83, 72, 0.04));
                border: 1px solid rgba(150, 206, 180, 0.2);
                border-radius: 25px;
            }
        """)
        
        layout = QHBoxLayout(status_area)
        layout.setContentsMargins(30, 20, 30, 20)
        layout.setSpacing(20)
        
        # 状态卡片
        status_cards = [
            ("🔑", "API密钥", "5/6", "#96CEB4"),
            ("📊", "调用次数", "1,234", "#45B7D1"),
            ("💰", "余额", "$123.45", "#FFA726"),
            ("⚡", "状态", "正常", "#4ECDC4")
        ]
        
        for icon, title, value, color in status_cards:
            card = self.create_status_card(icon, title, value, color)
            layout.addWidget(card)
        
        return status_area
    
    def create_status_card(self, icon, title, value, accent_color):
        """创建状态卡片"""
        card = RoundedFrame()
        card.setFixedSize(120, 80)
        card.setStyleSheet(f"""
            QFrame {{
                background: rgba({StyleManager.hex_to_rgb(accent_color)}, 0.15);
                border: 1px solid rgba({StyleManager.hex_to_rgb(accent_color)}, 0.3);
                border-radius: 15px;
            }}
        """)
        
        layout = QVBoxLayout(card)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(5)
        
        # 图标和标题
        header = QHBoxLayout()
        header.setSpacing(5)
        
        icon_label = QLabel(icon)
        icon_label.setStyleSheet(StyleManager.get_label_style(color=accent_color, font_size=16))
        header.addWidget(icon_label)
        
        title_label = QLabel(title)
        title_label.setStyleSheet(StyleManager.get_label_style(color=StyleManager.COLORS['text_gray'], font_size=12))
        header.addWidget(title_label)
        
        header.addStretch()
        
        layout.addLayout(header)
        
        # 值
        value_label = QLabel(value)
        value_label.setStyleSheet(StyleManager.get_label_style(color='#FFFFFF', font_size=20))
        value_label.setProperty("font-weight", "bold")
        layout.addWidget(value_label)
        
        return card
    
    def create_api_settings(self):
        """创建API设置区域"""
        settings_area = RoundedFrame()
        
        layout = QVBoxLayout(settings_area)
        layout.setContentsMargins(30, 30, 30, 30)
        layout.setSpacing(30)
        
        # Azure TTS设置
        azure_section = self.create_azure_tts_setting_section()
        layout.addWidget(azure_section)
        
        # DeepL设置
        deepl_settings = [
            ("API密钥", "your-deepl-api-key", "输入DeepL API密钥"),
            ("区域", "eu", "选择API区域")
        ]
        deepl_section = self.create_modern_setting_section(
            "DeepL翻译",
            "用于高质量的文本翻译",
            deepl_settings,
            "#45B7D1"
        )
        layout.addWidget(deepl_section)
        
        # OpenAI设置
        openai_settings = [
            ("API密钥", "your-openai-api-key", "输入OpenAI API密钥"),
            ("组织ID", "org-xxxxx", "输入组织ID（可选）")
        ]
        openai_section = self.create_modern_setting_section(
            "OpenAI",
            "用于AI增强功能",
            openai_settings,
            "#FFA726"
        )
        layout.addWidget(openai_section)
        
        return settings_area
    
    def create_azure_tts_setting_section(self):
        """创建Azure TTS设置区域"""
        section = RoundedFrame()
        section.setStyleSheet("""
            QFrame {
                background-color: rgba(150, 206, 180, 0.1);
                border: 1px solid rgba(150, 206, 180, 0.2);
                border-radius: 20px;
            }
        """)
        
        layout = QVBoxLayout(section)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)
        
        # 标题区域
        header = QHBoxLayout()
        header.setSpacing(10)
        
        icon = QLabel("🎙️")
        icon.setStyleSheet(StyleManager.get_label_style(color='#96CEB4', font_size=24))
        header.addWidget(icon)
        
        title = QLabel("Azure语音服务")
        title.setStyleSheet(StyleManager.get_label_style(color='#FFFFFF', font_size=18))
        title.setProperty("font-weight", "bold")
        header.addWidget(title)
        
        header.addStretch()
        
        # 状态标签
        status = QLabel("已连接")
        status.setStyleSheet("""
            QLabel {
                background-color: rgba(150, 206, 180, 0.2);
                color: #96CEB4;
                font-size: 12px;
                padding: 4px 8px;
                border-radius: 10px;
            }
        """)
        header.addWidget(status)
        
        layout.addLayout(header)
        
        # 描述
        desc = QLabel("用于高质量的文本转语音服务")
        desc.setStyleSheet(StyleManager.get_label_style(color=StyleManager.COLORS['text_gray'], font_size=14))
        layout.addWidget(desc)
        
        # 输入字段
        subscription_key = self.create_smart_input_field(
            "订阅密钥",
            "your-subscription-key",
            "输入Azure订阅密钥",
            "#96CEB4",
            lambda v: self.save_azure_config("subscription_key", v)
        )
        layout.addWidget(subscription_key)
        
        region = self.create_smart_input_field(
            "区域",
            "eastus",
            "输入服务区域",
            "#96CEB4",
            lambda v: self.save_azure_config("region", v)
        )
        layout.addWidget(region)
        
        return section
    
    def create_smart_input_field(self, label_text, default_value, placeholder, accent_color, save_callback):
        """创建智能输入字段"""
        container = QWidget()
        container.setFixedHeight(70)
        
        layout = QVBoxLayout(container)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(5)
        
        # 标签
        label = QLabel(label_text)
        label.setStyleSheet(StyleManager.get_label_style(color=StyleManager.COLORS['text_gray'], font_size=12))
        layout.addWidget(label)
        
        # 输入框和按钮的容器
        input_container = QHBoxLayout()
        input_container.setSpacing(10)
        
        # 输入框
        input_field = QLineEdit()
        input_field.setText(default_value)
        input_field.setPlaceholderText(placeholder)
        input_field.setStyleSheet(f"""
            QLineEdit {{
                background-color: rgba(255, 255, 255, 0.05);
                color: #FFFFFF;
                font-size: 14px;
                border: 1px solid rgba(255, 255, 255, 0.1);
                border-radius: 10px;
                padding: 8px 12px;
            }}
            QLineEdit:focus {{
                border: 1px solid {accent_color};
                background-color: rgba(255, 255, 255, 0.08);
            }}
        """)
        input_container.addWidget(input_field)
        
        # 保存按钮
        save_btn = QPushButton("保存")
        save_btn.setFixedSize(60, 35)
        save_btn.setStyleSheet(StyleManager.get_button_style(bg_color=accent_color, font_size=12, border_radius=10))
        save_btn.clicked.connect(lambda: save_callback(input_field.text()))
        input_container.addWidget(save_btn)
        
        layout.addLayout(input_container)
        
        return container
    
    def save_azure_config(self, key_type, value):
        """保存Azure配置"""
        print(f"保存Azure配置 - {key_type}: {value}")
        # TODO: 实际的保存逻辑
    
    def create_modern_setting_section(self, title, description, settings, accent_color):
        """创建现代化设置区域"""
        section = RoundedFrame()
        section.setStyleSheet(f"""
            QFrame {{
                background-color: rgba({StyleManager.hex_to_rgb(accent_color)}, 0.1);
                border: 1px solid rgba({StyleManager.hex_to_rgb(accent_color)}, 0.2);
                border-radius: 20px;
            }}
        """)
        
        layout = QVBoxLayout(section)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)
        
        # 标题区域
        header = QHBoxLayout()
        header.setSpacing(10)
        
        title_label = QLabel(title)
        title_label.setStyleSheet(StyleManager.get_label_style(color='#FFFFFF', font_size=18))
        title_label.setProperty("font-weight", "bold")
        header.addWidget(title_label)
        
        header.addStretch()
        
        # 状态标签
        status = QLabel("已连接")
        status.setStyleSheet(f"""
            QLabel {{
                background-color: rgba({StyleManager.hex_to_rgb(accent_color)}, 0.2);
                color: {accent_color};
                font-size: 12px;
                padding: 4px 8px;
                border-radius: 10px;
            }}
        """)
        header.addWidget(status)
        
        layout.addLayout(header)
        
        # 描述
        desc = QLabel(description)
        desc.setStyleSheet(StyleManager.get_label_style(color=StyleManager.COLORS['text_gray'], font_size=14))
        layout.addWidget(desc)
        
        # 设置字段
        for label_text, default_value, placeholder in settings:
            field = self.create_modern_input_field(label_text, default_value, placeholder, accent_color)
            layout.addWidget(field)
        
        return section
    
    def create_modern_input_field(self, label_text, default_value, placeholder, accent_color):
        """创建现代化输入字段"""
        container = QWidget()
        container.setFixedHeight(70)
        
        layout = QVBoxLayout(container)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(5)
        
        # 标签
        label = QLabel(label_text)
        label.setStyleSheet(StyleManager.get_label_style(color=StyleManager.COLORS['text_gray'], font_size=12))
        layout.addWidget(label)
        
        # 输入框和按钮的容器
        input_container = QHBoxLayout()
        input_container.setSpacing(10)
        
        # 输入框
        input_field = QLineEdit()
        input_field.setText(default_value)
        input_field.setPlaceholderText(placeholder)
        input_field.setStyleSheet(f"""
            QLineEdit {{
                background-color: rgba(255, 255, 255, 0.05);
                color: #FFFFFF;
                font-size: 14px;
                border: 1px solid rgba(255, 255, 255, 0.1);
                border-radius: 10px;
                padding: 8px 12px;
            }}
            QLineEdit:focus {{
                border: 1px solid {accent_color};
                background-color: rgba(255, 255, 255, 0.08);
            }}
        """)
        input_container.addWidget(input_field)
        
        # 保存按钮
        save_btn = QPushButton("保存")
        save_btn.setFixedSize(60, 35)
        save_btn.setStyleSheet(StyleManager.get_button_style(bg_color=accent_color, font_size=12, border_radius=10))
        save_btn.clicked.connect(lambda: print(f"保存设置 - {label_text}: {input_field.text()}"))
        input_container.addWidget(save_btn)
        
        layout.addLayout(input_container)
        
        return container 