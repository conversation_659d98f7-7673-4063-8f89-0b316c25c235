from PySide6.QtWidgets import QWidget, QHBoxLayout, QLabel, QLineEdit
from PySide6.QtCore import Qt
from ..style_manager import StyleManager

class SubtitleEditItem(QWidget):
    """
    字幕编辑项组件
    显示时间码和字幕文本
    """
    def __init__(self, start_time, end_time, subtitle_text, is_marked=False):
        super().__init__()
        self.start_time = start_time
        self.end_time = end_time
        self.subtitle_text = subtitle_text
        self.is_marked = is_marked
        self.setup_ui()
    
    def setup_ui(self):
        """初始化字幕编辑项UI"""
        # 主布局
        main_layout = QHBoxLayout(self)
        main_layout.setContentsMargins(10, 5, 10, 5)
        main_layout.setSpacing(10)
        
        # 时间码
        time_label = QLabel(f"{self.start_time} → {self.end_time}")
        time_label.setFixedWidth(200)
        time_label.setStyleSheet(StyleManager.get_label_style(color=StyleManager.COLORS['text_gray'], font_size=12))
        main_layout.addWidget(time_label)
        
        # 字幕文本输入框
        text_input = QLineEdit(self.subtitle_text)
        text_input.setStyleSheet(f"""
            QLineEdit {{
                background-color: transparent;
                color: {StyleManager.COLORS['marked'] if self.is_marked else StyleManager.COLORS['text_white']};
                font-size: 14px;
                border: none;
                border-radius: 0;
                border-bottom: 1px solid {StyleManager.COLORS['border']};
                padding: 5px;
            }}
            QLineEdit:focus {{
                border-bottom: 1px solid {StyleManager.COLORS['primary']};
            }}
        """)
        main_layout.addWidget(text_input) 