from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel,
    QPushButton, QFrame
)
from PySide6.QtCore import Qt, Signal
from ..style_manager import StyleManager

class FunctionArea(QWidget):
    """
    功能区域
    显示各种功能卡片：视频提取音频、音频提取字幕、字幕翻译、字幕配音等
    """
    # 添加信号用于通知主窗口切换到音频提取界面
    switch_to_audio_extraction = Signal()

    def __init__(self):
        super().__init__()
        self.setup_ui()

    def setup_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(30, 30, 30, 30)
        layout.setSpacing(20)

        # 添加标题区域
        layout.addWidget(self.create_title_section())

        # 添加功能卡片区域
        layout.addWidget(self.create_function_cards())

        # 添加底部提示区域
        layout.addWidget(self.create_bottom_tips())

    def create_title_section(self):
        """创建标题区域"""
        title_frame = QFrame()
        title_frame.setStyleSheet("""
            QFrame {
                background-color: #1B1E24;
                border-radius: 30px;
            }
        """)
        
        title_layout = QVBoxLayout(title_frame)
        title_layout.setContentsMargins(30, 30, 30, 30)
        title_layout.setSpacing(20)
        
        # 添加标题和副标题
        title_label = QLabel("功能中心")
        title_label.setStyleSheet("""
            QLabel {
                color: #FFFFFF;
                font-size: 24px;
                font-weight: bold;
            }
        """)
        
        subtitle_label = QLabel("选择您需要的功能，开始创作之旅")
        subtitle_label.setStyleSheet("""
            QLabel {
                color: rgba(255, 255, 255, 0.7);
                font-size: 14px;
            }
        """)
        
        title_layout.addWidget(title_label)
        title_layout.addWidget(subtitle_label)
        
        # 添加统计卡片
        stats_layout = QHBoxLayout()
        stats_layout.setSpacing(20)
        
        stats_data = [
            ("24", "今日处理视频", "#2B9D7C"),
            ("128", "本月处理视频", "#FF6B6B"),
            ("1024", "累计处理视频", "#4D7CFE")
        ]
        
        for value, label, color in stats_data:
            stats_layout.addWidget(self.create_stat_card(value, label, color))
        
        title_layout.addLayout(stats_layout)
        return title_frame

    def create_stat_card(self, value, label, color):
        """创建统计卡片"""
        card = QFrame()
        card.setStyleSheet(f"""
            QFrame {{
                background-color: {color};
                border-radius: 20px;
                padding: 15px;
            }}
        """)
        
        layout = QVBoxLayout(card)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(5)
        
        value_label = QLabel(value)
        value_label.setStyleSheet("""
            QLabel {
                color: #FFFFFF;
                font-size: 24px;
                font-weight: bold;
            }
        """)
        
        desc_label = QLabel(label)
        desc_label.setStyleSheet("""
            QLabel {
                color: rgba(255, 255, 255, 0.7);
                font-size: 12px;
            }
        """)
        
        layout.addWidget(value_label)
        layout.addWidget(desc_label)
        
        return card

    def create_function_cards(self):
        """创建功能卡片区域"""
        cards_frame = QFrame()
        cards_frame.setStyleSheet("""
            QFrame {
                background-color: #1B1E24;
                border-radius: 30px;
            }
        """)
        
        cards_layout = QVBoxLayout(cards_frame)
        cards_layout.setContentsMargins(30, 30, 30, 30)
        cards_layout.setSpacing(20)
        
        # 功能卡片数据
        functions_data = [
            {
                "title": "视频提取音频",
                "description": "从视频中提取音频文件，支持多种格式",
                "icon": "🎵",
                "color": "#2B9D7C"
            },
            {
                "title": "音频提取字幕",
                "description": "从音频中识别语音并生成字幕文件",
                "icon": "📝",
                "color": "#FF6B6B"
            },
            {
                "title": "字幕翻译",
                "description": "将字幕文件翻译成多种语言",
                "icon": "🌍",
                "color": "#4D7CFE"
            },
            {
                "title": "字幕配音",
                "description": "使用AI语音为字幕生成配音",
                "icon": "🎙️",
                "color": "#FFB86C"
            }
        ]
        
        # 创建功能卡片
        for func_data in functions_data:
            cards_layout.addWidget(self.create_function_card(func_data))
        
        return cards_frame

    def create_function_card(self, func_data):
        """创建功能卡片"""
        card = QFrame()
        card.setStyleSheet(f"""
            QFrame {{
                background-color: #14161A;
                border-radius: 20px;
                border: 1px solid rgba(255, 255, 255, 0.1);
            }}
            QFrame:hover {{
                background-color: #1B1E24;
                border: 1px solid {func_data['color']};
            }}
        """)
        
        layout = QHBoxLayout(card)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)
        
        # 图标
        icon_label = QLabel(func_data['icon'])
        icon_label.setStyleSheet(f"""
            QLabel {{
                background-color: {func_data['color']};
                color: #FFFFFF;
                font-size: 24px;
                padding: 10px;
                border-radius: 15px;
            }}
        """)
        icon_label.setFixedSize(50, 50)
        icon_label.setAlignment(Qt.AlignCenter)
        
        # 标题和描述
        text_layout = QVBoxLayout()
        text_layout.setSpacing(5)
        
        title_label = QLabel(func_data['title'])
        title_label.setStyleSheet("""
            QLabel {
                color: #FFFFFF;
                font-size: 16px;
                font-weight: bold;
            }
        """)
        
        desc_label = QLabel(func_data['description'])
        desc_label.setStyleSheet("""
            QLabel {
                color: rgba(255, 255, 255, 0.7);
                font-size: 12px;
            }
        """)
        
        text_layout.addWidget(title_label)
        text_layout.addWidget(desc_label)
        
        # 开始按钮
        start_button = QPushButton("开始")
        start_button.setStyleSheet(f"""
            QPushButton {{
                background-color: {func_data['color']};
                color: #FFFFFF;
                border: none;
                border-radius: 15px;
                padding: 8px 20px;
                font-size: 14px;
            }}
            QPushButton:hover {{
                background-color: {func_data['color']}CC;
            }}
        """)
        start_button.setCursor(Qt.PointingHandCursor)
        start_button.clicked.connect(lambda: self.on_function_clicked(func_data['title']))
        
        layout.addWidget(icon_label)
        layout.addLayout(text_layout, stretch=1)
        layout.addWidget(start_button)
        
        return card

    def create_bottom_tips(self):
        """创建底部提示区域"""
        tips_frame = QFrame()
        tips_frame.setStyleSheet("""
            QFrame {
                background-color: #1B1E24;
                border-radius: 30px;
            }
        """)
        
        tips_layout = QHBoxLayout(tips_frame)
        tips_layout.setContentsMargins(30, 20, 30, 20)
        tips_layout.setSpacing(20)
        
        # 提示图标
        tip_icon = QLabel("💡")
        tip_icon.setStyleSheet("""
            QLabel {
                color: #FFB86C;
                font-size: 24px;
            }
        """)
        
        # 提示文本
        tip_text = QLabel("提示：您可以通过拖放文件到处理区域来快速开始处理")
        tip_text.setStyleSheet("""
            QLabel {
                color: rgba(255, 255, 255, 0.7);
                font-size: 14px;
            }
        """)
        
        tips_layout.addWidget(tip_icon)
        tips_layout.addWidget(tip_text, stretch=1)
        
        return tips_frame

    def on_function_clicked(self, function_title):
        """处理功能按钮点击事件"""
        if function_title == "视频提取音频":
            self.switch_to_audio_extraction.emit()
        elif function_title == "音频提取字幕":
            self.show_subtitle_editor_dialog()
        elif function_title == "字幕翻译":
            self.show_subtitle_translation_dialog()
        elif function_title == "字幕配音":
            self.show_subtitle_voiceover_dialog()

    def show_subtitle_editor_dialog(self):
        """显示字幕编辑器对话框"""
        from ..subtitle_editor_dialog import SubtitleEditorDialog
        dialog = SubtitleEditorDialog(self)
        dialog.exec_()

    def show_subtitle_translation_dialog(self):
        """显示字幕翻译对话框"""
        from ..subtitle_translation_dialog import SubtitleTranslationDialog
        dialog = SubtitleTranslationDialog(self)
        dialog.exec_()

    def show_subtitle_voiceover_dialog(self):
        """显示字幕配音对话框"""
        from ..subtitle_voiceover_dialog import SubtitleVoiceoverDialog
        dialog = SubtitleVoiceoverDialog(self)
        dialog.exec_() 