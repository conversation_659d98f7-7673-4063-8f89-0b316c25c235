from PySide6.QtWidgets import QVBoxLayout, QScrollArea, QWidget
from PySide6.QtCore import Qt
from .base import RoundedWidget
from .subtitle_edit import SubtitleEditItem

class SubtitleEditArea(RoundedWidget):
    """
    字幕编辑区域组件
    显示和编辑字幕列表
    """
    def __init__(self):
        super().__init__()
        self.setup_ui()
    
    def setup_ui(self):
        """初始化字幕编辑区域UI"""
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setStyleSheet("""
            QScrollArea {
                background-color: transparent;
                border: none;
            }
            QScrollBar:vertical {
                background-color: #14161A;
                width: 8px;
                border-radius: 4px;
            }
            QScrollBar::handle:vertical {
                background-color: #2B9D7C;
                border-radius: 4px;
            }
        """)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setWidgetResizable(True)
        
        # 创建滚动内容容器
        scroll_content = QWidget()
        scroll_content.setStyleSheet("background-color: transparent;")
        
        # 滚动内容布局
        content_layout = QVBoxLayout(scroll_content)
        content_layout.setContentsMargins(10, 10, 10, 10)
        content_layout.setSpacing(5)
        content_layout.setAlignment(Qt.AlignTop)
        
        # 设置滚动内容
        scroll_area.setWidget(scroll_content)
        
        # 将滚动区域添加到主布局
        main_layout.addWidget(scroll_area)
    
    def add_subtitle(self, start_time, end_time, text, is_marked=False):
        """添加字幕条目"""
        subtitle_item = SubtitleEditItem(start_time, end_time, text, is_marked)
        self.findChild(QScrollArea).widget().layout().addWidget(subtitle_item)
    
    def clear_subtitles(self):
        """清空所有字幕"""
        scroll_content = self.findChild(QScrollArea).widget()
        while scroll_content.layout().count():
            item = scroll_content.layout().takeAt(0)
            if item.widget():
                item.widget().deleteLater() 