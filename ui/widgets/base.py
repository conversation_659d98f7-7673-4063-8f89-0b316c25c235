from PySide6.QtWidgets import QWidget, QFrame

# 导入样式管理器，使用错误处理来避免循环导入
try:
    from ..style_manager import StyleManager
except ImportError:
    # 如果导入失败，创建一个简单的StyleManager类
    class StyleManager:
        @staticmethod
        def get_frame_style(bg_color='#1B1E24', border_radius=30):
            return f"""
                QFrame {{
                    background-color: {bg_color};
                    border-radius: {border_radius}px;
                    border: none;
                }}
            """

class RoundedWidget(QWidget):
    """
    圆角矩形组件基类
    提供统一的圆角矩形样式
    """
    def __init__(self, bg_color="#1B1E24", border_radius=30):
        super().__init__()
        self.bg_color = bg_color  # 背景颜色
        self.border_radius = border_radius  # 圆角半径
        self.setup_style()
    
    def setup_style(self):
        """设置组件样式"""
        self.setStyleSheet(f"""
            QWidget {{
                background-color: {self.bg_color};
                border-radius: {self.border_radius}px;
            }}
        """)


class RoundedFrame(QFrame):
    """
    圆角矩形Frame组件
    提供统一的圆角矩形样式
    """
    def __init__(self, bg_color="#1B1E24", border_radius=30):
        super().__init__()
        self.bg_color = bg_color  # 背景颜色
        self.border_radius = border_radius  # 圆角半径
        self.setup_style()
    
    def setup_style(self):
        """设置组件样式"""
        self.setStyleSheet(f"""
            QFrame {{
                background-color: {self.bg_color};
                border-radius: {self.border_radius}px;
                border: none;
            }}
        """) 