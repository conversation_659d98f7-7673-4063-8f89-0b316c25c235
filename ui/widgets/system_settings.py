from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QFrame, QScrollArea, QColorDialog, QFileDialog, QComboBox,
    QSlider
)
from PySide6.QtCore import Qt
from ..style_manager import StyleManager
from .base import RoundedFrame

class SystemSettingsArea(QWidget):
    """
    系统设置区域组件
    提供软件的各种系统级配置选项，包含界面、处理、性能等设置
    """
    def __init__(self):
        super().__init__()
        self.setFixedSize(1359, 776)  # 使用全宽尺寸
        self.current_category = "界面设置"  # 当前选中的设置分类
        self.setup_ui()
    
    def setup_ui(self):
        """初始化系统设置区域UI"""
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # 创建背景Frame
        background_frame = RoundedFrame()
        background_frame.setFixedSize(1359, 776)
        background_frame.setStyleSheet(StyleManager.get_frame_style())
        
        # Frame内部布局
        frame_layout = QVBoxLayout(background_frame)
        frame_layout.setContentsMargins(40, 30, 40, 30)
        frame_layout.setSpacing(20)
        
        # 标题区域
        header_section = self.create_header_section()
        frame_layout.addWidget(header_section)
        
        # 内容区域
        content_section = self.create_content_section()
        frame_layout.addWidget(content_section)
        
        # 将背景Frame添加到主布局
        main_layout.addWidget(background_frame)
    
    def create_header_section(self):
        """创建标题区域"""
        header = RoundedFrame()
        header.setFixedHeight(60)
        
        layout = QHBoxLayout(header)
        layout.setContentsMargins(20, 0, 20, 0)
        layout.setSpacing(10)
        
        # 标题
        title = QLabel("系统设置")
        title.setStyleSheet(StyleManager.get_label_style(color='#FFFFFF', font_size=24))
        layout.addWidget(title)
        
        layout.addStretch()
        
        # 保存按钮
        save_btn = QPushButton("保存设置")
        save_btn.setFixedSize(120, 40)
        save_btn.setStyleSheet(StyleManager.get_button_style(bg_color='#96CEB4', font_size=14, border_radius=20))
        save_btn.clicked.connect(self.save_settings)
        layout.addWidget(save_btn)
        
        # 重置按钮
        reset_btn = QPushButton("重置")
        reset_btn.setFixedSize(80, 40)
        reset_btn.setStyleSheet(StyleManager.get_button_style(bg_color='#666666', font_size=14, border_radius=20))
        reset_btn.clicked.connect(self.reset_settings)
        layout.addWidget(reset_btn)
        
        return header
    
    def create_content_section(self):
        """创建内容区域"""
        content = RoundedFrame()
        
        layout = QHBoxLayout(content)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(20)
        
        # 左侧导航面板
        nav_panel = self.create_navigation_panel()
        layout.addWidget(nav_panel)
        
        # 右侧设置面板
        settings_panel = self.create_settings_panel()
        layout.addWidget(settings_panel)
        
        return content
    
    def create_navigation_panel(self):
        """创建导航面板"""
        nav_panel = RoundedFrame()
        nav_panel.setFixedWidth(200)
        nav_panel.setStyleSheet("""
            QFrame {
                background-color: rgba(255, 255, 255, 0.05);
                border-radius: 20px;
            }
        """)
        
        layout = QVBoxLayout(nav_panel)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)
        
        # 导航按钮列表
        nav_categories = [
            ("🎨", "界面设置", "主题、颜色、字体等"),
            ("⚡", "性能设置", "处理速度、内存使用等"),
            ("📁", "文件设置", "保存路径、格式等"),
            ("🎬", "媒体设置", "视频、音频参数等"),
            ("🔧", "高级设置", "开发者选项等"),
            ("🔒", "安全设置", "隐私、权限等")
        ]
        
        for icon, title, desc in nav_categories:
            nav_btn = self.create_nav_category_button(icon, title, desc)
            layout.addWidget(nav_btn)
        
        layout.addStretch()
        
        return nav_panel
    
    def create_nav_category_button(self, icon, title, desc):
        """创建导航分类按钮"""
        btn = QPushButton()
        btn.setFixedHeight(70)
        btn.setCursor(Qt.PointingHandCursor)
        
        # 设置按钮样式
        is_selected = title == self.current_category
        btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {StyleManager.COLORS['primary'] if is_selected else 'transparent'};
                border: none;
                border-radius: 15px;
                padding: 10px;
                text-align: left;
            }}
            QPushButton:hover {{
                background-color: {StyleManager.COLORS['primary'] if is_selected else 'rgba(255, 255, 255, 0.1)'};
            }}
        """)
        
        # 创建按钮布局
        layout = QVBoxLayout(btn)
        layout.setContentsMargins(15, 5, 15, 5)
        layout.setSpacing(2)
        
        # 图标和标题行
        header = QHBoxLayout()
        header.setSpacing(10)
        
        icon_label = QLabel(icon)
        icon_label.setStyleSheet(StyleManager.get_label_style(
            color='#FFFFFF' if is_selected else StyleManager.COLORS['text_gray'],
            font_size=20
        ))
        header.addWidget(icon_label)
        
        title_label = QLabel(title)
        title_label.setStyleSheet(StyleManager.get_label_style(
            color='#FFFFFF' if is_selected else StyleManager.COLORS['text_gray'],
            font_size=14
        ))
        header.addWidget(title_label)
        
        header.addStretch()
        
        layout.addLayout(header)
        
        # 描述文本
        desc_label = QLabel(desc)
        desc_label.setStyleSheet(StyleManager.get_label_style(
            color=StyleManager.COLORS['text_light_gray'],
            font_size=12
        ))
        layout.addWidget(desc_label)
        
        # 绑定点击事件
        btn.clicked.connect(lambda: self.switch_category(title))
        
        return btn
    
    def create_settings_panel(self):
        """创建设置面板"""
        settings_panel = RoundedFrame()
        settings_panel.setStyleSheet("""
            QFrame {
                background-color: rgba(255, 255, 255, 0.05);
                border-radius: 20px;
            }
        """)
        
        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setStyleSheet("""
            QScrollArea {
                background-color: transparent;
                border: none;
            }
            QScrollBar:vertical {
                background-color: #14161A;
                width: 8px;
                border-radius: 4px;
            }
            QScrollBar::handle:vertical {
                background-color: #2B9D7C;
                border-radius: 4px;
            }
        """)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setWidgetResizable(True)
        
        # 创建滚动内容容器
        scroll_content = QWidget()
        scroll_content.setStyleSheet("background-color: transparent;")
        
        # 滚动内容布局
        content_layout = QVBoxLayout(scroll_content)
        content_layout.setContentsMargins(20, 20, 20, 20)
        content_layout.setSpacing(20)
        content_layout.setAlignment(Qt.AlignTop)
        
        # 根据当前分类添加设置组
        if self.current_category == "界面设置":
            settings = [
                ("主题", "combo", ["深色", "浅色", "跟随系统"]),
                ("主题色", "color", None),
                ("背景图片", "file", None),
                ("背景透明度", "slider", (0, 100)),
                ("字体大小", "combo", ["小", "中", "大"]),
                ("动画效果", "combo", ["开启", "关闭"])
            ]
            group_widget = self.create_setting_group("界面", settings)
            content_layout.addWidget(group_widget)
            
        elif self.current_category == "性能设置":
            settings = [
                ("处理线程数", "combo", ["1", "2", "4", "8", "自动"]),
                ("内存使用限制", "combo", ["2GB", "4GB", "8GB", "不限制"]),
                ("缓存大小", "combo", ["1GB", "2GB", "4GB", "不限制"]),
                ("硬件加速", "combo", ["开启", "关闭"])
            ]
            group_widget = self.create_setting_group("性能", settings)
            content_layout.addWidget(group_widget)
            
        elif self.current_category == "文件设置":
            settings = [
                ("默认保存路径", "file", None),
                ("视频输出格式", "combo", ["MP4", "MKV", "MOV"]),
                ("音频输出格式", "combo", ["MP3", "WAV", "AAC"]),
                ("字幕输出格式", "combo", ["SRT", "ASS", "VTT"])
            ]
            group_widget = self.create_setting_group("文件", settings)
            content_layout.addWidget(group_widget)
            
        elif self.current_category == "媒体设置":
            settings = [
                ("视频编码器", "combo", ["H.264", "H.265", "VP9"]),
                ("视频质量", "combo", ["高", "中", "低"]),
                ("音频编码器", "combo", ["AAC", "MP3", "OPUS"]),
                ("音频质量", "combo", ["高", "中", "低"])
            ]
            group_widget = self.create_setting_group("媒体", settings)
            content_layout.addWidget(group_widget)
            
        elif self.current_category == "高级设置":
            settings = [
                ("调试模式", "combo", ["开启", "关闭"]),
                ("日志级别", "combo", ["详细", "普通", "简略"]),
                ("API超时时间", "combo", ["30秒", "60秒", "120秒"]),
                ("并发请求数", "combo", ["1", "2", "4", "8"])
            ]
            group_widget = self.create_setting_group("高级", settings)
            content_layout.addWidget(group_widget)
            
        elif self.current_category == "安全设置":
            settings = [
                ("数据加密", "combo", ["开启", "关闭"]),
                ("自动备份", "combo", ["开启", "关闭"]),
                ("隐私模式", "combo", ["开启", "关闭"]),
                ("访问权限", "combo", ["完全访问", "受限访问", "只读访问"])
            ]
            group_widget = self.create_setting_group("安全", settings)
            content_layout.addWidget(group_widget)
        
        # 设置滚动内容
        scroll_area.setWidget(scroll_content)
        
        # 创建面板布局
        panel_layout = QVBoxLayout(settings_panel)
        panel_layout.setContentsMargins(0, 0, 0, 0)
        panel_layout.addWidget(scroll_area)
        
        return settings_panel
    
    def create_setting_group(self, group_name, settings):
        """创建设置组"""
        group = RoundedFrame()
        group.setStyleSheet("""
            QFrame {
                background-color: rgba(255, 255, 255, 0.05);
                border-radius: 15px;
            }
        """)
        
        layout = QVBoxLayout(group)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)
        
        # 组标题
        title = QLabel(f"{group_name}设置")
        title.setStyleSheet(StyleManager.get_label_style(color='#FFFFFF', font_size=16))
        layout.addWidget(title)
        
        # 设置项
        for setting_name, setting_type, options in settings:
            setting_item = self.create_setting_item(setting_name, setting_type, options)
            layout.addWidget(setting_item)
        
        return group
    
    def create_setting_item(self, name, item_type, options):
        """创建设置项"""
        item = QWidget()
        item.setFixedHeight(50)
        
        layout = QHBoxLayout(item)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(10)
        
        # 设置项名称
        name_label = QLabel(name)
        name_label.setStyleSheet(StyleManager.get_label_style(color=StyleManager.COLORS['text_gray'], font_size=14))
        name_label.setFixedWidth(120)
        layout.addWidget(name_label)
        
        # 根据类型创建不同的控件
        if item_type == "combo":
            combo = QComboBox()
            combo.addItems(options)
            combo.setFixedWidth(150)
            combo.setStyleSheet("""
                QComboBox {
                    background-color: rgba(255, 255, 255, 0.1);
                    color: #FFFFFF;
                    font-size: 14px;
                    border: 1px solid rgba(255, 255, 255, 0.2);
                    border-radius: 10px;
                    padding: 5px 10px;
                }
                QComboBox::drop-down {
                    border: none;
                }
                QComboBox::down-arrow {
                    image: none;
                }
            """)
            layout.addWidget(combo)
            
        elif item_type == "color":
            color_preview = QLabel()
            color_preview.setFixedSize(30, 30)
            color_preview.setStyleSheet("""
                QLabel {
                    background-color: #2B9D7C;
                    border-radius: 5px;
                }
            """)
            layout.addWidget(color_preview)
            
            select_btn = QPushButton("选择颜色")
            select_btn.setFixedSize(100, 30)
            select_btn.setStyleSheet(StyleManager.get_button_style(bg_color='#666666', font_size=12, border_radius=15))
            select_btn.clicked.connect(lambda: self.update_color_preview(QColorDialog.getColor(), color_preview))
            layout.addWidget(select_btn)
            
        elif item_type == "file":
            path_label = QLabel("未选择")
            path_label.setStyleSheet(StyleManager.get_label_style(color=StyleManager.COLORS['text_gray'], font_size=12))
            layout.addWidget(path_label)
            
            select_btn = QPushButton("选择路径")
            select_btn.setFixedSize(100, 30)
            select_btn.setStyleSheet(StyleManager.get_button_style(bg_color='#666666', font_size=12, border_radius=15))
            select_btn.clicked.connect(lambda: self.browse_folder(path_label))
            layout.addWidget(select_btn)
            
        elif item_type == "slider":
            min_val, max_val = options
            slider = QSlider(Qt.Horizontal)
            slider.setMinimum(min_val)
            slider.setMaximum(max_val)
            slider.setValue((min_val + max_val) // 2)
            slider.setStyleSheet("""
                QSlider::groove:horizontal {
                    height: 4px;
                    background: rgba(255, 255, 255, 0.1);
                    border-radius: 2px;
                }
                QSlider::handle:horizontal {
                    width: 12px;
                    margin: -4px 0;
                    border-radius: 6px;
                    background: #2B9D7C;
                }
                QSlider::sub-page:horizontal {
                    background: #2B9D7C;
                    border-radius: 2px;
                }
            """)
            layout.addWidget(slider)
            
            value_label = QLabel(f"{slider.value()}%")
            value_label.setStyleSheet(StyleManager.get_label_style(color=StyleManager.COLORS['text_gray'], font_size=12))
            value_label.setFixedWidth(50)
            slider.valueChanged.connect(lambda v: value_label.setText(f"{v}%"))
            layout.addWidget(value_label)
        
        layout.addStretch()
        
        return item
    
    def update_color_preview(self, color, preview_label):
        """更新颜色预览"""
        if color.isValid():
            preview_label.setStyleSheet(f"""
                QLabel {{
                    background-color: {color.name()};
                    border-radius: 5px;
                }}
            """)
    
    def browse_folder(self, label):
        """浏览文件夹"""
        folder = QFileDialog.getExistingDirectory(self, "选择文件夹")
        if folder:
            label.setText(folder)
    
    def switch_category(self, category):
        """切换设置分类"""
        if category != self.current_category:
            self.current_category = category
            self.refresh_content()
    
    def refresh_content(self):
        """刷新内容"""
        # 更新导航按钮状态
        nav_panel = self.findChild(QFrame)
        for btn in nav_panel.findChildren(QPushButton):
            title = btn.findChild(QLabel).text()
            is_selected = title == self.current_category
            btn.setStyleSheet(f"""
                QPushButton {{
                    background-color: {StyleManager.COLORS['primary'] if is_selected else 'transparent'};
                    border: none;
                    border-radius: 15px;
                    padding: 10px;
                    text-align: left;
                }}
                QPushButton:hover {{
                    background-color: {StyleManager.COLORS['primary'] if is_selected else 'rgba(255, 255, 255, 0.1)'};
                }}
            """)
    
    def save_settings(self):
        """保存设置"""
        print("保存设置")
        # TODO: 实际的保存逻辑
    
    def reset_settings(self):
        """重置设置"""
        print("重置设置")
        # TODO: 实际的重置逻辑 