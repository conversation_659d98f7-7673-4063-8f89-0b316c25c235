from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel,
    QPushButton, QFrame, QScrollArea
)
from PySide6.QtCore import Qt
from ..style_manager import StyleManager

class LogArea(QWidget):
    """
    日志区域组件
    显示系统运行日志，支持按类型筛选和导出
    """
    def __init__(self):
        super().__init__()
        self.current_filter = "all"  # 当前筛选类型
        self.setup_ui()

    def setup_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(30, 30, 30, 30)
        layout.setSpacing(20)

        # 添加头部区域
        layout.addWidget(self.create_header_section())

        # 添加筛选区域
        layout.addWidget(self.create_filter_section())

        # 添加日志列表区域
        layout.addWidget(self.create_log_list_section())

        # 添加底部区域
        layout.addWidget(self.create_bottom_section())

    def create_header_section(self):
        """创建头部区域"""
        header_frame = QFrame()
        header_frame.setStyleSheet("""
            QFrame {
                background-color: #1B1E24;
                border-radius: 30px;
            }
        """)
        
        header_layout = QVBoxLayout(header_frame)
        header_layout.setContentsMargins(30, 30, 30, 30)
        header_layout.setSpacing(20)
        
        # 添加标题和副标题
        title_label = QLabel("系统日志")
        title_label.setStyleSheet("""
            QLabel {
                color: #FFFFFF;
                font-size: 24px;
                font-weight: bold;
            }
        """)
        
        subtitle_label = QLabel("查看系统运行状态和错误信息")
        subtitle_label.setStyleSheet("""
            QLabel {
                color: rgba(255, 255, 255, 0.7);
                font-size: 14px;
            }
        """)
        
        header_layout.addWidget(title_label)
        header_layout.addWidget(subtitle_label)
        
        # 添加统计卡片
        stats_layout = QHBoxLayout()
        stats_layout.setSpacing(20)
        
        stats_data = [
            ("24", "今日日志", "#2B9D7C"),
            ("3", "错误日志", "#FF6B6B"),
            ("12", "警告日志", "#FFB86C"),
            ("9", "信息日志", "#4D7CFE")
        ]
        
        for count, label, color in stats_data:
            stats_layout.addWidget(self.create_log_stat_card(count, label, color))
        
        header_layout.addLayout(stats_layout)
        return header_frame

    def create_log_stat_card(self, count, label, color):
        """创建日志统计卡片"""
        card = QFrame()
        card.setStyleSheet(f"""
            QFrame {{
                background-color: {color};
                border-radius: 20px;
                padding: 15px;
            }}
        """)
        
        layout = QVBoxLayout(card)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(5)
        
        count_label = QLabel(count)
        count_label.setStyleSheet("""
            QLabel {
                color: #FFFFFF;
                font-size: 24px;
                font-weight: bold;
            }
        """)
        
        desc_label = QLabel(label)
        desc_label.setStyleSheet("""
            QLabel {
                color: rgba(255, 255, 255, 0.7);
                font-size: 12px;
            }
        """)
        
        layout.addWidget(count_label)
        layout.addWidget(desc_label)
        
        return card

    def create_filter_section(self):
        """创建筛选区域"""
        filter_frame = QFrame()
        filter_frame.setStyleSheet("""
            QFrame {
                background-color: #1B1E24;
                border-radius: 30px;
            }
        """)
        
        filter_layout = QHBoxLayout(filter_frame)
        filter_layout.setContentsMargins(30, 20, 30, 20)
        filter_layout.setSpacing(10)
        
        # 添加筛选按钮
        filter_layout.addWidget(self.create_filter_button("全部", "all", "#4D7CFE"))
        filter_layout.addWidget(self.create_filter_button("错误", "error", "#FF6B6B"))
        filter_layout.addWidget(self.create_filter_button("警告", "warning", "#FFB86C"))
        filter_layout.addWidget(self.create_filter_button("信息", "info", "#2B9D7C"))
        
        filter_layout.addStretch()
        return filter_frame

    def create_filter_button(self, text, filter_type, color):
        """创建筛选按钮"""
        button = QPushButton(text)
        button.setCheckable(True)
        button.setChecked(filter_type == self.current_filter)
        button.setCursor(Qt.PointingHandCursor)
        
        # 设置按钮样式
        button.setStyleSheet(f"""
            QPushButton {{
                background-color: {color if filter_type == self.current_filter else '#14161A'};
                color: {'#FFFFFF' if filter_type == self.current_filter else 'rgba(255, 255, 255, 0.7)'};
                border: none;
                border-radius: 15px;
                padding: 8px 20px;
                font-size: 14px;
            }}
            QPushButton:hover {{
                background-color: {color};
                color: #FFFFFF;
            }}
        """)
        
        button.clicked.connect(lambda: self.set_filter(filter_type))
        return button

    def create_log_list_section(self):
        """创建日志列表区域"""
        list_frame = QFrame()
        list_frame.setStyleSheet("""
            QFrame {
                background-color: #1B1E24;
                border-radius: 30px;
            }
        """)
        
        list_layout = QVBoxLayout(list_frame)
        list_layout.setContentsMargins(30, 30, 30, 30)
        list_layout.setSpacing(10)
        
        # 添加示例日志
        sample_logs = [
            ("2024-01-01 12:00:00", "INFO", "系统", "系统启动成功"),
            ("2024-01-01 12:01:00", "WARNING", "视频处理", "视频编码不支持"),
            ("2024-01-01 12:02:00", "ERROR", "音频处理", "音频文件损坏"),
            ("2024-01-01 12:03:00", "INFO", "字幕", "字幕生成完成")
        ]
        
        for timestamp, level, log_type, message in sample_logs:
            list_layout.addWidget(self.create_log_item(timestamp, level, log_type, message))
        
        list_layout.addStretch()
        return list_frame

    def create_log_item(self, timestamp, level, log_type, message):
        """创建日志项"""
        item = QFrame()
        item.setStyleSheet("""
            QFrame {
                background-color: #14161A;
                border-radius: 15px;
                border: 1px solid rgba(255, 255, 255, 0.1);
            }
            QFrame:hover {
                background-color: #1B1E24;
                border: 1px solid rgba(255, 255, 255, 0.2);
            }
        """)
        
        layout = QHBoxLayout(item)
        layout.setContentsMargins(20, 15, 20, 15)
        layout.setSpacing(20)
        
        # 日志等级标签
        level_colors = {
            "INFO": "#2B9D7C",
            "WARNING": "#FFB86C",
            "ERROR": "#FF6B6B"
        }
        
        level_label = QLabel(level)
        level_label.setStyleSheet(f"""
            QLabel {{
                background-color: {level_colors.get(level, '#4D7CFE')};
                color: #FFFFFF;
                padding: 5px 10px;
                border-radius: 10px;
                font-size: 12px;
            }}
        """)
        
        # 时间戳标签
        time_label = QLabel(timestamp)
        time_label.setStyleSheet("""
            QLabel {
                color: rgba(255, 255, 255, 0.4);
                font-size: 12px;
            }
        """)
        
        # 日志类型标签
        type_label = QLabel(log_type)
        type_label.setStyleSheet("""
            QLabel {
                color: rgba(255, 255, 255, 0.7);
                font-size: 14px;
            }
        """)
        
        # 日志消息标签
        message_label = QLabel(message)
        message_label.setStyleSheet("""
            QLabel {
                color: #FFFFFF;
                font-size: 14px;
            }
        """)
        
        layout.addWidget(level_label)
        layout.addWidget(time_label)
        layout.addWidget(type_label)
        layout.addWidget(message_label, stretch=1)
        
        return item

    def create_bottom_section(self):
        """创建底部区域"""
        bottom_frame = QFrame()
        bottom_frame.setStyleSheet("""
            QFrame {
                background-color: #1B1E24;
                border-radius: 30px;
            }
        """)
        
        bottom_layout = QHBoxLayout(bottom_frame)
        bottom_layout.setContentsMargins(30, 20, 30, 20)
        bottom_layout.setSpacing(20)
        
        # 添加操作按钮
        clear_button = QPushButton("清空日志")
        clear_button.setStyleSheet("""
            QPushButton {
                background-color: #FF6B6B;
                color: #FFFFFF;
                border: none;
                border-radius: 15px;
                padding: 8px 20px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #FF8B8B;
            }
        """)
        clear_button.setCursor(Qt.PointingHandCursor)
        clear_button.clicked.connect(self.clear_logs)
        
        export_button = QPushButton("导出日志")
        export_button.setStyleSheet("""
            QPushButton {
                background-color: #2B9D7C;
                color: #FFFFFF;
                border: none;
                border-radius: 15px;
                padding: 8px 20px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #00CC55;
            }
        """)
        export_button.setCursor(Qt.PointingHandCursor)
        export_button.clicked.connect(self.export_logs)
        
        refresh_button = QPushButton("刷新")
        refresh_button.setStyleSheet("""
            QPushButton {
                background-color: #4D7CFE;
                color: #FFFFFF;
                border: none;
                border-radius: 15px;
                padding: 8px 20px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #6D9CFE;
            }
        """)
        refresh_button.setCursor(Qt.PointingHandCursor)
        refresh_button.clicked.connect(self.refresh_logs)
        
        bottom_layout.addStretch()
        bottom_layout.addWidget(clear_button)
        bottom_layout.addWidget(export_button)
        bottom_layout.addWidget(refresh_button)
        
        return bottom_frame

    def set_filter(self, filter_type):
        """设置日志筛选类型"""
        self.current_filter = filter_type
        self.refresh_logs()

    def clear_logs(self):
        """清空日志"""
        pass  # 实现清空日志的逻辑

    def export_logs(self):
        """导出日志"""
        pass  # 实现导出日志的逻辑

    def refresh_logs(self):
        """刷新日志列表"""
        pass  # 实现刷新日志的逻辑 