from PySide6.QtWidgets import QVBoxLayout, QHBoxLayout, QLabel, QProgressBar, QPushButton
from PySide6.QtCore import Qt
from ..style_manager import StyleManager
from .base import RoundedWidget

class ProcessListItem(RoundedWidget):
    """
    处理列表项组件
    显示文件名、处理状态和进度条
    """
    def __init__(self, filename, status, progress):
        super().__init__()
        self.filename = filename
        self.status = status
        self.progress = progress
        self.setup_ui()
    
    def setup_ui(self):
        """初始化处理列表项UI"""
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(15, 10, 15, 10)
        main_layout.setSpacing(8)
        
        # 上部分：文件名和状态
        top_section = QHBoxLayout()
        top_section.setContentsMargins(0, 0, 0, 0)
        top_section.setSpacing(10)
        
        # 文件名
        filename_label = QLabel(self.filename)
        filename_label.setStyleSheet(StyleManager.get_label_style(color='#FFFFFF', font_size=14))
        top_section.addWidget(filename_label)
        
        top_section.addStretch()
        
        # 状态标签
        status_label = QLabel(self.status)
        status_label.setStyleSheet("""
            QLabel {
                background-color: rgba(150, 206, 180, 0.2);
                color: #96CEB4;
                font-size: 12px;
                padding: 4px 8px;
                border-radius: 10px;
            }
        """)
        top_section.addWidget(status_label)
        
        # 删除按钮
        delete_btn = QPushButton("×")
        delete_btn.setFixedSize(24, 24)
        delete_btn.setStyleSheet("""
            QPushButton {
                background-color: transparent;
                color: rgba(255, 255, 255, 0.5);
                font-size: 16px;
                border: none;
                border-radius: 12px;
            }
            QPushButton:hover {
                background-color: rgba(255, 255, 255, 0.1);
                color: #FFFFFF;
            }
        """)
        top_section.addWidget(delete_btn)
        
        main_layout.addLayout(top_section)
        
        # 下部分：进度条
        progress_bar = QProgressBar()
        progress_bar.setFixedHeight(6)
        progress_bar.setValue(self.progress)
        progress_bar.setTextVisible(False)
        progress_bar.setStyleSheet("""
            QProgressBar {
                background-color: rgba(255, 255, 255, 0.1);
                border: none;
                border-radius: 3px;
            }
            QProgressBar::chunk {
                background-color: #96CEB4;
                border-radius: 3px;
            }
        """)
        main_layout.addWidget(progress_bar) 