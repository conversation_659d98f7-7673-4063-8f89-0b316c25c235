from PySide6.QtWidgets import QLabel, QFileDialog
from PySide6.QtCore import Qt
from PySide6.QtGui import QPixmap

class ClickableAvatar(QLabel):
    """
    可点击上传图片的头像组件
    支持点击选择本地图片并显示，具有现代化企业级UI效果
    """
    def __init__(self, size=48):
        super().__init__()
        self.avatar_size = size  # 头像尺寸
        self.has_custom_image = False  # 是否有自定义图片
        self.setFixedSize(size, size)
        self.setAlignment(Qt.AlignCenter)
        self.setCursor(Qt.PointingHandCursor)  # 设置鼠标悬停时为手型光标
        
        # 添加默认的用户图标文字
        self.setText("🎭")  # 使用更有艺术感的图标
        self.setStyleSheet(self.get_default_style())
    
    def get_default_style(self):
        """获取默认样式 - 现代化多层渐变背景与阴影效果"""
        return f"""
            QLabel {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #00FF7F, stop:0.3 #2B9D7C, stop:0.7 #00BB45, stop:1 #009930);
                border: 3px solid qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.4), stop:0.5 rgba(255, 255, 255, 0.2), stop:1 rgba(255, 255, 255, 0.1));
                border-radius: {self.avatar_size//2}px;
                color: #FFFFFF;
                font-size: {self.avatar_size//2.5}px;
                font-weight: bold;
            }}
        """
    
    def get_hover_style(self):
        """获取鼠标悬停时的样式"""
        return f"""
            QLabel {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #00FF9F, stop:0.3 #2BBDAC, stop:0.7 #00DB65, stop:1 #00B940);
                border: 3px solid qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.6), stop:0.5 rgba(255, 255, 255, 0.4), stop:1 rgba(255, 255, 255, 0.2));
                border-radius: {self.avatar_size//2}px;
                color: #FFFFFF;
                font-size: {self.avatar_size//2.5}px;
                font-weight: bold;
            }}
        """
    
    def get_image_style(self):
        """获取图片模式的样式"""
        return f"""
            QLabel {{
                border: 3px solid qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.4), stop:0.5 rgba(255, 255, 255, 0.2), stop:1 rgba(255, 255, 255, 0.1));
                border-radius: {self.avatar_size//2}px;
            }}
        """
    
    def get_image_hover_style(self):
        """获取图片模式下鼠标悬停时的样式"""
        return f"""
            QLabel {{
                border: 3px solid qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.6), stop:0.5 rgba(255, 255, 255, 0.4), stop:1 rgba(255, 255, 255, 0.2));
                border-radius: {self.avatar_size//2}px;
            }}
        """
    
    def enterEvent(self, event):
        """鼠标进入事件"""
        if self.has_custom_image:
            self.setStyleSheet(self.get_image_hover_style())
        else:
            self.setStyleSheet(self.get_hover_style())
    
    def leaveEvent(self, event):
        """鼠标离开事件"""
        if self.has_custom_image:
            self.setStyleSheet(self.get_image_style())
        else:
            self.setStyleSheet(self.get_default_style())
    
    def mousePressEvent(self, event):
        """鼠标点击事件 - 打开文件选择器"""
        if event.button() == Qt.LeftButton:
            file_path, _ = QFileDialog.getOpenFileName(
                self,
                "选择头像图片",
                "",
                "图片文件 (*.png *.jpg *.jpeg *.bmp *.gif)"
            )
            if file_path:
                self.load_avatar_image(file_path)
    
    def load_avatar_image(self, file_path):
        """加载头像图片"""
        pixmap = QPixmap(file_path)
        if not pixmap.isNull():
            # 保持纵横比缩放图片
            scaled_pixmap = pixmap.scaled(
                self.avatar_size,
                self.avatar_size,
                Qt.KeepAspectRatio,
                Qt.SmoothTransformation
            )
            self.setPixmap(scaled_pixmap)
            self.has_custom_image = True
            self.setStyleSheet(self.get_image_style()) 