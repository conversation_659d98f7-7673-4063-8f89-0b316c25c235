from PySide6.QtWidgets import QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QComboBox, QSlider
from PySide6.QtCore import Qt
from ..style_manager import StyleManager
from .base import RoundedWidget

class VideoProcessingArea(RoundedWidget):
    """
    视频处理区域组件
    包含语音识别、翻译、配音和合成等功能区域
    """
    def __init__(self):
        super().__init__()
        self.setup_ui()
    
    def setup_ui(self):
        """初始化视频处理区域UI"""
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)
        
        # 标题区域
        header = self.create_header()
        main_layout.addWidget(header)
        
        # 语音识别区域
        speech_recognition = self.create_speech_recognition_section()
        main_layout.addWidget(speech_recognition)
        
        # 翻译区域
        translation = self.create_translation_section()
        main_layout.addWidget(translation)
        
        # 配音区域
        voice = self.create_voice_section()
        main_layout.addWidget(voice)
        
        # 合成区域
        synthesis = self.create_synthesis_section()
        main_layout.addWidget(synthesis)
        
        # 按钮区域
        buttons = self.create_button_section()
        main_layout.addWidget(buttons)
    
    def create_header(self):
        """创建标题区域"""
        header = RoundedWidget()
        header.setFixedHeight(60)
        
        layout = QHBoxLayout(header)
        layout.setContentsMargins(20, 0, 20, 0)
        layout.setSpacing(10)
        
        # 标题
        title = QLabel("视频处理")
        title.setStyleSheet(StyleManager.get_label_style(color='#FFFFFF', font_size=24))
        layout.addWidget(title)
        
        layout.addStretch()
        
        # 设置按钮
        settings_btn = QPushButton("⚙️ 设置")
        settings_btn.setFixedSize(100, 36)
        settings_btn.setStyleSheet(StyleManager.get_button_style(bg_color='#666666', font_size=14, border_radius=18))
        layout.addWidget(settings_btn)
        
        return header
    
    def create_speech_recognition_section(self):
        """创建语音识别区域"""
        section = RoundedWidget()
        section.setFixedHeight(120)
        
        layout = QVBoxLayout(section)
        layout.setContentsMargins(20, 15, 20, 15)
        layout.setSpacing(10)
        
        # 标题行
        title_row = QHBoxLayout()
        title_row.setSpacing(10)
        
        title = QLabel("语音识别")
        title.setStyleSheet(StyleManager.get_label_style(color='#FFFFFF', font_size=16))
        title_row.addWidget(title)
        
        status = QLabel("准备就绪")
        status.setStyleSheet("""
            QLabel {
                background-color: rgba(150, 206, 180, 0.2);
                color: #96CEB4;
                font-size: 12px;
                padding: 4px 8px;
                border-radius: 10px;
            }
        """)
        title_row.addWidget(status)
        
        title_row.addStretch()
        
        # 模型选择
        model_combo = QComboBox()
        model_combo.addItems(["Whisper Base", "Whisper Small", "Whisper Medium", "Whisper Large"])
        model_combo.setFixedWidth(150)
        model_combo.setStyleSheet("""
            QComboBox {
                background-color: rgba(255, 255, 255, 0.1);
                color: #FFFFFF;
                font-size: 12px;
                border: 1px solid rgba(255, 255, 255, 0.2);
                border-radius: 15px;
                padding: 5px 10px;
            }
            QComboBox::drop-down {
                border: none;
            }
            QComboBox::down-arrow {
                image: none;
            }
        """)
        title_row.addWidget(model_combo)
        
        layout.addLayout(title_row)
        
        # 选项行
        options_row = QHBoxLayout()
        options_row.setSpacing(20)
        
        # 语言选择
        language_combo = QComboBox()
        language_combo.addItems(["自动检测", "中文", "英语", "日语", "韩语"])
        language_combo.setFixedWidth(100)
        language_combo.setStyleSheet("""
            QComboBox {
                background-color: rgba(255, 255, 255, 0.1);
                color: #FFFFFF;
                font-size: 12px;
                border: 1px solid rgba(255, 255, 255, 0.2);
                border-radius: 15px;
                padding: 5px 10px;
            }
            QComboBox::drop-down {
                border: none;
            }
            QComboBox::down-arrow {
                image: none;
            }
        """)
        options_row.addWidget(language_combo)
        
        # 降噪等级
        noise_reduction = QHBoxLayout()
        noise_reduction.setSpacing(10)
        
        noise_label = QLabel("降噪等级")
        noise_label.setStyleSheet(StyleManager.get_label_style(color=StyleManager.COLORS['text_gray'], font_size=12))
        noise_reduction.addWidget(noise_label)
        
        noise_slider = QSlider(Qt.Horizontal)
        noise_slider.setFixedWidth(100)
        noise_slider.setMinimum(0)
        noise_slider.setMaximum(100)
        noise_slider.setValue(50)
        noise_slider.setStyleSheet("""
            QSlider::groove:horizontal {
                height: 4px;
                background: rgba(255, 255, 255, 0.1);
                border-radius: 2px;
            }
            QSlider::handle:horizontal {
                width: 12px;
                margin: -4px 0;
                border-radius: 6px;
                background: #96CEB4;
            }
            QSlider::sub-page:horizontal {
                background: #96CEB4;
                border-radius: 2px;
            }
        """)
        noise_reduction.addWidget(noise_slider)
        
        options_row.addLayout(noise_reduction)
        
        # 音量增益
        volume_gain = QHBoxLayout()
        volume_gain.setSpacing(10)
        
        volume_label = QLabel("音量增益")
        volume_label.setStyleSheet(StyleManager.get_label_style(color=StyleManager.COLORS['text_gray'], font_size=12))
        volume_gain.addWidget(volume_label)
        
        volume_slider = QSlider(Qt.Horizontal)
        volume_slider.setFixedWidth(100)
        volume_slider.setMinimum(-20)
        volume_slider.setMaximum(20)
        volume_slider.setValue(0)
        volume_slider.setStyleSheet("""
            QSlider::groove:horizontal {
                height: 4px;
                background: rgba(255, 255, 255, 0.1);
                border-radius: 2px;
            }
            QSlider::handle:horizontal {
                width: 12px;
                margin: -4px 0;
                border-radius: 6px;
                background: #96CEB4;
            }
            QSlider::sub-page:horizontal {
                background: #96CEB4;
                border-radius: 2px;
            }
        """)
        volume_gain.addWidget(volume_slider)
        
        options_row.addLayout(volume_gain)
        
        options_row.addStretch()
        
        layout.addLayout(options_row)
        
        return section
    
    def create_translation_section(self):
        """创建翻译区域"""
        section = RoundedWidget()
        section.setFixedHeight(120)
        
        layout = QVBoxLayout(section)
        layout.setContentsMargins(20, 15, 20, 15)
        layout.setSpacing(10)
        
        # 标题行
        title_row = QHBoxLayout()
        title_row.setSpacing(10)
        
        title = QLabel("翻译")
        title.setStyleSheet(StyleManager.get_label_style(color='#FFFFFF', font_size=16))
        title_row.addWidget(title)
        
        status = QLabel("准备就绪")
        status.setStyleSheet("""
            QLabel {
                background-color: rgba(150, 206, 180, 0.2);
                color: #96CEB4;
                font-size: 12px;
                padding: 4px 8px;
                border-radius: 10px;
            }
        """)
        title_row.addWidget(status)
        
        title_row.addStretch()
        
        # 翻译服务选择
        service_combo = QComboBox()
        service_combo.addItems(["DeepL Pro", "OpenAI", "Azure", "Google"])
        service_combo.setFixedWidth(150)
        service_combo.setStyleSheet("""
            QComboBox {
                background-color: rgba(255, 255, 255, 0.1);
                color: #FFFFFF;
                font-size: 12px;
                border: 1px solid rgba(255, 255, 255, 0.2);
                border-radius: 15px;
                padding: 5px 10px;
            }
            QComboBox::drop-down {
                border: none;
            }
            QComboBox::down-arrow {
                image: none;
            }
        """)
        title_row.addWidget(service_combo)
        
        layout.addLayout(title_row)
        
        # 选项行
        options_row = QHBoxLayout()
        options_row.setSpacing(20)
        
        # 目标语言
        target_combo = QComboBox()
        target_combo.addItems(["中文", "英语", "日语", "韩语"])
        target_combo.setFixedWidth(100)
        target_combo.setStyleSheet("""
            QComboBox {
                background-color: rgba(255, 255, 255, 0.1);
                color: #FFFFFF;
                font-size: 12px;
                border: 1px solid rgba(255, 255, 255, 0.2);
                border-radius: 15px;
                padding: 5px 10px;
            }
            QComboBox::drop-down {
                border: none;
            }
            QComboBox::down-arrow {
                image: none;
            }
        """)
        options_row.addWidget(target_combo)
        
        # 翻译风格
        style_combo = QComboBox()
        style_combo.addItems(["正式", "口语", "文学", "技术"])
        style_combo.setFixedWidth(100)
        style_combo.setStyleSheet("""
            QComboBox {
                background-color: rgba(255, 255, 255, 0.1);
                color: #FFFFFF;
                font-size: 12px;
                border: 1px solid rgba(255, 255, 255, 0.2);
                border-radius: 15px;
                padding: 5px 10px;
            }
            QComboBox::drop-down {
                border: none;
            }
            QComboBox::down-arrow {
                image: none;
            }
        """)
        options_row.addWidget(style_combo)
        
        options_row.addStretch()
        
        layout.addLayout(options_row)
        
        return section
    
    def create_voice_section(self):
        """创建配音区域"""
        section = RoundedWidget()
        section.setFixedHeight(120)
        
        layout = QVBoxLayout(section)
        layout.setContentsMargins(20, 15, 20, 15)
        layout.setSpacing(10)
        
        # 标题行
        title_row = QHBoxLayout()
        title_row.setSpacing(10)
        
        title = QLabel("配音")
        title.setStyleSheet(StyleManager.get_label_style(color='#FFFFFF', font_size=16))
        title_row.addWidget(title)
        
        status = QLabel("准备就绪")
        status.setStyleSheet("""
            QLabel {
                background-color: rgba(150, 206, 180, 0.2);
                color: #96CEB4;
                font-size: 12px;
                padding: 4px 8px;
                border-radius: 10px;
            }
        """)
        title_row.addWidget(status)
        
        title_row.addStretch()
        
        # 配音服务选择
        service_combo = QComboBox()
        service_combo.addItems(["Azure TTS", "Google TTS", "Amazon Polly"])
        service_combo.setFixedWidth(150)
        service_combo.setStyleSheet("""
            QComboBox {
                background-color: rgba(255, 255, 255, 0.1);
                color: #FFFFFF;
                font-size: 12px;
                border: 1px solid rgba(255, 255, 255, 0.2);
                border-radius: 15px;
                padding: 5px 10px;
            }
            QComboBox::drop-down {
                border: none;
            }
            QComboBox::down-arrow {
                image: none;
            }
        """)
        title_row.addWidget(service_combo)
        
        layout.addLayout(title_row)
        
        # 选项行
        options_row = QHBoxLayout()
        options_row.setSpacing(20)
        
        # 声音选择
        voice_combo = QComboBox()
        voice_combo.addItems(["晓晓", "云扬", "晓涵", "晓墨"])
        voice_combo.setFixedWidth(100)
        voice_combo.setStyleSheet("""
            QComboBox {
                background-color: rgba(255, 255, 255, 0.1);
                color: #FFFFFF;
                font-size: 12px;
                border: 1px solid rgba(255, 255, 255, 0.2);
                border-radius: 15px;
                padding: 5px 10px;
            }
            QComboBox::drop-down {
                border: none;
            }
            QComboBox::down-arrow {
                image: none;
            }
        """)
        options_row.addWidget(voice_combo)
        
        # 语速调节
        speed = QHBoxLayout()
        speed.setSpacing(10)
        
        speed_label = QLabel("语速")
        speed_label.setStyleSheet(StyleManager.get_label_style(color=StyleManager.COLORS['text_gray'], font_size=12))
        speed.addWidget(speed_label)
        
        speed_slider = QSlider(Qt.Horizontal)
        speed_slider.setFixedWidth(100)
        speed_slider.setMinimum(-50)
        speed_slider.setMaximum(50)
        speed_slider.setValue(0)
        speed_slider.setStyleSheet("""
            QSlider::groove:horizontal {
                height: 4px;
                background: rgba(255, 255, 255, 0.1);
                border-radius: 2px;
            }
            QSlider::handle:horizontal {
                width: 12px;
                margin: -4px 0;
                border-radius: 6px;
                background: #96CEB4;
            }
            QSlider::sub-page:horizontal {
                background: #96CEB4;
                border-radius: 2px;
            }
        """)
        speed.addWidget(speed_slider)
        
        options_row.addLayout(speed)
        
        # 音调调节
        pitch = QHBoxLayout()
        pitch.setSpacing(10)
        
        pitch_label = QLabel("音调")
        pitch_label.setStyleSheet(StyleManager.get_label_style(color=StyleManager.COLORS['text_gray'], font_size=12))
        pitch.addWidget(pitch_label)
        
        pitch_slider = QSlider(Qt.Horizontal)
        pitch_slider.setFixedWidth(100)
        pitch_slider.setMinimum(-50)
        pitch_slider.setMaximum(50)
        pitch_slider.setValue(0)
        pitch_slider.setStyleSheet("""
            QSlider::groove:horizontal {
                height: 4px;
                background: rgba(255, 255, 255, 0.1);
                border-radius: 2px;
            }
            QSlider::handle:horizontal {
                width: 12px;
                margin: -4px 0;
                border-radius: 6px;
                background: #96CEB4;
            }
            QSlider::sub-page:horizontal {
                background: #96CEB4;
                border-radius: 2px;
            }
        """)
        pitch.addWidget(pitch_slider)
        
        options_row.addLayout(pitch)
        
        # 试听按钮
        test_btn = QPushButton("试听")
        test_btn.setFixedSize(80, 30)
        test_btn.setStyleSheet(StyleManager.get_button_style(bg_color='#96CEB4', font_size=12, border_radius=15))
        test_btn.clicked.connect(self.test_voice)
        options_row.addWidget(test_btn)
        
        options_row.addStretch()
        
        layout.addLayout(options_row)
        
        return section
    
    def create_synthesis_section(self):
        """创建合成区域"""
        section = RoundedWidget()
        section.setFixedHeight(120)
        
        layout = QVBoxLayout(section)
        layout.setContentsMargins(20, 15, 20, 15)
        layout.setSpacing(10)
        
        # 标题行
        title_row = QHBoxLayout()
        title_row.setSpacing(10)
        
        title = QLabel("合成")
        title.setStyleSheet(StyleManager.get_label_style(color='#FFFFFF', font_size=16))
        title_row.addWidget(title)
        
        status = QLabel("准备就绪")
        status.setStyleSheet("""
            QLabel {
                background-color: rgba(150, 206, 180, 0.2);
                color: #96CEB4;
                font-size: 12px;
                padding: 4px 8px;
                border-radius: 10px;
            }
        """)
        title_row.addWidget(status)
        
        title_row.addStretch()
        
        # 输出格式选择
        format_combo = QComboBox()
        format_combo.addItems(["MP4", "MKV", "MOV"])
        format_combo.setFixedWidth(150)
        format_combo.setStyleSheet("""
            QComboBox {
                background-color: rgba(255, 255, 255, 0.1);
                color: #FFFFFF;
                font-size: 12px;
                border: 1px solid rgba(255, 255, 255, 0.2);
                border-radius: 15px;
                padding: 5px 10px;
            }
            QComboBox::drop-down {
                border: none;
            }
            QComboBox::down-arrow {
                image: none;
            }
        """)
        title_row.addWidget(format_combo)
        
        layout.addLayout(title_row)
        
        # 选项行
        options_row = QHBoxLayout()
        options_row.setSpacing(20)
        
        # 编码器选择
        codec_combo = QComboBox()
        codec_combo.addItems(["H.264", "H.265", "VP9"])
        codec_combo.setFixedWidth(100)
        codec_combo.setStyleSheet("""
            QComboBox {
                background-color: rgba(255, 255, 255, 0.1);
                color: #FFFFFF;
                font-size: 12px;
                border: 1px solid rgba(255, 255, 255, 0.2);
                border-radius: 15px;
                padding: 5px 10px;
            }
            QComboBox::drop-down {
                border: none;
            }
            QComboBox::down-arrow {
                image: none;
            }
        """)
        options_row.addWidget(codec_combo)
        
        # 质量选择
        quality_combo = QComboBox()
        quality_combo.addItems(["高质量", "中等", "低质量"])
        quality_combo.setFixedWidth(100)
        quality_combo.setStyleSheet("""
            QComboBox {
                background-color: rgba(255, 255, 255, 0.1);
                color: #FFFFFF;
                font-size: 12px;
                border: 1px solid rgba(255, 255, 255, 0.2);
                border-radius: 15px;
                padding: 5px 10px;
            }
            QComboBox::drop-down {
                border: none;
            }
            QComboBox::down-arrow {
                image: none;
            }
        """)
        options_row.addWidget(quality_combo)
        
        options_row.addStretch()
        
        layout.addLayout(options_row)
        
        return section
    
    def create_button_section(self):
        """创建按钮区域"""
        section = RoundedWidget()
        section.setFixedHeight(60)
        
        layout = QHBoxLayout(section)
        layout.setContentsMargins(20, 0, 20, 0)
        layout.setSpacing(15)
        
        layout.addStretch()
        
        # 保存配置按钮
        save_btn = QPushButton("保存配置")
        save_btn.setFixedSize(120, 40)
        save_btn.setStyleSheet(StyleManager.get_button_style(bg_color='#666666', font_size=14, border_radius=20))
        layout.addWidget(save_btn)
        
        # 开始处理按钮
        start_btn = QPushButton("开始处理")
        start_btn.setFixedSize(120, 40)
        start_btn.setStyleSheet(StyleManager.get_button_style(bg_color='#96CEB4', font_size=14, border_radius=20))
        layout.addWidget(start_btn)
        
        return section
    
    def test_voice(self):
        """测试配音效果"""
        print("测试配音效果")
    
    def get_config(self):
        """获取当前配置"""
        config = {
            "speech_recognition": {
                "model": "Whisper Base",
                "language": "自动检测",
                "noise_reduction": 50,
                "volume_gain": 0
            },
            "translation": {
                "service": "DeepL Pro",
                "target_language": "中文",
                "style": "正式"
            },
            "voice": {
                "service": "Azure TTS",
                "voice": "晓晓",
                "speed": 0,
                "pitch": 0
            },
            "synthesis": {
                "format": "MP4",
                "codec": "H.264",
                "quality": "高质量"
            }
        }
        return config
    
    def set_config(self, config):
        """设置配置"""
        try:
            # 语音识别配置
            speech_config = config.get("speech_recognition", {})
            # TODO: 设置语音识别相关控件的值
            
            # 翻译配置
            translation_config = config.get("translation", {})
            # TODO: 设置翻译相关控件的值
            
            # 配音配置
            voice_config = config.get("voice", {})
            # TODO: 设置配音相关控件的值
            
            # 合成配置
            synthesis_config = config.get("synthesis", {})
            # TODO: 设置合成相关控件的值
            
            print("配置已更新")
            
        except Exception as e:
            print(f"设置配置失败: {e}") 