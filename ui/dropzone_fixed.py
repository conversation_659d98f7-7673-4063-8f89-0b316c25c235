#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QPushButton, QFileDialog, QWidget, QLabel
from PySide6.QtMultimedia import QMediaPlayer, QAudioOutput
from PySide6.QtMultimediaWidgets import QVideoWidget
from PySide6.QtCore import QUrl, Qt, QSize

class VideoPlayerTest(QMainWindow):
    """
    简单的视频播放测试类，用于验证PySide6多媒体功能
    """
    def __init__(self):
        super().__init__()
        self.setWindowTitle("视频播放测试")
        self.setMinimumSize(800, 600)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建布局
        layout = QVBoxLayout(central_widget)
        
        # 状态标签
        self.status_label = QLabel("请选择视频文件进行测试...")
        self.status_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(self.status_label)
        
        # 创建视频播放部件
        self.video_widget = QVideoWidget()
        self.video_widget.setMinimumSize(QSize(640, 360))
        layout.addWidget(self.video_widget)
        
        # 创建媒体播放器
        self.media_player = QMediaPlayer()
        self.audio_output = QAudioOutput()
        self.media_player.setAudioOutput(self.audio_output)
        self.media_player.setVideoOutput(self.video_widget)
        
        # 连接信号
        self.media_player.errorOccurred.connect(self.handle_error)
        self.media_player.playbackStateChanged.connect(self.state_changed)
        
        # 创建按钮
        button_layout = QVBoxLayout()
        
        # 打开文件按钮
        self.open_button = QPushButton("选择视频文件")
        self.open_button.clicked.connect(self.open_file)
        button_layout.addWidget(self.open_button)
        
        # 播放按钮
        self.play_button = QPushButton("播放")
        self.play_button.clicked.connect(self.play)
        self.play_button.setEnabled(False)
        button_layout.addWidget(self.play_button)
        
        # 停止按钮
        self.stop_button = QPushButton("停止")
        self.stop_button.clicked.connect(self.stop)
        self.stop_button.setEnabled(False)
        button_layout.addWidget(self.stop_button)
        
        layout.addLayout(button_layout)
    
    def open_file(self):
        """打开视频文件"""
        file_dialog = QFileDialog(self)
        file_dialog.setFileMode(QFileDialog.ExistingFile)
        file_path, _ = file_dialog.getOpenFileName(
            self, "选择视频文件", "", "视频文件 (*.mp4 *.avi *.mov *.mkv *.wmv)"
        )
        
        if file_path:
            self.status_label.setText(f"已选择: {os.path.basename(file_path)}")
            
            # 创建QUrl并检查有效性
            url = QUrl.fromLocalFile(os.path.abspath(file_path))
            if url.isValid():
                self.media_player.setSource(url)
                self.play_button.setEnabled(True)
                self.status_label.setText(f"文件已加载: {os.path.basename(file_path)}")
                
                # 自动播放
                self.media_player.play()
            else:
                self.status_label.setText(f"错误: 无效的文件路径")
    
    def play(self):
        """播放视频"""
        self.media_player.play()
        self.play_button.setEnabled(False)
        self.stop_button.setEnabled(True)
    
    def stop(self):
        """停止视频"""
        self.media_player.stop()
        self.play_button.setEnabled(True)
        self.stop_button.setEnabled(False)
    
    def handle_error(self, error, error_string):
        """处理媒体播放器错误"""
        self.status_label.setText(f"播放错误: {error_string} (代码: {error})")
        self.play_button.setEnabled(False)
        self.stop_button.setEnabled(False)
    
    def state_changed(self, state):
        """处理播放状态变化"""
        from PySide6.QtMultimedia import QMediaPlayer
        
        if state == QMediaPlayer.PlaybackState.PlayingState:
            self.status_label.setText("视频播放中...")
            self.stop_button.setEnabled(True)
            self.play_button.setEnabled(False)
        elif state == QMediaPlayer.PlaybackState.PausedState:
            self.status_label.setText("视频已暂停")
        elif state == QMediaPlayer.PlaybackState.StoppedState:
            self.status_label.setText("视频已停止")
            self.play_button.setEnabled(True)
            self.stop_button.setEnabled(False)

def main():
    """主函数"""
    app = QApplication(sys.argv)
    window = VideoPlayerTest()
    window.show()
    sys.exit(app.exec())

if __name__ == "__main__":
    main() 