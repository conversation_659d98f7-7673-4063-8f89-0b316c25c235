#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FlipTalk AI - 参数设置窗口
功能：提供语音识别、字幕翻译、配音设置等参数配置功能
"""

from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, 
    QComboBox, QCheckBox, QFrame, QLineEdit, QWidget, QScrollArea
)
from PySide6.QtCore import Qt
from PySide6.QtGui import QFont


class RoundedFrame(QFrame):
    """
    圆角矩形框架组件
    提供统一的圆角矩形样式
    """
    def __init__(self, bg_color="#252525", border_radius=16):
        super().__init__()
        self.bg_color = bg_color  # 背景颜色
        self.border_radius = border_radius  # 圆角半径
        self.setup_style()
    
    def setup_style(self):
        """设置框架样式"""
        self.setStyleSheet(f"""
            QFrame {{
                background-color: {self.bg_color};
                border-radius: {self.border_radius}px;
                border: none;
            }}
        """)


class ConfigWindow(QDialog):
    """
    参数设置窗口
    包含语音识别、字幕翻译、配音设置和合成设置四个模块
    """
    def __init__(self):
        super().__init__()
        self.setWindowTitle("参数配置")
        self.setFixedSize(500, 750)  # 设置窗口固定尺寸
        self.setModal(True)  # 设置为模态窗口
        self.setup_ui()
        self.setup_style()
    
    def setup_ui(self):
        """初始化参数设置窗口UI"""
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(15, 20, 15, 20)
        main_layout.setSpacing(15)
        
        # 标题
        header_layout = QHBoxLayout()
        title_label = QLabel("参数设置")
        title_label.setStyleSheet("""
            QLabel {
                color: #FFFFFF;
                font-size: 22px;
                font-weight: bold;
            }
        """)
        header_layout.addWidget(title_label)
        header_layout.addStretch()
        main_layout.addLayout(header_layout)
        
        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setFrameShape(QFrame.NoFrame)  # 无边框
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)  # 禁用水平滚动条
        scroll_area.setStyleSheet("""
            QScrollArea {
                background-color: transparent;
                border: none;
            }
            QScrollBar:vertical {
                background-color: #353535;
                width: 8px;
                margin: 0px;
                border-radius: 4px;
            }
            QScrollBar::handle:vertical {
                background-color: #555555;
                min-height: 20px;
                border-radius: 4px;
            }
            QScrollBar::handle:vertical:hover {
                background-color: #666666;
            }
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
                height: 0px;
            }
            QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {
                background-color: transparent;
            }
        """)
        
        # 创建内容容器
        content_widget = QWidget()
        content_widget.setStyleSheet("background-color: transparent;")
        content_layout = QVBoxLayout(content_widget)
        content_layout.setContentsMargins(0, 0, 8, 0)  # 右侧留出滚动条空间
        content_layout.setSpacing(15)
        
        # 语音识别设置区域
        speech_frame = self.create_section("语音识别", [
            ("识别引擎", ["whisperX", "whisper", "Azure Speech"], "whisperX"),
            ("识别模型", ["large-v3", "medium", "small", "base"], "large-v3"),
            ("源语言", ["自动识别", "中文", "英文", "日文", "韩文"], "自动识别")
        ])
        content_layout.addWidget(speech_frame)
        
        # 字幕翻译设置区域
        translation_frame = self.create_section("字幕翻译", [
            ("翻译渠道", ["谷歌翻译 (免费)", "微软翻译 (需要API Key)", "DeepL Pro (需要API Key)"], "谷歌翻译 (免费)"),
            ("目标语言", ["中文 (zh)", "英语 (en)", "日语 (ja)", "韩语 (ko)", "法语 (fr)", "德语 (de)", "西班牙语 (es)", "俄语 (ru)", "阿拉伯语 (ar)", "泰语 (th)"], "中文 (zh)")
        ])
        content_layout.addWidget(translation_frame)
        
        # 配音设置区域
        voice_frame = self.create_section("配音设置", [
            ("配音渠道", ["Azure TTS", "Google TTS", "Amazon Polly", "讯飞语音"], "Azure TTS"),
            ("配音角色", ["晓晨", "晓萱", "晓伊", "晓宇", "云希", "云扬"], "晓晨")
        ])
        content_layout.addWidget(voice_frame)
        
        # 合成设置区域
        options_frame = self.create_options_section()
        content_layout.addWidget(options_frame)
        
        # 为了显示需要，添加更多示例设置项
        extra_frame = self.create_section("高级设置", [
            ("音频格式", ["WAV", "MP3", "AAC", "FLAC"], "MP3"),
            ("采样率", ["16kHz", "22kHz", "44.1kHz", "48kHz"], "44.1kHz"),
            ("比特率", ["64kbps", "128kbps", "192kbps", "320kbps"], "128kbps")
        ])
        content_layout.addWidget(extra_frame)
        
        # 添加弹性空间
        content_layout.addStretch()
        
        # 将内容部件设置为滚动区域的部件
        scroll_area.setWidget(content_widget)
        
        # 将滚动区域添加到主布局
        main_layout.addWidget(scroll_area, 1)  # 1表示拉伸因子，使其填充剩余空间
        
        # 底部按钮区域
        button_layout = QHBoxLayout()
        button_layout.setSpacing(10)
        
        # 取消按钮
        cancel_btn = QPushButton("取消")
        cancel_btn.setFixedSize(100, 36)
        cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #3A3A3A;
                color: #FFFFFF;
                font-size: 15px;
                font-weight: normal;
                border: none;
                border-radius: 18px;
            }
            QPushButton:hover {
                background-color: #444444;
            }
            QPushButton:pressed {
                background-color: #2A2A2A;
            }
        """)
        cancel_btn.clicked.connect(self.reject)
        
        # 确定按钮
        confirm_btn = QPushButton("确定")
        confirm_btn.setFixedSize(100, 36)
        confirm_btn.setStyleSheet("""
            QPushButton {
                background-color: #0076FF;
                color: #FFFFFF;
                font-size: 15px;
                font-weight: normal;
                border: none;
                border-radius: 18px;
            }
            QPushButton:hover {
                background-color: #0A84FF;
            }
            QPushButton:pressed {
                background-color: #0062D8;
            }
        """)
        confirm_btn.clicked.connect(self.accept)
        
        button_layout.addStretch()
        button_layout.addWidget(cancel_btn)
        button_layout.addWidget(confirm_btn)
        
        main_layout.addLayout(button_layout)
    
    def create_section(self, title, options):
        """创建设置区域"""
        frame = RoundedFrame()
        
        layout = QVBoxLayout(frame)
        layout.setContentsMargins(20, 15, 20, 15)
        layout.setSpacing(12)
        
        # 标题
        title_label = QLabel(title)
        title_label.setStyleSheet("""
            QLabel {
                color: #FFFFFF;
                font-size: 16px;
                font-weight: bold;
            }
        """)
        layout.addWidget(title_label)
        
        # 选项
        for i, (label_text, items, default_value) in enumerate(options):
            option_layout = QHBoxLayout()
            option_layout.setContentsMargins(0, 5, 0, 5)
            
            # 标签
            label = QLabel(label_text)
            label.setStyleSheet("""
                QLabel {
                    color: #E0E0E0;
                    font-size: 14px;
                }
            """)
            label.setFixedWidth(90)
            
            # 下拉框
            combo = QComboBox()
            combo.addItems(items)
            combo.setCurrentText(default_value)
            combo.setStyleSheet("""
                QComboBox {
                    background-color: #353535;
                    color: #FFFFFF;
                    font-size: 14px;
                    border: none;
                    border-radius: 6px;
                    padding: 5px 10px;
                    min-height: 24px;
                }
                QComboBox::drop-down {
                    border: none;
                    width: 20px;
                }
                QComboBox::down-arrow {
                    image: none;
                    border-left: 5px solid transparent;
                    border-right: 5px solid transparent;
                    border-top: 6px solid #CCCCCC;
                    margin-right: 10px;
                }
                QComboBox QAbstractItemView {
                    background-color: #353535;
                    color: #FFFFFF;
                    selection-background-color: #0076FF;
                    border: none;
                    border-radius: 6px;
                    padding: 5px;
                }
            """)
            
            option_layout.addWidget(label)
            option_layout.addWidget(combo)
            layout.addLayout(option_layout)
            
            # 添加分割线，除了最后一项
            if i < len(options) - 1:
                line = QFrame()
                line.setFrameShape(QFrame.HLine)
                line.setStyleSheet("background-color: #3A3A3A;")
                line.setFixedHeight(1)
                layout.addWidget(line)
        
        return frame
    
    def create_options_section(self):
        """创建选项设置区域"""
        frame = RoundedFrame()
        
        layout = QVBoxLayout(frame)
        layout.setContentsMargins(20, 15, 20, 15)
        layout.setSpacing(12)
        
        # 标题
        title_label = QLabel("选项设置")
        title_label.setStyleSheet("""
            QLabel {
                color: #FFFFFF;
                font-size: 16px;
                font-weight: bold;
            }
        """)
        layout.addWidget(title_label)
        
        # 选项
        options = [
            ("启用GPU加速", True),
            ("保留视频原声", False),
            ("保留背景音乐", False)
        ]
        
        for i, (option_text, default_checked) in enumerate(options):
            option_layout = QHBoxLayout()
            option_layout.setContentsMargins(0, 5, 0, 5)
            
            # 标签
            label = QLabel(option_text)
            label.setStyleSheet("""
                QLabel {
                    color: #E0E0E0;
                    font-size: 14px;
                }
            """)
            
            # 复选框
            checkbox = QCheckBox()
            checkbox.setChecked(default_checked)
            checkbox.setStyleSheet("""
                QCheckBox {
                    background-color: transparent;
                }
                QCheckBox::indicator {
                    width: 18px;
                    height: 18px;
                    background-color: #353535;
                    border: none;
                    border-radius: 4px;
                }
                QCheckBox::indicator:checked {
                    background-color: #0076FF;
                    image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iMTAiIHZpZXdCb3g9IjAgMCAxMiAxMCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTQgNy4zMzMzM0wxLjMzMzM0IDQuNjY2NjZMMC4zMzMzMzcgNS42NjY2Nkw0IDkuMzMzMzNMMTEuMzMzMyAyTDEwLjMzMzMgMUw0IDcuMzMzMzNaIiBmaWxsPSJ3aGl0ZSIvPgo8L3N2Zz4K);
                    background-repeat: no-repeat;
                    background-position: center;
                }
            """)
            
            option_layout.addWidget(label)
            option_layout.addStretch()
            option_layout.addWidget(checkbox)
            layout.addLayout(option_layout)
            
            # 添加分割线，除了最后一项
            if i < len(options) - 1:
                line = QFrame()
                line.setFrameShape(QFrame.HLine)
                line.setStyleSheet("background-color: #3A3A3A;")
                line.setFixedHeight(1)
                layout.addWidget(line)
        
        return frame
    
    def setup_style(self):
        """设置窗口样式"""
        self.setStyleSheet("""
            QDialog {
                background-color: #1A1A1A;
            }
        """)
    
    def get_config(self):
        """获取当前配置"""
        # 可以在这里添加配置获取逻辑
        return {}
    
    def set_config(self, config):
        """设置配置"""
        # 可以在这里添加配置设置逻辑
        pass 