#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FlipTalk AI - 音频字幕提取对话框
使用WhisperX提供音频转字幕功能的图形界面（现代化设计）
"""

import os
import sys
import json
import threading
from pathlib import Path
from typing import Dict, List, Optional

from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QFileDialog, QProgressBar, QTextEdit, QComboBox, QCheckBox,
    QGroupBox, QGridLayout, QSpinBox, QLineEdit, QMessageBox,
    QTabWidget, QWidget, QListWidget, QListWidgetItem, QSplitter,
    QTableWidget, QTableWidgetItem, QHeaderView, QFrame,
    QScrollArea, QButtonGroup, QRadioButton
)
from PySide6.QtCore import Qt, QThread, Signal, QTimer, QPropertyAnimation, QEasingCurve, QRect
from PySide6.QtGui import QFont, QIcon, QPalette, QPixmap, QPainter, QColor, QBrush, QPen

# 添加项目路径
current_dir = Path(__file__).parent
sys.path.append(str(current_dir.parent))

try:
    from plugins.subtitle_extractor.plugin import WhisperXSubtitleExtractor
    WHISPERX_AVAILABLE = True
except ImportError as e:
    print(f"警告：无法导入WhisperX插件 - {e}")
    WHISPERX_AVAILABLE = False
    # 创建占位符类
    class WhisperXSubtitleExtractor:
        def __init__(self): pass
        def initialize(self, config): return False


class StyleManager:
    """统一的样式管理类"""
    
    # 颜色常量
    COLORS = {
        'primary': '#2B9D7C',
        'primary_hover': '#00CC55', 
        'background_dark': '#1B1E24',
        'background_light': '#14161A',
        'text_white': '#FFFFFF',
        'text_gray': 'rgba(255, 255, 255, 0.7)',
        'text_light_gray': 'rgba(255, 255, 255, 0.4)',
        'border': '#444444',
        'transparent': 'transparent',
        'error': '#FF4444',
        'success': '#44FF44',
        'warning': '#FFAA44'
    }
    
    @staticmethod
    def get_dialog_style():
        """获取对话框主样式"""
        return f"""
            QDialog {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #0F1114, stop:0.5 #1B1E24, stop:1 #14161A);
                border: none;
            }}
        """
    
    @staticmethod
    def get_group_style():
        """获取分组框样式"""
        return f"""
            QGroupBox {{
                font-size: 15px;
                font-weight: bold;
                color: {StyleManager.COLORS['text_white']};
                border: 2px solid {StyleManager.COLORS['border']};
                border-radius: 12px;
                margin-top: 15px;
                padding-top: 12px;
                min-height: 180px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(27, 30, 36, 0.8), stop:1 rgba(20, 22, 26, 0.9));
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 8px 0 8px;
                color: {StyleManager.COLORS['primary']};
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(43, 157, 124, 0.2), stop:1 rgba(0, 204, 85, 0.1));
                border-radius: 6px;
            }}
        """
    
    @staticmethod
    def get_button_style():
        """获取按钮样式（现代化美化版）"""
        return f"""
            QPushButton {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(67, 161, 255, 0.9), stop:0.5 rgba(52, 152, 219, 0.95), stop:1 rgba(41, 128, 185, 1));
                color: white;
                font-size: 13px;
                font-weight: 600;
                letter-spacing: 0.5px;
                border: 2px solid rgba(255, 255, 255, 0.15);
                border-radius: 12px;
                padding: 8px 16px;
                min-height: 32px;
                min-width: 70px;
                margin: 2px;
            }}
            QPushButton:hover {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(72, 166, 255, 1), stop:0.5 rgba(57, 157, 224, 1), stop:1 rgba(46, 133, 190, 1));
                border: 2px solid rgba(255, 255, 255, 0.25);
            }}
            QPushButton:pressed {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 116, 169, 1), stop:1 rgba(31, 97, 141, 1));
                border: 2px solid rgba(255, 255, 255, 0.1);
            }}
            QPushButton:disabled {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(68, 68, 68, 0.4), stop:1 rgba(58, 58, 58, 0.6));
                color: rgba(255, 255, 255, 0.4);
                border: 2px solid rgba(100, 100, 100, 0.3);
            }}
        """
    
    @staticmethod
    def get_primary_button_style():
        """获取主要按钮样式（现代化美化版）"""
        return f"""
            QPushButton {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(76, 175, 80, 0.95), stop:0.3 rgba(67, 160, 71, 1), 
                    stop:0.7 rgba(56, 142, 60, 1), stop:1 rgba(46, 125, 50, 1));
                color: white;
                font-size: 14px;
                font-weight: 700;
                letter-spacing: 0.8px;
                border: 2px solid rgba(255, 255, 255, 0.2);
                border-radius: 14px;
                padding: 10px 20px;
                min-height: 36px;
                min-width: 80px;
            }}
            QPushButton:hover {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(81, 180, 85, 1), stop:0.3 rgba(72, 165, 76, 1), 
                    stop:0.7 rgba(61, 147, 65, 1), stop:1 rgba(51, 130, 55, 1));
                border: 2px solid rgba(255, 255, 255, 0.35);
            }}
            QPushButton:pressed {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(46, 125, 50, 1), stop:1 rgba(33, 89, 36, 1));
                border: 2px solid rgba(255, 255, 255, 0.15);
            }}
            QPushButton:disabled {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(68, 68, 68, 0.4), stop:1 rgba(58, 58, 58, 0.6));
                color: rgba(255, 255, 255, 0.4);
                border: 2px solid rgba(100, 100, 100, 0.3);
            }}
        """
    
    @staticmethod
    def get_secondary_button_style():
        """获取次要按钮（如浏览、下载）的现代化样式"""
        return f"""
            QPushButton {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(95, 105, 115, 0.8), stop:0.5 rgba(85, 95, 105, 0.9), stop:1 rgba(75, 85, 95, 1));
                color: rgba(255, 255, 255, 0.9);
                font-size: 12px;
                font-weight: 500;
                letter-spacing: 0.3px;
                border: 2px solid rgba(255, 255, 255, 0.12);
                border-radius: 10px;
                padding: 6px 14px;
                min-height: 30px;
                min-width: 60px;
                margin: 1px;
            }}
            QPushButton:hover {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(105, 115, 125, 0.9), stop:0.5 rgba(95, 105, 115, 1), stop:1 rgba(85, 95, 105, 1));
                border: 2px solid rgba(255, 255, 255, 0.2);
                color: white;
            }}
            QPushButton:pressed {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(65, 75, 85, 1), stop:1 rgba(55, 65, 75, 1));
                border: 2px solid rgba(255, 255, 255, 0.08);
            }}
            QPushButton:disabled {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(68, 68, 68, 0.4), stop:1 rgba(58, 58, 58, 0.6));
                color: rgba(255, 255, 255, 0.3);
                border: 2px solid rgba(100, 100, 100, 0.3);
            }}
        """
    
    @staticmethod
    def get_input_style():
        """获取输入框样式（紧凑版）"""
        return f"""
            QLineEdit {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {StyleManager.COLORS['background_light']}, 
                    stop:1 {StyleManager.COLORS['background_dark']});
                color: {StyleManager.COLORS['text_white']};
                font-size: 13px;
                border: 2px solid {StyleManager.COLORS['border']};
                border-radius: 8px;
                padding: 6px 10px;
                min-height: 16px;
                margin: 1px;
            }}
            QLineEdit:focus {{
                border: 2px solid {StyleManager.COLORS['primary']};
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {StyleManager.COLORS['background_dark']}, 
                    stop:1 rgba(43, 157, 124, 0.1));
            }}
            QLineEdit::placeholder {{
                color: {StyleManager.COLORS['text_light_gray']};
            }}
        """
    
    @staticmethod
    def get_combo_style():
        """获取下拉框样式（紧凑版）"""
        return f"""
            QComboBox {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {StyleManager.COLORS['background_light']}, 
                    stop:1 {StyleManager.COLORS['background_dark']});
                color: {StyleManager.COLORS['text_white']};
                font-size: 13px;
                border: 2px solid {StyleManager.COLORS['border']};
                border-radius: 8px;
                padding: 6px 10px;
                min-height: 16px;
                min-width: 120px;
                margin: 1px;
            }}
            QComboBox:hover {{
                border: 2px solid {StyleManager.COLORS['primary']};
            }}
            QComboBox::drop-down {{
                border: none;
                width: 18px;
                margin-right: 6px;
            }}
            QComboBox::down-arrow {{
                image: none;
                width: 10px;
                height: 10px;
                background: {StyleManager.COLORS['primary']};
            }}
            QComboBox QAbstractItemView {{
                background-color: {StyleManager.COLORS['background_dark']};
                color: {StyleManager.COLORS['text_white']};
                border: 2px solid {StyleManager.COLORS['primary']};
                border-radius: 6px;
                outline: none;
                selection-background-color: {StyleManager.COLORS['primary']};
                padding: 4px;
            }}
            QComboBox QAbstractItemView::item {{
                padding: 6px 10px;
                min-height: 16px;
                border: none;
            }}
        """
    
    @staticmethod
    def get_radio_button_style():
        """获取单选按钮样式"""
        return f"""
            QRadioButton {{
                color: {StyleManager.COLORS['text_white']};
                font-size: 12px;
                spacing: 8px;
                padding: 4px;
            }}
            QRadioButton::indicator {{
                width: 16px;
                height: 16px;
                border: 2px solid {StyleManager.COLORS['border']};
                border-radius: 9px;
                background: {StyleManager.COLORS['background_light']};
            }}
            QRadioButton::indicator:checked {{
                background-color: {StyleManager.COLORS['primary']};
                border: 2px solid {StyleManager.COLORS['primary_hover']};
                image: none;
            }}
            QRadioButton::indicator:hover {{
                border: 2px solid {StyleManager.COLORS['primary']};
            }}
            QRadioButton:disabled {{
                color: {StyleManager.COLORS['text_light_gray']};
            }}
            QRadioButton::indicator:disabled {{
                border-color: rgba(68, 68, 68, 0.5);
                background-color: rgba(68, 68, 68, 0.2);
            }}
        """
    
    @staticmethod
    def get_checkbox_style():
        """获取复选框样式（紧凑版）"""
        return f"""
            QCheckBox {{
                color: {StyleManager.COLORS['text_white']};
                font-size: 12px;
                spacing: 8px;
                padding: 4px 3px;
                min-height: 20px;
                margin: 2px;
            }}
            QCheckBox::indicator {{
                width: 16px;
                height: 16px;
                border: 2px solid {StyleManager.COLORS['border']};
                border-radius: 4px;
                background: {StyleManager.COLORS['background_light']};
                margin-right: 4px;
            }}
            QCheckBox::indicator:hover {{
                border: 2px solid {StyleManager.COLORS['primary']};
                background: rgba(43, 157, 124, 0.1);
            }}
            QCheckBox::indicator:checked {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {StyleManager.COLORS['primary']}, stop:1 #1E8A6F);
                border: 2px solid {StyleManager.COLORS['primary']};
            }}
            QCheckBox::indicator:checked:hover {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {StyleManager.COLORS['primary_hover']}, stop:1 #00B847);
            }}
        """
    
    @staticmethod
    def get_progress_style():
        """获取进度条样式"""
        return f"""
            QProgressBar {{
                background: {StyleManager.COLORS['background_dark']};
                color: {StyleManager.COLORS['text_white']};
                font-size: 12px;
                font-weight: bold;
                border: 2px solid {StyleManager.COLORS['border']};
                border-radius: 12px;
                text-align: center;
                min-height: 20px;
            }}
            QProgressBar::chunk {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 {StyleManager.COLORS['primary']}, 
                    stop:0.5 #00BB45, stop:1 {StyleManager.COLORS['primary_hover']});
                border-radius: 10px;
            }}
        """
    
    @staticmethod
    def get_tab_style():
        """获取选项卡样式"""
        return f"""
            QTabWidget::pane {{
                border: 2px solid {StyleManager.COLORS['border']};
                border-radius: 15px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(27, 30, 36, 0.8), stop:1 rgba(20, 22, 26, 0.9));
                margin-top: 10px;
            }}
            QTabBar::tab {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {StyleManager.COLORS['background_light']}, 
                    stop:1 {StyleManager.COLORS['background_dark']});
                color: {StyleManager.COLORS['text_gray']};
                font-size: 14px;
                font-weight: bold;
                border: 2px solid {StyleManager.COLORS['border']};
                border-bottom: none;
                border-radius: 10px 10px 0 0;
                padding: 12px 24px;
                margin-right: 4px;
                min-width: 100px;
            }}
            QTabBar::tab:selected {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {StyleManager.COLORS['primary']}, stop:1 #1E8A6F);
                color: {StyleManager.COLORS['text_white']};
                border: 2px solid {StyleManager.COLORS['primary']};
                border-bottom: 2px solid {StyleManager.COLORS['primary']};
            }}
            QTabBar::tab:hover:!selected {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(43, 157, 124, 0.2), stop:1 rgba(0, 204, 85, 0.1));
                color: {StyleManager.COLORS['text_white']};
                border: 2px solid rgba(43, 157, 124, 0.5);
            }}
        """
    
    @staticmethod
    def get_label_style():
        """获取标签样式"""
        return f"""
            QLabel {{
                color: {StyleManager.COLORS['text_white']};
                font-size: 14px;
                background: transparent;
                border: none;
                padding: 5px 2px;
                min-height: 20px;
                line-height: 1.4;
            }}
        """
    
    @staticmethod
    def get_message_box_style():
        """获取消息框样式"""
        return f"""
            QMessageBox {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {StyleManager.COLORS['background_dark']}, 
                    stop:1 {StyleManager.COLORS['background_light']});
                color: {StyleManager.COLORS['text_white']};
                border: 2px solid {StyleManager.COLORS['primary']};
                border-radius: 12px;
                font-size: 13px;
                min-width: 350px;
                min-height: 120px;
            }}
            QMessageBox QLabel {{
                color: {StyleManager.COLORS['text_white']};
                font-size: 14px;
                font-weight: 500;
                padding: 12px;
                background: transparent;
                border: none;
            }}
            QMessageBox QPushButton {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(67, 161, 255, 0.9), stop:0.5 rgba(52, 152, 219, 0.95), stop:1 rgba(41, 128, 185, 1));
                color: white;
                font-size: 12px;
                font-weight: 600;
                border: 2px solid rgba(255, 255, 255, 0.15);
                border-radius: 8px;
                padding: 8px 16px;
                min-height: 28px;
                min-width: 60px;
                margin: 4px;
            }}
            QMessageBox QPushButton:hover {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(72, 166, 255, 1), stop:0.5 rgba(57, 157, 224, 1), stop:1 rgba(46, 133, 190, 1));
                border: 2px solid rgba(255, 255, 255, 0.25);
            }}
            QMessageBox QPushButton:pressed {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 116, 169, 1), stop:1 rgba(31, 97, 141, 1));
            }}
        """


class DragDropArea(QFrame):
    """现代化拖拽上传区域"""
    
    files_dropped = Signal(list)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setAcceptDrops(True)
        self.setup_ui()
        
    def setup_ui(self):
        """设置UI"""
        self.setMinimumHeight(120)
        self.setMaximumHeight(150)
        layout = QVBoxLayout(self)
        layout.setAlignment(Qt.AlignCenter)
        layout.setContentsMargins(15, 12, 15, 12)
        layout.setSpacing(6)
        
        # 图标和文字
        icon_label = QLabel("🎵📹")
        icon_label.setAlignment(Qt.AlignCenter)
        icon_label.setStyleSheet("""
            QLabel {
                font-size: 36px;
                color: rgba(43, 157, 124, 0.8);
                background: transparent;
                border: none;
                margin: 5px;
            }
        """)
        
        main_text = QLabel("📂 点击选择文件或拖拽文件")
        main_text.setAlignment(Qt.AlignCenter)
        main_text.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #FFFFFF;
                background: transparent;
                border: none;
                margin: 3px;
            }
        """)
        
        layout.addWidget(icon_label)
        layout.addWidget(main_text)
        
        self.setup_style()
    
    def setup_style(self):
        """设置样式"""
        self.setStyleSheet("""
            QFrame {
                border: 3px dashed rgba(43, 157, 124, 0.5);
                border-radius: 20px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(27, 30, 36, 0.3), stop:1 rgba(20, 22, 26, 0.5));
            }
            QFrame:hover {
                border: 3px dashed #2B9D7C;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(43, 157, 124, 0.1), stop:1 rgba(0, 204, 85, 0.05));
            }
        """)
    
    def dragEnterEvent(self, event):
        """拖拽进入事件"""
        if event.mimeData().hasUrls():
            event.acceptProposedAction()
            self.setStyleSheet("""
                QFrame {
                    border: 3px dashed #00CC55;
                    border-radius: 20px;
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(0, 204, 85, 0.2), stop:1 rgba(43, 157, 124, 0.1));
                }
            """)
    
    def dragLeaveEvent(self, event):
        """拖拽离开事件"""
        self.setup_style()
    
    def dropEvent(self, event):
        """拖拽放下事件"""
        self.setup_style()
        if event.mimeData().hasUrls():
            file_paths = []
            for url in event.mimeData().urls():
                if url.isLocalFile():
                    file_paths.append(url.toLocalFile())
            
            if file_paths:
                self.files_dropped.emit(file_paths)
    
    def mousePressEvent(self, event):
        """鼠标点击事件"""
        if event.button() == Qt.LeftButton:
            self.select_files()
    
    def select_files(self):
        """选择文件 - 支持批量选择"""
        file_paths, _ = QFileDialog.getOpenFileNames(
            self, 
            "选择音频文件（支持多选）",
            "",
            "音频文件 (*.wav *.mp3 *.flac *.aac *.m4a *.ogg);;所有文件 (*)"
        )
        if file_paths:
            self.files_dropped.emit(file_paths)


class FileListItem(QWidget):
    """文件列表项组件"""
    
    remove_requested = Signal(str)  # 文件路径
    
    def __init__(self, file_path: str, parent=None):
        super().__init__(parent)
        self.file_path = file_path
        self.status = "waiting"  # waiting, processing, completed, failed
        self.progress = 0
        self.setup_ui()
    
    def setup_ui(self):
        """设置UI"""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(12, 8, 12, 8)
        layout.setSpacing(12)
        
        # 文件图标
        self.icon_label = QLabel("📄")
        self.icon_label.setFixedSize(24, 24)
        self.icon_label.setStyleSheet("font-size: 16px;")
        layout.addWidget(self.icon_label)
        
        # 文件信息区域
        info_layout = QVBoxLayout()
        info_layout.setSpacing(2)
        
        # 文件名
        file_name = Path(self.file_path).name
        self.name_label = QLabel(file_name)
        self.name_label.setStyleSheet(f"""
            QLabel {{
                color: {StyleManager.COLORS['text_white']};
                font-size: 13px;
                font-weight: 600;
            }}
        """)
        info_layout.addWidget(self.name_label)
        
        # 状态和进度
        self.status_label = QLabel("等待处理")
        self.status_label.setStyleSheet(f"""
            QLabel {{
                color: {StyleManager.COLORS['text_gray']};
                font-size: 11px;
            }}
        """)
        info_layout.addWidget(self.status_label)
        
        layout.addLayout(info_layout)
        layout.addStretch()
        
        # 进度条（初始隐藏）
        self.progress_bar = QProgressBar()
        self.progress_bar.setFixedHeight(4)
        self.progress_bar.setVisible(False)
        self.progress_bar.setStyleSheet(f"""
            QProgressBar {{
                background-color: rgba(255, 255, 255, 0.1);
                border: none;
                border-radius: 2px;
            }}
            QProgressBar::chunk {{
                background-color: {StyleManager.COLORS['primary']};
                border-radius: 2px;
            }}
        """)
        
        # 删除按钮
        self.remove_button = QPushButton("❌")
        self.remove_button.setFixedSize(20, 20)
        self.remove_button.setStyleSheet("""
            QPushButton {
                background: transparent;
                border: none;
                font-size: 12px;
                border-radius: 10px;
                color: rgba(255, 255, 255, 0.6);
            }
            QPushButton:hover {
                background: rgba(255, 68, 68, 0.2);
                color: #FF4444;
            }
        """)
        self.remove_button.clicked.connect(lambda: self.remove_requested.emit(self.file_path))
        layout.addWidget(self.remove_button)
        
        # 添加进度条到底部
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        main_layout.addLayout(layout)
        main_layout.addWidget(self.progress_bar)
        
        # 移除之前的布局，设置新的主布局
        self.setLayout(main_layout)
    
    def update_status(self, status: str, progress: int = 0, message: str = ""):
        """更新状态"""
        self.status = status
        self.progress = progress
        
        # 更新图标
        if status == "waiting":
            self.icon_label.setText("📄")
            self.status_label.setText("等待处理")
            self.status_label.setStyleSheet(f"color: {StyleManager.COLORS['text_gray']};")
            self.progress_bar.setVisible(False)
        elif status == "processing":
            self.icon_label.setText("⚡")
            self.status_label.setText(f"处理中... {progress}%")
            self.status_label.setStyleSheet(f"color: {StyleManager.COLORS['primary']};")
            self.progress_bar.setVisible(True)
            self.progress_bar.setValue(progress)
        elif status == "completed":
            self.icon_label.setText("✅")
            self.status_label.setText("处理完成")
            self.status_label.setStyleSheet(f"color: {StyleManager.COLORS['success']};")
            self.progress_bar.setVisible(False)
        elif status == "failed":
            self.icon_label.setText("❌")
            self.status_label.setText(f"处理失败: {message}")
            self.status_label.setStyleSheet(f"color: {StyleManager.COLORS['error']};")
            self.progress_bar.setVisible(False)
    
    def update_selection_indicator(self, selected: bool):
        """更新选中状态指示器"""
        file_name = Path(self.file_path).name
        if selected:
            # 添加选中标识
            self.name_label.setText(f"✅ {file_name}")
            self.name_label.setStyleSheet(f"""
                QLabel {{
                    color: {StyleManager.COLORS['primary']};
                    font-size: 13px;
                    font-weight: bold;
                }}
            """)
        else:
            # 移除选中标识
            self.name_label.setText(file_name)
            self.name_label.setStyleSheet(f"""
                QLabel {{
                    color: {StyleManager.COLORS['text_white']};
                    font-size: 13px;
                    font-weight: 600;
                }}
            """)


class FileListWidget(QWidget):
    """文件列表组件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.file_items = {}  # file_path -> FileListItem
        self.selected_files = set()  # 选中的文件路径
        self.setup_ui()
    
    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(8)
        
        # 顶部操作栏
        toolbar_layout = QHBoxLayout()
        toolbar_layout.setContentsMargins(8, 8, 8, 8)
        
        self.file_count_label = QLabel("文件列表 (0)")
        self.file_count_label.setStyleSheet(f"""
            QLabel {{
                color: {StyleManager.COLORS['text_white']};
                font-size: 14px;
                font-weight: 600;
            }}
        """)
        toolbar_layout.addWidget(self.file_count_label)
        
        toolbar_layout.addStretch()
        
        layout.addLayout(toolbar_layout)
        
        # 文件列表滚动区域
        self.scroll_area = QScrollArea()
        self.scroll_area.setWidgetResizable(True)
        self.scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        self.scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        self.scroll_area.setStyleSheet(f"""
            QScrollArea {{
                background: {StyleManager.COLORS['background_dark']};
                border: 1px solid {StyleManager.COLORS['border']};
                border-radius: 8px;
            }}
            QScrollBar:vertical {{
                background: rgba(255, 255, 255, 0.1);
                width: 8px;
                border-radius: 4px;
            }}
            QScrollBar::handle:vertical {{
                background: {StyleManager.COLORS['primary']};
                border-radius: 4px;
                min-height: 20px;
            }}
            QScrollBar::handle:vertical:hover {{
                background: {StyleManager.COLORS['primary_hover']};
            }}
        """)
        
        # 文件列表容器
        self.list_container = QWidget()
        self.list_container.setStyleSheet(f"""
            QWidget {{
                background: {StyleManager.COLORS['background_dark']};
                border: none;
            }}
        """)
        self.list_layout = QVBoxLayout(self.list_container)
        self.list_layout.setContentsMargins(8, 8, 8, 8)
        self.list_layout.setSpacing(4)
        
        # 空状态提示
        self.empty_label = QLabel("📁 拖拽文件到上方区域或点击上方区域选择文件")
        self.empty_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.empty_label.setStyleSheet(f"""
            QLabel {{
                color: {StyleManager.COLORS['text_gray']};
                font-size: 14px;
                font-weight: 500;
                padding: 60px 20px;
                background: transparent;
                border: 2px dashed rgba(255, 255, 255, 0.2);
                border-radius: 8px;
                margin: 20px;
            }}
        """)
        self.list_layout.addWidget(self.empty_label)
        self.list_layout.addStretch()
        
        self.scroll_area.setWidget(self.list_container)
        layout.addWidget(self.scroll_area)
    
    def add_file(self, file_path: str):
        """添加文件"""
        if file_path in self.file_items:
            return  # 文件已存在
        
        # 隐藏空状态提示
        self.empty_label.setVisible(False)
        
        # 创建文件项
        file_item = FileListItem(file_path)
        file_item.remove_requested.connect(self.remove_file)
        
        # 添加点击选择功能
        file_item.mousePressEvent = lambda event, path=file_path: self.toggle_file_selection(path, event)
        
        # 设置样式
        file_item.setStyleSheet(f"""
            FileListItem {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(43, 157, 124, 0.08), 
                    stop:1 rgba(30, 32, 36, 0.15));
                border: 1px solid rgba(255, 255, 255, 0.15);
                border-radius: 8px;
                margin: 2px;
            }}
            FileListItem:hover {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(43, 157, 124, 0.15), 
                    stop:1 rgba(30, 32, 36, 0.25));
                border: 1px solid rgba(43, 157, 124, 0.4);
            }}
        """)
        
        # 添加到布局（在stretch之前）
        self.list_layout.insertWidget(self.list_layout.count() - 1, file_item)
        self.file_items[file_path] = file_item
        
        self.update_file_count()
        self.update_button_states()
    
    def remove_file(self, file_path: str):
        """移除文件"""
        if file_path in self.file_items:
            item = self.file_items[file_path]
            self.list_layout.removeWidget(item)
            item.deleteLater()
            del self.file_items[file_path]
            
            # 同时从选中列表中移除
            if file_path in self.selected_files:
                self.selected_files.remove(file_path)
            
            self.update_file_count()
            self.update_button_states()
            
            # 如果没有文件了，显示空状态
            if not self.file_items:
                self.empty_label.setVisible(True)
    
    def update_file_count(self):
        """更新文件计数"""
        total_count = len(self.file_items)
        selected_count = len(self.selected_files)
        
        if selected_count > 0:
            self.file_count_label.setText(f"文件列表 ({total_count}) | 已选中 {selected_count} 个")
        else:
            self.file_count_label.setText(f"文件列表 ({total_count})")
    
    def update_button_states(self):
        """更新按钮状态"""
        has_files = len(self.file_items) > 0
        has_selected = len(self.selected_files) > 0
        
        # 在父对话框中查找按钮（递归查找）
        def find_dialog_buttons(widget):
            while widget and not isinstance(widget, SubtitleExtractionDialog):
                widget = widget.parent()
            return widget
        
        dialog = find_dialog_buttons(self)
        if dialog:
            if hasattr(dialog, 'clear_files_button'):
                dialog.clear_files_button.setEnabled(has_files)
            if hasattr(dialog, 'remove_selected_button'):
                dialog.remove_selected_button.setEnabled(has_selected)
    
    def get_file_list(self) -> List[str]:
        """获取文件列表"""
        return list(self.file_items.keys())
    
    def update_file_status(self, file_path: str, status: str, progress: int = 0, message: str = ""):
        """更新文件状态"""
        if file_path in self.file_items:
            self.file_items[file_path].update_status(status, progress, message)
    
    def toggle_file_selection(self, file_path: str, event):
        """切换文件选择状态"""
        if file_path in self.selected_files:
            self.selected_files.remove(file_path)
        else:
            self.selected_files.add(file_path)
        
        self.update_file_selection_style(file_path)
        self.update_file_count()  # 更新文件计数显示
        self.update_button_states()
    
    def update_file_selection_style(self, file_path: str):
        """更新文件选择样式"""
        if file_path in self.file_items:
            item = self.file_items[file_path]
            if file_path in self.selected_files:
                # 选中状态 - 更明显的视觉反馈
                item.setStyleSheet(f"""
                    FileListItem {{
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                            stop:0 rgba(43, 157, 124, 0.4), 
                            stop:1 rgba(0, 204, 85, 0.3));
                        border: 3px solid {StyleManager.COLORS['primary']};
                        border-radius: 8px;
                        margin: 2px;
                    }}
                """)
                # 添加选中标识到文件名前
                item.update_selection_indicator(True)
            else:
                # 未选中状态
                item.setStyleSheet(f"""
                    FileListItem {{
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                            stop:0 rgba(43, 157, 124, 0.08), 
                            stop:1 rgba(30, 32, 36, 0.15));
                        border: 1px solid rgba(255, 255, 255, 0.15);
                        border-radius: 8px;
                        margin: 2px;
                    }}
                    FileListItem:hover {{
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                            stop:0 rgba(43, 157, 124, 0.15), 
                            stop:1 rgba(30, 32, 36, 0.25));
                        border: 1px solid rgba(43, 157, 124, 0.4);
                    }}
                """)
                # 移除选中标识
                item.update_selection_indicator(False)
    
    def remove_selected_files(self):
        """移除选中的文件"""
        for file_path in list(self.selected_files):
            self.remove_file(file_path)
        self.selected_files.clear()
        self.update_file_count()  # 更新文件计数显示
        self.update_button_states()
    
    def get_selected_files(self) -> List[str]:
        """获取选中的文件列表"""
        return list(self.selected_files)
    
    def clear_all_files(self):
        """清空所有文件"""
        for file_path in list(self.file_items.keys()):
            self.remove_file(file_path)
        self.selected_files.clear()
        self.update_button_states()


class ModernProgressWidget(QWidget):
    """现代化进度显示组件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        # 进度条始终显示，不再隐藏
        self.show()
        
    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setValue(0)  # 初始进度为0
        self.progress_bar.setStyleSheet(StyleManager.get_progress_style())
        
        # 合并的状态信息标签（放在进度条下方）
        self.status_label = QLabel("📊 系统就绪，请添加文件并开始处理")
        self.status_label.setAlignment(Qt.AlignCenter)
        self.status_label.setStyleSheet("""
            QLabel {
                color: #FFFFFF;
                font-size: 14px;
                font-weight: bold;
                background: transparent;
                border: none;
                margin: 10px;
                line-height: 1.3;
            }
        """)
        
        layout.addWidget(self.progress_bar)
        layout.addWidget(self.status_label)
        
        # 保留detail_label的引用以保持兼容性，但实际不使用
        self.detail_label = self.status_label
    
    def update_progress(self, message: str, percentage: int, detail: str = ""):
        """更新进度"""
        self.progress_bar.setValue(percentage)
        # 合并主要消息和详细信息
        if detail:
            combined_text = f"{message}\n{detail}"
        else:
            combined_text = message
        self.status_label.setText(combined_text)
    
    def start_progress(self, message: str = "开始处理..."):
        """开始进度显示"""
        self.progress_bar.setValue(0)
        self.status_label.setText(message)
        # 进度条已经始终显示，不需要再调用show()
    
    def finish_progress(self, message: str = "完成！"):
        """完成进度显示"""
        self.progress_bar.setValue(100)
        self.status_label.setText(message)
        # 进度条固定显示，不再自动隐藏
    
    def reset_progress(self, message: str = "📊 系统就绪，请添加文件并开始处理"):
        """重置进度显示到初始状态"""
        self.progress_bar.setValue(0)
        self.status_label.setText(message)


class SubtitleExtractionWorker(QThread):
    """字幕提取工作线程"""
    
    progress_updated = Signal(str, int)
    extraction_completed = Signal(dict)
    error_occurred = Signal(str)
    
    def __init__(self, extractor, input_config):
        super().__init__()
        self.extractor = extractor
        self.input_config = input_config
    
    def run(self):
        """执行字幕提取"""
        try:
            def progress_callback(message, percentage):
                self.progress_updated.emit(message, percentage)
            
            # 首先切换到用户选择的设备
            target_device = self.input_config.get("device", "auto")
            if target_device and hasattr(self.extractor, 'change_device'):
                success = self.extractor.change_device(target_device)
                if success:
                    progress_callback(f"已切换到设备: {target_device}", 5)
                else:
                    progress_callback(f"设备切换失败，使用当前设备", 5)
            
            result = self.extractor.extract_subtitle(
                audio_path=self.input_config.get("audio_path"),
                video_path=self.input_config.get("video_path"),
                output_dir=self.input_config.get("output_dir"),
                model_name=self.input_config.get("model_name"),
                language=self.input_config.get("language"),
                output_formats=self.input_config.get("output_formats", ["srt"]),
                enable_uvr5=self.input_config.get("enable_uvr5", True),
                target_duration=self.input_config.get("target_duration", 20*60),
                progress_callback=progress_callback
            )
            
            self.extraction_completed.emit(result)
            
        except Exception as e:
            self.error_occurred.emit(str(e))


class ModelDownloadWorker(QThread):
    """模型下载工作线程"""
    
    download_completed = Signal(bool, str)
    progress_updated = Signal(str, int)
    
    def __init__(self, extractor, model_name):
        super().__init__()
        self.extractor = extractor
        self.model_name = model_name
    
    def run(self):
        """执行模型下载"""
        try:
            def progress_callback(message, percentage):
                self.progress_updated.emit(message, percentage)
            
            success = self.extractor.download_model(self.model_name, progress_callback)
            self.download_completed.emit(success, self.model_name)
            
        except Exception as e:
            self.download_completed.emit(False, f"下载失败: {e}")


class BatchSubtitleExtractionWorker(QThread):
    """批量字幕提取工作线程"""
    
    progress_updated = Signal(str, int, str)  # 消息, 总进度, 当前文件
    file_completed = Signal(str, dict)        # 文件路径, 结果
    file_failed = Signal(str, str)           # 文件路径, 错误信息
    all_completed = Signal(list)             # 所有结果列表
    
    def __init__(self, extractor, file_list, config):
        super().__init__()
        self.extractor = extractor
        self.file_list = file_list
        self.config = config
        self.results = []
        self.stop_requested = False
    
    def run(self):
        """执行批量字幕提取"""
        try:
            # 首先切换到用户选择的设备
            target_device = self.config.get("device", "auto")
            if target_device and hasattr(self.extractor, 'change_device'):
                success = self.extractor.change_device(target_device)
                device_msg = f"已切换到设备: {target_device}" if success else "设备切换失败，使用当前设备"
                self.progress_updated.emit(device_msg, 2, "")
            
            total_files = len(self.file_list)
            completed_files = 0
            
            for i, file_path in enumerate(self.file_list):
                if self.stop_requested:
                    break
                
                # 更新进度 - 开始处理当前文件
                current_progress = int((i / total_files) * 100)
                self.progress_updated.emit(
                    f"正在处理文件 {i+1}/{total_files}", 
                    current_progress,
                    Path(file_path).name
                )
                
                try:
                    # 创建单文件配置
                    file_config = self.config.copy()
                    
                    # 根据文件类型设置路径
                    file_type = self.get_file_type(file_path)
                    if file_type == "audio":
                        file_config["audio_path"] = file_path
                        file_config["video_path"] = None
                    elif file_type == "video":
                        file_config["video_path"] = file_path
                        file_config["audio_path"] = None
                    else:
                        # 不支持的文件类型
                        self.file_failed.emit(file_path, f"不支持的文件格式: {Path(file_path).suffix}")
                        continue
                    
                    # 设置输出目录（为每个文件创建子目录）
                    if self.config.get("output_dir"):
                        file_output_dir = Path(self.config["output_dir"]) / Path(file_path).stem
                        file_output_dir.mkdir(parents=True, exist_ok=True)
                        file_config["output_dir"] = str(file_output_dir)
                    
                    # 进度回调
                    def file_progress_callback(message, percentage):
                        # 计算总体进度
                        file_base_progress = int((i / total_files) * 100)
                        file_progress = int((percentage / 100) * (100 / total_files))
                        total_progress = file_base_progress + file_progress
                        
                        self.progress_updated.emit(
                            f"文件 {i+1}/{total_files}: {message}",
                            min(total_progress, 99),  # 防止超过100%
                            Path(file_path).name
                        )
                    
                    # 执行字幕提取
                    result = self.extractor.extract_subtitle(
                        audio_path=file_config.get("audio_path"),
                        video_path=file_config.get("video_path"),
                        output_dir=file_config.get("output_dir"),
                        model_name=file_config.get("model_name"),
                        language=file_config.get("language"),
                        output_formats=file_config.get("output_formats", ["srt"]),
                        enable_uvr5=file_config.get("enable_uvr5", False),
                        target_duration=file_config.get("target_duration", 20*60),
                        progress_callback=file_progress_callback
                    )
                    
                    # 处理结果
                    if result.get("success"):
                        completed_files += 1
                        self.file_completed.emit(file_path, result)
                        self.results.append({
                            "file_path": file_path,
                            "success": True,
                            "result": result
                        })
                    else:
                        self.file_failed.emit(file_path, result.get("error", "未知错误"))
                        self.results.append({
                            "file_path": file_path,
                            "success": False,
                            "error": result.get("error", "未知错误")
                        })
                
                except Exception as e:
                    self.file_failed.emit(file_path, str(e))
                    self.results.append({
                        "file_path": file_path,
                        "success": False,
                        "error": str(e)
                    })
            
            # 完成所有处理
            if not self.stop_requested:
                self.progress_updated.emit(
                    f"批量处理完成！成功: {completed_files}/{total_files}",
                    100,
                    ""
                )
                self.all_completed.emit(self.results)
            
        except Exception as e:
            self.progress_updated.emit(f"批量处理失败: {str(e)}", 0, "")
    
    def stop(self):
        """停止批量处理"""
        self.stop_requested = True
    
    def get_file_type(self, file_path: str) -> str:
        """获取文件类型"""
        suffix = Path(file_path).suffix.lower()
        
        # 音频格式
        audio_formats = ['.wav', '.mp3', '.flac', '.aac', '.m4a', '.ogg', '.wma']
        if suffix in audio_formats:
            return "audio"
        
        # 视频格式
        video_formats = ['.mp4', '.avi', '.mov', '.mkv', '.flv', '.wmv', '.webm']
        if suffix in video_formats:
            return "video"
        
        return "unknown"


class SubtitleExtractionDialog(QDialog):
    """音频字幕提取对话框（现代化设计）"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("🎯 FlipTalk AI - 智能字幕提取")
        self.setMinimumSize(1100, 900)
        self.setModal(True)
        
        # 检查WhisperX可用性
        if not WHISPERX_AVAILABLE:
            self.show_dependency_error()
            return
        
        # 初始化插件 - 支持动态设备选择
        self.extractor = WhisperXSubtitleExtractor()
        
        # 默认配置：使用auto模式，支持GPU和CPU自动切换
        default_config = {
            "device": "auto",  # 支持GPU/CPU自动选择
            "default_model": "tiny",  # 使用更小的模型提高稳定性
            "enable_uvr5": False,  # 默认禁用UVR5
            "models_dir": "models/whisperx_subtitle/weights"  # 指向weights目录
        }
        
        if not self.extractor.initialize(default_config):
            QMessageBox.critical(self, "错误", "WhisperX插件初始化失败！")
            return
        
        # 当前任务
        self.current_worker = None
        self.current_download_worker = None
        
        # 批量处理相关
        self.batch_files = []  # 批量文件列表
        self.current_batch_worker = None  # 批量处理工作线程
        self.is_batch_mode = False  # 是否为批量模式
        
        # 设置界面
        self.setup_ui()
        self.setup_connections()
        self.load_initial_data()
        
        # 应用样式
        self.apply_styles()
    
    def show_dependency_error(self):
        """显示依赖错误提示"""
        msg = QMessageBox(self)
        msg.setWindowTitle("依赖错误")
        msg.setText("WhisperX 字幕提取功能不可用")
        msg.setInformativeText(
            "请安装以下依赖：\n\n"
            "pip install whisperx>=3.1.0\n"
            "pip install torch>=2.0.0\n"
            "pip install soundfile>=0.12.1\n"
            "pip install srt>=3.5.0"
        )
        msg.setIcon(QMessageBox.Critical)
        msg.exec()
        self.reject()
    
    def setup_ui(self):
        """设置用户界面（现代化设计）"""
        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(12)  # 减少主布局间距
        main_layout.setContentsMargins(25, 15, 25, 15)  # 减少主布局边距
        
        # 标题区域
        self.create_header_section(main_layout)
        
        # 主内容区域
        content_widget = QWidget()
        content_layout = QHBoxLayout(content_widget)
        content_layout.setSpacing(35)
        content_layout.setContentsMargins(10, 8, 10, 8)  # 减少上下边距
        
        # 左侧 - 文件选择和基础配置
        left_panel = self.create_left_panel()
        content_layout.addWidget(left_panel, 3)
        
        # 右侧 - 高级选项、进度和结果
        right_panel = self.create_right_panel() 
        content_layout.addWidget(right_panel, 2)
        
        main_layout.addWidget(content_widget)
        
        # 底部按钮区域
        self.create_bottom_section(main_layout)
    
    def create_header_section(self, parent_layout):
        """创建标题区域"""
        header_frame = QFrame()
        header_frame.setMaximumHeight(120)  # 限制标题区域高度
        header_frame.setStyleSheet(f"""
            QFrame {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(43, 157, 124, 0.2), stop:1 rgba(0, 204, 85, 0.1));
                border-radius: 15px;
                border: 2px solid rgba(43, 157, 124, 0.3);
                margin: 5px;
                padding: 15px;
            }}
        """)
        
        header_layout = QHBoxLayout(header_frame)
        
        # 图标和标题
        title_layout = QVBoxLayout()
        
        main_title = QLabel("🎯 智能字幕提取")
        main_title.setStyleSheet("""
            QLabel {
                font-size: 20px;
                font-weight: bold;
                color: #FFFFFF;
                background: transparent;
                border: none;
                margin: 0px;
                padding: 0px;
            }
        """)
        
        subtitle = QLabel("使用先进的 WhisperX AI 技术，将音频/视频快速转换为精准字幕")
        subtitle.setStyleSheet("""
            QLabel {
                font-size: 13px;
                color: rgba(255, 255, 255, 0.8);
                background: transparent;
                border: none;
                margin: 0px;
                padding: 2px 0px;
            }
        """)
        
        title_layout.addWidget(main_title)
        title_layout.addWidget(subtitle)
        header_layout.addLayout(title_layout)
        
        header_layout.addStretch()
        
        # 状态信息
        status_layout = QVBoxLayout()
        
        self.model_status_label = QLabel("模型状态: 已就绪")
        self.model_status_label.setStyleSheet("""
            QLabel {
                font-size: 11px;
                color: #44FF44;
                background: transparent;
                border: none;
                margin: 0px;
                padding: 1px;
            }
        """)
        
        self.device_status_label = QLabel("运行设备: 检测中...")
        self.device_status_label.setStyleSheet("""
            QLabel {
                font-size: 11px;
                color: rgba(255, 255, 255, 0.7);
                background: transparent;
                border: none;
                margin: 0px;
                padding: 1px;
            }
        """)
        
        status_layout.addWidget(self.model_status_label)
        status_layout.addWidget(self.device_status_label)
        header_layout.addLayout(status_layout)
        
        parent_layout.addWidget(header_frame)
    
    def create_left_panel(self) -> QWidget:
        """创建左侧面板"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        layout.setSpacing(18)  # 减少GroupBox间距
        layout.setContentsMargins(5, 5, 5, 5)  # 减少面板边距
        
        # 文件选择区域
        file_group = self.create_file_selection_group()
        layout.addWidget(file_group)
        
        # 提取设置区域
        settings_group = self.create_compact_settings_group()
        layout.addWidget(settings_group)
        
        # 减少底部空白，仅添加较小的拉伸
        layout.addStretch(1)
        
        return panel
    
    def create_file_selection_group(self) -> QGroupBox:
        """创建文件选择组"""
        group = QGroupBox("📁 文件选择")
        group.setFixedHeight(280)  # 增加固定高度
        layout = QVBoxLayout(group)
        layout.setSpacing(12)  # 进一步减少组件间距
        layout.setContentsMargins(20, 16, 20, 18)  # 进一步减少边距
        
        # 拖拽上传区域
        self.drag_drop_area = DragDropArea()
        self.drag_drop_area.setMaximumHeight(185)  # 进一步增加最大高度
        self.drag_drop_area.setMinimumHeight(155)  # 进一步增加最小高度
        layout.addWidget(self.drag_drop_area)
        
        # 输出目录设置（超紧凑布局）
        output_widget = QWidget()
        file_layout = QGridLayout(output_widget)
        file_layout.setVerticalSpacing(10)  # 进一步减少垂直间距
        file_layout.setHorizontalSpacing(12)
        file_layout.setContentsMargins(0, 2, 0, 0)  # 进一步减少上边距
        
        # 输出目录行
        output_label = QLabel("输出目录:")
        output_label.setMinimumWidth(80)
        file_layout.addWidget(output_label, 0, 0)
        
        self.output_dir_edit = QLineEdit()
        self.output_dir_edit.setPlaceholderText("选择字幕文件输出目录（留空使用输入文件同目录）")
        self.output_dir_edit.setMinimumHeight(28)  # 更紧凑高度
        file_layout.addWidget(self.output_dir_edit, 0, 1)
        
        self.output_browse_button = QPushButton("📂 浏览")
        self.output_browse_button.setMaximumWidth(80)
        self.output_browse_button.setMinimumHeight(28)  # 与输入框一致
        file_layout.addWidget(self.output_browse_button, 0, 2)
        
        # 设置列拉伸比例
        file_layout.setColumnStretch(0, 0)  # 标签列固定宽度
        file_layout.setColumnStretch(1, 1)  # 输入框列拉伸
        file_layout.setColumnStretch(2, 0)  # 按钮列固定宽度
        
        layout.addWidget(output_widget)
        
        # 创建隐藏的输入文件路径编辑框（用于内部逻辑）
        self.input_path_edit = QLineEdit()
        self.input_path_edit.hide()  # 隐藏但保留用于代码逻辑
        
        return group
    
    def create_compact_settings_group(self) -> QGroupBox:
        """创建美化后的提取设置组（保持原布局）"""
        group = QGroupBox("⚙️ 提取设置")
        group.setFixedHeight(360)  # 增加固定高度以适应Token输入框
        layout = QGridLayout(group)
        layout.setVerticalSpacing(16)
        layout.setHorizontalSpacing(15)
        layout.setContentsMargins(18, 22, 18, 18)

        # --- Row 0: 运行方式和设备 ---
        mode_label = self._create_styled_label("⚡ 方式:")
        layout.addWidget(mode_label, 0, 0)
        
        # 运行方式下拉框
        self.mode_combo = QComboBox()
        self.mode_combo.addItem("WhisperX 本地", "local")
        self.mode_combo.addItem("WhisperX API", "api")
        self.mode_combo.setCurrentIndex(0)  # 默认选择本地模式
        self.mode_combo.setMinimumHeight(32)
        layout.addWidget(self.mode_combo, 0, 1)

        device_label = self._create_styled_label("🖥️ 设备:")
        layout.addWidget(device_label, 0, 2)
        
        # 设备选择按钮组
        device_container = self._create_button_group_container()
        device_layout = QHBoxLayout(device_container)
        device_layout.setContentsMargins(8, 6, 8, 6)
        
        self.cpu_radio = QRadioButton("CPU")
        self.gpu_radio = QRadioButton("GPU")
        self.device_button_group = QButtonGroup(self)
        self.device_button_group.addButton(self.cpu_radio)
        self.device_button_group.addButton(self.gpu_radio)
        self.cpu_radio.setChecked(True)
        
        device_layout.addWidget(self.cpu_radio)
        device_layout.addWidget(self.gpu_radio)
        device_layout.addStretch()
        layout.addWidget(device_container, 0, 3)

        # --- Row 1: AI模型 ---
        model_label = self._create_styled_label("🧠 AI模型:")
        layout.addWidget(model_label, 1, 0)
        
        self.model_combo = QComboBox()
        self.model_combo.setMinimumHeight(32)
        layout.addWidget(self.model_combo, 1, 1, 1, 2)
        
        self.download_model_button = QPushButton("⬇️ 下载")
        self.download_model_button.setToolTip("下载选中的模型")
        self.download_model_button.setMinimumHeight(32)
        self.download_model_button.setMinimumWidth(70)
        layout.addWidget(self.download_model_button, 1, 3)

        # --- Row 2: 识别语言 ---
        lang_label = self._create_styled_label("🌍 语言:")
        layout.addWidget(lang_label, 2, 0)
        
        self.language_combo = QComboBox()
        self.language_combo.setMinimumHeight(32)
        layout.addWidget(self.language_combo, 2, 1, 1, 3)

        # --- Row 3: 美化分隔区域 ---
        separator_widget = self._create_visual_separator()
        layout.addWidget(separator_widget, 3, 0, 1, 4)

        # --- Row 4: 输出格式 ---
        format_label = self._create_styled_label("📄 格式:")
        layout.addWidget(format_label, 4, 0)
        
        format_container = self._create_checkbox_container()
        format_layout = QHBoxLayout(format_container)
        format_layout.setContentsMargins(8, 6, 8, 6)
        
        self.srt_checkbox = QCheckBox("SRT")
        self.vtt_checkbox = QCheckBox("VTT")
        self.txt_checkbox = QCheckBox("TXT")
        self.srt_checkbox.setChecked(True)
        
        format_layout.addWidget(self.srt_checkbox)
        format_layout.addWidget(self.vtt_checkbox)
        format_layout.addWidget(self.txt_checkbox)
        format_layout.addStretch()
        layout.addWidget(format_container, 4, 1, 1, 3)

        # --- Row 5: 美化分隔区域 ---
        separator_widget2 = self._create_visual_separator()
        layout.addWidget(separator_widget2, 5, 0, 1, 4)

        # --- Row 6: API Token (固定显示，根据模式启用/禁用) ---
        self.api_token_label = self._create_styled_label("🔑 Token:")
        self.api_token_edit = QLineEdit()
        self.api_token_edit.setPlaceholderText("本地模式时不需要，API模式时需要填写")
        self.api_token_edit.setEchoMode(QLineEdit.Password)
        self.api_token_edit.setMinimumHeight(32)
        
        # 默认禁用API相关控件（本地模式）
        self.api_token_label.setEnabled(False)
        self.api_token_edit.setEnabled(False)
        
        layout.addWidget(self.api_token_label, 6, 0)
        layout.addWidget(self.api_token_edit, 6, 1, 1, 3)

        # 设置列拉伸
        layout.setColumnStretch(1, 1)
        layout.setColumnStretch(3, 1)

        return group
    
    def _create_styled_label(self, text: str) -> QLabel:
        """创建美化的标签"""
        label = QLabel(text)
        label.setStyleSheet(f"""
            QLabel {{
                color: {StyleManager.COLORS['primary']};
                font-size: 13px;
                font-weight: bold;
                padding: 4px 8px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(43, 157, 124, 0.15), stop:1 rgba(43, 157, 124, 0.05));
                border-radius: 6px;
                border: 1px solid rgba(43, 157, 124, 0.3);
                min-width: 60px;
            }}
        """)
        return label
    
    def _create_visual_separator(self) -> QFrame:
        """创建美化的视觉分隔线"""
        separator = QFrame()
        separator.setFrameStyle(QFrame.NoFrame)
        separator.setFixedHeight(2)
        separator.setStyleSheet(f"""
            QFrame {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 transparent, stop:0.2 {StyleManager.COLORS['primary']}, 
                    stop:0.8 {StyleManager.COLORS['primary']}, stop:1 transparent);
                border-radius: 1px;
                margin: 8px 20px;
            }}
        """)
        return separator
    
    def _create_button_group_container(self) -> QFrame:
        """创建按钮组容器"""
        container = QFrame()
        container.setStyleSheet(f"""
            QFrame {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(43, 157, 124, 0.08), 
                    stop:1 rgba(20, 22, 26, 0.15));
                border: 1px solid rgba(43, 157, 124, 0.25);
                border-radius: 8px;
                padding: 2px;
            }}
        """)
        return container
    
    def _create_checkbox_container(self) -> QFrame:
        """创建复选框容器"""
        container = QFrame()
        container.setStyleSheet(f"""
            QFrame {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(43, 157, 124, 0.08), 
                    stop:1 rgba(20, 22, 26, 0.15));
                border: 1px solid rgba(43, 157, 124, 0.25);
                border-radius: 8px;
                padding: 2px;
            }}
        """)
        return container
    
    def create_right_panel(self) -> QWidget:
        """创建右侧面板"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        layout.setSpacing(25)
        layout.setContentsMargins(0, 0, 0, 0)
        
        # 文件列表区域
        files_group = QGroupBox("📁 处理文件列表")
        files_layout = QVBoxLayout(files_group)
        files_layout.setContentsMargins(8, 8, 8, 8)
        
        # 文件列表组件
        self.file_list_widget = FileListWidget()
        self.file_list_widget.setMinimumHeight(300)  # 设置最小高度300px，能显示更多文件
        files_layout.addWidget(self.file_list_widget)
        
        # 文件操作按钮
        file_buttons_layout = QHBoxLayout()
        
        self.clear_files_button = QPushButton("🗑️ 清空")
        self.clear_files_button.setStyleSheet(StyleManager.get_secondary_button_style())
        self.clear_files_button.clicked.connect(self.clear_all_files)
        self.clear_files_button.setEnabled(False)  # 初始状态禁用
        file_buttons_layout.addWidget(self.clear_files_button)
        
        self.remove_selected_button = QPushButton("🗑️ 移除选中")
        self.remove_selected_button.setStyleSheet(StyleManager.get_button_style())
        self.remove_selected_button.clicked.connect(self.remove_selected_files)
        self.remove_selected_button.setEnabled(False)  # 初始状态禁用
        file_buttons_layout.addWidget(self.remove_selected_button)
        
        files_layout.addLayout(file_buttons_layout)
        layout.addWidget(files_group)
        
        # 进度显示区域
        self.progress_widget = ModernProgressWidget()
        layout.addWidget(self.progress_widget)
        
        # 开始提取按钮
        self.start_button = QPushButton("🚀 开始提取字幕")
        self.start_button.setStyleSheet(StyleManager.get_primary_button_style())
        layout.addWidget(self.start_button)
        
        # 取消按钮
        self.cancel_button = QPushButton("⏹️ 取消处理")
        self.cancel_button.setStyleSheet(StyleManager.get_button_style())
        self.cancel_button.hide()
        layout.addWidget(self.cancel_button)
        
        layout.addStretch()
        
        return panel
    
    def create_bottom_section(self, parent_layout):
        """创建底部区域"""
        bottom_layout = QHBoxLayout()
        
        # 左侧状态信息
        self.status_label = QLabel("💡 请选择要处理的音频/视频文件")
        self.status_label.setStyleSheet("""
            QLabel {
                color: rgba(255, 255, 255, 0.8);
                font-size: 13px;
                background: transparent;
                border: none;
                padding: 5px 10px;
            }
        """)
        bottom_layout.addWidget(self.status_label)
        
        bottom_layout.addStretch()
        
        # 右侧按钮
        self.close_button = QPushButton("❌ 关闭")
        self.close_button.setStyleSheet(StyleManager.get_button_style())
        bottom_layout.addWidget(self.close_button)
        
        parent_layout.addLayout(bottom_layout)
    
    def setup_connections(self):
        """设置信号连接"""
        # 文件选择
        self.output_browse_button.clicked.connect(self.browse_output_dir)
        self.drag_drop_area.files_dropped.connect(self.on_files_dropped)
        
        # 文件类型将自动根据扩展名判断
        
        # 模式选择
        self.mode_combo.currentIndexChanged.connect(self.on_mode_changed)
        
        # 设备选择
        self.cpu_radio.toggled.connect(self.on_device_changed)
        self.gpu_radio.toggled.connect(self.on_device_changed)
        
        # 提取功能
        self.start_button.clicked.connect(self.start_extraction)
        
        # 模型管理
        self.download_model_button.clicked.connect(self.download_selected_model)
        
        # 文件列表操作
        self.clear_files_button.clicked.connect(self.clear_all_files)
        self.remove_selected_button.clicked.connect(self.remove_selected_files)
        
        # 关闭按钮
        self.close_button.clicked.connect(self.close)
    
    def load_initial_data(self):
        """加载初始数据"""
        if not self.extractor:
            return
            
        # 加载模型列表
        self.load_model_combo()
        
        # 加载语言列表
        self.load_language_combo()
        
        # 刷新模型管理表格
        self.refresh_model_list()
        
        # 刷新结果列表
        self.refresh_results_list()
        
        # 确保API Token输入框显示（默认禁用状态）
        self.api_token_label.show()
        self.api_token_edit.show()
        self.api_token_label.setEnabled(False)
        self.api_token_edit.setEnabled(False)
        
        # 检测GPU可用性并更新设备状态
        self.update_device_status()
    
    def load_model_combo(self):
        """加载模型下拉列表"""
        try:
            models = self.extractor.get_available_models()
            downloaded_models = self.extractor.get_downloaded_models()
            
            self.model_combo.clear()
            
            for model_name, info in models.items():
                status = "已下载" if model_name in downloaded_models else "未下载"
                display_text = f"{model_name} ({info['size']}) - {status}"
                self.model_combo.addItem(display_text, model_name)
            
            # 设置默认选择
            default_model = "medium"
            for i in range(self.model_combo.count()):
                if self.model_combo.itemData(i) == default_model:
                    self.model_combo.setCurrentIndex(i)
                    break
        except Exception as e:
            print(f"加载模型列表失败: {e}")
    
    def load_language_combo(self):
        """加载语言下拉列表"""
        try:
            languages = self.extractor.get_supported_languages()
            
            # 语言代码到中文名称的映射
            language_chinese_names = {
                "auto": "自动检测",
                "zh": "中文",
                "en": "英语", 
                "ja": "日语",
                "ko": "韩语",
                "es": "西班牙语",
                "fr": "法语",
                "de": "德语",
                "it": "意大利语",
                "pt": "葡萄牙语",
                "ru": "俄语",
                "ar": "阿拉伯语",
                "hi": "印地语",
                "nl": "荷兰语",
                "sv": "瑞典语",
                "da": "丹麦语",
                "no": "挪威语",
                "fi": "芬兰语",
                "pl": "波兰语",
                "tr": "土耳其语",
                "th": "泰语",
                "vi": "越南语",
                "id": "印尼语",
                "ms": "马来语",
                "tl": "菲律宾语",
                "bn": "孟加拉语",
                "ur": "乌尔都语",
                "fa": "波斯语",
                "he": "希伯来语",
                "el": "希腊语",
                "cs": "捷克语",
                "sk": "斯洛伐克语",
                "hu": "匈牙利语",
                "ro": "罗马尼亚语",
                "bg": "保加利亚语",
                "hr": "克罗地亚语",
                "sr": "塞尔维亚语",
                "sl": "斯洛文尼亚语",
                "et": "爱沙尼亚语",
                "lv": "拉脱维亚语",
                "lt": "立陶宛语",
                "uk": "乌克兰语",
                "be": "白俄罗斯语",
                "kk": "哈萨克语",
                "ky": "吉尔吉斯语",
                "uz": "乌兹别克语",
                "tg": "塔吉克语",
                "mn": "蒙古语",
                "ka": "格鲁吉亚语",
                "hy": "亚美尼亚语",
                "az": "阿塞拜疆语",
                "is": "冰岛语",
                "mt": "马耳他语",
                "cy": "威尔士语",
                "ga": "爱尔兰语",
                "eu": "巴斯克语",
                "ca": "加泰罗尼亚语",
                "gl": "加利西亚语",
                "af": "南非荷兰语",
                "sw": "斯瓦希里语",
                "am": "阿姆哈拉语",
                "yo": "约鲁巴语",
                "zu": "祖鲁语",
                "sn": "绍纳语",
                "my": "缅甸语",
                "km": "高棉语",
                "lo": "老挝语",
                "si": "僧伽罗语",
                "ne": "尼泊尔语",
                "ta": "泰米尔语",
                "te": "泰卢固语",
                "kn": "卡纳达语",
                "ml": "马拉雅拉姆语",
                "gu": "古吉拉特语",
                "pa": "旁遮普语",
                "or": "奥里亚语",
                "as": "阿萨姆语",
                "mr": "马拉地语"
            }
            
            self.language_combo.clear()
            for code, name in languages.items():
                # 使用中文名称，如果没有映射则使用原名称
                chinese_name = language_chinese_names.get(code, name)
                self.language_combo.addItem(f"{chinese_name} ({code})", code)
            
            # 设置默认为自动检测
            self.language_combo.setCurrentIndex(0)
        except Exception as e:
            print(f"加载语言列表失败: {e}")
    
    def get_file_type(self, file_path: str) -> str:
        """根据文件扩展名自动判断文件类型"""
        audio_extensions = {'.mp3', '.wav', '.flac', '.aac', '.m4a', '.ogg', '.wma'}
        video_extensions = {'.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm', '.m4v', '.3gp'}
        
        file_ext = Path(file_path).suffix.lower()
        
        if file_ext in audio_extensions:
            return "audio"
        elif file_ext in video_extensions:
            return "video"
        else:
            return "unknown"
    
    def on_mode_changed(self):
        """处理模式改变时的UI更新"""
        current_mode = self.mode_combo.currentData()
        is_api_mode = (current_mode == "api")
        
        # 确保Token输入框始终显示，只控制启用/禁用状态
        self.api_token_label.show()
        self.api_token_edit.show()
        self.api_token_label.setEnabled(is_api_mode)
        self.api_token_edit.setEnabled(is_api_mode)
        
        # 更新输入框样式提示
        if is_api_mode:
            self.api_token_edit.setPlaceholderText("请输入Replicate API Token")
        else:
            self.api_token_edit.setPlaceholderText("WhisperX 本地模式时不需要")
            self.api_token_edit.clear()  # 切换到本地模式时清空内容
    
    def check_gpu_availability(self):
        """检测GPU可用性"""
        try:
            import torch
            if torch.cuda.is_available():
                device_count = torch.cuda.device_count()
                device_name = torch.cuda.get_device_name(0)
                return {
                    "available": True,
                    "device": f"{device_name} ({device_count} GPU{'s' if device_count > 1 else ''})",
                    "error": None
                }
            else:
                return {
                    "available": False,
                    "device": None,
                    "error": "CUDA不可用或未安装"
                }
        except ImportError:
            return {
                "available": False,
                "device": None,
                "error": "PyTorch未安装"
            }
        except Exception as e:
            return {
                "available": False,
                "device": None,
                "error": f"GPU检测失败: {str(e)}"
            }
    
    def on_device_changed(self):
        """设备改变时的处理"""
        if self.gpu_radio.isChecked():
            self.cpu_radio.setChecked(False)
            # 尝试切换到GPU
            if self.extractor:
                success = self.extractor.change_device("cuda")
                if not success:
                    # GPU切换失败，回退到CPU
                    self.cpu_radio.setChecked(True)
                    self.gpu_radio.setChecked(False)
                    self.extractor.change_device("cpu")
        else:
            self.cpu_radio.setChecked(True)
            # 切换到CPU
            if self.extractor:
                self.extractor.change_device("cpu")
        
        # 更新设备状态显示
        self.update_device_status()
    
    def browse_output_dir(self):
        """浏览输出目录"""
        dir_path = QFileDialog.getExistingDirectory(
            self, "选择输出目录"
        )
        
        if dir_path:
            self.output_dir_edit.setText(dir_path)
    
    def add_files_manually(self):
        """手动添加文件"""
        file_paths, _ = QFileDialog.getOpenFileNames(
            self,
            "选择音频/视频文件",
            "",
            "音频视频文件 (*.wav *.mp3 *.flac *.aac *.m4a *.ogg *.mp4 *.avi *.mov *.mkv *.flv *.wmv *.webm);;所有文件 (*.*)"
        )
        
        if file_paths:
            for file_path in file_paths:
                self.file_list_widget.add_file(file_path)
    
    def clear_all_files(self):
        """清空所有文件"""
        self.file_list_widget.clear_all_files()
        # 清空文件后重置进度条状态
        self.progress_widget.reset_progress()
    
    def remove_selected_files(self):
        """移除选中的文件"""
        self.file_list_widget.remove_selected_files()
    
    def on_files_dropped(self, file_paths: list):
        """处理拖拽文件 - 添加到文件列表"""
        if not file_paths:
            return
        
        # 过滤有效的音频/视频文件
        valid_files = []
        invalid_files = []
        
        for file_path in file_paths:
            file_type = self.get_file_type(file_path)
            if file_type in ["audio", "video"]:
                valid_files.append(file_path)
            else:
                invalid_files.append(file_path)
        
        if not valid_files:
            self.status_label.setText("❌ 没有找到支持的音频/视频文件")
            return
        
        # 将所有有效文件添加到文件列表
        for file_path in valid_files:
            self.file_list_widget.add_file(file_path)
        
        # 更新状态显示
        audio_count = sum(1 for f in valid_files if self.get_file_type(f) == "audio")
        video_count = len(valid_files) - audio_count
        
        status_parts = []
        if audio_count > 0:
            status_parts.append(f"{audio_count}个音频文件")
        if video_count > 0:
            status_parts.append(f"{video_count}个视频文件")
        
        if valid_files:
            status_text = f"✅ 已添加{' + '.join(status_parts)}到处理列表"
            self.status_label.setText(status_text)
        
        # 如果有无效文件，显示警告
        if invalid_files:
            invalid_names = [Path(f).name for f in invalid_files[:3]]
            if len(invalid_files) > 3:
                invalid_names.append(f"等{len(invalid_files)}个文件")
            
            warning_msg = f"跳过不支持的文件: {', '.join(invalid_names)}"
            self.show_styled_message("文件格式警告", warning_msg, "warning")
    
# 已移除on_input_path_changed方法，因为现在使用文件列表而不是输入路径编辑框
    
    def start_extraction(self):
        """开始字幕提取 - 处理文件列表中的文件"""
        # 检查文件列表
        file_list = self.file_list_widget.get_file_list()
        if not file_list:
            self.show_styled_message("警告", "请先添加要处理的文件！", "warning")
            return
        
        try:
            # 检查模型是否已下载
            selected_model = self.model_combo.currentData()
            downloaded_models = self.extractor.get_downloaded_models()
            
            if selected_model not in downloaded_models:
                reply = self.show_styled_message(
                    "模型未下载", 
                    f"模型 {selected_model} 尚未下载，是否现在下载？",
                    "question"
                )
                
                if reply == QMessageBox.StandardButton.Yes:
                    self.download_model(selected_model)
                    return
                else:
                    return
            
            # 准备基础配置
            config = {
                "model_name": selected_model,
                "language": self.language_combo.currentData() if self.language_combo.currentData() != "auto" else "auto",
                "output_dir": self.output_dir_edit.text().strip() or None,
                "output_formats": [],
                "enable_uvr5": False,  # 默认关闭UVR5人声分离
                "target_duration": 20 * 60,  # 默认20分钟分段
                "use_api": self.mode_combo.currentData() == "api",
                "api_token": self.api_token_edit.text().strip() if self.mode_combo.currentData() == "api" else None,
                "device": "cpu" if self.cpu_radio.isChecked() else "cuda"  # 设备选择
            }
            
            # 输出格式
            if self.srt_checkbox.isChecked():
                config["output_formats"].append("srt")
            if self.vtt_checkbox.isChecked():
                config["output_formats"].append("vtt")
            if self.txt_checkbox.isChecked():
                config["output_formats"].append("txt")
            
            if not config["output_formats"]:
                self.show_styled_message("警告", "请至少选择一种输出格式！", "warning")
                return
            
            # 验证API模式配置
            if config["use_api"] and not config["api_token"]:
                self.show_styled_message("警告", "WhisperX API模式需要提供Replicate API Token！", "warning")
                return
            
            # 调试信息：显示用户选择的设备
            selected_device = config["device"]
            print(f"🎯 用户选择的设备: {selected_device}")
            print(f"🎯 CPU单选按钮状态: {self.cpu_radio.isChecked()}")
            print(f"🎯 GPU单选按钮状态: {self.gpu_radio.isChecked()}")
            
            # 开始提取
            self.start_button.setEnabled(False)
            
            # 重置所有文件状态为等待处理
            for file_path in file_list:
                self.file_list_widget.update_file_status(file_path, "waiting", 0, "")
            
            if len(file_list) == 1:
                # 单文件处理模式
                input_path = file_list[0]
                file_type = self.get_file_type(input_path)
                
                # 添加文件路径到配置
                config["audio_path"] = input_path if file_type == "audio" else None
                config["video_path"] = input_path if file_type == "video" else None
                
                self.progress_widget.start_progress("正在提取字幕...")
                self.model_status_label.setText("正在提取字幕...")
                
                # 更新文件状态
                self.file_list_widget.update_file_status(input_path, "processing", 0, "开始处理...")
                
                # 创建单文件工作线程
                self.current_worker = SubtitleExtractionWorker(
                    self.extractor, config
                )
                
                self.current_worker.progress_updated.connect(self.on_extraction_progress)
                self.current_worker.extraction_completed.connect(self.on_extraction_completed)
                self.current_worker.error_occurred.connect(self.on_extraction_error)
                
                self.current_worker.start()
                
            else:
                # 批量处理模式
                self.progress_widget.start_progress("开始批量字幕提取...")
                self.model_status_label.setText("批量处理中...")
                
                # 创建批量工作线程
                self.current_batch_worker = BatchSubtitleExtractionWorker(
                    self.extractor, file_list, config
                )
                
                # 连接信号
                self.current_batch_worker.progress_updated.connect(self.on_batch_progress)
                self.current_batch_worker.file_completed.connect(self.on_batch_file_completed)
                self.current_batch_worker.file_failed.connect(self.on_batch_file_failed)
                self.current_batch_worker.all_completed.connect(self.on_batch_all_completed)
                
                self.current_batch_worker.start()
            
        except Exception as e:
            self.show_styled_message("错误", f"启动提取失败: {e}", "critical")
            self.start_button.setEnabled(True)
            self.progress_widget.finish_progress("提取失败")
            self.model_status_label.setText("提取失败")
    
    def on_extraction_progress(self, message: str, percentage: int):
        """提取进度更新"""
        self.progress_widget.update_progress(message, percentage)
        
        # 更新单文件的进度
        file_list = self.file_list_widget.get_file_list()
        if len(file_list) == 1:
            self.file_list_widget.update_file_status(file_list[0], "processing", percentage, message)
    
    def on_extraction_completed(self, result: Dict):
        """提取完成"""
        self.progress_widget.finish_progress("字幕提取完成！")
        self.model_status_label.setText("字幕提取完成！")
        
        # 重新启用开始按钮，允许再次提取
        self.start_button.setEnabled(True)
        
        # 更新单文件的状态
        file_list = self.file_list_widget.get_file_list()
        if len(file_list) == 1:
            file_path = file_list[0]
            if result.get("success"):
                self.file_list_widget.update_file_status(file_path, "completed", 100, "处理完成")
            else:
                error_msg = result.get("error", "未知错误")
                self.file_list_widget.update_file_status(file_path, "failed", 0, error_msg)
        
        # 5秒后重置进度条状态
        QTimer.singleShot(5000, lambda: self.progress_widget.reset_progress())
    
    def on_extraction_error(self, error_message: str):
        """提取出错"""
        self.progress_widget.finish_progress("提取失败")
        self.model_status_label.setText("提取失败")
        
        # 重新启用开始按钮，允许再次提取
        self.start_button.setEnabled(True)
        
        # 更新单文件的状态
        file_list = self.file_list_widget.get_file_list()
        if len(file_list) == 1:
            file_path = file_list[0]
            self.file_list_widget.update_file_status(file_path, "failed", 0, error_message)
        
        self.show_styled_message("提取失败", error_message, "critical")
        
        # 5秒后重置进度条状态
        QTimer.singleShot(5000, lambda: self.progress_widget.reset_progress())
    
    def download_selected_model(self):
        """下载选中的模型"""
        selected_model = self.model_combo.currentData()
        if selected_model:
            self.download_model(selected_model)
    
    def download_model(self, model_name: str):
        """下载指定模型"""
        try:
            # 检查是否已下载
            downloaded_models = self.extractor.get_downloaded_models()
            if model_name in downloaded_models:
                self.show_styled_message("信息", f"模型 {model_name} 已经下载", "information")
                return
            
            # 开始下载
            self.download_model_button.setEnabled(False)
            self.progress_widget.start_progress("正在下载模型...")
            self.model_status_label.setText("正在下载模型...")
            
            # 创建下载线程
            self.current_download_worker = ModelDownloadWorker(self.extractor, model_name)
            
            self.current_download_worker.progress_updated.connect(self.on_extraction_progress)
            self.current_download_worker.download_completed.connect(self.on_download_completed)
            
            self.current_download_worker.start()
            
        except Exception as e:
            self.show_styled_message("错误", f"启动下载失败: {e}", "critical")
            self.download_model_button.setEnabled(True)
    
    def on_download_completed(self, success: bool, message: str):
        """模型下载完成"""
        self.progress_widget.finish_progress("模型下载完成！")
        self.model_status_label.setText(f"模型下载完成！\n\n{message}")
        
        # 重新启用下载按钮
        self.download_model_button.setEnabled(True)
        
        if success:
            self.load_model_combo()
            self.refresh_model_list()
        else:
            self.model_status_label.setText("模型下载失败")
        
        # 5秒后重置进度条状态
        QTimer.singleShot(5000, lambda: self.progress_widget.reset_progress())
    
    def refresh_model_list(self):
        """刷新模型管理列表"""
        try:
            models = self.extractor.get_available_models()
            downloaded_models = self.extractor.get_downloaded_models()
            
            self.model_combo.clear()
            
            for model_name, info in models.items():
                status = "已下载" if model_name in downloaded_models else "未下载"
                display_text = f"{model_name} ({info['size']}) - {status}"
                self.model_combo.addItem(display_text, model_name)
            
            # 设置默认选择
            default_model = "medium"
            for i in range(self.model_combo.count()):
                if self.model_combo.itemData(i) == default_model:
                    self.model_combo.setCurrentIndex(i)
                    break
        except Exception as e:
            print(f"刷新模型列表失败: {e}")
    
    def refresh_results_list(self):
        """刷新结果列表 - 已改为文件列表模式，此方法保留以防其他地方调用"""
        # 文件列表模式下不需要刷新历史记录
        pass
    
    def open_selected_result(self):
        """打开选中的结果文件"""
        self.show_styled_message("提示", "此功能正在开发中", "information")
    
    def copy_selected_result(self):
        pass

    def show_styled_message(self, title: str, message: str, icon_type: str = "information"):
        """显示自定义样式的消息框"""
        msg = QMessageBox(self)
        msg.setWindowTitle(title)
        msg.setText(message)
        
        # 设置图标
        if icon_type == "information":
            msg.setIcon(QMessageBox.Icon.Information)
        elif icon_type == "warning":
            msg.setIcon(QMessageBox.Icon.Warning)
        elif icon_type == "critical":
            msg.setIcon(QMessageBox.Icon.Critical)
        elif icon_type == "question":
            msg.setIcon(QMessageBox.Icon.Question)
            msg.setStandardButtons(QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No)
        
        # 应用自定义样式
        msg.setStyleSheet(StyleManager.get_message_box_style())
        
        # 设置最小尺寸
        msg.setMinimumSize(400, 150)
        
        return msg.exec() if icon_type == "question" else msg.exec()

    def apply_styles(self):
        """应用所有自定义样式"""
        self.setStyleSheet(StyleManager.get_dialog_style())
        
        # 统一设置所有 GroupBox 样式
        for group_box in self.findChildren(QGroupBox):
            group_box.setStyleSheet(StyleManager.get_group_style())

        # 标签
        for label in self.findChildren(QLabel):
            if not label.objectName(): # 避免覆盖 ModernProgressWidget 里的标签
                label.setStyleSheet(StyleManager.get_label_style())

        # 按钮
        buttons = self.findChildren(QPushButton)
        for button in buttons:
            if button == self.start_button:
                continue
            
            # 为图标和次要功能按钮应用特定样式
            if button in [self.download_model_button, self.output_browse_button, 
                          self.close_button, self.clear_files_button, self.remove_selected_button]:
                button.setStyleSheet(StyleManager.get_secondary_button_style())
            # 其他所有普通按钮
            else:
                button.setStyleSheet(StyleManager.get_button_style())

        self.start_button.setStyleSheet(StyleManager.get_primary_button_style())
        
        # 输入框和下拉框
        for editor in self.findChildren(QLineEdit) + self.findChildren(QComboBox):
            if isinstance(editor, QLineEdit):
                editor.setStyleSheet(StyleManager.get_input_style())
            else:
                editor.setStyleSheet(StyleManager.get_combo_style())
        
        # 复选框
        for checkbox in self.findChildren(QCheckBox):
            checkbox.setStyleSheet(StyleManager.get_checkbox_style())

        # 单选按钮
        for radio_button in self.findChildren(QRadioButton):
            radio_button.setStyleSheet(StyleManager.get_radio_button_style())

# 已移除results_text样式，因为现在使用文件列表组件

    def closeEvent(self, event):
        """关闭事件，确保线程停止"""
        # 停止所有工作线程
        if self.current_worker and self.current_worker.isRunning():
            self.current_worker.terminate()
            self.current_worker.wait()
        
        if self.current_download_worker and self.current_download_worker.isRunning():
            self.current_download_worker.terminate()
            self.current_download_worker.wait()
        
        # 停止批量处理线程
        if self.current_batch_worker and self.current_batch_worker.isRunning():
            self.current_batch_worker.stop()  # 优雅停止
            self.current_batch_worker.wait(3000)  # 等待3秒
            if self.current_batch_worker.isRunning():
                self.current_batch_worker.terminate()  # 强制终止
                self.current_batch_worker.wait()
        
        # 清理插件资源
        if self.extractor:
            self.extractor.cleanup()
        
        event.accept()

    def on_batch_progress(self, message: str, percentage: int, current_file: str):
        """批量处理进度更新"""
        detail = f"当前: {Path(current_file).name}" if current_file else ""
        self.progress_widget.update_progress(message, percentage, detail)
        
        # 更新当前处理文件的进度
        if current_file:
            # 计算单个文件的进度（假设批量处理中每个文件的进度是线性的）
            file_progress = percentage % 100 if percentage > 0 else 0
            self.file_list_widget.update_file_status(current_file, "processing", file_progress, message)
    
    def on_batch_file_completed(self, file_path: str, result: dict):
        """批量处理 - 单个文件完成"""
        if result.get("success"):
            self.file_list_widget.update_file_status(file_path, "completed", 100, "处理完成")
        else:
            error_msg = result.get("error", "处理失败")
            self.file_list_widget.update_file_status(file_path, "failed", 0, error_msg)
    
    def on_batch_file_failed(self, file_path: str, error: str):
        """批量处理 - 单个文件失败"""
        self.file_list_widget.update_file_status(file_path, "failed", 0, error)
    
    def on_batch_all_completed(self, results: list):
        """批量处理 - 全部完成"""
        self.progress_widget.finish_progress("批量处理完成！")
        self.model_status_label.setText("批量处理完成！")
        self.start_button.setEnabled(True)
        
        # 统计结果
        success_count = sum(1 for r in results if r.get("success", False))
        total_count = len(results)
        failed_count = total_count - success_count
        
        # 注释：文件夹打开功能已移除，用户可以通过文件管理器查看输出目录
        
        # 显示完成通知
        if failed_count == 0:
            self.show_styled_message("处理完成", f"所有 {total_count} 个文件都已成功处理！", "information")
        else:
            self.show_styled_message("处理完成", f"处理完成！成功：{success_count}，失败：{failed_count}", "warning")
        
        # 5秒后重置进度条状态
        QTimer.singleShot(5000, lambda: self.progress_widget.reset_progress())

    def update_device_status(self):
        """更新设备状态显示"""
        gpu_status = self.check_gpu_availability()
        
        # 获取当前实际使用的设备
        current_device = "unknown"
        if self.extractor:
            current_device = self.extractor.get_current_device()
        
        if current_device == "cuda":
            # 当前使用GPU
            self.device_status_label.setText(f"🚀 GPU加速: {gpu_status.get('device', 'CUDA设备')}")
            self.device_status_label.setStyleSheet("""
                QLabel {
                    font-size: 11px;
                    color: #44FF44;
                    background: transparent;
                    border: none;
                    margin: 0px;
                    padding: 1px;
                }
            """)
        elif current_device == "cpu":
            # 当前使用CPU
            if gpu_status["available"]:
                self.device_status_label.setText("🖥️ CPU模式 (可切换至GPU)")
                color = "rgba(255, 255, 255, 0.7)"
            else:
                self.device_status_label.setText(f"🖥️ CPU模式 ({gpu_status['error']})")
                color = "#FFAA44"
            
            self.device_status_label.setStyleSheet(f"""
                QLabel {{
                    font-size: 11px;
                    color: {color};
                    background: transparent;
                    border: none;
                    margin: 0px;
                    padding: 1px;
                }}
            """)
        else:
            # 设备未知或未初始化
            self.device_status_label.setText("⚠️ 设备状态未知")
            self.device_status_label.setStyleSheet("""
                QLabel {
                    font-size: 11px;
                    color: #FFAA44;
                    background: transparent;
                    border: none;
                    margin: 0px;
                    padding: 1px;
                }
            """)
        
        # 如果GPU不可用，禁用GPU选项
        if not gpu_status["available"] and hasattr(self, 'gpu_radio'):
            self.gpu_radio.setEnabled(False)
            self.gpu_radio.setToolTip(f"GPU不可用: {gpu_status['error']}")
        elif gpu_status["available"] and hasattr(self, 'gpu_radio'):
            self.gpu_radio.setEnabled(True)
            self.gpu_radio.setToolTip("使用GPU加速处理")
    
    def load_model_combo(self):
        """加载模型下拉列表"""
        try:
            models = self.extractor.get_available_models()
            downloaded_models = self.extractor.get_downloaded_models()
            
            self.model_combo.clear()
            
            for model_name, info in models.items():
                status = "已下载" if model_name in downloaded_models else "未下载"
                display_text = f"{model_name} ({info['size']}) - {status}"
                self.model_combo.addItem(display_text, model_name)
            
            # 设置默认选择
            default_model = "medium"
            for i in range(self.model_combo.count()):
                if self.model_combo.itemData(i) == default_model:
                    self.model_combo.setCurrentIndex(i)
                    break
        except Exception as e:
            print(f"加载模型列表失败: {e}")


# 测试代码
if __name__ == "__main__":
    from PySide6.QtWidgets import QApplication
    
    app = QApplication(sys.argv)
    
    dialog = SubtitleExtractionDialog()
    dialog.show()
    
    sys.exit(app.exec()) 
