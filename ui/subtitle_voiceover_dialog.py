#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
字幕配音对话框
功能：支持多种TTS引擎的字幕配音功能，包含试听和批量生成功能
"""

import sys
import os
import re
import asyncio
from typing import Dict, List, Any, Optional
from pathlib import Path
from threading import Thread
import time
import json
import tempfile
import logging
import datetime

# 导入srt库用于音频合并功能
try:
    import srt
    SRT_AVAILABLE = True
except ImportError as e:
    print(f"警告: 无法导入srt库: {e}")
    SRT_AVAILABLE = False

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, 
    QTextEdit, QLineEdit, QScrollArea, QWidget, QFrame,
    QFileDialog, QMessageBox, QSplitter, QTableWidget, 
    QTableWidgetItem, QHeaderView, QComboBox, QSpinBox,
    QCheckBox, QGroupBox, QGridLayout, QProgressBar,
    QTabWidget, QFormLayout, QListWidget, QListWidgetItem,
    QStackedWidget, QSlider, QDoubleSpinBox, QTextBrowser,
    QPlainTextEdit, QStyledItemDelegate, QApplication
)
from PySide6.QtCore import Qt, Signal, QTimer, QThread, QUrl, QMetaObject, Q_ARG, Slot
from PySide6.QtGui import QFont, QTextCursor, QColor, QPalette, QDragEnterEvent, QDragMoveEvent, QDropEvent

# 尝试导入音频处理库
try:
    from pydub import AudioSegment
    AUDIO_PROCESSING_AVAILABLE = True
except ImportError as e:
    print(f"警告: 无法导入音频处理库pydub: {e}")
    AUDIO_PROCESSING_AVAILABLE = False

from PySide6.QtMultimedia import QMediaPlayer, QAudioOutput

# 尝试导入TTS相关模块
try:
    from plugins.tts.tts_manager_plugin import TtsManagerPlugin
    TTS_AVAILABLE = True
except ImportError as e:
    print(f"警告: 无法导入TTS插件: {e}")
    TTS_AVAILABLE = False


# 音频重叠检测常量
MAX_SPEED_UP = 1.2  # 最大音频加速比例
MIN_SPEED_UP = 1.05  # 最小音频加速比例 
MIN_GAP_DURATION = 0.1  # 最小间隔时间，单位秒


import threading
from concurrent.futures import ThreadPoolExecutor, TimeoutError as FutureTimeoutError

class SimpleLogger:
    """简单的日志记录器，用于替代diagnosisLog"""
    def __init__(self):
        self.messages = []
    
    def write(self, message, newline=True):
        """写入日志消息"""
        if newline:
            print(f"🔧 {message}")
        else:
            print(f"🔧 {message}", end="")
        self.messages.append(message)

class AudioOverlapDetector:
    """音频重叠检测器"""
    
    def __init__(self):
        self.overlapping_subtitles = set()  # 存储需要标红的字幕索引
        # 参考refer.py中的常量
        self.MAX_SPEED_UP = 1.2  # 最大音频加速比例，超过此值需要人工调整
        self.MIN_SPEED_UP = 1.05  # 最小音频加速比例
        self.MIN_GAP_DURATION = 0.1  # 最小间隔时间（秒）
        
    def process_audio_speedup(self, audio_path, required_speedup, subtitle_index, generate_test_files=False):
        """处理音频加速（按需求：≤1.2x直接加速，>1.2x生成对比文件）
        
        Args:
            audio_path: 音频文件路径
            required_speedup: 需要的加速比例
            subtitle_index: 字幕索引
            generate_test_files: 是否生成测试对比文件（仅用于>1.2x的情况）
            
        Returns:
            bool: 是否成功处理加速
        """
        try:
            if not AUDIO_PROCESSING_AVAILABLE:
                print(f"⚠️ 音频处理库不可用，无法加速音频")
                return False
                
            if not audio_path or not os.path.exists(audio_path):
                print(f"⚠️ 音频文件不存在，无法加速: {audio_path}")
                return False
                
            # 如果加速比例小于等于1.0，不处理
            if required_speedup <= 1.0:
                print(f"ℹ️ 字幕 {subtitle_index + 1} 不需要加速")
                return True
            
            # 检查是否已经处理过（使用更简单的标记避免重复处理）
            base_path = os.path.splitext(audio_path)[0]
            processed_marker = f"{base_path}_processed.marker"
            
            if os.path.exists(processed_marker):
                print(f"⏩ 字幕 {subtitle_index + 1} 已经处理过，跳过")
                return True
            
            # 按照refer.py的实现方式：音频如果提速比例太接近1.0，强制设为MIN_SPEED_UP
            if required_speedup < self.MIN_SPEED_UP:
                print(f"⚠️ 加速比例 {required_speedup:.3f}x 太接近1.0，强制设为 {self.MIN_SPEED_UP}x")
                required_speedup = self.MIN_SPEED_UP
                
            print(f"🔄 开始处理字幕 {subtitle_index + 1} 的音频加速，比例: {required_speedup:.3f}x")
            
            # 加载音频文件
            audio = AudioSegment.from_wav(audio_path)
            
            # 按照refer.py的方式处理音频加速：先去除头尾静音
            audio_trimmed = audio.strip_silence(silence_thresh=-40, silence_len=100)
            
            # 应用加速
            audio_speedup = audio_trimmed.speedup(playback_speed=required_speedup)
            
            # 根据加速比例决定是否生成测试文件
            if required_speedup > self.MAX_SPEED_UP and generate_test_files:
                # 超过阈值：生成对比文件用于测试分析
                original_backup_path = f"{base_path}_original.wav"
                speedup_path = f"{base_path}_speedup_{required_speedup:.2f}x.wav"
                
                # 备份原始音频
                if not os.path.exists(original_backup_path):
                    audio.export(original_backup_path, format="wav")
                    print(f"💾 已备份原始音频: {os.path.basename(original_backup_path)}")
                
                # 生成加速对比文件
                audio_speedup.export(speedup_path, format="wav")
                print(f"⚡ 已生成加速对比文件: {os.path.basename(speedup_path)}")
                
                print(f"🔴 字幕 {subtitle_index + 1} 超过最大加速阈值 {self.MAX_SPEED_UP}x，建议人工调整")
                print(f"   📁 原始音频: {os.path.basename(original_backup_path)} ({len(audio)/1000:.2f}s)")
                print(f"   ⚡ 加速示例: {os.path.basename(speedup_path)} ({len(audio_speedup)/1000:.2f}s)")
                
                # 注意：对于超过阈值的情况，不覆盖原文件
                
            else:
                # 在阈值内：直接加速处理，不生成对比文件
                audio_speedup.export(audio_path, format="wav")
                
                original_duration = len(audio) / 1000.0
                trimmed_duration = len(audio_trimmed) / 1000.0  
                speedup_duration = len(audio_speedup) / 1000.0
                
                print(f"🎵 字幕 {subtitle_index + 1} 自动加速处理完成:")
                print(f"   📊 时长变化: {original_duration:.2f}s → {speedup_duration:.2f}s (压缩 {(original_duration - speedup_duration)/original_duration*100:.1f}%)")
                print(f"   ⚡ 加速比例: {required_speedup:.3f}x")
            
            # 创建处理标记，避免重复处理
            with open(processed_marker, 'w') as f:
                f.write(f"processed_at_{required_speedup:.3f}x")
            
            print(f"✅ 字幕 {subtitle_index + 1} 音频处理完成")
            return True
            
        except Exception as e:
            print(f"❌ 处理音频加速失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def detect_overlaps(self, subtitles: List[Dict], audio_paths: Dict[int, str]) -> List[int]:
        """
        检测音频重叠并返回需要标红的字幕索引列表
        
        Args:
            subtitles: 字幕列表
            audio_paths: 字幕索引到音频文件路径的映射
            
        Returns:
            需要标红的字幕索引列表
        """
        if not AUDIO_PROCESSING_AVAILABLE:
            print("⚠️ 音频处理库不可用，跳过重叠检测")
            return []
        
        overlapping_indices = []
        
        try:
            print("🔍 开始检测音频重叠...")
            
            for i in range(len(subtitles) - 1):
                current_subtitle = subtitles[i]
                next_subtitle = subtitles[i + 1]
                
                # 检查当前字幕是否有对应的音频文件
                if i not in audio_paths or not os.path.exists(audio_paths[i]):
                    print(f"⚠️ 字幕 {i + 1} 音频文件不存在，跳过检测")
                    continue
                
                try:
                    # 加载音频文件并去除静音
                    audio_path = audio_paths[i]
                    audio = AudioSegment.from_wav(audio_path)
                    
                    # 去除头尾静音（参数与refer.py保持一致）
                    audio_trimmed = audio.strip_silence(silence_thresh=-40, silence_len=100)
                    actual_duration_ms = len(audio_trimmed)  # pydub中的长度单位是毫秒
                    
                    # 计算字幕时间位置
                    current_start_ms = current_subtitle['start'] * 1000
                    next_start_ms = next_subtitle['start'] * 1000
                    
                    # 计算音频结束位置（包含最小间隔）
                    audio_end_ms = current_start_ms + actual_duration_ms + (self.MIN_GAP_DURATION * 1000)
                    
                    # 检查是否会重叠
                    if next_start_ms < audio_end_ms:
                        # 计算需要的加速比例
                        available_time_ms = next_start_ms - current_start_ms
                        required_speedup = (actual_duration_ms + self.MIN_GAP_DURATION * 1000) / available_time_ms
                        
                        # 转换时间为可读格式
                        time_str = str(datetime.timedelta(seconds=current_subtitle['start']))
                        
                        print(f"⚠️ 字幕 {i + 1} 检测到重叠:")
                        print(f"   时间位置: {time_str}")
                        print(f"   实际音频时长: {actual_duration_ms/1000:.2f}s")
                        print(f"   可用时间: {available_time_ms/1000:.2f}s") 
                        print(f"   需要加速: {required_speedup:.3f}x")
                        
                        # 如果加速比例过小，设置为最小加速比例
                        if required_speedup < self.MIN_SPEED_UP and required_speedup > 1.0:
                            print(f"⚠️ 字幕 {i + 1} 加速比例 {required_speedup:.3f}x 太接近1.0，强制设为 {self.MIN_SPEED_UP}x")
                            required_speedup = self.MIN_SPEED_UP
                        
                        # 如果超过最大加速比例，标记为红色
                        if required_speedup > self.MAX_SPEED_UP:
                            overlapping_indices.append(i)
                            print(f"🔴 字幕 {i + 1} 需要标红 (加速比例 {required_speedup:.3f}x > {self.MAX_SPEED_UP}x)")
                            # 超过阈值的情况可以生成对比文件用于分析
                            if hasattr(self, 'process_audio_speedup'):
                                self.process_audio_speedup(audio_path, required_speedup, i, generate_test_files=True)
                        else:
                            # 可以通过加速解决，直接应用加速（不生成对比文件）
                            print(f"🟡 字幕 {i + 1} 可通过加速解决 (加速比例 {required_speedup:.3f}x)")
                            if hasattr(self, 'process_audio_speedup'):
                                self.process_audio_speedup(audio_path, required_speedup, i, generate_test_files=False)
                            else:
                                print(f"⚠️ 无法加速音频：process_audio_speedup 方法不存在")
                    
                except Exception as e:
                    print(f"❌ 检测字幕 {i + 1} 重叠时出错: {e}")
                    continue
            
            print(f"✅ 重叠检测完成，发现 {len(overlapping_indices)} 条需要人工调整的字幕")
            return overlapping_indices
            
        except Exception as e:
            print(f"❌ 音频重叠检测失败: {e}")
            import traceback
            traceback.print_exc()
            return []
    
    def mark_subtitle_overlapping(self, subtitle_index: int):
        """标记字幕为重叠状态"""
        self.overlapping_subtitles.add(subtitle_index)
    
    def unmark_subtitle_overlapping(self, subtitle_index: int):
        """取消字幕的重叠标记"""
        self.overlapping_subtitles.discard(subtitle_index)
    
    def is_subtitle_overlapping(self, subtitle_index: int) -> bool:
        """检查字幕是否被标记为重叠"""
        return subtitle_index in self.overlapping_subtitles
    
    def clear_all_marks(self):
        """清除所有重叠标记"""
        self.overlapping_subtitles.clear()
    
    def get_overlapping_count(self) -> int:
        """获取重叠字幕数量"""
        return len(self.overlapping_subtitles)
    
    def manage_test_files(self, output_dir, action="list"):
        """管理音频测试文件（原始音频备份和加速对比文件）
        
        Args:
            output_dir: 输出目录
            action: 操作类型 ("list", "cleanup", "export_summary")
        
        Returns:
            dict: 测试文件信息
        """
        try:
            test_files = {
                'original_files': [],
                'speedup_files': [],
                'total_size': 0
            }
            
            if not os.path.exists(output_dir):
                return test_files
            
            for file in os.listdir(output_dir):
                file_path = os.path.join(output_dir, file)
                if not os.path.isfile(file_path):
                    continue
                
                # 跳过处理标记文件
                if file.endswith('.marker'):
                    continue
                    
                file_size = os.path.getsize(file_path)
                test_files['total_size'] += file_size
                
                if file.endswith('_original.wav'):
                    test_files['original_files'].append({
                        'name': file,
                        'path': file_path,
                        'size': file_size
                    })
                elif '_speedup_' in file and file.endswith('.wav'):
                    # 提取加速比例
                    speedup_match = file.split('_speedup_')[1].split('.wav')[0]
                    test_files['speedup_files'].append({
                        'name': file,
                        'path': file_path,
                        'size': file_size,
                        'speedup_ratio': speedup_match
                    })
            
            if action == "list":
                print(f"📊 音频测试文件统计:")
                print(f"   📁 原始备份文件: {len(test_files['original_files'])} 个")
                print(f"   ⚡ 加速对比文件: {len(test_files['speedup_files'])} 个")
                print(f"   💾 总大小: {test_files['total_size'] / (1024*1024):.2f} MB")
                
                if test_files['speedup_files']:
                    print(f"   🎯 加速比例分布:")
                    speedup_ratios = [f['speedup_ratio'] for f in test_files['speedup_files']]
                    ratio_counts = {}
                    for ratio in speedup_ratios:
                        ratio_counts[ratio] = ratio_counts.get(ratio, 0) + 1
                    for ratio, count in sorted(ratio_counts.items()):
                        print(f"      {ratio}x: {count} 个文件")
            
            elif action == "cleanup":
                cleanup_count = 0
                cleanup_size = 0
                
                for file_info in test_files['original_files'] + test_files['speedup_files']:
                    try:
                        cleanup_size += file_info['size']
                        os.remove(file_info['path'])
                        cleanup_count += 1
                    except Exception as e:
                        print(f"⚠️ 删除文件失败: {file_info['name']} - {e}")
                
                # 清理处理标记文件
                for file in os.listdir(output_dir):
                    if file.endswith('.marker'):
                        try:
                            marker_path = os.path.join(output_dir, file)
                            os.remove(marker_path)
                            print(f"   🏷️ 已删除标记: {file}")
                            cleanup_count += 1
                        except Exception as e:
                            print(f"   ❌ 删除标记失败: {file} - {e}")
                
                print(f"🧹 清理完成:")
                print(f"   删除文件: {cleanup_count} 个")
                print(f"   释放空间: {cleanup_size / (1024*1024):.2f} MB")
            
            elif action == "export_summary":
                summary_file = os.path.join(output_dir, "audio_speedup_summary.txt")
                with open(summary_file, 'w', encoding='utf-8') as f:
                    f.write("FlipTalk AI - 音频加速测试总结\n")
                    f.write("=" * 50 + "\n\n")
                    f.write(f"生成时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
                    
                    f.write(f"📊 统计信息:\n")
                    f.write(f"原始备份文件数量: {len(test_files['original_files'])}\n")
                    f.write(f"加速对比文件数量: {len(test_files['speedup_files'])}\n")
                    f.write(f"总文件大小: {test_files['total_size'] / (1024*1024):.2f} MB\n\n")
                    
                    if test_files['speedup_files']:
                        f.write(f"🎯 加速文件详情:\n")
                        for file_info in sorted(test_files['speedup_files'], key=lambda x: x['name']):
                            f.write(f"  {file_info['name']} (加速: {file_info['speedup_ratio']}x, 大小: {file_info['size']/1024:.1f} KB)\n")
                
                print(f"📄 已生成测试总结文件: {os.path.basename(summary_file)}")
            
            return test_files
            
        except Exception as e:
            print(f"❌ 管理测试文件失败: {e}")
            return {'original_files': [], 'speedup_files': [], 'total_size': 0}



class SubtitleDropZone(QWidget):
    """
    字幕文件拖拽上传区域
    支持拖拽和点击上传字幕文件
    """
    file_uploaded = Signal(str)  # 文件上传信号
    
    def __init__(self):
        super().__init__()
        self.setAcceptDrops(True)
        self.uploaded_file = None
        self.setup_ui()
    
    def setup_ui(self):
        """设置UI组件"""
        # 设置固定尺寸，不随文件名变化
        self.setFixedHeight(120)
        self.setMinimumWidth(340)
        self.setMaximumWidth(360)
        
        # 创建一个QFrame作为虚线边框容器
        self.frame = QFrame(self)
        self.frame.setObjectName("dropZoneFrame")
        self.frame.setStyleSheet("""
            #dropZoneFrame {
                border: 2px dashed rgba(255, 255, 255, 0.8);
                border-radius: 12px;
                background-color: transparent;
            }
        """)
        
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.addWidget(self.frame)
        
        # 内容布局
        content_layout = QVBoxLayout(self.frame)
        content_layout.setContentsMargins(15, 15, 15, 15)
        content_layout.setSpacing(8)
        
        # 图标
        icon_label = QLabel("📁")
        icon_label.setFont(QFont("Arial", 24))
        icon_label.setAlignment(Qt.AlignCenter)
        icon_label.setStyleSheet("color: #2B9D7C; background: transparent; border: none;")
        
        # 主要提示文本
        self.main_label = QLabel("拖拽字幕文件到这里")
        self.main_label.setFont(QFont("Microsoft YaHei", 11, QFont.Bold))
        self.main_label.setAlignment(Qt.AlignCenter)
        self.main_label.setStyleSheet("color: #FFFFFF; background: transparent; border: none;")
        
        # 副标题文本
        self.sub_label = QLabel("或点击选择文件 (支持 .srt .vtt .ass)")
        self.sub_label.setFont(QFont("Microsoft YaHei", 9))
        self.sub_label.setAlignment(Qt.AlignCenter)
        self.sub_label.setStyleSheet("color: rgba(255, 255, 255, 0.7); background: transparent; border: none;")
        
        # 文件名标签（用于显示已上传的文件）
        self.filename_label = QLabel("")
        self.filename_label.setFont(QFont("Microsoft YaHei", 9, QFont.Bold))
        self.filename_label.setAlignment(Qt.AlignCenter)
        self.filename_label.setStyleSheet("color: #00CC55; background: transparent; border: none;")
        self.filename_label.setVisible(False)
        self.filename_label.setWordWrap(True)
        
        content_layout.addWidget(icon_label)
        content_layout.addWidget(self.main_label)
        content_layout.addWidget(self.sub_label)
        content_layout.addWidget(self.filename_label)
    
    def dragEnterEvent(self, event: QDragEnterEvent):
        """拖拽进入事件"""
        if event.mimeData().hasUrls():
            # 检查文件类型
            urls = event.mimeData().urls()
            if urls and self.is_subtitle_file(urls[0].toLocalFile()):
                event.acceptProposedAction()
                self.set_hover_style(True)
            else:
                event.ignore()
        else:
            event.ignore()
    
    def dragLeaveEvent(self, event):
        """拖拽离开事件"""
        self.set_hover_style(False)
    
    def dropEvent(self, event: QDropEvent):
        """拖拽释放事件"""
        self.set_hover_style(False)
        files = [url.toLocalFile() for url in event.mimeData().urls()]
        
        if files and self.is_subtitle_file(files[0]):
            self.load_file(files[0])
            event.acceptProposedAction()
        else:
            QMessageBox.warning(self, "文件格式错误", "请选择字幕文件（.srt, .vtt, .ass 格式）")
    
    def mousePressEvent(self, event):
        """鼠标点击事件"""
        if event.button() == Qt.LeftButton:
            file_dialog = QFileDialog()
            file_path, _ = file_dialog.getOpenFileName(
                self, "选择字幕文件", "", 
                "字幕文件 (*.srt *.vtt *.ass);;所有文件 (*)"
            )
            if file_path:
                self.load_file(file_path)
    
    def is_subtitle_file(self, file_path):
        """检查是否为字幕文件"""
        return file_path.lower().endswith(('.srt', '.vtt', '.ass'))
    
    def load_file(self, file_path):
        """加载文件"""
        self.uploaded_file = file_path
        filename = os.path.basename(file_path)
        
        # 截断过长的文件名显示
        display_filename = self.truncate_filename(filename, max_width=30)
        
        # 更新显示
        self.main_label.setText("✅ 文件已上传")
        self.main_label.setStyleSheet("color: #00CC55; background: transparent; border: none;")
        self.sub_label.setVisible(True)  # 保持可见
        self.sub_label.setText(display_filename)  # 在副标题显示文件名
        self.sub_label.setStyleSheet("color: rgba(255, 255, 255, 0.8); background: transparent; border: none;")
        self.filename_label.setVisible(False)  # 不使用额外的文件名标签
        
        # 更新边框样式为绿色实线（表示文件已上传）
        self.set_hover_style(False)
        
        # 发送信号
        self.file_uploaded.emit(file_path)
    
    def truncate_filename(self, filename, max_width=30):
        """截断过长的文件名"""
        if len(filename) <= max_width:
            return filename
        
        # 保留文件扩展名
        name, ext = os.path.splitext(filename)
        available_width = max_width - len(ext) - 3  # 预留扩展名和省略号的空间
        
        if available_width > 0:
            return f"{name[:available_width]}...{ext}"
        else:
            return f"...{ext}"
    
    def set_hover_style(self, hover):
        """设置悬停样式"""
        if hover:
            self.frame.setStyleSheet("""
                #dropZoneFrame {
                    border: 2px dashed #00CC55;
                    border-radius: 12px;
                    background-color: rgba(0, 0, 0, 0.1);
                }
            """)
        else:
            style = """
                #dropZoneFrame {
                    border: 2px dashed rgba(255, 255, 255, 0.8);
                    border-radius: 12px;
                    background-color: transparent;
                }
            """
            if self.uploaded_file:
                style = """
                    #dropZoneFrame {
                        border: 2px solid #00CC55;
                        border-radius: 12px;
                        background-color: transparent;
                    }
                """
            self.frame.setStyleSheet(style)
    
    def clear_file(self):
        """清空文件"""
        self.uploaded_file = None
        self.main_label.setText("点击或拖拽上传")
        self.main_label.setStyleSheet("color: rgba(255, 255, 255, 0.9); background: transparent; border: none;")
        self.sub_label.setText("仅支持mp4、mov、mkv等视频文件")
        self.sub_label.setStyleSheet("color: rgba(255, 255, 255, 0.6); background: transparent; border: none;")
        self.filename_label.setVisible(False)
        self.setStyleSheet(self.get_default_style())
    
    def get_uploaded_file(self):
        """获取上传的文件路径"""
        return self.uploaded_file

    def get_default_style(self):
        """获取默认样式"""
        return """
            QWidget {
                border: 2px dashed rgba(255, 255, 255, 0.8);
                border-radius: 12px;
                background-color: rgba(0, 0, 0, 0.2);
            }
            QWidget:hover {
                border: 2px dashed #00CC55;
                background-color: rgba(0, 0, 0, 0.3);
            }
        """


class SubtitleActionWidget(QWidget):
    """字幕操作按钮组件"""
    play_audio = Signal(int)  # 播放音频信号，传递行索引
    stop_audio = Signal(int)  # 停止音频信号，传递行索引
    retry_voiceover = Signal(int)  # 重新配音信号，传递行索引
    
    def __init__(self, row_index, parent=None):
        super().__init__(parent)
        self.row_index = row_index
        self.is_playing = False  # 播放状态跟踪
        self.setup_ui()
        
    def setup_ui(self):
        """初始化按钮UI"""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(4, 3, 4, 3)  # 紧凑的边距
        layout.setSpacing(8)  # 增加按钮间距，避免挤在一起
        
        # 播放/停止按钮（缩小宽度）
        self.play_btn = QPushButton("播放")
        self.play_btn.setFixedSize(60, 28)  # 缩小按钮尺寸
        self.play_btn.setToolTip("播放音频")
        self.play_btn.setEnabled(False)  # 默认禁用
        self.play_btn.setStyleSheet("""
            QPushButton {
                background-color: #2B9D7C;
                color: #FFFFFF;
                border: none;
                border-radius: 4px;
                font-size: 10px;
                font-weight: bold;
                padding: 2px 4px;
            }
            QPushButton:hover:enabled {
                background-color: #00CC55;
            }
            QPushButton:disabled {
                background-color: #444444;
                color: #888888;
            }
        """)
        self.play_btn.clicked.connect(self.on_play_stop_clicked)
        
        # 重新配音按钮（缩小宽度）
        self.retry_btn = QPushButton("重配")
        self.retry_btn.setFixedSize(60, 28)  # 缩小按钮尺寸
        self.retry_btn.setToolTip("重新配音")
        self.retry_btn.setStyleSheet("""
            QPushButton {
                background-color: #FF9500;
                color: #FFFFFF;
                border: none;
                border-radius: 4px;
                font-size: 10px;
                font-weight: bold;
                padding: 2px 4px;
            }
            QPushButton:hover {
                background-color: #FFB84D;
            }
        """)
        self.retry_btn.clicked.connect(lambda: self.retry_voiceover.emit(self.row_index))
        
        layout.addWidget(self.play_btn)
        layout.addWidget(self.retry_btn)
        layout.addStretch()  # 推到左侧，留出右侧空间
    
    def on_play_stop_clicked(self):
        """处理播放/停止按钮点击"""
        if self.is_playing:
            # 当前正在播放，点击停止
            self.stop_audio.emit(self.row_index)
        else:
            # 当前未播放，点击播放
            self.play_audio.emit(self.row_index)
    
    def set_playing_state(self, playing):
        """设置播放状态"""
        self.is_playing = playing
        if playing:
            self.play_btn.setText("停止")
            self.play_btn.setToolTip("停止播放")
            self.play_btn.setStyleSheet("""
                QPushButton {
                    background-color: #FF6B6B;
                    color: #FFFFFF;
                    border: none;
                    border-radius: 4px;
                    font-size: 10px;
                    font-weight: bold;
                    padding: 2px 4px;
                }
                QPushButton:hover:enabled {
                    background-color: #FF8E8E;
                }
                QPushButton:disabled {
                    background-color: #444444;
                    color: #888888;
                }
            """)
        else:
            self.play_btn.setText("播放")
            self.play_btn.setToolTip("播放音频")
            self.play_btn.setStyleSheet("""
                QPushButton {
                    background-color: #2B9D7C;
                    color: #FFFFFF;
                    border: none;
                    border-radius: 4px;
                    font-size: 10px;
                    font-weight: bold;
                    padding: 2px 4px;
                }
                QPushButton:hover:enabled {
                    background-color: #00CC55;
                }
                QPushButton:disabled {
                    background-color: #444444;
                    color: #888888;
                }
            """)
        
    def update_play_button(self, can_play):
        """更新播放按钮状态"""
        self.play_btn.setEnabled(can_play)
        if not can_play:
            self.set_playing_state(False)  # 如果不能播放，重置为非播放状态
            self.play_btn.setToolTip("音频未生成")
        elif not self.is_playing:
            self.play_btn.setToolTip("播放音频")


class SubtitleTextEditor(QPlainTextEdit):
    """简洁的内联字幕编辑器"""
    text_saved = Signal(str, int)  # 新文本, 行索引
    edit_cancelled = Signal()  # 编辑取消
    
    def __init__(self, original_text, row_index, parent=None):
        super().__init__(parent)
        self.original_text = original_text
        self.row_index = row_index
        self.setPlainText(original_text)
        
        # 设置样式
        self.setStyleSheet("""
            QPlainTextEdit {
                background-color: #14161A;
                color: #FFFFFF;
                border: 2px solid #2B9D7C;
                border-radius: 6px;
                padding: 8px;
                font-family: "Microsoft YaHei";
                font-size: 13px;
                selection-background-color: #2B9D7C;
                selection-color: #1B1E24;
            }
        """)
        
        # 设置最小大小
        self.setMinimumHeight(80)
        self.setMaximumHeight(150)
        
        # 选中所有文本
        self.selectAll()
        
    def keyPressEvent(self, event):
        """处理键盘事件"""
        if event.key() == Qt.Key_Return and not (event.modifiers() & Qt.ShiftModifier):
            # Enter键保存（Shift+Enter换行）
            self.save_text()
        elif event.key() == Qt.Key_Escape:
            # Esc键取消
            self.cancel_edit()
        else:
            super().keyPressEvent(event)
    
    def focusOutEvent(self, event):
        """失去焦点时自动保存"""
        super().focusOutEvent(event)
        self.save_text()
        
    def save_text(self):
        """保存文本"""
        new_text = self.toPlainText().strip()
        if new_text and new_text != self.original_text:
            self.text_saved.emit(new_text, self.row_index)
        else:
            self.edit_cancelled.emit()
            
    def cancel_edit(self):
        """取消编辑"""
        self.edit_cancelled.emit()


class SubtitleTextDelegate(QStyledItemDelegate):
    """字幕文本列的自定义委托"""
    
    def __init__(self, parent_dialog):
        super().__init__()
        self.parent_dialog = parent_dialog
    
    def createEditor(self, parent, option, index):
        """创建简洁的内联编辑器"""
        if index.column() == 3:  # 字幕内容列（修复：从2改为3）
            original_text = index.data(Qt.UserRole + 1) or index.data(Qt.DisplayRole)
            row = index.row()
            
            editor = SubtitleTextEditor(original_text, row, parent)
            editor.text_saved.connect(self.parent_dialog.update_subtitle_text)
            # 修复：使用正确的列索引
            item = self.parent_dialog.subtitles_table.item(row, 3)
            editor.edit_cancelled.connect(lambda: self.parent_dialog.subtitles_table.closePersistentEditor(item))
            
            print(f"📝 为字幕 {row + 1} 创建编辑器，原始文本: {original_text[:30]}...")
            
            return editor
        return super().createEditor(parent, option, index)
    
    def setEditorData(self, editor, index):
        """设置编辑器数据"""
        if isinstance(editor, SubtitleTextEditor):
            # 数据已在构造函数中设置
            pass
        else:
            super().setEditorData(editor, index)
    
    def setModelData(self, editor, model, index):
        """设置模型数据 - 由text_saved信号处理，这里不需要做任何事"""
        if isinstance(editor, SubtitleTextEditor):
            # 数据保存由信号处理
            pass
        else:
            super().setModelData(editor, model, index)


class TtsInitializationThread(QThread):
    """TTS初始化线程"""
    progress_updated = Signal(int, str)  # 进度, 消息
    stage_completed = Signal(str, object)  # 阶段名, 数据
    init_completed = Signal(bool)  # 是否成功
    
    def __init__(self, tts_config=None):
        super().__init__()
        self.tts_config = tts_config or {}
        self.cached_voices = {}
        
    def run(self):
        try:
            # 初始化TTS管理器
            self.progress_updated.emit(10, "正在初始化TTS插件...")
            
            # 创建TTS管理器实例
            from plugins.tts import TtsManagerPlugin
            tts_manager = TtsManagerPlugin()
            
            # 初始化TTS管理器
            if not tts_manager.initialize(self.tts_config):
                print("❌ TTS管理器初始化失败")
                self.init_completed.emit(False)
                return
                
            # 获取可用引擎列表
            self.progress_updated.emit(20, "正在获取可用引擎...")
            engines = tts_manager.get_available_engines()
            if not engines:
                print("❌ 没有可用的TTS引擎")
                self.init_completed.emit(False)
                return
                
            # 发送引擎列表
            self.stage_completed.emit("engines", engines)
            self.progress_updated.emit(30, "正在加载语音列表...")
            
            # 获取支持的语言列表
            languages = tts_manager.get_supported_languages()
            if not languages:
                print("❌ 获取语言列表失败")
                self.init_completed.emit(False)
                return
                
            # 发送语言列表
            self.stage_completed.emit("languages", languages)
            self.progress_updated.emit(50, "正在加载语音列表...")
            
            # 获取每种语言的语音列表
            all_voices = {}
            total_languages = len(languages)
            for i, (lang_code, lang_name) in enumerate(languages.items()):
                progress = 50 + int((i + 1) / total_languages * 40)
                self.progress_updated.emit(progress, f"正在加载{lang_name}的语音列表...")
                
                voices = tts_manager.get_available_voices(lang_code)
                if voices:
                    all_voices[lang_code] = voices
                    print(f"✅ 语言 {lang_code} ({lang_name}) 可用声音数量: {len(voices)}")
            
            if not all_voices:
                print("❌ 没有可用的语音")
                self.init_completed.emit(False)
                return
                
            # 发送语音列表
            self.stage_completed.emit("voices", all_voices)
            self.progress_updated.emit(90, "TTS初始化完成")
            
            # 完成初始化
            print("✅ TTS初始化成功")
            self.init_completed.emit(True)
            
        except Exception as e:
            print(f"❌ TTS初始化失败: {e}")
            self.init_completed.emit(False)


class VoiceoverWorker(QThread):
    """配音工作线程"""
    progress_updated = Signal(int)
    voiceover_completed = Signal(int, dict, bool, str)  # 索引, 字幕信息, 成功状态, 音频路径或错误信息
    error_occurred = Signal(str)
    finished_all = Signal()
    
    def __init__(self, tts_manager, subtitles, voice, speed, output_dir, engine_name):
        super().__init__()
        self.tts_manager = tts_manager
        self.subtitles = subtitles
        self.voice = voice
        self.speed = speed
        self.output_dir = output_dir
        self.engine_name = engine_name
        self._stop_flag = False
        
    def run(self):
        try:
            total_count = len(self.subtitles)
            for index, subtitle in enumerate(self.subtitles):
                if self._stop_flag:
                    break
                    
                # 更新进度
                progress = int((index + 1) / total_count * 100)
                self.progress_updated.emit(progress)
                
                # 生成音频文件名 - 使用字幕自身的index字段（如果存在）
                # 这样可以保证重配音时生成正确的文件名
                subtitle_number = subtitle.get('index', index + 1)
                output_file = os.path.join(
                    self.output_dir,
                    f"subtitle_{subtitle_number:04d}_{self.engine_name}.wav"
                )
                
                print(f"🎵 生成音频文件: {os.path.basename(output_file)} (字幕编号: {subtitle_number})")
                
                try:
                    # 生成语音
                    success, result = self.tts_manager.generate_speech(
                        text=subtitle["text"],
                        voice_id=self.voice,
                        output_path=output_file,
                        speed=self.speed
                    )
                    
                    # 发送结果
                    if success:
                        self.voiceover_completed.emit(index, subtitle, True, output_file)
                    else:
                        self.voiceover_completed.emit(index, subtitle, False, result)
                        
                except Exception as e:
                    error_msg = f"生成语音失败: {str(e)}"
                    self.voiceover_completed.emit(index, subtitle, False, error_msg)
                    
            # 完成所有任务
            self.finished_all.emit()
            
        except Exception as e:
            error_msg = f"配音任务失败: {str(e)}"
            self.error_occurred.emit(error_msg)
            
    def stop(self):
        """停止线程"""
        self._stop_flag = True


class SubtitleVoiceoverDialog(QDialog):
    """字幕配音对话框"""
    
    def __init__(self, parent=None):
        """初始化对话框"""
        super().__init__(parent)
        
        # 初始化基本属性
        self.tts_manager = None
        self.tts_initialized = False
        self.is_testing = False
        self.media_player = None
        self.audio_output = None
        
        # 预览相关变量
        self._preview_audio_path = None
        self._preview_in_progress = False
        self._preview_cleanup_timer = None
        self._preview_media_player = None
        self._preview_audio_output = None
        
        # 初始化音频重叠检测器
        self.overlap_detector = AudioOverlapDetector()
        
        # 试听功能已禁用
        
        # 初始化媒体播放器
        self._init_media_player_safely()
        
        # 设置窗口属性
        self.setWindowTitle("字幕配音")
        self.setMinimumSize(800, 600)
        
        # 设置样式
        self.setup_style()
        
        # 设置界面
        self.setup_ui()
        
        # 初始化TTS管理器
        self.init_tts_manager()
        
        print("✅ 对话框初始化完成")
    
    def showEvent(self, event):
        """窗口显示事件"""
        try:
            print("👁️ showEvent触发 - 窗口正在显示")
            super().showEvent(event)
            print("✅ 窗口显示完成")
        except Exception as e:
            print(f"❌ showEvent处理失败: {e}")
            import traceback
            traceback.print_exc()
    

    
    def _init_media_player_safely(self):
        """安全初始化媒体播放器（增强音频设备兼容性）"""
        try:
            # 清理旧的媒体播放器（如果存在）
            if hasattr(self, 'media_player') and self.media_player:
                try:
                    self.media_player.stop()
                    self.media_player.setAudioOutput(None)
                    self.media_player = None
                except Exception:
                    pass
            
            if hasattr(self, 'audio_output') and self.audio_output:
                try:
                    self.audio_output = None
                except Exception:
                    pass
                
            # 创建媒体播放器
            self.media_player = QMediaPlayer()
            print("🔄 媒体播放器创建完成，正在配置音频设备...")
            
            # 增强的音频输出设备配置
            self.audio_output = QAudioOutput()
            
            # 尝试获取默认音频设备信息并进行兼容性设置
            try:
                from PySide6.QtMultimedia import QMediaDevices, QAudioDevice
                
                # 获取默认音频输出设备
                default_device = QMediaDevices.defaultAudioOutput()
                if default_device.isNull():
                    print("⚠️ 没有检测到默认音频输出设备")
                    # 尝试获取可用的音频设备列表
                    available_devices = QMediaDevices.audioOutputs()
                    if available_devices:
                        print(f"🔍 找到 {len(available_devices)} 个可用音频设备")
                        # 使用第一个可用设备
                        self.audio_output.setDevice(available_devices[0])
                        print(f"✅ 使用音频设备: {available_devices[0].description()}")
                    else:
                        print("⚠️ 没有找到任何可用的音频输出设备，使用默认配置")
                else:
                    print(f"✅ 检测到默认音频设备: {default_device.description()}")
                    self.audio_output.setDevice(default_device)
                    
            except Exception as device_error:
                print(f"⚠️ 音频设备配置失败: {device_error}，使用默认配置")
            
            # 设置音频输出参数
            self.audio_output.setVolume(1.0)  # 设置音量为最大
            
            # 将音频输出连接到媒体播放器
            self.media_player.setAudioOutput(self.audio_output)
            
            # 连接媒体播放器信号
            self.media_player.mediaStatusChanged.connect(self.on_media_status_changed)
            self.media_player.playbackStateChanged.connect(self.on_playback_state_changed)
            self.media_player.errorOccurred.connect(self.on_media_error_occurred)
            
            # 验证媒体播放器状态
            if not self.media_player.isAvailable():
                print("⚠️ 媒体播放器不可用，但仍尝试继续使用")
                # 不抛出异常，因为某些系统上这可能是误报
                
            # 简化的媒体播放器测试（去除可能导致错误的测试）
            try:
                # 只检查基本的错误状态
                if self.media_player.error() != QMediaPlayer.NoError:
                    print(f"⚠️ 媒体播放器初始状态有警告: {self.media_player.errorString()}")
                    # 不抛出异常，某些警告是正常的
                
                print("✅ 媒体播放器基本功能验证完成")
            except Exception as e:
                print(f"⚠️ 媒体播放器功能测试警告: {e}")
                # 继续使用播放器，不中断初始化
                
            print("✅ 媒体播放器初始化成功（增强兼容性版本）")
            return True
            
        except Exception as e:
            print(f"❌ 媒体播放器初始化失败: {e}")
            
            # 尝试创建最基本的播放器作为备用
            try:
                print("🔄 尝试创建基础媒体播放器...")
                self.media_player = QMediaPlayer()
                self.audio_output = QAudioOutput()
                # 不设置特定设备，使用系统默认
                self.media_player.setAudioOutput(self.audio_output)
                
                # 连接基本信号
                self.media_player.mediaStatusChanged.connect(self.on_media_status_changed)
                self.media_player.playbackStateChanged.connect(self.on_playback_state_changed)
                self.media_player.errorOccurred.connect(self.on_media_error_occurred)
                
                print("✅ 基础媒体播放器创建成功")
                return True
                
            except Exception as fallback_error:
                print(f"❌ 基础媒体播放器也创建失败: {fallback_error}")
                self.media_player = None
                self.audio_output = None
                return False
    
    def _setup_minimal_ui(self):
        """设置最简界面（当正常界面初始化失败时）"""
        try:
            main_layout = QVBoxLayout(self)
            
            # 简单的标题
            title_label = QLabel("字幕配音")
            title_label.setStyleSheet("font-size: 18px; font-weight: bold; color: #FFFFFF; padding: 20px;")
            title_label.setAlignment(Qt.AlignCenter)
            main_layout.addWidget(title_label)
            
            # 状态标签
            self.status_label = QLabel("正在初始化...")
            self.status_label.setStyleSheet("color: #4CAF50; font-size: 14px; padding: 10px;")
            self.status_label.setAlignment(Qt.AlignCenter)
            main_layout.addWidget(self.status_label)
            
            # 简单的进度条
            self.init_progress = QProgressBar()
            self.init_progress.setRange(0, 100)
            self.init_progress.setValue(0)
            main_layout.addWidget(self.init_progress)
            
            print("✅ 最简界面设置完成")
            
        except Exception as e:
            print(f"❌ 最简界面设置也失败: {e}")
    
    def _setup_error_ui(self, error_message):
        """设置错误显示界面"""
        try:
            main_layout = QVBoxLayout(self)
            
            # 错误标题
            error_title = QLabel("❌ 初始化失败")
            error_title.setStyleSheet("font-size: 18px; font-weight: bold; color: #FF6B6B; padding: 20px;")
            error_title.setAlignment(Qt.AlignCenter)
            main_layout.addWidget(error_title)
            
            # 错误详情
            error_detail = QLabel(f"错误信息:\n{error_message}")
            error_detail.setStyleSheet("color: #FFFFFF; font-size: 12px; padding: 10px; background-color: #2b2b2b; border-radius: 5px;")
            error_detail.setAlignment(Qt.AlignCenter)
            error_detail.setWordWrap(True)
            main_layout.addWidget(error_detail)
            
            # 关闭按钮
            close_btn = QPushButton("关闭")
            close_btn.clicked.connect(self.close)
            close_btn.setStyleSheet("""
                QPushButton {
                    background-color: #FF6B6B;
                    color: white;
                    border: none;
                    border-radius: 5px;
                    padding: 10px 20px;
                    font-size: 14px;
                }
                QPushButton:hover {
                    background-color: #FF5252;
                }
            """)
            main_layout.addWidget(close_btn)
            
        except Exception as e:
            print(f"设置错误界面失败: {e}")
    
    def _show_init_error(self, error_message):
        """显示初始化错误"""
        try:
            if hasattr(self, 'status_label') and self.status_label:
                self.status_label.setText(f"❌ 初始化失败: {error_message}")
                self.status_label.setStyleSheet("color: #FF6B6B; font-size: 14px; padding: 10px;")
        except Exception as e:
            print(f"显示初始化错误失败: {e}")
    

    

    

    
    def _enable_tts_features(self, enabled):
        """启用或禁用TTS相关功能"""
        try:
            # 启用/禁用试听按钮
            if hasattr(self, 'voice_test_btn'):
                self.voice_test_btn.setEnabled(enabled)
                if enabled:
                    self.voice_test_btn.setToolTip("点击试听当前选中的声音")
                else:
                    self.voice_test_btn.setToolTip("TTS服务不可用")
            
            # 启用/禁用配音相关按钮（这些可能在界面构建完成后创建）
            if hasattr(self, 'start_voiceover_btn'):
                self.start_voiceover_btn.setEnabled(enabled)
                
            # 更新界面状态提示
            if not enabled and hasattr(self, 'status_label') and self.status_label:
                self.status_label.setText("⚠️ TTS服务不可用，部分功能已禁用")
                
        except Exception as e:
            print(f"启用/禁用TTS功能失败: {e}")
    

    

    
    def build_full_interface(self):
        """构建完整界面（进一步增强安全性）"""
        try:
            # 完全移除加载状态区域
            if hasattr(self, 'loading_frame'):
                try:
                    self.loading_frame.setVisible(False)
                    self.loading_frame.deleteLater()
                except Exception as e:
                    print(f"清理加载框架失败: {e}")
            
            # 清除对旧 status_label 的引用，避免对象引用冲突
            if hasattr(self, 'status_label'):
                self.status_label = None
            
            # 安全地清空主内容区域
            if hasattr(self, 'main_content_widget') and self.main_content_widget:
                try:
                    layout = self.main_content_widget.layout()
                    if layout:
                        while layout.count():
                            child = layout.takeAt(0)
                            if child.widget():
                                try:
                                    child.widget().deleteLater()
                                except Exception as e:
                                    print(f"删除子组件失败: {e}")
                    else:
                        layout = QVBoxLayout(self.main_content_widget)
                except Exception as e:
                    print(f"清理主内容区域失败: {e}")
                    # 如果清理失败，创建新的主内容区域
                    self.main_content_widget = QWidget()
                    layout = QVBoxLayout(self.main_content_widget)
                    # 重新添加到主布局
                    main_layout = self.layout()
                    if main_layout and main_layout.count() > 2:
                        main_layout.insertWidget(1, self.main_content_widget)
            else:
                # 如果主内容区域不存在，创建它
                self.main_content_widget = QWidget()
                layout = QVBoxLayout(self.main_content_widget)
                main_layout = self.layout()
                if main_layout:
                    main_layout.addWidget(self.main_content_widget)
            
            # 安全地创建完整的主要内容区域
            try:
                print("🔧 创建分割器...")
                content_splitter = QSplitter(Qt.Horizontal)
                layout.addWidget(content_splitter)
                print("✅ 分割器创建成功")
                
                # 左侧：字幕列表和配置
                print("🔧 创建左侧面板...")
                left_widget = self.create_left_panel()
                print("✅ 左侧面板创建成功")
                content_splitter.addWidget(left_widget)
                print("✅ 左侧面板添加到分割器")
                
                # 右侧：预览和控制
                print("🔧 创建右侧面板...")
                right_widget = self.create_right_panel()
                print("✅ 右侧面板创建成功")
                content_splitter.addWidget(right_widget)
                print("✅ 右侧面板添加到分割器")
                
                # 设置分割器比例 - 左侧占30%，右侧占70%
                print("🔧 设置分割器比例...")
                content_splitter.setSizes([360, 840])
                content_splitter.setStretchFactor(0, 0)  # 左侧面板不拉伸
                content_splitter.setStretchFactor(1, 1)  # 右侧面板拉伸填充
                print("✅ 分割器比例设置完成")
                
                print("✅ 界面组件创建成功")
                
            except Exception as e:
                print(f"❌ 创建界面组件失败: {e}")
                import traceback
                traceback.print_exc()
                # 创建降级界面
                print("🔄 创建降级界面...")
                self._create_simple_interface(layout)
            
            # 先加载默认设置，再设置信号连接
            try:
                self.load_default_settings()
                print("✅ 默认设置加载成功")
            except Exception as e:
                print(f"⚠️ 默认设置加载失败: {e}")
            
            # 信号连接已在setup_ui()中设置
            
            print("✅ 完整界面构建完成")
            
            # 标记初始化完成
            self.initialization_complete = True
            print("🎉 字幕配音对话框完全初始化完成")
            
        except Exception as e:
            print(f"❌ 构建完整界面失败: {e}")
            import traceback
            traceback.print_exc()
            # 最后的降级措施
            try:
                self._create_simple_fallback_ui()
                # 即使是降级界面也标记为完成
                self.initialization_complete = True
                print("⚠️ 降级界面初始化完成")
            except Exception as fallback_error:
                print(f"❌ 降级界面也失败: {fallback_error}")
                # 显示最基本的错误信息
                self._show_critical_error()
                # 强制标记完成以避免循环等待
                self.initialization_complete = True
    
    def _create_simple_interface(self, layout):
        """创建简化界面（当复杂界面创建失败时）"""
        try:
            simple_widget = QWidget()
            simple_layout = QVBoxLayout(simple_widget)
            
            # 简单的功能按钮
            file_button = QPushButton("选择字幕文件")
            file_button.clicked.connect(self.browse_subtitle_file)
            simple_layout.addWidget(file_button)
            
            # 状态显示
            status_text = QLabel("字幕配音功能（简化模式）")
            status_text.setAlignment(Qt.AlignCenter)
            simple_layout.addWidget(status_text)
            
            layout.addWidget(simple_widget)
            print("✅ 简化界面创建成功")
            
        except Exception as e:
            print(f"❌ 创建简化界面失败: {e}")
    
    def _show_critical_error(self):
        """显示关键错误（最后的降级措施）"""
        try:
            # 清空所有内容
            layout = self.layout()
            if layout:
                while layout.count():
                    child = layout.takeAt(0)
                    if child.widget():
                        child.widget().deleteLater()
            else:
                layout = QVBoxLayout(self)
            
            # 最基本的错误显示
            error_label = QLabel("字幕配音模块启动异常\n\n请重新启动程序或联系技术支持")
            error_label.setAlignment(Qt.AlignCenter)
            error_label.setStyleSheet("color: #FF6B6B; font-size: 16px; padding: 20px;")
            layout.addWidget(error_label)
            
            close_button = QPushButton("关闭")
            close_button.clicked.connect(self.close)
            layout.addWidget(close_button)
            
        except Exception as e:
            print(f"❌ 显示关键错误失败: {e}")
            # 如果连这个都失败了，至少确保对话框可以关闭
            try:
                self.close()
            except:
                pass

    def init_tts_manager(self):
        """初始化TTS管理器（已移到异步线程中）"""
        if not TTS_AVAILABLE:
            return False
        
        try:
            self.tts_manager = TtsManagerPlugin()
            config = self.load_tts_config()
            # 禁用快速模式以确保加载声音列表
            config["fast_mode"] = False
            config["use_cache"] = True
            
            if self.tts_manager.initialize(config):
                print("✅ TTS管理器初始化成功")
                # 预加载语音列表
                try:
                    languages = self.tts_manager.get_supported_languages()
                    if languages:
                        print(f"✅ 支持的语言数量: {len(languages)}")
                        for lang_code in languages.keys():
                            voices = self.tts_manager.get_available_voices(lang_code)
                            if voices:
                                print(f"✅ 语言 {lang_code} 可用声音数量: {len(voices)}")
                except Exception as e:
                    print(f"⚠️ 加载语音列表失败: {e}")
                
                # ---------- 新增：初始化成功后的后续处理 ----------
                self.tts_initialized = True
                print("🔧 设置 TTS 初始化状态为 True")
                
                # 填充下拉框等默认设置
                try:
                    print("🔧 开始加载默认设置...")
                    self.load_default_settings()
                    print("✅ 默认设置加载完成")
                except Exception as e:
                    print(f"⚠️ 加载默认设置时出错: {e}")

                # 启用 TTS 相关按钮/功能
                try:
                    print("🔧 启用 TTS 相关功能...")
                    self._enable_tts_features(True)
                    print("✅ TTS 功能启用完成")
                except Exception as e:
                    print(f"⚠️ 启用 TTS 功能时出错: {e}")
                
                # 连接试听系统到TTS管理器
                try:
                    if hasattr(self, 'preview_system') and self.preview_system and self.tts_manager:
                        self.preview_system.set_tts_manager(self.tts_manager)
                        print("🔗 试听系统已连接到TTS管理器")
                except Exception as e:
                    print(f"⚠️ 连接试听系统失败: {e}")
                
                return True
            else:
                print("❌ TTS管理器初始化失败")
                self.tts_initialized = False
                # 禁用 TTS 相关功能
                try:
                    self._enable_tts_features(False)
                    print("🔒 TTS 功能已禁用")
                except Exception as e:
                    print(f"⚠️ 禁用 TTS 功能时出错: {e}")
                return False
                
        except Exception as e:
            print(f"❌ 初始化TTS管理器失败: {e}")
            self.tts_initialized = False
            # 禁用 TTS 相关功能
            try:
                self._enable_tts_features(False)
                print("🔒 TTS 功能已禁用")
            except Exception as e:
                print(f"⚠️ 禁用 TTS 功能时出错: {e}")
            return False
    
    def load_tts_config(self) -> Dict[str, Any]:
        """加载TTS配置"""
        try:
            from core.config_manager import get_config_manager
            config_manager = get_config_manager()
            return config_manager.get_tts_config()
        except ImportError:
            print("配置管理器未找到，使用默认配置")
            return {
                "azure_api_key": "",  # 用户需要在设置中配置
                "azure_region": "eastus",
                "fast_mode": True,
                "use_cache": True
            }
    
    def setup_style(self):
        """设置界面样式 - 与项目整体风格保持一致"""
        self.setStyleSheet("""
            /* 主对话框样式 */
            QDialog {
                background-color: #14161A;
                color: #FFFFFF;
                border: none;
            }
            
            /* 分组框样式 - 现代化圆角设计 */
            QGroupBox {
                font-family: "Microsoft YaHei";
                font-weight: 600;
                font-size: 13px;
                border: 2px solid #2B9D7C;
                border-radius: 10px;
                margin-top: 10px;
                padding-top: 15px;
                padding-bottom: 15px;
                color: #FFFFFF;
                background-color: #1B1E24;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 12px;
                padding: 0 8px;
                color: #2B9D7C;
                font-weight: bold;
                background-color: #1B1E24;
            }
            
            /* 按钮样式 - 统一的现代化设计 */
            QPushButton {
                background-color: #2B9D7C;
                color: #1B1E24;
                font-family: "Microsoft YaHei";
                font-size: 14px;
                font-weight: 600;
                border: none;
                border-radius: 8px;
                padding: 10px 20px;
                min-height: 16px;
            }
            QPushButton:hover {
                background-color: #00CC55;
            }
            QPushButton:pressed {
                background-color: #229954;
            }
            QPushButton:disabled {
                background-color: #444444;
                color: #888888;
            }
            
            /* 输入框和下拉框样式 */
            QComboBox, QLineEdit, QSpinBox, QDoubleSpinBox {
                background-color: #1B1E24;
                color: #FFFFFF;
                font-family: "Microsoft YaHei";
                font-size: 13px;
                border: 2px solid #444444;
                border-radius: 8px;
                padding: 8px 12px;
                min-height: 18px;
            }
            QComboBox:focus, QLineEdit:focus {
                border-color: #2B9D7C;
                outline: none;
            }
            QComboBox::drop-down {
                border: none;
                background-color: transparent;
                width: 30px;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 6px solid #FFFFFF;
                margin-right: 8px;
            }
            QComboBox QAbstractItemView {
                background-color: #1B1E24;
                color: #FFFFFF;
                border: 1px solid #2B9D7C;
                border-radius: 6px;
                selection-background-color: #2B9D7C;
                selection-color: #1B1E24;
                padding: 4px;
            }
            
            /* 表格样式 */
            QTableWidget {
                background-color: #1B1E24;
                color: #FFFFFF;
                gridline-color: #333333;
                selection-background-color: #2B9D7C;
                selection-color: #1B1E24;
                border: 1px solid #444444;
                border-radius: 8px;
                font-family: "Microsoft YaHei";
                font-size: 12px;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #333333;
            }
            QTableWidget::item:selected {
                background-color: #2B9D7C;
                color: #1B1E24;
            }
            QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2B9D7C, stop:1 #229954);
                color: #1B1E24;
                font-family: "Microsoft YaHei";
                font-weight: bold;
                font-size: 12px;
                padding: 10px;
                border: none;
                border-bottom: 1px solid #444444;
            }
            
            /* 进度条样式 */
            QProgressBar {
                background-color: #1B1E24;
                border: 2px solid #444444;
                border-radius: 8px;
                text-align: center;
                color: #FFFFFF;
                font-family: "Microsoft YaHei";
                font-size: 12px;
                font-weight: bold;
                min-height: 20px;
            }
            QProgressBar::chunk {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #2B9D7C, stop:1 #00CC55);
                border-radius: 6px;
                margin: 2px;
            }
            
            /* 滑块样式 - 现代化设计 */
            QSlider::groove:horizontal {
                background: #1B1E24;
                border: 2px solid #444444;
                height: 10px;
                border-radius: 7px;
            }
            QSlider::handle:horizontal {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #2B9D7C, stop:1 #00CC55);
                border: 2px solid #FFFFFF;
                width: 20px;
                height: 20px;
                border-radius: 12px;
                margin: -7px 0;
            }
            QSlider::handle:horizontal:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #00CC55, stop:1 #2B9D7C);
                border: 3px solid #FFFFFF;
            }
            QSlider::handle:horizontal:pressed {
                background: #229954;
            }
            
            /* 滚动条样式 */
            QScrollBar:vertical {
                background-color: #1B1E24;
                width: 12px;
                border-radius: 6px;
            }
            QScrollBar::handle:vertical {
                background-color: #2B9D7C;
                border-radius: 6px;
                min-height: 20px;
            }
            QScrollBar::handle:vertical:hover {
                background-color: #00CC55;
            }
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
                height: 0px;
            }
            
            /* 标签样式 */
            QLabel {
                color: #FFFFFF;
                font-family: "Microsoft YaHei";
                font-size: 13px;
                background-color: transparent;
                border: none;
            }
            
            /* 分割器样式 */
            QSplitter {
                background-color: #14161A;
            }
            QSplitter::handle {
                background-color: #2B9D7C;
                border-radius: 2px;
            }
            QSplitter::handle:horizontal {
                width: 4px;
            }
            QSplitter::handle:vertical {
                height: 4px;
            }
        """)
    
    def setup_ui(self):
        """设置界面布局"""
        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(8)
        main_layout.setContentsMargins(10, 10, 10, 10)
        
        # 创建标题区域 - 现代化设计
        self.create_title_area(main_layout)
        
        # 创建主要内容区域
        content_splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(content_splitter)
        
        # 左侧：字幕列表和配置
        left_widget = self.create_left_panel()
        content_splitter.addWidget(left_widget)
        
        # 右侧：预览和控制
        right_widget = self.create_right_panel()
        content_splitter.addWidget(right_widget)
        
        # 设置左侧面板固定宽度，右侧面板自适应
        left_widget.setMinimumWidth(360)
        left_widget.setMaximumWidth(380)  # 设置最大宽度，确保不会过度拉伸
        content_splitter.setSizes([370, 1030])  # 固定左侧宽度
        content_splitter.setStretchFactor(0, 0)  # 左侧面板不拉伸
        content_splitter.setStretchFactor(1, 1)  # 右侧面板拉伸填充
        content_splitter.setCollapsible(0, False)  # 左侧面板不可折叠
        
        # 底部控制栏
        self.create_bottom_controls(main_layout)
        
        # 在界面组件创建完成后设置信号连接
        try:
            self.setup_connections()
            print("✅ 界面组件信号连接设置成功")
        except Exception as e:
            print(f"⚠️ 界面组件信号连接设置失败: {e}")
        
        # 如果TTS已经初始化，加载默认设置
        if hasattr(self, 'tts_manager') and self.tts_manager and self.tts_initialized:
            self.load_default_settings()
    
    def create_title_area(self, parent_layout):
        """创建标题区域 - 简洁紧凑设计"""
        title_frame = QFrame()
        title_frame.setFixedHeight(60)
        title_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
                    stop: 0 #2B9D7C, stop: 0.5 #00CC55, stop: 1 #229954);
                border-radius: 15px;
                border: 2px solid rgba(255, 255, 255, 0.1);
            }
        """)
        
        title_layout = QHBoxLayout(title_frame)
        title_layout.setContentsMargins(15, 8, 15, 8)
        
        # 图标区域
        icon_widget = QWidget()
        icon_widget.setFixedSize(40, 40)
        icon_widget.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.2), stop:1 rgba(255, 255, 255, 0.1));
                border-radius: 20px;
                border: 2px solid rgba(255, 255, 255, 0.3);
            }
        """)
        
        icon_layout = QVBoxLayout(icon_widget)
        icon_layout.setContentsMargins(0, 0, 0, 0)
        
        icon_label = QLabel("🎤")
        icon_label.setFont(QFont("Arial", 18))
        icon_label.setAlignment(Qt.AlignCenter)
        icon_label.setStyleSheet("color: white; background: transparent; border: none;")
        icon_layout.addWidget(icon_label)
        
        # 标题文本区域
        title_widget = QWidget()
        title_text_layout = QVBoxLayout(title_widget)
        title_text_layout.setContentsMargins(15, 0, 0, 0)
        title_text_layout.setSpacing(2)
        
        main_title = QLabel("字幕配音")
        main_title.setFont(QFont("Microsoft YaHei", 16, QFont.Bold))
        main_title.setStyleSheet("""
            color: white; 
            background: transparent; 
            border: none;
        """)
        
        subtitle = QLabel("AI智能语音合成，支持多种TTS引擎和语音效果")
        subtitle.setFont(QFont("Microsoft YaHei", 9))
        subtitle.setStyleSheet("""
            color: rgba(255, 255, 255, 0.9); 
            background: transparent; 
            border: none;
        """)
        
        title_text_layout.addWidget(main_title)
        title_text_layout.addWidget(subtitle)
        title_text_layout.addStretch()
        
        # 状态信息区域
        status_widget = QWidget()
        status_widget.setFixedSize(100, 40)
        status_widget.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.15), stop:1 rgba(255, 255, 255, 0.05));
                border-radius: 10px;
                border: 1px solid rgba(255, 255, 255, 0.2);
            }
        """)
        
        status_layout = QVBoxLayout(status_widget)
        status_layout.setContentsMargins(8, 3, 8, 3)
        
        status_title = QLabel("状态")
        status_title.setFont(QFont("Microsoft YaHei", 8))
        status_title.setStyleSheet("color: rgba(255, 255, 255, 0.7); background: transparent; border: none;")
        status_title.setAlignment(Qt.AlignCenter)
        
        self.status_label = QLabel("就绪")
        self.status_label.setFont(QFont("Microsoft YaHei", 10, QFont.Bold))
        self.status_label.setStyleSheet("color: white; background: transparent; border: none;")
        self.status_label.setAlignment(Qt.AlignCenter)
        
        status_layout.addWidget(status_title)
        status_layout.addWidget(self.status_label)
        
        title_layout.addWidget(icon_widget)
        title_layout.addWidget(title_widget, 1)
        title_layout.addWidget(status_widget)
        
        parent_layout.addWidget(title_frame)
    
    def create_left_panel(self):
        """创建左侧面板 - 现代化设计"""
        print("🔧 开始创建左侧面板...")
        try:
            left_widget = QWidget()
            left_layout = QVBoxLayout(left_widget)
            left_layout.setSpacing(6)  # 从8减少到6
            print("✅ 左侧面板基础结构创建成功")
            
            # 文件拖拽上传区域（直接添加，无外部分组框）
            print("🔧 创建字幕拖拽区域...")
            self.subtitle_drop_zone = SubtitleDropZone()
            print("✅ 字幕拖拽区域创建成功")
            left_layout.addWidget(self.subtitle_drop_zone)
            print("✅ 字幕拖拽区域添加到布局")
        except Exception as e:
            print(f"❌ 创建左侧面板失败: {e}")
            import traceback
            traceback.print_exc()
            raise
        
        # TTS配置区域
        config_group = QGroupBox("配音设置")
        config_layout = QVBoxLayout(config_group)
        config_layout.setSpacing(2)  # 进一步从4减少到2，让各配置项之间更紧凑
        config_layout.setContentsMargins(10, 4, 10, 4)  # 增加上下边距，让上下间距更协调
        
        # TTS引擎选择
        engine_widget = QWidget()
        engine_layout = QVBoxLayout(engine_widget)
        engine_layout.setContentsMargins(0, 0, 0, 0)
        engine_layout.setSpacing(1)  # 进一步从2减少到1px，更紧凑
        
        engine_label = QLabel("配音渠道:")
        engine_label.setFont(QFont("Microsoft YaHei", 9, QFont.Bold))  # 从10减少到9，更紧凑
        engine_label.setStyleSheet("color: #2B9D7C; background: transparent; border: none;")
        
        self.engine_combo = QComboBox()
        self.engine_combo.setFixedHeight(26)  # 从28减少到26，更紧凑
        self.engine_combo.setStyleSheet("""
            QComboBox {
                padding: 4px 8px;
                font-size: 11px;
                min-height: 16px;
            }
        """)
        
        engine_layout.addWidget(engine_label)
        engine_layout.addWidget(self.engine_combo)
        config_layout.addWidget(engine_widget)
        
        # 语言选择
        language_widget = QWidget()
        language_layout = QVBoxLayout(language_widget)
        language_layout.setContentsMargins(0, 0, 0, 0)
        language_layout.setSpacing(1)  # 进一步从2减少到1px，更紧凑
        
        language_label = QLabel("配音语言:")
        language_label.setFont(QFont("Microsoft YaHei", 9, QFont.Bold))  # 从10减少到9，更紧凑
        language_label.setStyleSheet("color: #2B9D7C; background: transparent; border: none;")
        
        self.language_combo = QComboBox()
        self.language_combo.setFixedHeight(26)  # 从28减少到26，更紧凑
        self.language_combo.setStyleSheet("""
            QComboBox {
                padding: 4px 8px;
                font-size: 11px;
                min-height: 16px;
            }
        """)
        
        language_layout.addWidget(language_label)
        language_layout.addWidget(self.language_combo)
        config_layout.addWidget(language_widget)
        
        # 声音选择
        voice_widget = QWidget()
        voice_layout = QVBoxLayout(voice_widget)
        voice_layout.setContentsMargins(0, 0, 0, 0)
        voice_layout.setSpacing(1)  # 进一步从2减少到1px，更紧凑
        
        voice_label = QLabel("配音角色:")
        voice_label.setFont(QFont("Microsoft YaHei", 9, QFont.Bold))  # 从10减少到9，更紧凑
        voice_label.setStyleSheet("color: #2B9D7C; background: transparent; border: none;")
        
        voice_control_layout = QHBoxLayout()
        voice_control_layout.setSpacing(2)  # 进一步从3减少到2，更紧凑
        
        self.voice_combo = QComboBox()
        self.voice_combo.setFixedHeight(26)  # 从28减少到26，更紧凑
        self.voice_combo.setStyleSheet("""
            QComboBox {
                padding: 4px 8px;
                font-size: 11px;
                min-height: 16px;
            }
        """)
        
        self.voice_test_btn = QPushButton("试听")
        self.voice_test_btn.setFixedSize(64, 28)  # 增加宽度到64px，高度到28px，确保文字有足够空间
        self.voice_test_btn.setEnabled(False)  # 初始化时禁用，等待TTS初始化完成
        self.voice_test_btn.setCheckable(True)  # 设置为可切换按钮
        self.voice_test_btn.setToolTip("TTS服务初始化中，请稍候...")
        self.is_testing = False  # 添加状态跟踪
        self.voice_test_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #4ECDC4, stop:1 #45B7D1);
                color: #000000;
                font-size: 12px;
                font-weight: bold;
                font-family: "Microsoft YaHei";
                padding: 2px 8px;
                border-radius: 4px;
                border: 1px solid rgba(255, 255, 255, 0.3);
            }
            QPushButton:hover:enabled {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #45B7D1, stop:1 #4ECDC4);
                color: #000000;
            }
            QPushButton:checked {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #FF6B6B, stop:1 #FF5252);
                color: #FFFFFF;
                font-weight: bold;
            }
            QPushButton:checked:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #FF5252, stop:1 #FF6B6B);
                color: #FFFFFF;
            }
            QPushButton:disabled {
                background-color: #444444;
                color: #888888;
            }
        """)
        
        voice_control_layout.addWidget(self.voice_combo, 1)
        voice_control_layout.addWidget(self.voice_test_btn)
        
        voice_layout.addWidget(voice_label)
        voice_layout.addLayout(voice_control_layout)
        config_layout.addWidget(voice_widget)
        
        # 语速设置
        speed_widget = QWidget()
        speed_layout = QVBoxLayout(speed_widget)
        speed_layout.setContentsMargins(0, 0, 0, 0)
        speed_layout.setSpacing(1)  # 进一步从2减少到1px，更紧凑
        
        speed_top_layout = QHBoxLayout()
        speed_label_main = QLabel("语速:")
        speed_label_main.setFont(QFont("Microsoft YaHei", 9, QFont.Bold))  # 从10减少到9，更紧凑
        speed_label_main.setStyleSheet("color: #2B9D7C; background: transparent; border: none;")
        
        self.speed_label = QLabel("1.0x")
        self.speed_label.setFont(QFont("Microsoft YaHei", 9, QFont.Bold))  # 从10减少到9
        self.speed_label.setStyleSheet("color: #00CC55; background: transparent; border: none;")
        self.speed_label.setFixedWidth(35)  # 从40减少到35
        self.speed_label.setAlignment(Qt.AlignRight)
        
        speed_top_layout.addWidget(speed_label_main)
        speed_top_layout.addStretch()
        speed_top_layout.addWidget(self.speed_label)
        
        self.speed_slider = QSlider(Qt.Horizontal)
        self.speed_slider.setRange(50, 200)  # 0.5x to 2.0x
        self.speed_slider.setValue(100)  # 1.0x
        self.speed_slider.setFixedHeight(20)  # 增加高度给滑块留足空间
        self.speed_slider.setStyleSheet("""
            QSlider::groove:horizontal {
                border: 1px solid #444444;
                height: 4px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #333333, stop:1 #555555);
                border-radius: 2px;
                margin: 8px 0;
            }
            QSlider::handle:horizontal {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #2B9D7C, stop:1 #00CC55);
                border: 1px solid #1a7a5c;
                width: 12px;
                height: 12px;
                margin: -4px 0;
                border-radius: 6px;
            }
            QSlider::handle:horizontal:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #00CC55, stop:1 #2B9D7C);
            }
            QSlider::handle:horizontal:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #1a7a5c, stop:1 #145c42);
            }
            QSlider::sub-page:horizontal {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #2B9D7C, stop:1 #00CC55);
                border-radius: 2px;
                margin: 8px 0;
            }
        """)
        
        speed_layout.addLayout(speed_top_layout)
        speed_layout.addWidget(self.speed_slider)
        config_layout.addWidget(speed_widget)
        
        # 输出目录
        output_widget = QWidget()
        output_layout = QVBoxLayout(output_widget)
        output_layout.setContentsMargins(0, 0, 0, 5)  # 增加下边距到5px
        output_layout.setSpacing(1)  # 进一步从2减少到1px，更紧凑
        
        output_label = QLabel("输出目录:")
        output_label.setFont(QFont("Microsoft YaHei", 9, QFont.Bold))  # 从10减少到9，更紧凑
        output_label.setStyleSheet("color: #2B9D7C; background: transparent; border: none;")
        
        output_control_layout = QHBoxLayout()
        output_control_layout.setSpacing(2)  # 进一步从4减少到2，更紧凑
        
        self.output_dir_edit = QLineEdit()
        self.output_dir_edit.setPlaceholderText("选择音频输出目录...")
        self.output_dir_edit.setFixedHeight(26)  # 从28减少到26，更紧凑
        self.output_dir_edit.setStyleSheet("""
            QLineEdit {
                padding: 4px 8px;
                font-size: 11px;
                min-height: 16px;
            }
        """)
        
        self.output_browse_btn = QPushButton("浏览...")
        self.output_browse_btn.setFixedSize(68, 28)  # 增加宽度到68px，高度到28px，确保文字有足够空间
        self.output_browse_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #4ECDC4, stop:1 #45B7D1);
                color: #000000;
                font-size: 11px;
                font-weight: bold;
                font-family: "Microsoft YaHei";
                padding: 2px 6px;
                border-radius: 4px;
                border: 1px solid rgba(255, 255, 255, 0.3);
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #45B7D1, stop:1 #4ECDC4);
                color: #000000;
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #3a9bb8, stop:1 #3cb5a8);
                color: #000000;
            }
        """)
        
        output_control_layout.addWidget(self.output_dir_edit)
        output_control_layout.addWidget(self.output_browse_btn)
        
        output_layout.addWidget(output_label)
        output_layout.addLayout(output_control_layout)
        config_layout.addWidget(output_widget)
        
        # 给上传区域设置更大的固定高度
        self.subtitle_drop_zone.setFixedHeight(180)  # 从150增加到180，让上传区更突出
        
        # 将组件添加到左侧布局，优化空间分配
        print("🔧 添加组件到左侧布局...")
        left_layout.addWidget(self.subtitle_drop_zone,0)  # 文件上传区域固定空间
        left_layout.addWidget(config_group,1)  # 配音设置区域获得剩余空间
        print("✅ 左侧面板组件布局完成")
        
        print("✅ 左侧面板创建完成")
        return left_widget

    def create_right_panel(self):
        """创建右侧面板 - 统一的字幕列表与配音状态"""
        right_widget = QWidget()
        right_layout = QVBoxLayout(right_widget)
        right_layout.setSpacing(8)
        right_layout.setContentsMargins(8, 8, 8, 8)
        
        # 字幕预览区域（合并配音状态）
        subtitles_group = QGroupBox("字幕预览与编辑")
        subtitles_group.setStyleSheet("""
            QGroupBox {
                font-size: 14px;
                font-weight: bold;
                color: #2B9D7C;
                border: 2px solid #2B9D7C;
                border-radius: 8px;
                margin-top: 12px;
                padding-top: 8px;
                background-color: #1B1E24;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px 0 8px;
                background-color: #1B1E24;
                color: #2B9D7C;
            }
        """)
        subtitles_layout = QVBoxLayout(subtitles_group)
        subtitles_layout.setSpacing(4)
        subtitles_layout.setContentsMargins(8, 15, 8, 8)
        
        # 创建统一的字幕表格 - 六列设计（序号+时间+内容+状态+操作）
        self.subtitles_table = QTableWidget()
        self.subtitles_table.setColumnCount(6)
        self.subtitles_table.setHorizontalHeaderLabels(["序号", "开始时间", "结束时间", "字幕内容", "状态", "操作"])
        
        # 设置表格样式 - 参照图1的深色主题
        self.subtitles_table.setStyleSheet("""
            QTableWidget {
                background-color: #1B1E24;
                border: 1px solid #333333;
                gridline-color: #333333;
                selection-background-color: #2B9D7C;
                selection-color: #FFFFFF;
                color: #FFFFFF;
                font-size: 12px;
                border-radius: 4px;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #333333;
                background-color: transparent;
            }
            QTableWidget::item:selected {
                background-color: #2B9D7C;
                color: #FFFFFF;
            }
            QTableWidget::item:hover {
                background-color: rgba(43, 157, 124, 0.3);
            }
            QTableWidget::item:focus {
                background-color: rgba(43, 157, 124, 0.5);
                border: 2px solid #2B9D7C;
            }
            QHeaderView::section {
                background-color: #2B9D7C;
                color: #FFFFFF;
                padding: 8px;
                border: none;
                border-right: 1px solid #1B1E24;
                font-weight: bold;
                font-size: 11px;
            }
            QHeaderView::section:last {
                border-right: none;
            }
            QScrollBar:vertical {
                background-color: #333333;
                width: 12px;
                border-radius: 6px;
            }
            QScrollBar::handle:vertical {
                background-color: #2B9D7C;
                border-radius: 6px;
                min-height: 20px;
            }
            QScrollBar::handle:vertical:hover {
                background-color: #00CC55;
            }
        """)
        
        # 设置列宽 - 优化六列布局
        subtitles_header = self.subtitles_table.horizontalHeader()
        subtitles_header.setSectionResizeMode(0, QHeaderView.Fixed)  # 序号
        subtitles_header.setSectionResizeMode(1, QHeaderView.Fixed)  # 开始时间
        subtitles_header.setSectionResizeMode(2, QHeaderView.Fixed)  # 结束时间
        subtitles_header.setSectionResizeMode(3, QHeaderView.Stretch)  # 字幕内容
        subtitles_header.setSectionResizeMode(4, QHeaderView.Fixed)  # 状态
        subtitles_header.setSectionResizeMode(5, QHeaderView.Fixed)  # 操作
        
        self.subtitles_table.setColumnWidth(0, 60)   # 序号列宽
        self.subtitles_table.setColumnWidth(1, 100)  # 开始时间列宽
        self.subtitles_table.setColumnWidth(2, 100)  # 结束时间列宽
        self.subtitles_table.setColumnWidth(4, 100)  # 状态列宽
        self.subtitles_table.setColumnWidth(5, 140)  # 操作列宽，优化按钮布局
        
        # 设置行高和选择模式
        self.subtitles_table.setAlternatingRowColors(False)
        self.subtitles_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.subtitles_table.setSelectionMode(QTableWidget.ExtendedSelection)
        self.subtitles_table.verticalHeader().setVisible(False)
        self.subtitles_table.setShowGrid(True)
        
        # 设置行高，适应按钮高度
        self.subtitles_table.verticalHeader().setDefaultSectionSize(45)
        
        # 设置自定义委托用于字幕文本编辑
        self.subtitle_delegate = SubtitleTextDelegate(self)
        self.subtitles_table.setItemDelegateForColumn(3, self.subtitle_delegate)  # 字幕内容列
        
        subtitles_layout.addWidget(self.subtitles_table)
        
        # 单一表格占据全部空间
        right_layout.addWidget(subtitles_group, 1)
        
        return right_widget
    
    def create_bottom_controls(self, parent_layout):
        """创建底部控制栏 - 重新设计，更清晰的显示"""
        bottom_frame = QFrame()
        bottom_frame.setFixedHeight(80)  # 增加高度以容纳更清晰的内容
        bottom_frame.setStyleSheet("""
            QFrame {
                background-color: #1B1E24;
                border: 2px solid #2B9D7C;
                border-radius: 12px;
                margin: 5px;
            }
        """)
        bottom_layout = QHBoxLayout(bottom_frame)
        bottom_layout.setContentsMargins(20, 12, 20, 12)
        bottom_layout.setSpacing(20)
        
        # 左侧信息区域 - 简化显示，无装饰框
        info_widget = QWidget()
        info_widget.setFixedWidth(200)  # 减小宽度
        info_widget.setStyleSheet("background: transparent; border: none;")
        info_layout = QVBoxLayout(info_widget)
        info_layout.setContentsMargins(5, 5, 5, 5)  # 减少与上下边缘的距离
        info_layout.setSpacing(1)  # 进一步减少两行文字之间的间距
        info_layout.setAlignment(Qt.AlignBottom | Qt.AlignLeft)  # 左下角对齐
        
        self.subtitle_count_label = QLabel("📋 字幕: 0条")
        self.subtitle_count_label.setFont(QFont("Microsoft YaHei", 11))  # 增大字体
        self.subtitle_count_label.setStyleSheet("""
            color: #AAAAAA; 
            background: transparent; 
            border: none;
            font-size: 11px;
            margin: 0px;
            padding: 0px;
        """)
        
        self.engine_status_label = QLabel("🔧 引擎: 初始化中") 
        self.engine_status_label.setFont(QFont("Microsoft YaHei", 10))  # 增大字体
        self.engine_status_label.setStyleSheet("""
            color: #888888; 
            background: transparent; 
            border: none;
            font-size: 10px;
            margin: 0px;
            padding: 0px;
        """)
        
        # 重叠检测状态标签
        self.overlap_status_label = QLabel("") 
        self.overlap_status_label.setFont(QFont("Microsoft YaHei", 9))
        self.overlap_status_label.setStyleSheet("""
            color: #FF6B6B; 
            background: transparent; 
            border: none;
            font-size: 9px;
            margin: 0px;
            padding: 0px;
        """)
        self.overlap_status_label.setVisible(False)  # 默认隐藏
        
        info_layout.addWidget(self.subtitle_count_label)
        info_layout.addWidget(self.engine_status_label)
        info_layout.addWidget(self.overlap_status_label)
        
        # 中间进度区域 - 仅保留进度条
        progress_widget = QWidget()
        progress_widget.setStyleSheet("background: transparent; border: none;")
        progress_layout = QVBoxLayout(progress_widget)
        progress_layout.setContentsMargins(20, 0, 20, 0)  # 左右留白，上下无边距
        progress_layout.setAlignment(Qt.AlignCenter)
        
        # 进度条 - 完全居中显示
        self.progress_bar = QProgressBar()
        self.progress_bar.setFixedHeight(20)  # 保持合适高度
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                background-color: #2C2F36;
                border: 2px solid #555555;
                border-radius: 10px;
                text-align: center;
                font: bold 11px "Microsoft YaHei";
                color: #FFFFFF;
                margin: 0px;
                padding: 0px;
            }
            QProgressBar::chunk {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #00FF88, stop:0.5 #2B9D7C, stop:1 #00CC55);
                border-radius: 8px;
                margin: 0px;
            }
        """)
        
        # 创建隐藏的进度标签，仅用于内部状态跟踪
        self.progress_label = QLabel()
        self.progress_label.setVisible(False)  # 隐藏标签
        
        progress_layout.addWidget(self.progress_bar)
        
        # 右侧按钮区域
        button_widget = QWidget()
        button_layout = QHBoxLayout(button_widget)
        button_layout.setContentsMargins(0, 0, 0, 0)
        button_layout.setSpacing(8)
        
        # 主要操作按钮
        self.start_btn = QPushButton("开始配音")
        self.start_btn.setFixedSize(100, 40)
        self.start_btn.setEnabled(False)
        self.start_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #2B9D7C, stop:1 #00CC55);
                color: #1B1E24;
                font-family: "Microsoft YaHei";
                font-size: 13px;
                font-weight: bold;
                border: none;
                border-radius: 8px;
                padding: 8px 12px;
            }
                         QPushButton:hover:enabled {
                 background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                     stop:0 #00CC55, stop:1 #2B9D7C);
             }
            QPushButton:disabled {
                background-color: #444444;
                color: #888888;
            }
        """)
        
        self.stop_btn = QPushButton("停止配音")
        self.stop_btn.setFixedSize(100, 40)
        self.stop_btn.setEnabled(False)
        self.stop_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #FF6B6B, stop:1 #FF5252);
                color: #FFFFFF;
                font-family: "Microsoft YaHei";
                font-size: 13px;
                font-weight: bold;
                border: none;
                border-radius: 8px;
                padding: 8px 12px;
            }
                         QPushButton:hover:enabled {
                 background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                     stop:0 #FF5252, stop:1 #FF6B6B);
             }
            QPushButton:disabled {
                background-color: #444444;
                color: #888888;
            }
        """)
        
        self.export_btn = QPushButton("合并音频")
        self.export_btn.setFixedSize(100, 40)
        self.export_btn.setEnabled(False)
        self.export_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #45B7D1, stop:1 #4ECDC4);
                color: #1B1E24;
                font-family: "Microsoft YaHei";
                font-size: 13px;
                font-weight: bold;
                border: none;
                border-radius: 8px;
                padding: 8px 12px;
            }
                         QPushButton:hover:enabled {
                 background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                     stop:0 #4ECDC4, stop:1 #45B7D1);
             }
            QPushButton:disabled {
                background-color: #444444;
                color: #888888;
            }
        """)
        
        self.close_btn = QPushButton("关闭")
        self.close_btn.setFixedSize(75, 40)
        self.close_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #666666, stop:1 #555555);
                color: #FFFFFF;
                font-family: "Microsoft YaHei";
                font-size: 13px;
                font-weight: bold;
                border: none;
                border-radius: 8px;
                padding: 8px 12px;
            }
                         QPushButton:hover {
                 background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                     stop:0 #777777, stop:1 #666666);
             }
        """)
        
        button_layout.addWidget(self.start_btn)
        button_layout.addWidget(self.stop_btn)
        button_layout.addWidget(self.export_btn)
        button_layout.addWidget(self.close_btn)
        
        bottom_layout.addWidget(info_widget)
        bottom_layout.addWidget(progress_widget, 1)
        bottom_layout.addWidget(button_widget)
        
        parent_layout.addWidget(bottom_frame)
        
        # 初始化底部信息显示
        self.update_bottom_info()
    
    def update_bottom_info(self):
        """更新底部信息栏显示"""
        try:
            print("🔄 开始更新底部信息...")
            
            # 更新字幕计数和状态信息
            if hasattr(self, 'subtitle_count_label') and self.subtitle_count_label:
                subtitle_count = len(self.subtitles) if hasattr(self, 'subtitles') and self.subtitles else 0
                if subtitle_count > 0:
                    text = f"📋 已加载 {subtitle_count} 条字幕"
                else:
                    text = "📋 等待字幕文件"
                
                # 更新主标签
                self.subtitle_count_label.setText(text)
                print(f"📊 字幕计数更新: {text}")
                
                # 更新引擎状态标签
                if hasattr(self, 'engine_status_label') and self.engine_status_label:
                    if subtitle_count > 0:
                        # 显示TTS引擎状态
                        if hasattr(self, 'tts_initialized') and self.tts_initialized:
                            if hasattr(self, 'engine_combo') and self.engine_combo and self.engine_combo.currentText():
                                engine_name = self.engine_combo.currentText()
                                status_text = f"🔧 TTS引擎: {engine_name} 已就绪"
                            else:
                                status_text = "🔧 TTS引擎: 已初始化"
                        else:
                            status_text = "🔧 TTS引擎: 正在初始化..."
                    else:
                        status_text = "💡 支持 SRT、VTT、ASS 格式"
                    
                    self.engine_status_label.setText(status_text)
                    print(f"🔧 状态信息更新: {status_text}")
                    
                # 强制刷新界面显示
                self.subtitle_count_label.repaint()
                if hasattr(self, 'engine_status_label') and self.engine_status_label:
                    self.engine_status_label.repaint()
            else:
                print("⚠️ subtitle_count_label 不存在或为None")
            
            # 更新进度信息
            if hasattr(self, 'progress_label') and self.progress_label:
                if hasattr(self, 'subtitles') and self.subtitles:
                    completed_count = len([s for s in self.subtitles if s.get('audio_path')])
                    total_count = len(self.subtitles)
                    if completed_count == 0:
                        text = f"⏳ 准备配音 {completed_count}/{total_count}"
                    elif completed_count == total_count:
                        text = f"✅ 配音完成 {completed_count}/{total_count}"
                    else:
                        text = f"🎵 配音中 {completed_count}/{total_count}"
                else:
                    text = "⏳ 准备配音 0/0"
                self.progress_label.setText(text)
                print(f"📈 进度信息更新: {text}")
            else:
                print("⚠️ progress_label 不存在或为None")
            
            print("✅ 底部信息更新完成")
            
        except Exception as e:
            print(f"❌ 更新底部信息失败: {e}")
            import traceback
            traceback.print_exc()
    
    def setup_connections(self):
        """设置信号连接"""
        # 文件上传 - 添加安全检查
        if hasattr(self, 'subtitle_drop_zone') and self.subtitle_drop_zone:
            self.subtitle_drop_zone.file_uploaded.connect(self.load_subtitle_file)
        
        # 输出目录选择 - 添加安全检查
        if hasattr(self, 'output_browse_btn') and self.output_browse_btn:
            self.output_browse_btn.clicked.connect(self.browse_output_dir)
        
        # TTS配置 - 添加安全检查，使用currentIndexChanged确保触发
        if hasattr(self, 'engine_combo') and self.engine_combo:
            self.engine_combo.currentIndexChanged.connect(self.on_engine_changed)
        if hasattr(self, 'language_combo') and self.language_combo:
            self.language_combo.currentIndexChanged.connect(self.update_voice_list)
        if hasattr(self, 'voice_combo') and self.voice_combo:
            # 使用currentIndexChanged确保在选择改变时触发
            self.voice_combo.currentIndexChanged.connect(self.on_voice_changed)
        if hasattr(self, 'speed_slider') and self.speed_slider:
            self.speed_slider.valueChanged.connect(self.on_speed_changed)
        
        # 预览相关连接
        if hasattr(self, 'voice_test_btn') and self.voice_test_btn:
            self.voice_test_btn.clicked.connect(self.toggle_voice_test)
        
        # 主要操作 - 添加安全检查
        if hasattr(self, 'start_btn') and self.start_btn:
            self.start_btn.clicked.connect(self.start_voiceover)
        if hasattr(self, 'stop_btn') and self.stop_btn:
            self.stop_btn.clicked.connect(self.stop_voiceover)
        if hasattr(self, 'export_btn') and self.export_btn:
            self.export_btn.clicked.connect(self.export_audio)
        if hasattr(self, 'close_btn') and self.close_btn:
            self.close_btn.clicked.connect(self.close)
        
        # 表格双击播放 - 添加安全检查
        if hasattr(self, 'subtitles_table') and self.subtitles_table:
            self.subtitles_table.itemDoubleClicked.connect(self.on_subtitle_item_double_clicked)
            # 表格内容变化 - 处理字幕编辑
            self.subtitles_table.itemChanged.connect(self.on_subtitle_text_changed)
        
        # 媒体播放器 - 添加安全检查
        if hasattr(self, 'media_player') and self.media_player:
            self.media_player.mediaStatusChanged.connect(self.on_media_status_changed)
    
    def load_default_settings(self):
        """加载默认设置"""
        try:
            print("🔧 开始加载默认设置...")
            if self.tts_manager:
                # 加载可用引擎
                engines = self.tts_manager.get_available_engines()
                print(f"📋 获取到TTS引擎列表: {engines}")
                
                if hasattr(self, 'engine_combo') and self.engine_combo:
                    # 清空现有项目
                    self.engine_combo.clear()
                    
                    for engine in engines:
                        try:
                            engine_info = self.tts_manager.get_engine_info(engine)
                            display_name = engine_info.get("name", engine)
                            
                            # 美化引擎显示名称
                            if engine == "edge_tts":
                                display_name = "Edge TTS (免费)"
                            elif engine == "azure_tts":
                                display_name = "Azure TTS (高级)"
                            
                            self.engine_combo.addItem(display_name, engine)
                            print(f"✅ 添加引擎: {display_name} -> {engine}")
                        except Exception as e:
                            print(f"❌ 添加引擎 {engine} 失败: {e}")
                    
                    if engines:
                        self.engine_combo.setCurrentIndex(0)
                        print(f"🎯 设置默认引擎: {engines[0]}")
                        # 初始化时避免调用可能触发UI交互的方法
                        # 直接设置引擎并更新语言列表
                        try:
                            selected_engine = engines[0]
                            if self.tts_manager.set_current_engine(selected_engine):
                                print(f"✅ 引擎设置成功: {selected_engine}")
                                self.update_language_list()
                                # 更新语言列表后，立即更新声音列表
                                self.update_voice_list()
                            else:
                                print(f"⚠️ 引擎设置失败: {selected_engine}")
                        except Exception as e:
                            print(f"❌ 设置引擎时出错: {e}")
                        
                        # 安全更新引擎状态
                        try:
                            self.update_engine_status()
                        except Exception as e:
                            print(f"❌ 更新引擎状态失败: {e}")
                        
                    print(f"📊 引擎下拉框项目数: {self.engine_combo.count()}")
                else:
                    print("⚠️ engine_combo 不存在")
            else:
                print("⚠️ tts_manager 为空")
            
            # 设置默认输出目录
            default_output = os.path.join(os.getcwd(), "output", "voiceover")
            if hasattr(self, 'output_dir_edit') and self.output_dir_edit:
                self.output_dir_edit.setText(default_output)
                print(f"📁 设置默认输出目录: {default_output}")
            
            print("✅ 默认设置加载完成")
        except Exception as e:
            print(f"❌ 加载默认设置失败: {e}")
            import traceback
            traceback.print_exc()

    def update_engine_status(self):
        """更新引擎状态"""
        if self.tts_manager and self.tts_manager.get_available_engines():
            current_engine = self.tts_manager.get_current_engine()
            self.engine_status_label.setText(f"引擎状态: {current_engine} 已连接")
        else:
            self.engine_status_label.setText("引擎状态: 未连接")

    def _test_azure_tts(self):
        """测试Azure TTS是否可用"""
        try:
            # 重新初始化TTS管理器
            test_config = self.load_tts_config()
            test_config["fast_mode"] = False  # 禁用快速模式以确保加载声音列表
            if not self.tts_manager.initialize(test_config):
                raise Exception("TTS管理器初始化失败")
            
            # 切换到Azure TTS引擎
            if not self.tts_manager.set_current_engine("azure_tts"):
                raise Exception("切换到Azure TTS引擎失败")
            
            # 获取语言列表
            languages = self.tts_manager.get_supported_languages()
            if not languages:
                raise Exception("获取语言列表失败")
            
            # 获取中文声音列表
            voices = self.tts_manager.get_available_voices("zh-CN")
            if not voices:
                raise Exception("获取声音列表失败")
            
            print("✅ Azure TTS测试成功")
            return True
        except Exception as e:
            print(f"❌ Azure TTS测试失败: {e}")
            return False
            
    def on_engine_changed(self):
        """当TTS引擎改变时"""
        try:
            # 清理预览
            self._cleanup_preview_file()
            self._reset_preview_state()
            
            engine_name = self.engine_combo.currentData()
            print(f"🔄 正在切换到TTS引擎: {engine_name}")
            
            if not engine_name:
                print("⚠️ 未选择TTS引擎")
                return
            
            # 如果是Azure TTS，先检查API密钥
            if engine_name == "azure_tts":
                config = self.load_tts_config()
                if not config.get("azure_api_key"):
                    QMessageBox.warning(self, "Azure TTS未配置", 
                                      "请先在设置中配置Azure TTS的API密钥")
                    # 切换回Edge TTS
                    for i in range(self.engine_combo.count()):
                        if self.engine_combo.itemData(i) == "edge_tts":
                            self.engine_combo.setCurrentIndex(i)
                            break
                    return
                
                # 在新线程中执行测试
                Thread(target=self._test_azure_tts).start()
            
            # 切换TTS引擎
            if self.tts_manager.set_current_engine(engine_name):
                print(f"✅ 切换到TTS引擎成功: {engine_name}")
                # 更新语言和声音列表
                self.update_language_list()
                self.update_voice_list()
            else:
                print(f"❌ 切换TTS引擎失败: {engine_name}")
                # 如果切换失败，回退到Edge TTS
                for i in range(self.engine_combo.count()):
                    if self.engine_combo.itemData(i) == "edge_tts":
                        self.engine_combo.setCurrentIndex(i)
                        break
                
        except Exception as e:
            print(f"❌ 切换TTS引擎时出错: {e}")
            import traceback
            traceback.print_exc()
    
    def update_language_list(self):
        """更新语言列表"""
        try:
            # 保存当前选择
            current_lang_code = self.language_combo.currentData()
            
            # 清空列表
            self.language_combo.clear()
            
            # 获取当前引擎支持的语言
            supported_languages = self.tts_manager.get_supported_languages()
            print(f"📋 获取到支持的语言列表: {supported_languages}")
            
            if not supported_languages:
                print("⚠️ 没有找到支持的语言")
                # 如果是Azure TTS，尝试重新初始化
                current_engine = self.engine_combo.currentData()
                if current_engine == "azure_tts":
                    print("🔄 尝试重新初始化Azure TTS...")
                    config = self.load_tts_config()
                    config["fast_mode"] = False  # 禁用快速模式以确保加载语音列表
                    if self.tts_manager.initialize(config):
                        print("✅ Azure TTS重新初始化成功")
                        # 重新初始化后确保当前引擎为 Azure
                        try:
                            if not self.tts_manager.set_current_engine("azure_tts"):
                                print("⚠️ 重新设置当前引擎到 Azure 失败")
                            else:
                                print("🔧 已将当前引擎设置为 Azure TTS")
                        except Exception as e:
                            print(f"⚠️ 重新设置 Azure TTS 引擎失败: {e}")
                        supported_languages = self.tts_manager.get_supported_languages()
                    else:
                        print("❌ Azure TTS重新初始化失败")
            
            # 添加语言到下拉框
            for lang_code, lang_name in supported_languages.items():
                self.language_combo.addItem(lang_name, lang_code)
                print(f"✅ 添加语言: {lang_name} -> {lang_code}")
            
            # 恢复之前的选择或默认选择中文
            if current_lang_code:
                # 先尝试恢复之前的选择
                for i in range(self.language_combo.count()):
                    if self.language_combo.itemData(i) == current_lang_code:
                        self.language_combo.setCurrentIndex(i)
                        print(f"✅ 恢复之前选择的语言: {current_lang_code}")
                        break
            
            # 如果没有恢复成功，尝试选择中文
            if self.language_combo.currentData() is None:
                for i in range(self.language_combo.count()):
                    lang_code = self.language_combo.itemData(i)
                    if lang_code and ("zh" in lang_code.lower() or "chinese" in lang_code.lower()):
                        self.language_combo.setCurrentIndex(i)
                        print(f"✅ 默认选择中文: {lang_code}")
                        break
            
            # 如果还是没有选择，选择第一个
            if self.language_combo.currentData() is None and self.language_combo.count() > 0:
                self.language_combo.setCurrentIndex(0)
                print(f"✅ 选择第一个语言: {self.language_combo.currentData()}")
                    
        except Exception as e:
            print(f"❌ 更新语言列表失败: {e}")
            import traceback
            traceback.print_exc()
            
    def update_voice_list(self):
        """更新声音列表"""
        try:
            # 试听功能已禁用
            # self._auto_cleanup_preview_on_settings_change("声音列表")
            
            print("🔄 正在更新声音列表...")
            
            # 保存当前选择
            current_voice_id = self.voice_combo.currentData()
            print(f"📌 当前选择的声音: {current_voice_id}")
            
            # 清空列表
            self.voice_combo.clear()
            
            # 获取当前语言代码
            current_lang_code = self.language_combo.currentData()
            if not current_lang_code:
                print("⚠️ 未选择语言，无法更新声音列表")
                return
            print(f"📌 当前语言: {current_lang_code}")
            
            # 获取当前引擎
            current_engine = self.engine_combo.currentData()
            if not current_engine:
                print("⚠️ 未选择TTS引擎")
                return
            print(f"📌 当前引擎: {current_engine}")
            
            # 如果是Azure TTS，先确保配置正确
            if current_engine == "azure_tts":
                config = self.load_tts_config()
                if not config.get("azure_api_key"):
                    print("⚠️ Azure TTS API密钥未配置")
                    return
                
                # 重新初始化TTS管理器
                print("🔄 重新初始化Azure TTS...")
                config["fast_mode"] = False  # 禁用快速模式以确保加载声音列表
                config["skip_voice_list"] = False  # 确保加载声音列表
                if not self.tts_manager.initialize(config):
                    print("❌ Azure TTS重新初始化失败")
                    return
                print("✅ Azure TTS重新初始化成功")
                # 重新初始化后确保当前引擎设置为 Azure TTS
                try:
                    if not self.tts_manager.set_current_engine("azure_tts"):
                        print("⚠️ 重新设置当前引擎到 Azure 失败")
                    else:
                        print("🔧 已将当前引擎设置为 Azure TTS")
                except Exception as e:
                    print(f"⚠️ 重新设置 Azure TTS 引擎失败: {e}")
            
            # 获取可用声音列表
            try:
                available_voices = self.tts_manager.get_available_voices(current_lang_code)
                print(f"📋 获取到可用声音列表: {available_voices}")
                
                if not available_voices:
                    print("⚠️ 没有找到可用声音")
                    return
                            
                # 添加声音到下拉框
                for voice_id, voice_name in available_voices.items():
                    self.voice_combo.addItem(voice_name, voice_id)
                    print(f"✅ 添加声音: {voice_name} -> {voice_id}")
                
                # 恢复之前的选择
                if current_voice_id:
                    for i in range(self.voice_combo.count()):
                        if self.voice_combo.itemData(i) == current_voice_id:
                            self.voice_combo.setCurrentIndex(i)
                            print(f"✅ 恢复之前选择的声音: {current_voice_id}")
                            break
                
                # 如果没有恢复成功，选择第一个
                if self.voice_combo.currentData() is None and self.voice_combo.count() > 0:
                    self.voice_combo.setCurrentIndex(0)
                    print(f"✅ 选择第一个声音: {self.voice_combo.currentData()}")
                
                # 试听功能已禁用
                # self._update_voice_test_button_state()
                
            except Exception as e:
                print(f"❌ 获取声音列表失败: {e}")
                import traceback
                traceback.print_exc()
                return
            
        except Exception as e:
            print(f"❌ 更新声音列表失败: {e}")
            import traceback
            traceback.print_exc()

    def on_voice_changed(self):
        """当声音改变时"""
        # 试听功能已禁用
        # self._auto_cleanup_preview_on_settings_change("声音")
        
        voice_id = self.voice_combo.currentData() if hasattr(self, 'voice_combo') and self.voice_combo else None
        
        # 检查当前选择的TTS引擎是否可用
        current_engine = self.engine_combo.currentData()
        if current_engine == 'azure_tts':
            # 对Azure TTS进行额外检查
            try:
                # 快速测试Azure TTS是否响应
                import threading
                from concurrent.futures import ThreadPoolExecutor, TimeoutError as FutureTimeoutError
                
                def quick_azure_test():
                    return hasattr(self.tts_manager, 'tts_engines') and 'azure_tts' in self.tts_manager.tts_engines
                
                with ThreadPoolExecutor(max_workers=1) as executor:
                    future = executor.submit(quick_azure_test)
                    is_available = future.result(timeout=2)  # 2秒快速检查
                    
                if not is_available:
                    QMessageBox.warning(self, "Azure TTS不可用", 
                                      "Azure TTS引擎当前不可用\n\n可能原因：\n• 网络连接问题\n• API配置错误\n• 服务暂时不可用\n\n已自动切换到Edge TTS")
                    # 切换到Edge TTS
                    for i in range(self.engine_combo.count()):
                        if self.engine_combo.itemData(i) == 'edge_tts':
                            self.engine_combo.setCurrentIndex(i)
                            break
                    return
                    
            except (FutureTimeoutError, Exception):
                QMessageBox.warning(self, "Azure TTS响应超时", 
                                  "Azure TTS服务响应超时\n\n建议：\n• 检查网络连接\n• 切换到Edge TTS引擎\n• 稍后再试")
                return
        
        # 试听功能已禁用
        # self._update_voice_test_button_state()
        
        # 重置按钮状态
        if hasattr(self, 'is_testing'):
            self.is_testing = False
            if hasattr(self, 'voice_test_btn') and self.voice_test_btn:
                self.voice_test_btn.setChecked(False)
                self.voice_test_btn.setText("试听")
        
        # 试听功能已禁用
        # if voice_id and hasattr(self, 'preview_cache'):
        #     speed = self.speed_slider.value() / 100.0
        #     cache_key = f"{voice_id}_{int(speed * 100)}"
        #     if cache_key in self.preview_cache:
        #         if hasattr(self, 'status_label') and self.status_label and not self.status_label.isHidden():
        #             try:
        #                 self.status_label.setText("已缓存，可快速试听")
        #             except RuntimeError:
        #                 pass

    def on_speed_changed(self):
        """当语速改变时"""
        # 试听功能已禁用
        # self._auto_cleanup_preview_on_settings_change("语速")
        
        speed_value = self.speed_slider.value() / 100.0
        self.speed_label.setText(f"{speed_value:.1f}x")

    def on_subtitle_selection_changed(self):
        """当字幕条目选择改变时"""
        # 预览区域已移除，此方法保留以防其他地方使用
        pass

    def start_voiceover(self):
        """开始配音"""
        if not self.subtitles:
            QMessageBox.warning(self, "警告", "请先加载字幕文件")
            return
        
        if not self.voice_combo.currentData():
            QMessageBox.warning(self, "警告", "请选择语音")
            return
        
        output_dir = self.output_dir_edit.text().strip()
        if not output_dir:
            QMessageBox.warning(self, "警告", "请选择输出目录")
            return
        
        # 创建输出目录
        try:
            os.makedirs(output_dir, exist_ok=True)
        except Exception as e:
            QMessageBox.critical(self, "错误", f"无法创建输出目录: {e}")
            return
        
        # 准备配音参数
        voice_id = self.voice_combo.currentData()
        speed = self.speed_slider.value() / 100.0
        engine_name = self.engine_combo.currentData()
        
        # 验证配置
        try:
            if not self.tts_manager:
                raise Exception("TTS管理器未初始化")
            
            if engine_name not in self.tts_manager.tts_engines:
                raise Exception(f"TTS引擎 {engine_name} 不可用")
            
            # 测试第一个字幕的合成（确保引擎真正可用）
            if self.subtitles:
                test_text = self.subtitles[0].get('text', '').strip()[:20]  # 取前20个字符测试
                if test_text:
                    print(f"🧪 测试 {engine_name} 引擎...")
                    try:
                        engine = self.tts_manager.tts_engines[engine_name]
                        test_output = os.path.join(output_dir, "test_voice.wav")
                        
                        # 生成测试音频
                        test_result = engine.synthesize(test_text, voice_id, speed, 1.0, 0.0, test_output)
                        
                        # 清理测试文件
                        if test_result and os.path.exists(test_result):
                            os.remove(test_result)
                        
                        print(f"✅ {engine_name} 引擎测试通过")
                        
                    except Exception as test_error:
                        print(f"❌ {engine_name} 引擎测试失败: {test_error}")
                        QMessageBox.critical(self, "引擎测试失败", 
                                           f"{engine_name} 引擎测试失败\n\n错误详情: {test_error}\n\n请检查配置或切换到其他引擎")
                        return
                        
        except Exception as e:
            QMessageBox.critical(self, "配置错误", f"配音配置检查失败: {e}")
            return
        
        # 在开始配音前同步最新的字幕文本（确保包含用户编辑的内容）
        print("🔄 同步最新的字幕文本...")
        self._sync_latest_subtitle_texts()
        
        # 确保所有字幕都有正确的index字段（用于生成正确的文件名）
        for i, subtitle in enumerate(self.subtitles):
            if 'index' not in subtitle:
                subtitle['index'] = i + 1
        
        # 启动配音线程
        try:
            self.worker = VoiceoverWorker(
                self.tts_manager, self.subtitles, voice_id, speed, output_dir, engine_name
            )
            
            # 连接信号
            self.worker.progress_updated.connect(self.on_progress_updated)
            self.worker.voiceover_completed.connect(self.on_voiceover_completed)
            self.worker.error_occurred.connect(self.on_error_occurred)
            self.worker.finished_all.connect(self.on_finished_all)
            
            # 更新UI状态
            self.start_btn.setEnabled(False)
            self.stop_btn.setEnabled(True)
            self.progress_bar.setValue(0)
            self.progress_label.setText("0/0")
            self.voiceover_results = []
            
            # 重置所有字幕状态为等待
            if hasattr(self, 'subtitle_status'):
                for i in range(len(self.subtitles)):
                    self.update_subtitle_status(i, 'waiting')
            
            # 启动线程
            print(f"🚀 启动配音任务，使用引擎: {engine_name}")
            self.worker.start()
            # 安全地更新状态标签
            if hasattr(self, 'status_label') and self.status_label and not self.status_label.isHidden():
                try:
                    self.status_label.setText("配音进行中...")
                except RuntimeError:
                    pass
        
        except Exception as e:
            QMessageBox.critical(self, "启动失败", f"无法启动配音任务: {e}")
            print(f"❌ 启动配音任务失败: {e}")
            import traceback
            traceback.print_exc()
            return

    def stop_voiceover(self):
        """停止配音"""
        if self.worker:
            try:
                self.worker.stop()
                # 安全地等待线程结束
                try:
                    self.worker.wait()
                except RuntimeError:
                    # C++对象已被删除，跳过等待操作
                    print("配音工作线程的C++对象已被删除，跳过等待操作")
            except Exception as e:
                print(f"停止配音工作线程时出错: {e}")
        
        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        # 安全地更新状态标签
        if hasattr(self, 'status_label') and self.status_label and not self.status_label.isHidden():
            try:
                self.status_label.setText("配音已停止")
            except RuntimeError:
                pass

    def update_subtitle_status(self, index, status):
        """更新字幕状态"""
        try:
            if index < 0 or index >= self.subtitles_table.rowCount():
                return
            
            status_item = self.subtitles_table.item(index, 4)  # 修正：状态显示在第5列（索引4）
            if not status_item:
                status_item = QTableWidgetItem()
                self.subtitles_table.setItem(index, 4, status_item)
            
            if status == 'processing':
                status_item.setText("🔄 配音中")
                status_item.setBackground(QColor(255, 193, 7, 80))  # 黄色背景
                status_item.setToolTip("正在生成语音...")
            elif status == 'waiting':
                status_item.setText("⏳ 等待")
                status_item.setBackground(QColor(100, 100, 100, 50))  # 灰色背景
                status_item.setToolTip("等待配音...")
            elif status == 'success':
                status_item.setText("✅ 成功")
                status_item.setBackground(QColor(0, 204, 85, 80))  # 绿色背景
                status_item.setTextAlignment(Qt.AlignCenter)
                # 获取音频路径用于工具提示
                if hasattr(self, 'subtitle_status') and index in self.subtitle_status:
                    audio_path = self.subtitle_status[index].get('audio_path')
                    if audio_path:
                        filename = os.path.basename(audio_path)
                        status_item.setToolTip(f"音频文件: {filename}\n双击播放")
            elif status == 'failed':
                status_item.setText("❌ 失败")
                status_item.setBackground(QColor(255, 107, 107, 80))  # 红色背景
                status_item.setTextAlignment(Qt.AlignCenter)
                # 获取错误信息用于工具提示
                if hasattr(self, 'subtitle_status') and index in self.subtitle_status:
                    error_msg = self.subtitle_status[index].get('error_message')
                    if error_msg:
                        status_item.setToolTip(f"错误: {error_msg}")
                
            # 更新状态记录，确保字典已初始化
            if hasattr(self, 'subtitle_status'):
                if index not in self.subtitle_status:
                    self.subtitle_status[index] = {
                        'status': status,
                        'audio_path': None,
                        'error_message': None,
                        'action_widget': None
                    }
                else:
                    self.subtitle_status[index]['status'] = status
                
                # 更新操作按钮状态
                action_widget = self.subtitle_status[index].get('action_widget')
                if action_widget:
                    can_play = (status == 'success' and 
                              self.subtitle_status[index].get('audio_path') and 
                              os.path.exists(self.subtitle_status[index]['audio_path']))
                    action_widget.update_play_button(can_play)
                
        except Exception as e:
            print(f"❌ 更新字幕状态失败: {e}")
            import traceback
            traceback.print_exc()

    def on_progress_updated(self, progress):
        """更新进度（优化版本，支持平滑动画）"""
        # 设置进度条值
        self.progress_bar.setValue(progress)
        
        # 更新进度标签
        total = len(self.subtitles) if hasattr(self, 'subtitles') and self.subtitles else 0
        current = int(progress * total / 100) if total > 0 else 0
        
        if progress == 0:
            self.progress_label.setText(f"⏳ 准备配音 {current}/{total}")
        elif progress >= 100:
            self.progress_label.setText(f"✅ 配音完成 {total}/{total}")
        else:
            self.progress_label.setText(f"🎵 配音中 {current}/{total}")
        
        # 更新当前正在处理的字幕状态 - 只有在尚未完成的情况下才设置为处理中
        if current > 0 and current <= total:
            subtitle_index = current - 1
            # 检查该字幕是否已经完成配音
            if hasattr(self, 'subtitle_status') and subtitle_index in self.subtitle_status:
                current_status = self.subtitle_status[subtitle_index]['status']
                # 只有在等待状态时才设置为处理中，避免覆盖已完成的状态
                if current_status == 'waiting':
                    self.update_subtitle_status(subtitle_index, 'processing')
                    print(f"🔄 设置字幕 {subtitle_index + 1} 为配音中")
                else:
                    print(f"⏭️ 跳过已完成的字幕 {subtitle_index + 1}，当前状态: {current_status}")
            else:
                # 如果没有状态记录，则设置为处理中
                self.update_subtitle_status(subtitle_index, 'processing')
                print(f"🔄 设置字幕 {subtitle_index + 1} 为配音中（首次）")
        
        # 如果有状态标签，更新状态信息
        if hasattr(self, 'status_label') and self.status_label and not self.status_label.isHidden():
            try:
                if progress < 100:
                    self.status_label.setText(f"配音进行中... ({progress}%)")
                else:
                    self.status_label.setText("配音即将完成...")
            except RuntimeError:
                pass
        
        # 更新底部信息显示
        self.update_bottom_info()
        
        # 实时刷新界面，确保进度条动画流畅
        from PySide6.QtWidgets import QApplication
        QApplication.processEvents()

    def on_voiceover_completed(self, index, subtitle, success, result):
        """处理单个配音完成 - 更新统一表格状态"""
        try:
            # 使用传入的索引，它已经是正确的行索引
            subtitle_index = index
            
            if subtitle_index < 0 or subtitle_index >= self.subtitles_table.rowCount():
                print(f"⚠️ 字幕索引超出范围: {subtitle_index}")
                return
            
            print(f"📝 配音完成 - 行索引: {subtitle_index}, 成功: {success}")
            if success:
                print(f"🎵 音频路径: {result}")
            else:
                print(f"❌ 错误信息: {result}")
            
            # 更新状态列
            if success:
                status_item = QTableWidgetItem("✅ 成功")
                status_item.setBackground(QColor(0, 204, 85, 80))  # 绿色背景
                status_item.setTextAlignment(Qt.AlignCenter)
                if result:
                    filename = os.path.basename(result)
                    status_item.setToolTip(f"音频文件: {filename}\n双击播放")
                    # 为整行添加音频路径数据
                    for col in range(5):
                        item = self.subtitles_table.item(subtitle_index, col)
                        if item:
                            item.setData(Qt.UserRole, result)
            else:
                status_item = QTableWidgetItem("❌ 失败")
                status_item.setBackground(QColor(255, 107, 107, 80))  # 红色背景
                status_item.setTextAlignment(Qt.AlignCenter)
                status_item.setToolTip(f"错误: {result}")
            
            status_item.setFlags(Qt.ItemIsSelectable | Qt.ItemIsEnabled)
            self.subtitles_table.setItem(subtitle_index, 4, status_item)
            
            # 关键修复：将配音结果添加到voiceover_results列表中
            if not hasattr(self, 'voiceover_results'):
                self.voiceover_results = []
            
            # 更新或添加到voiceover_results
            result_entry = {
                'index': subtitle_index,
                'success': success,
                'result': result,
                'subtitle': subtitle
            }
            
            # 检查是否已存在该索引的结果（重新配音的情况）
            existing_index = None
            for i, existing_result in enumerate(self.voiceover_results):
                if existing_result['index'] == subtitle_index:
                    existing_index = i
                    break
            
            if existing_index is not None:
                # 更新现有结果
                self.voiceover_results[existing_index] = result_entry
                print(f"🔄 更新配音结果 - 索引 {subtitle_index}")
            else:
                # 添加新结果
                self.voiceover_results.append(result_entry)
                print(f"➕ 添加配音结果 - 索引 {subtitle_index}")
            
            # 更新状态记录 - 关键修复：确保正确更新音频路径
            if hasattr(self, 'subtitle_status'):
                if subtitle_index not in self.subtitle_status:
                    self.subtitle_status[subtitle_index] = {
                        'status': 'success' if success else 'failed',
                        'audio_path': result if success else None,
                        'error_message': result if not success else None,
                        'action_widget': None
                    }
                else:
                    # 更新现有记录
                    self.subtitle_status[subtitle_index]['status'] = 'success' if success else 'failed'
                    self.subtitle_status[subtitle_index]['audio_path'] = result if success else None
                    self.subtitle_status[subtitle_index]['error_message'] = result if not success else None
                
                print(f"🔄 状态记录已更新 - 行 {subtitle_index}: 状态={self.subtitle_status[subtitle_index]['status']}, 音频路径={self.subtitle_status[subtitle_index]['audio_path']}")
                
                # 关键修复：在更新状态记录后，调用update_subtitle_status确保UI同步
                final_status = 'success' if success else 'failed'
                self.update_subtitle_status(subtitle_index, final_status)
                print(f"🎯 UI状态已同步更新为: {final_status}")
                
                # 更新操作按钮状态
                action_widget = self.subtitle_status[subtitle_index].get('action_widget')
                if action_widget:
                    # 无论是否有重叠问题，都允许播放
                    can_play = success and result and os.path.exists(result)
                    action_widget.update_play_button(can_play)
                    print(f"🎮 操作按钮已更新 - 播放按钮可用: {can_play}")
                
                # 如果是重新配音成功，清理媒体播放器缓存并刷新文件
                if success and result and hasattr(self, 'media_player') and self.media_player:
                    try:
                        # 如果当前正在播放，先停止
                        if self.media_player.playbackState() != self.media_player.PlaybackState.StoppedState:
                            self.media_player.stop()
                        
                        # 清空媒体源以清除缓存
                        empty_url = QUrl()
                        self.media_player.setSource(empty_url)
                        print(f"🧹 已清理媒体播放器缓存（重新配音完成）")
                        
                        # 强制刷新音频文件
                        self.force_refresh_audio_file(result)
                        
                        # 清理重配音备份文件
                        backup_path = result + ".backup"
                        if os.path.exists(backup_path):
                            try:
                                os.remove(backup_path)
                                print(f"🗑️ 已删除重配音备份文件: {os.path.basename(backup_path)}")
                            except Exception as backup_error:
                                print(f"⚠️ 删除备份文件失败: {backup_error}")
                        
                        # 验证新音频文件
                        if os.path.exists(result):
                            new_size = os.path.getsize(result)
                            print(f"✅ 新音频文件已生成: {os.path.basename(result)} ({new_size} 字节)")
                        
                    except Exception as e:
                        print(f"⚠️ 清理媒体播放器缓存失败: {e}")
                
                # 如果重配音失败，尝试恢复备份文件
                elif not success and result:
                    # 查找可能的备份文件
                    potential_backup = None
                    if hasattr(self, 'output_dir_edit') and self.output_dir_edit.text():
                        output_dir = os.path.join(self.output_dir_edit.text(), "voiceover")
                        engine_name = self.engine_combo.currentText() if hasattr(self, 'engine_combo') else 'unknown'
                        audio_filename = f"subtitle_{subtitle_index + 1:04d}_{engine_name}.wav"
                        audio_path = os.path.join(output_dir, audio_filename)
                        potential_backup = audio_path + ".backup"
                    
                    if potential_backup and os.path.exists(potential_backup):
                        try:
                            # 恢复备份文件
                            audio_path = potential_backup[:-7]  # 移除.backup后缀
                            os.rename(potential_backup, audio_path)
                            print(f"♻️ 已恢复备份文件: {os.path.basename(audio_path)}")
                            
                            # 更新状态为成功（使用备份文件）
                            self.subtitle_status[subtitle_index]['status'] = 'success'
                            self.subtitle_status[subtitle_index]['audio_path'] = audio_path
                            self.subtitle_status[subtitle_index]['error_message'] = None
                            
                            # 使用update_subtitle_status统一更新UI状态
                            self.update_subtitle_status(subtitle_index, 'success')
                            print(f"🎯 备份恢复后UI状态已更新为: success")
                            
                            # 更新操作按钮
                            if action_widget:
                                action_widget.update_play_button(True)
                            
                        except Exception as e:
                            print(f"⚠️ 恢复备份文件失败: {e}")
                
            # 检查重叠状态
            if success and result:
                self.check_single_subtitle_overlap(subtitle_index, result)
                
        except Exception as e:
            print(f"❌ 更新配音完成状态失败: {e}")
            import traceback
            traceback.print_exc()

    def on_error_occurred(self, error_msg):
        """处理配音错误"""
        QMessageBox.critical(self, "错误", f"配音失败: {error_msg}")
        # 安全地更新状态标签
        if hasattr(self, 'status_label') and self.status_label and not self.status_label.isHidden():
            try:
                self.status_label.setText("配音出错")
            except RuntimeError:
                pass

    def on_finished_all(self):
        """处理配音全部完成"""
        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        self.export_btn.setEnabled(True)
        
        # 统计成功数量
        success_count = sum(1 for result in self.voiceover_results if result.get('success', False))
        total_count = len(self.voiceover_results)
        
        # 执行音频重叠检测
        self.detect_and_mark_overlapping_subtitles()
        
        # 统计和管理测试文件
        if hasattr(self, 'output_dir_edit') and self.output_dir_edit.text():
            output_dir = os.path.join(self.output_dir_edit.text(), "voiceover")
            if hasattr(self, 'overlap_detector'):
                test_files_info = self.overlap_detector.manage_test_files(output_dir, "list")
                
                # 如果有测试文件，生成详细总结
                if test_files_info['speedup_files'] or test_files_info['original_files']:
                    self.overlap_detector.manage_test_files(output_dir, "export_summary")
        
        # 安全地更新状态标签
        if hasattr(self, 'status_label') and self.status_label and not self.status_label.isHidden():
            try:
                self.status_label.setText(f"配音完成: {success_count}/{total_count}")
            except RuntimeError:
                pass
        
        # 获取重叠数量用于提示
        overlap_count = self.overlap_detector.get_overlapping_count()
        
        # 构建完成提示信息
        message = f"配音完成!\n成功: {success_count}/{total_count}"
        if overlap_count > 0:
            message += f"\n\n⚠️ 检测到 {overlap_count} 条字幕可能存在音频重叠问题"
            message += "\n（已用红色背景标记，建议人工调整字幕内容或时间）"
            message += f"\n\n🎵 已自动处理 {len([f for f in test_files_info.get('speedup_files', [])]) if 'test_files_info' in locals() else 0} 个音频加速"
            message += "\n📁 查看输出目录可找到原始音频备份和加速对比文件"
        
        QMessageBox.information(self, "完成", message)
        print(f"✅ 配音全部完成，成功: {success_count}/{total_count}, 重叠字幕: {overlap_count}")
    
    def detect_and_mark_overlapping_subtitles(self):
        """检测音频重叠并标记需要人工调整的字幕"""
        try:
            print("🔍 开始音频重叠检测...")
            
            # 不要清除所有标记，而是保持已有的重叠标记
            # self.overlap_detector.clear_all_marks()
            
            # 构建音频路径映射
            audio_paths = {}
            for i, result in enumerate(self.voiceover_results):
                if result.get('success', False) and result.get('result'):
                    # 使用字幕表格的行索引作为键
                    row_index = result.get('index', i)
                    audio_paths[row_index] = result['result']
            
            print(f"📊 收集到 {len(audio_paths)} 个音频文件用于检测")
            
            # 执行重叠检测
            overlapping_indices = self.overlap_detector.detect_overlaps(self.subtitles, audio_paths)
            
            # 标记重叠的字幕
            for subtitle_index in overlapping_indices:
                self.overlap_detector.mark_subtitle_overlapping(subtitle_index)
                self.mark_subtitle_row_red(subtitle_index)
                print(f"🔴 字幕 {subtitle_index + 1} 已标记为红色（需要人工调整）")
            
            # 额外检查：确保所有被重叠检测器标记的字幕都显示红色背景
            # （即使当前没有音频文件）
            all_overlapping_count = 0
            for i in range(len(self.subtitles)):
                if self.overlap_detector.is_subtitle_overlapping(i):
                    self.mark_subtitle_row_red(i)
                    all_overlapping_count += 1
                    print(f"🔴 确保字幕 {i + 1} 显示红色背景（已标记为重叠）")
            
            if all_overlapping_count > 0:
                print(f"⚠️ 检测完成，共有 {all_overlapping_count} 条字幕需要人工调整")
                # 更新底部状态显示
                self.update_overlap_status(all_overlapping_count)
            else:
                print("✅ 检测完成，未发现需要人工调整的重叠字幕")
                # 隐藏重叠状态标签
                if hasattr(self, 'overlap_status_label'):
                    self.overlap_status_label.setVisible(False)
                
        except Exception as e:
            print(f"❌ 音频重叠检测失败: {e}")
            import traceback
            traceback.print_exc()
    
    def mark_subtitle_row_red(self, subtitle_index: int):
        """将指定字幕行标记为红色背景"""
        try:
            if not hasattr(self, 'subtitles_table') or subtitle_index < 0 or subtitle_index >= self.subtitles_table.rowCount():
                print(f"⚠️ 无法标记字幕 {subtitle_index + 1}：索引超出范围")
                return
            
            # 设置更明显的红色背景色 - 使用不透明的红色
            red_color = QColor(255, 100, 100)  # 更鲜明的红色，不透明
            
            # 为整行设置红色背景
            for col in range(self.subtitles_table.columnCount() - 1):  # 除了操作列外的所有列
                item = self.subtitles_table.item(subtitle_index, col)
                if item:
                    # 强制设置背景色
                    item.setBackground(red_color)
                    
                    # 为状态列添加特殊标记
                    if col == 4:  # 状态列
                        current_text = item.text()
                        if not current_text.startswith("🔴"):
                            item.setText(f"🔴 {current_text}")
                        item.setToolTip("⚠️ 音频重叠风险：需要加速超过1.2倍才能避免与下一字幕重叠\n建议：缩短字幕内容或调整时间\n\n注意：即使存在重叠风险，您仍然可以播放此音频")
                    else:
                        # 其他列添加提示
                        original_tooltip = item.toolTip()
                        overlap_tooltip = "⚠️ 此字幕存在音频重叠风险，建议人工调整"
                        if original_tooltip and "音频重叠风险" not in original_tooltip:
                            item.setToolTip(f"{original_tooltip}\n\n{overlap_tooltip}")
                        elif not original_tooltip:
                            item.setToolTip(overlap_tooltip)
                    
                    # 添加自定义数据标记，表示此行有重叠问题
                    item.setData(Qt.UserRole + 2, True)
            
            # 强制刷新表格显示
            self.subtitles_table.repaint()
            
            print(f"🔴 字幕 {subtitle_index + 1} 已标记为红色背景")
            
        except Exception as e:
            print(f"❌ 标记字幕行红色失败: {e}")
            import traceback
            traceback.print_exc()
    
    def unmark_subtitle_row_red(self, subtitle_index: int):
        """取消指定字幕行的红色标记"""
        try:
            if not hasattr(self, 'subtitles_table') or subtitle_index < 0 or subtitle_index >= self.subtitles_table.rowCount():
                return
            
            # 恢复原始背景色
            original_color = QColor(25, 30, 36, 50) if subtitle_index % 2 == 1 else QColor(0, 0, 0, 0)  # 交替行色
            
            # 恢复整行背景
            for col in range(self.subtitles_table.columnCount() - 1):
                item = self.subtitles_table.item(subtitle_index, col)
                if item:
                    if col == 4:  # 状态列 (修正索引)
                        # 移除红色标记
                        current_text = item.text()
                        if current_text.startswith("🔴 "):
                            item.setText(current_text[2:])  # 移除红色圆圈和空格
                        item.setBackground(self._get_status_background_color(item.text()))
                    else:
                        item.setBackground(original_color)
                        # 清理重叠提示
                        tooltip = item.toolTip()
                        if "音频重叠风险" in tooltip:
                            # 移除重叠相关的提示文本
                            lines = tooltip.split('\n')
                            filtered_lines = [line for line in lines if "音频重叠风险" not in line and "建议人工调整" not in line]
                            new_tooltip = '\n'.join(filtered_lines).strip()
                            item.setToolTip(new_tooltip)
                    
                    # 移除重叠标记数据
                    if item.data(Qt.UserRole + 2):
                        item.setData(Qt.UserRole + 2, False)
            
            print(f"✅ 字幕 {subtitle_index + 1} 红色标记已移除")
            
        except Exception as e:
            print(f"❌ 移除字幕行红色标记失败: {e}")
    
    def _get_status_background_color(self, status_text: str) -> QColor:
        """根据状态文本获取对应的背景色"""
        if "成功" in status_text or "✅" in status_text:
            return QColor(0, 204, 85, 80)  # 绿色
        elif "失败" in status_text or "❌" in status_text:
            return QColor(255, 107, 107, 80)  # 红色
        elif "配音中" in status_text or "🔄" in status_text:
            return QColor(255, 193, 7, 80)  # 黄色
        else:
            return QColor(100, 100, 100, 50)  # 灰色（等待状态）

    def check_single_subtitle_overlap(self, subtitle_index: int, audio_path: str):
        """检查单个字幕的重叠状态（重配音完成后调用）"""
        try:
            if not hasattr(self, 'overlap_detector') or not self.overlap_detector:
                print("⚠️ 重叠检测器未初始化，跳过单个字幕重叠检查")
                return
                
            if not audio_path or not os.path.exists(audio_path):
                print(f"⚠️ 音频文件不存在，跳过重叠检查: {audio_path}")
                return
                
            if subtitle_index < 0 or subtitle_index >= len(self.subtitles):
                print(f"⚠️ 字幕索引超出范围，跳过重叠检查: {subtitle_index}")
                return
                
            print(f"🔍 检查字幕 {subtitle_index + 1} 的重叠状态...")
            
            # 执行重叠检测
            overlapping_indices = self.overlap_detector.detect_overlaps(self.subtitles, {subtitle_index: audio_path})
            
            # 检查结果并标记（静默处理，不显示弹窗）
            if subtitle_index in overlapping_indices:
                # 字幕重新配音后仍然重叠（超过最大加速比例）
                self.overlap_detector.mark_subtitle_overlapping(subtitle_index)
                self.mark_subtitle_row_red(subtitle_index)
                print(f"🔴 字幕 {subtitle_index + 1} 重新配音后仍存在重叠，已重新标记为红色")
                print(f"💡 建议：缩短字幕文本内容或调整字幕时间")
                # 已删除弹窗提示，保持静默处理
            else:
                # 字幕重新配音后不再重叠或可以通过加速解决
                if self.overlap_detector.is_subtitle_overlapping(subtitle_index):
                    self.overlap_detector.unmark_subtitle_overlapping(subtitle_index)
                    self.unmark_subtitle_row_red(subtitle_index)
                    print(f"✅ 字幕 {subtitle_index + 1} 重新配音后不再重叠，红色标记已移除")
                    # 已删除成功提示弹窗，保持静默处理
                else:
                    print(f"✅ 字幕 {subtitle_index + 1} 重新配音完成，无重叠问题")
            
        except Exception as e:
            print(f"❌ 检查单个字幕重叠状态失败: {e}")
            import traceback
            traceback.print_exc()

    def update_overlap_status(self, overlap_count: int):
        """更新重叠状态显示（静默模式，不显示弹窗）"""
        try:
            # 只在控制台记录重叠状态，不显示界面提示
            if overlap_count > 0:
                print(f"📊 重叠检测结果: {overlap_count} 条字幕存在重叠风险（静默处理）")
            else:
                print("📊 重叠检测结果: 未发现音频重叠问题")
                
            # 可选：更新状态标签（如果存在）
            if hasattr(self, 'overlap_status_label'):
                if overlap_count > 0:
                    # 使用更温和的提示，不强调风险
                    self.overlap_status_label.setText(f"ℹ️ 检测到 {overlap_count} 条字幕音频有重叠")
                    self.overlap_status_label.setStyleSheet("color: #FFA726; font-weight: normal;")  # 使用橙色而非红色
                    self.overlap_status_label.setVisible(True)
                else:
                    self.overlap_status_label.setText("✅ 音频时间安排正常")
                    self.overlap_status_label.setStyleSheet("color: #4CAF50; font-weight: normal;")
                    self.overlap_status_label.setVisible(True)
            else:
                print(f"📝 重叠状态标签不存在，仅记录状态")
                
        except Exception as e:
            print(f"❌ 更新重叠状态失败: {e}")

    def on_subtitle_item_double_clicked(self, item):
        """处理字幕表格双击事件 - 编辑字幕或播放音频"""
        try:
            row = item.row()
            col = item.column()
            
            # 如果是字幕内容列，进入内联编辑模式
            if col == 2:
                print(f"📝 编辑字幕 {row + 1}")
                # 创建模型索引
                model_index = self.subtitles_table.model().index(row, col)
                self.subtitles_table.edit(model_index)
                return
            
            # 其他列：播放音频或显示状态信息
            # 先检查是否有音频文件可播放
            if hasattr(self, 'subtitle_status') and row in self.subtitle_status:
                status_info = self.subtitle_status[row]
                status = status_info['status']
                audio_path = status_info.get('audio_path')
                
                if status == 'success' and audio_path and os.path.exists(audio_path):
                    print(f"🎵 播放字幕 {row + 1} 的音频: {os.path.basename(audio_path)}")
                    self.play_result_audio(audio_path)
                elif status == 'waiting':
                    print(f"⏳ 字幕 {row + 1} 还未开始配音")
                elif status == 'processing':
                    print(f"🔄 字幕 {row + 1} 正在配音中...")
                elif status == 'failed':
                    error_msg = status_info.get('error_message', '未知错误')
                    print(f"❌ 字幕 {row + 1} 配音失败: {error_msg}")
                    QMessageBox.warning(self, "配音失败", f"第 {row + 1} 条字幕配音失败:\n{error_msg}")
                else:
                    print(f"🔍 字幕 {row + 1} 音频文件不存在")
            else:
                print(f"📄 字幕 {row + 1} 暂无配音结果")
                    
        except Exception as e:
            print(f"❌ 处理双击事件失败: {e}")
    
    def on_subtitle_text_changed(self, item):
        """处理字幕文本编辑"""
        try:
            row = item.row()
            col = item.column()
            
            # 只处理字幕内容列的变化 (列3是字幕内容列)
            if col == 3 and row < len(self.subtitles):
                new_text = item.text().strip()
                if new_text:
                    # 更新原始字幕数据
                    self.subtitles[row]['text'] = new_text
                    print(f"✏️ 字幕 {row + 1} 内容已更新: {new_text[:30]}...")
                    
                    # 如果已经配音过，重置状态为等待
                    if hasattr(self, 'subtitle_status') and row in self.subtitle_status:
                        current_status = self.subtitle_status[row]['status']
                        if current_status in ['success', 'failed']:
                            print(f"🔄 检测到文本修改，重置字幕 {row + 1} 状态（原状态: {current_status}）")
                            
                            # 保留旧音频文件路径（文本已修改，但会被新音频覆盖）
                            old_audio_path = self.subtitle_status[row].get('audio_path')
                            self.keep_audio_file_path(old_audio_path, "文本已修改，将被覆盖")
                            
                            # 重置状态
                            self.update_subtitle_status(row, 'waiting')
                            print(f"✅ 字幕 {row + 1} 状态已重置为等待（文本已修改）")
                            
                            # 如果该字幕之前被标记为重叠，移除红色标记
                            if hasattr(self, 'overlap_detector') and self.overlap_detector.is_subtitle_overlapping(row):
                                self.overlap_detector.unmark_subtitle_overlapping(row)
                                self.unmark_subtitle_row_red(row)
                                print(f"🟢 字幕 {row + 1} 重叠标记已清除（文本已修改）")
                            
                            # 更新操作按钮状态 - 不应该禁用播放按钮，应该基于音频文件是否存在
                            if 'action_widget' in self.subtitle_status[row]:
                                action_widget = self.subtitle_status[row]['action_widget']
                                if action_widget:
                                    # 检查是否仍有音频文件可播放
                                    audio_path = self.subtitle_status[row].get('audio_path')
                                    can_play = audio_path and os.path.exists(audio_path)
                                    action_widget.update_play_button(can_play)
                                    print(f"🎮 播放按钮状态已更新: {'可用' if can_play else '不可用'}（文本已修改）")
                else:
                    # 如果文本为空，恢复原始内容
                    original_text = item.data(Qt.UserRole + 1)
                    if original_text:
                        item.setText(original_text.replace('\n', ' ').strip())
                        
        except Exception as e:
            print(f"❌ 处理字幕文本变化失败: {e}")

    def force_refresh_audio_file(self, audio_path):
        """强制刷新音频文件以避免缓存问题"""
        try:
            if not audio_path or not os.path.exists(audio_path):
                return False
            
            # 获取文件信息
            file_stat = os.stat(audio_path)
            file_size = file_stat.st_size
            file_mtime = file_stat.st_mtime
            
            print(f"🔄 强制刷新音频文件: {os.path.basename(audio_path)}")
            print(f"   文件大小: {file_size} 字节")
            print(f"   修改时间: {file_mtime}")
            
            # 读取一小段文件内容来强制系统重新读取
            with open(audio_path, 'rb') as f:
                header = f.read(1024)  # 读取前1KB
                print(f"   文件头校验: {len(header)} 字节已读取")
            
            return True
            
        except Exception as e:
            print(f"⚠️ 强制刷新音频文件失败: {e}")
            return False

    def cleanup_audio_file(self, audio_path, reason=""):
        """清理音频文件的辅助函数"""
        if not audio_path:
            return False
        
        try:
            if os.path.exists(audio_path):
                os.remove(audio_path)
                reason_text = f"（{reason}）" if reason else ""
                print(f"🗑️ 已删除音频文件: {os.path.basename(audio_path)}{reason_text}")
                return True
            else:
                print(f"⚠️ 音频文件不存在，无需删除: {os.path.basename(audio_path)}")
                return False
        except Exception as e:
            print(f"❌ 删除音频文件失败: {os.path.basename(audio_path)}, 错误: {e}")
            return False

    def update_subtitle_text(self, new_text, row_index):
        """更新字幕文本 - 简化版本"""
        try:
            if row_index < len(self.subtitles):
                # 更新原始字幕数据
                old_text = self.subtitles[row_index]['text']
                self.subtitles[row_index]['text'] = new_text
                
                print(f"📝 字幕文本更新:")
                print(f"   行索引: {row_index + 1}")
                print(f"   原文本: {old_text}")
                print(f"   新文本: {new_text}")
                
                # 更新表格显示
                item = self.subtitles_table.item(row_index, 3)  # 字幕内容列（修复：从2改为3）
                if item:
                    # 显示时将多行文本转换为单行显示
                    display_text = new_text.replace('\n', ' ').strip()
                    item.setText(display_text)
                    # 保存原始多行文本到用户数据
                    item.setData(Qt.UserRole + 1, new_text)
                    print(f"✅ 表格项已更新: 显示文本='{display_text}'")
                    print(f"✅ 用户数据已保存: 完整文本='{new_text}'")
                else:
                    print(f"❌ 警告：无法找到行 {row_index + 1} 列 3 的表格项")
                
                print(f"✅ 字幕 {row_index + 1} 数据结构已更新: '{self.subtitles[row_index]['text']}'")
                print(f"✅ 字幕 {row_index + 1} 内容已更新完成")
                
                # 如果已经配音过，重置状态为等待
                if hasattr(self, 'subtitle_status') and row_index in self.subtitle_status:
                    current_status = self.subtitle_status[row_index]['status']
                    if current_status in ['success', 'failed']:
                        print(f"🔄 检测到文本修改，重置字幕 {row_index + 1} 状态（原状态: {current_status}）")
                        
                        # 保留旧音频文件路径（文本已修改，但会被新音频覆盖）
                        old_audio_path = self.subtitle_status[row_index].get('audio_path')
                        self.keep_audio_file_path(old_audio_path, "文本已修改，将被覆盖")
                        
                        # 重置状态
                        self.update_subtitle_status(row_index, 'waiting')
                        print(f"✅ 字幕 {row_index + 1} 状态已重置为等待（文本已修改）")
                        
                        # 如果该字幕之前被标记为重叠，移除红色标记
                        if hasattr(self, 'overlap_detector') and self.overlap_detector.is_subtitle_overlapping(row_index):
                            self.overlap_detector.unmark_subtitle_overlapping(row_index)
                            self.unmark_subtitle_row_red(row_index)
                            print(f"🟢 字幕 {row_index + 1} 重叠标记已清除（文本已修改）")
                        
                        # 更新操作按钮状态 - 基于音频文件是否存在决定播放按钮状态
                        if 'action_widget' in self.subtitle_status[row_index]:
                            action_widget = self.subtitle_status[row_index]['action_widget']
                            if action_widget:
                                # 检查是否仍有音频文件可播放
                                audio_path = self.subtitle_status[row_index].get('audio_path')
                                can_play = audio_path and os.path.exists(audio_path)
                                action_widget.update_play_button(can_play)
                                print(f"🎮 播放按钮状态已更新: {'可用' if can_play else '不可用'}（文本已修改）")
                
        except Exception as e:
            print(f"❌ 更新字幕文本失败: {e}")
            import traceback
            traceback.print_exc()

    def play_result_audio(self, audio_path):
        """播放结果音频（增强设备兼容性版本）"""
        # 检查媒体播放器是否存在，如果不存在则重新初始化
        if not hasattr(self, 'media_player') or self.media_player is None:
            print("⚠️ 媒体播放器未初始化，尝试重新初始化...")
            if not self._init_media_player_safely():
                print("❌ 媒体播放器初始化失败")
                QMessageBox.warning(self, "错误", "媒体播放器初始化失败，无法播放音频")
                return
        
        if not audio_path:
            print("❌ 音频路径为空")
            QMessageBox.warning(self, "错误", "音频路径为空")
            return
            
        if not os.path.exists(audio_path):
            print(f"❌ 音频文件不存在: {audio_path}")
            QMessageBox.warning(self, "错误", f"音频文件不存在:\n{audio_path}")
            return
        
        try:
            # 使用绝对路径
            abs_path = os.path.abspath(audio_path)
            
            # 获取文件信息并验证
            file_mtime = os.path.getmtime(abs_path)
            file_size = os.path.getsize(abs_path)
            
            print(f"🎵 准备播放音频: {os.path.basename(audio_path)}")
            print(f"📁 完整路径: {abs_path}")
            print(f"📊 文件信息: 大小={file_size}字节, 修改时间={file_mtime}")
            
            # 验证文件大小 - WAV文件应该至少有44字节的头部
            if file_size < 1000:  # 小于1KB的音频文件可能有问题
                print(f"⚠️ 音频文件可能损坏：文件过小 ({file_size} 字节)")
                QMessageBox.warning(self, "音频文件问题", f"音频文件可能损坏（文件过小：{file_size} 字节）\n建议重新生成该音频")
                return
            
            # 验证文件头部 - 检查是否为有效的WAV文件
            try:
                with open(abs_path, 'rb') as f:
                    header = f.read(1024)  # 读取更多字节以检查文件头
                    if len(header) >= 12:
                        # 检查WAV文件标识 - 放宽验证条件
                        if header[0:4] != b'RIFF':
                            print(f"⚠️ 音频文件可能不是标准WAV格式，但仍尝试播放")
                        print(f"   文件头校验: {len(header)} 字节已读取")
                        print(f"✅ 音频文件验证通过")
                    else:
                        print(f"❌ 无法读取文件头部，文件可能损坏")
                        QMessageBox.warning(self, "文件损坏", "无法读取音频文件，可能已损坏")
                        return
            except Exception as e:
                print(f"❌ 文件验证失败: {e}")
                QMessageBox.warning(self, "文件验证失败", f"无法验证音频文件：{e}")
                return
            
            # 彻底停止当前播放并清空媒体源
            self.media_player.stop()
            
            # 清空当前媒体源（重要：清除缓存）
            empty_url = QUrl()
            self.media_player.setSource(empty_url)
            
            # 处理应用程序事件，确保停止命令被处理
            QApplication.processEvents()
            
            # 等待一小段时间确保清理完成
            import time
            time.sleep(0.3)  # 增加等待时间以确保设备状态重置
            
            # 尝试重新初始化音频输出（解决设备激活问题）
            try:
                if hasattr(self, 'audio_output') and self.audio_output:
                    # 重新设置音频设备
                    from PySide6.QtMultimedia import QMediaDevices
                    default_device = QMediaDevices.defaultAudioOutput()
                    if not default_device.isNull():
                        self.audio_output.setDevice(default_device)
                        print(f"🔄 重新设置音频设备: {default_device.description()}")
                    
                    # 重新设置音量
                    self.audio_output.setVolume(1.0)
                    
                    # 确保音频输出与媒体播放器连接
                    self.media_player.setAudioOutput(self.audio_output)
                    
            except Exception as device_error:
                print(f"⚠️ 重新配置音频设备时出错: {device_error}")
                # 继续播放，不中断
            
            # 使用绝对路径创建URL
            file_url = QUrl.fromLocalFile(abs_path)
            
            print(f"🔗 文件URL: {file_url.toString()}")
            
            # 设置新的音频源
            self.media_player.setSource(file_url)
            
            # 处理事件以确保源设置完成
            QApplication.processEvents()
            
            # 等待一小段时间让媒体源加载
            time.sleep(0.2)
            
            # 检查媒体播放器状态（不中断播放尝试）
            if self.media_player.error() != QMediaPlayer.NoError:
                error_msg = f"媒体播放器警告: {self.media_player.errorString()}"
                print(f"⚠️ {error_msg}")
                # 不显示弹窗，只记录警告，仍尝试播放
            
            # 开始播放
            self.media_player.play()
            
            print("✅ 播放命令已发送（增强验证版本）")
            
        except Exception as e:
            error_msg = f"播放音频失败: {e}"
            print(f"❌ {error_msg}")
            import traceback
            traceback.print_exc()
            
            # 尝试重新初始化播放器并重试一次
            try:
                print("🔄 尝试重新初始化播放器并重试...")
                if self._init_media_player_safely():
                    # 简化的重试播放
                    self.media_player.setSource(QUrl.fromLocalFile(abs_path))
                    QApplication.processEvents()
                    time.sleep(0.1)
                    self.media_player.play()
                    print("✅ 重试播放成功")
                else:
                    QMessageBox.critical(self, "播放错误", f"音频播放失败，无法重新初始化播放器：{error_msg}")
            except Exception as retry_error:
                print(f"❌ 重试播放也失败: {retry_error}")
                QMessageBox.critical(self, "播放错误", f"音频播放失败：{error_msg}")
            
        # 在最后添加事件处理以确保界面响应
        QApplication.processEvents()

    def on_play_audio_clicked(self, row_index):
        """处理播放音频按钮点击"""
        try:
            print(f"🎵 点击播放按钮 - 行索引: {row_index}")
            
            if hasattr(self, 'subtitle_status') and row_index in self.subtitle_status:
                status_info = self.subtitle_status[row_index]
                status = status_info['status'] 
                audio_path = status_info.get('audio_path')
                
                print(f"📊 字幕状态信息 - 状态: {status}, 音频路径: {audio_path}")
                
                if status == 'success' and audio_path:
                    if os.path.exists(audio_path):
                        print(f"✅ 音频文件存在，开始播放: {os.path.basename(audio_path)}")
                        
                        # 检查是否有重叠标记
                        has_overlap = False
                        if hasattr(self, 'overlap_detector') and self.overlap_detector:
                            has_overlap = self.overlap_detector.is_subtitle_overlapping(row_index)
                            
                        if has_overlap:
                            print(f"⚠️ 字幕 {row_index + 1} 存在重叠风险，但仍允许播放")
                        
                        # 停止其他正在播放的音频
                        self.stop_all_audio_playback()
                        
                        # 强制刷新音频文件以避免缓存
                        self.force_refresh_audio_file(audio_path)
                        
                        # 设置当前播放状态
                        self.current_playing_row = row_index
                        
                        # 更新按钮状态为播放中
                        action_widget = status_info.get('action_widget')
                        if action_widget:
                            action_widget.set_playing_state(True)
                        
                        self.play_result_audio(audio_path)
                    else:
                        print(f"❌ 音频文件不存在: {audio_path}")
                        QMessageBox.warning(self, "提示", f"第 {row_index + 1} 条字幕的音频文件不存在\n路径: {audio_path}")
                else:
                    # 即使状态不是success，也检查是否有音频文件可播放
                    if audio_path and os.path.exists(audio_path):
                        print(f"✅ 音频文件存在，开始播放: {os.path.basename(audio_path)}")
                        
                        # 检查是否有重叠标记
                        has_overlap = False
                        if hasattr(self, 'overlap_detector') and self.overlap_detector:
                            has_overlap = self.overlap_detector.is_subtitle_overlapping(row_index)
                            
                        if has_overlap:
                            print(f"⚠️ 字幕 {row_index + 1} 存在重叠风险，但仍允许播放")
                        
                        # 停止其他正在播放的音频
                        self.stop_all_audio_playback()
                        
                        # 强制刷新音频文件以避免缓存
                        self.force_refresh_audio_file(audio_path)
                        
                        # 设置当前播放状态
                        self.current_playing_row = row_index
                        
                        # 更新按钮状态为播放中
                        action_widget = status_info.get('action_widget')
                        if action_widget:
                            action_widget.set_playing_state(True)
                        
                        self.play_result_audio(audio_path)
                    elif status == 'waiting':
                        print(f"⏳ 字幕 {row_index + 1} 还未开始配音")
                        QMessageBox.information(self, "提示", f"第 {row_index + 1} 条字幕还未配音")
                    elif status == 'processing':
                        print(f"🔄 字幕 {row_index + 1} 正在配音中")
                        QMessageBox.information(self, "提示", f"第 {row_index + 1} 条字幕正在配音中，请等待完成")
                    elif status == 'failed':
                        error_msg = status_info.get('error_message', '未知错误')
                        print(f"❌ 字幕 {row_index + 1} 配音失败: {error_msg}")
                        QMessageBox.warning(self, "配音失败", f"第 {row_index + 1} 条字幕配音失败:\n{error_msg}")
                    else:
                        print(f"🔍 字幕 {row_index + 1} 状态异常: {status}")
                        QMessageBox.warning(self, "提示", f"第 {row_index + 1} 条字幕的音频文件不可用")
            else:
                print(f"📄 字幕 {row_index + 1} 暂无配音状态记录")
                QMessageBox.warning(self, "提示", f"第 {row_index + 1} 条字幕还未配音")
                
        except Exception as e:
            print(f"❌ 播放音频失败: {e}")
            import traceback
            traceback.print_exc()
    
    def on_stop_audio_clicked(self, row_index):
        """处理停止音频按钮点击"""
        try:
            print(f"⏹️ 点击停止按钮 - 行索引: {row_index}")
            
            # 停止媒体播放
            if self.media_player:
                self.media_player.stop()
                print("🛑 音频播放已停止")
            
            # 重置播放状态
            self.reset_playing_state(row_index)
            
        except Exception as e:
            print(f"❌ 停止音频失败: {e}")
            import traceback
            traceback.print_exc()
    
    def stop_all_audio_playback(self):
        """停止所有音频播放并重置状态"""
        try:
            # 停止媒体播放
            if self.media_player:
                self.media_player.stop()
            
            # 重置所有播放状态
            if hasattr(self, 'current_playing_row') and self.current_playing_row is not None:
                self.reset_playing_state(self.current_playing_row)
                
        except Exception as e:
            print(f"❌ 停止所有音频播放失败: {e}")
    
    def reset_playing_state(self, row_index=None):
        """重置播放状态"""
        try:
            if row_index is None and hasattr(self, 'current_playing_row'):
                row_index = self.current_playing_row
            
            if row_index is not None and hasattr(self, 'subtitle_status') and row_index in self.subtitle_status:
                status_info = self.subtitle_status[row_index]
                action_widget = status_info.get('action_widget')
                if action_widget:
                    action_widget.set_playing_state(False)
                    print(f"🔄 重置第 {row_index + 1} 条字幕的播放状态")
            
            # 清除当前播放行记录
            if hasattr(self, 'current_playing_row'):
                self.current_playing_row = None
                
        except Exception as e:
            print(f"❌ 重置播放状态失败: {e}")

    def on_retry_voiceover_clicked(self, row_index):
        """处理重新配音按钮点击"""
        try:
            if row_index < 0 or row_index >= len(self.subtitles):
                QMessageBox.warning(self, "错误", "字幕索引无效")
                return
            
            # 检查是否有TTS管理器
            if not hasattr(self, 'tts_manager') or not self.tts_manager:
                QMessageBox.warning(self, "错误", "TTS引擎未初始化")
                return
            
            # 检查是否存在重叠标记
            has_overlap = False
            if hasattr(self, 'overlap_detector') and self.overlap_detector:
                has_overlap = self.overlap_detector.is_subtitle_overlapping(row_index)
            
            # 确认重新配音
            message = f"确定要重新为第 {row_index + 1} 条字幕配音吗？\n注意：这将覆盖原有的音频文件。"
            if has_overlap:
                message += "\n\n⚠️ 此字幕当前被标记为存在重叠风险。\n重新配音后将重新检测重叠状态。"
            
            reply = QMessageBox.question(
                self, "确认", 
                message,
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            
            if reply != QMessageBox.Yes:
                return
            
            # 获取配音参数
            # 检查当前选择的TTS引擎是否可用
            current_engine = self.engine_combo.currentData()
            if current_engine == 'azure_tts':
                # 对Azure TTS进行额外检查
                try:
                    # 快速测试Azure TTS是否响应
                    import threading
                    from concurrent.futures import ThreadPoolExecutor, TimeoutError as FutureTimeoutError
                    
                    def quick_azure_test():
                        return hasattr(self.tts_manager, 'tts_engines') and 'azure_tts' in self.tts_manager.tts_engines
                    
                    with ThreadPoolExecutor(max_workers=1) as executor:
                        future = executor.submit(quick_azure_test)
                        is_available = future.result(timeout=2)  # 2秒快速检查
                        
                    if not is_available:
                        QMessageBox.warning(self, "Azure TTS不可用", 
                                          "Azure TTS引擎当前不可用\n\n可能原因：\n• 网络连接问题\n• API配置错误\n• 服务暂时不可用\n\n已自动切换到Edge TTS")
                        # 切换到Edge TTS
                        for i in range(self.engine_combo.count()):
                            if self.engine_combo.itemData(i) == 'edge_tts':
                                self.engine_combo.setCurrentIndex(i)
                                break
                        return
                        
                except (FutureTimeoutError, Exception):
                    QMessageBox.warning(self, "Azure TTS响应超时", 
                                      "Azure TTS服务响应超时\n\n建议：\n• 检查网络连接\n• 切换到Edge TTS引擎\n• 稍后再试")
                    return
            
            voice_id = self.voice_combo.currentData()
            speed = self.speed_slider.value() / 100.0
            output_dir = self.output_dir_edit.text()
            engine_name = self.engine_combo.currentData()
            
            if not voice_id:
                QMessageBox.warning(self, "错误", "请选择语音")
                return
            
            if not output_dir or not os.path.exists(output_dir):
                QMessageBox.warning(self, "错误", "请选择有效的输出目录")
                return
            
            # 保留旧的音频文件路径（如果存在）
            if hasattr(self, 'subtitle_status') and row_index in self.subtitle_status:
                old_audio_path = self.subtitle_status[row_index].get('audio_path')
                self.keep_audio_file_path(old_audio_path, "重新配音将覆盖此文件")
            
            # 重置状态为等待
            self.update_subtitle_status(row_index, 'waiting')
            
            # 获取最新的字幕内容（从表格中获取，确保包含编辑后的内容）
            subtitle = self.subtitles[row_index].copy()  # 复制字幕数据
            
            print(f"🔍 获取字幕 {row_index + 1} 的最新文本:")
            print(f"   数据结构中的文本: '{subtitle['text']}'")
            
            # 从表格中获取最新的文本内容
            content_item = self.subtitles_table.item(row_index, 3)  # 修正：字幕内容在第4列（索引3）
            if content_item:
                # 获取原始多行文本（如果存在）
                latest_text = content_item.data(Qt.UserRole + 1)
                display_text = content_item.text()
                
                print(f"   表格显示文本: '{display_text}'")
                print(f"   用户数据文本: '{latest_text}'")
                
                if not latest_text:
                    latest_text = display_text
                    print(f"   使用显示文本作为最新文本")
                
                subtitle['text'] = latest_text
                print(f"✅ 最终使用文本: '{latest_text}'")
            else:
                print(f"❌ 警告：无法从表格获取字幕 {row_index + 1} 的文本，使用数据结构中的文本")
            
            # 确保字幕有正确的index字段
            subtitle['index'] = row_index + 1  # 字幕索引从1开始
            
            # 预先计算音频文件路径（用于替换）
            audio_filename = f"subtitle_{subtitle['index']:04d}_{engine_name}.wav"
            expected_audio_path = os.path.join(output_dir, audio_filename)
            print(f"📁 预期音频文件路径: {expected_audio_path}")
            
            # 检查并处理现有音频文件
            if os.path.exists(expected_audio_path):
                try:
                    # 获取旧文件信息
                    old_size = os.path.getsize(expected_audio_path)
                    old_mtime = os.path.getmtime(expected_audio_path)
                    print(f"🗂️ 发现现有音频文件: {os.path.basename(expected_audio_path)}")
                    print(f"   大小: {old_size} 字节, 修改时间: {old_mtime}")
                    
                    # 创建备份文件名（临时）
                    backup_path = expected_audio_path + ".backup"
                    if os.path.exists(backup_path):
                        os.remove(backup_path)
                    
                    # 移动旧文件到备份
                    os.rename(expected_audio_path, backup_path)
                    print(f"📦 旧音频文件已备份到: {os.path.basename(backup_path)}")
                    
                except Exception as e:
                    print(f"⚠️ 处理现有音频文件失败: {e}")
                    # 即使备份失败，也继续重配音
            else:
                print(f"ℹ️ 未发现现有音频文件，将创建新文件")
            
            # 创建单个字幕的配音工作线程
            self.single_worker = VoiceoverWorker(
                self.tts_manager,
                [subtitle],  # 只配音一个字幕，但包含正确的index
                voice_id,
                speed,
                output_dir,
                engine_name
            )
            
            # 连接信号 - 关键修复：传递正确的索引映射
            self.single_worker.voiceover_completed.connect(
                lambda worker_idx, sub, success, result: self.on_voiceover_completed(row_index, sub, success, result)
            )
            self.single_worker.error_occurred.connect(self.on_error_occurred)
            
            # 设置为配音中状态
            self.update_subtitle_status(row_index, 'processing')
            
            # 启动工作线程
            self.single_worker.start()
            
            print(f"🔄 开始重新配音字幕 {row_index + 1}，文本: {subtitle['text'][:30]}...")
            print(f"🎯 将覆盖音频文件: {os.path.basename(expected_audio_path)}")
            
        except Exception as e:
            print(f"❌ 重新配音失败: {e}")
            QMessageBox.critical(self, "错误", f"重新配音失败: {e}")

    def retry_single_voiceover(self, index):
        """重试单个配音（保留兼容性）"""
        self.on_retry_voiceover_clicked(index)

    def export_audio(self):
        """合并音频文件"""
        # 检查配音结果，支持从两个地方获取数据
        valid_results = []
        
        # 首先尝试从voiceover_results获取
        if hasattr(self, 'voiceover_results') and self.voiceover_results:
            for result in self.voiceover_results:
                if result['success'] and os.path.exists(result['result']):
                    valid_results.append(result)
        
        # 如果voiceover_results为空，尝试从subtitle_status获取
        if not valid_results and hasattr(self, 'subtitle_status'):
            print("🔄 从subtitle_status获取配音结果...")
            for index, status_info in self.subtitle_status.items():
                if (status_info.get('status') == 'success' and 
                    status_info.get('audio_path') and 
                    os.path.exists(status_info['audio_path'])):
                    # 构造结果格式
                    result_entry = {
                        'index': index,
                        'success': True,
                        'result': status_info['audio_path'],
                        'subtitle': self.subtitles[index] if index < len(self.subtitles) else None
                    }
                    valid_results.append(result_entry)
        
        if not valid_results:
            QMessageBox.warning(self, "警告", "没有配音结果可合并")
            return
        
        # 检查必要的库是否可用
        if not AUDIO_PROCESSING_AVAILABLE:
            QMessageBox.critical(self, "错误", "音频处理库(pydub)不可用，无法合并音频")
            return
            
        if not SRT_AVAILABLE:
            QMessageBox.critical(self, "错误", "SRT库不可用，无法生成字幕映射文件")
            return
        
        # 选择输出文件
        output_file, _ = QFileDialog.getSaveFileName(
            self, "选择合并音频保存位置", 
            os.path.join(self.output_dir_edit.text(), "merged_audio.wav"),
            "音频文件 (*.wav)"
        )
        
        if not output_file:
            return
            
        try:
            # 创建进度对话框
            progress = QProgressBar()
            progress.setWindowTitle("合并音频中...")
            progress.setRange(0, 100)
            progress.show()
            
            # valid_results already processed at function start
            if not valid_results:
                QMessageBox.warning(self, "警告", "没有有效的音频文件可合并")
                progress.close()
                return
                
            # 获取字幕数据
            subtitles_data = []
            for result in valid_results:
                subtitle_index = result['index']
                if subtitle_index < len(self.subtitles):
                    subtitle = self.subtitles[subtitle_index]
                    subtitles_data.append({
                        'index': subtitle_index,
                        'start': subtitle['start'],
                        'end': subtitle['end'],
                        'text': subtitle['text'],
                        'audio_path': result['result']
                    })
            
            # 按时间排序
            subtitles_data.sort(key=lambda x: x['start'])
            progress.setValue(20)
            
            # 创建临时目录用于处理
            temp_dir = os.path.join(os.path.dirname(output_file), "temp_merge")
            os.makedirs(temp_dir, exist_ok=True)
            
            try:
                # 调用音频合并功能
                success = self._voice_connect(subtitles_data, output_file, temp_dir, progress)
                
                if success:
                    progress.setValue(100)
                    QMessageBox.information(self, "完成", f"音频合并成功！\n输出文件: {output_file}")
                else:
                    QMessageBox.critical(self, "错误", "音频合并失败")
                    
            finally:
                # 清理临时目录
                try:
                    import shutil
                    if os.path.exists(temp_dir):
                        shutil.rmtree(temp_dir)
                except Exception as e:
                    print(f"⚠️ 清理临时目录失败: {e}")
                
                progress.close()
                
        except Exception as e:
            QMessageBox.critical(self, "错误", f"合并失败: {e}")
            import traceback
            traceback.print_exc()
    
    def _voice_connect(self, subtitles_data, output_path, temp_dir, progress=None):
        """
        基于refer.py的音频合并算法
        
        Args:
            subtitles_data: 字幕数据列表
            output_path: 输出音频文件路径
            temp_dir: 临时目录
            progress: 进度条对象
            
        Returns:
            bool: 是否成功
        """
        try:
            # 参考refer.py中的常量
            MAX_SPEED_UP = 1.2  # 最大音频加速
            MIN_SPEED_UP = 1.05  # 最小音频加速
            MIN_GAP_DURATION = 0.1  # 最小间隔时间，单位秒
            
            # 创建简单日志记录器
            diagnosisLog = SimpleLogger()
            
            # 创建voiceMap.srt文件
            srt_map_path = os.path.join(temp_dir, "voiceMap.srt")
            self._create_voice_map_srt(subtitles_data, srt_map_path)
            
            if progress:
                progress.setValue(40)
            
            # 读取并解析SRT文件
            with open(srt_map_path, "r", encoding="utf-8") as f:
                voice_map_srt_content = f.read()
            
            voice_map_srt = list(srt.parse(voice_map_srt_content))
            
            # 确定音频总长度
            duration = voice_map_srt[-1].end.total_seconds() * 1000
            final_audio_path = None
            
            # 查找最后一个音频文件
            for subtitle_data in subtitles_data:
                if subtitle_data['index'] == len(voice_map_srt) - 1:
                    final_audio_path = subtitle_data['audio_path']
                    break
            
            if final_audio_path and os.path.exists(final_audio_path):
                final_audio_end = voice_map_srt[-1].start.total_seconds() * 1000
                final_audio = AudioSegment.from_wav(final_audio_path)
                final_audio_end += final_audio.duration_seconds * 1000
                duration = max(duration, final_audio_end)
            
            diagnosisLog.write("\n<Voice connect section>", False)
            
            if progress:
                progress.setValue(50)
            
            # 初始化一个空的音频段
            combined = AudioSegment.silent(duration=duration)
            
            # 处理每个音频片段
            for i in range(len(voice_map_srt)):
                # 查找对应的音频文件
                audio_file_path = None
                for subtitle_data in subtitles_data:
                    if subtitle_data['index'] == i:
                        audio_file_path = subtitle_data['audio_path']
                        break
                
                if not audio_file_path or not os.path.exists(audio_file_path):
                    print(f"⚠️ 字幕 {i + 1} 的音频文件不存在，跳过")
                    continue
                
                audio = AudioSegment.from_wav(audio_file_path)
                audio = audio.strip_silence(silence_thresh=-40, silence_len=100)  # 去除头尾的静音
                audio_position = voice_map_srt[i].start.total_seconds() * 1000
                
                if i != len(voice_map_srt) - 1:
                    # 检查这一句的结尾到下一句的开头之间是否有静音，如果没有则需要缩小音频
                    audio_end_position = audio_position + audio.duration_seconds * 1000 + MIN_GAP_DURATION * 1000
                    audio_next_position = voice_map_srt[i + 1].start.total_seconds() * 1000
                    
                    if audio_next_position < audio_end_position:
                        speedup = (audio.duration_seconds * 1000 + MIN_GAP_DURATION * 1000) / (audio_next_position - audio_position)
                        seconds = audio_position / 1000.0
                        time_str = str(datetime.timedelta(seconds=seconds))
                        
                        if speedup > MAX_SPEED_UP:
                            # 超过1.2，标红提示人工调整
                            log_str = f"Warning: The audio {i + 1}, at {time_str}, speed up {speedup:.3f} exceeds maximum {MAX_SPEED_UP}. Manual adjustment needed."
                            diagnosisLog.write(log_str)
                            print(f"🔴 {log_str}")
                        
                        # 音频如果提速略大于1，speedup函数可能会出现错误，确定最小speedup为1.01
                        if speedup < MIN_SPEED_UP:
                            log_str = f"Warning: The audio {i + 1}, at {time_str}, speed up {speedup:.3f} is too near to 1.0. Set to {MIN_SPEED_UP} forcibly."
                            diagnosisLog.write(log_str)
                            speedup = MIN_SPEED_UP
                        
                        audio = audio.speedup(playback_speed=speedup)
                
                combined = combined.overlay(audio, position=audio_position)
                
                # 更新进度
                if progress:
                    progress_value = 50 + int((i + 1) / len(voice_map_srt) * 40)
                    progress.setValue(progress_value)
            
            # 导出合并后的音频
            combined.export(output_path, format="wav")
            
            if progress:
                progress.setValue(95)
            
            print("✅ 音频合并完成")
            return True
            
        except Exception as e:
            print(f"❌ 音频合并失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def _create_voice_map_srt(self, subtitles_data, srt_path):
        """
        创建voiceMap.srt文件
        
        Args:
            subtitles_data: 字幕数据列表
            srt_path: SRT文件保存路径
        """
        try:
            srt_subtitles = []
            
            for i, subtitle_data in enumerate(subtitles_data):
                # 创建SRT字幕项
                start_time = datetime.timedelta(seconds=subtitle_data['start'])
                end_time = datetime.timedelta(seconds=subtitle_data['end'])
                
                # 内容是音频文件的基础名称
                audio_filename = os.path.basename(subtitle_data['audio_path'])
                
                subtitle = srt.Subtitle(
                    index=i + 1,
                    start=start_time,
                    end=end_time,
                    content=audio_filename
                )
                srt_subtitles.append(subtitle)
            
            # 写入SRT文件
            with open(srt_path, 'w', encoding='utf-8') as f:
                f.write(srt.compose(srt_subtitles))
                
            print(f"✅ 创建voiceMap.srt文件: {srt_path}")
            
        except Exception as e:
            print(f"❌ 创建voiceMap.srt文件失败: {e}")
            raise

    def closeEvent(self, event):
        """处理对话框关闭事件（增强版）"""
        try:
            print("📱 开始关闭字幕配音对话框...")
            
            # 设置关闭状态标记，防止其他操作干扰
            self.is_closing = True
            
            # 停止所有活跃的工作线程
            self._stop_all_workers_safely()
            
            # 清理媒体播放器
            self._cleanup_media_player_safely()
            
            # 清理试听缓存
            self._cleanup_preview_cache_safely()
            
            # 清理TTS资源
            self._cleanup_tts_resources_safely()
            
            # 清理界面组件
            self._cleanup_ui_components_safely()
            
            # 清理试听音频文件
            self._cleanup_preview_file()
            
            # 强制清理所有待清理的文件
            self._force_cleanup_pending_files()
            
            # 清理孤立的音频文件
            if hasattr(self, 'output_dir_edit') and self.output_dir_edit:
                output_dir = self.output_dir_edit.text()
                if output_dir:
                    self.cleanup_orphaned_audio_files(output_dir)
            
            print("✅ 字幕配音对话框安全关闭完成")
            
            # 接受关闭事件
            event.accept()
            
        except Exception as e:
            print(f"❌ 关闭对话框时发生错误: {e}")
            import traceback
            traceback.print_exc()
            # 即使出错也要关闭
            event.accept()
    
    def _stop_all_workers_safely(self):
        """安全停止所有工作线程"""
        try:
            # 停止配音工作线程
            if hasattr(self, 'worker') and self.worker:
                try:
                    # 安全检查线程是否正在运行
                    try:
                        if self.worker.isRunning():
                            self.worker.stop()
                            self.worker.wait(3000)  # 等待最多3秒
                            if self.worker.isRunning():
                                self.worker.terminate()  # 强制终止
                    except RuntimeError:
                        # C++对象已被删除，跳过停止操作
                        print("配音工作线程的C++对象已被删除，跳过停止操作")
                    
                    self.worker.deleteLater()
                    self.worker = None
                    print("✅ 配音工作线程已停止")
                except Exception as e:
                    print(f"⚠️ 停止配音工作线程失败: {e}")
                    self.worker = None
            
            # 停止单个配音工作线程
            if hasattr(self, 'single_worker') and self.single_worker:
                try:
                    # 安全检查线程是否正在运行
                    try:
                        if self.single_worker.isRunning():
                            self.single_worker.stop()
                            self.single_worker.wait(2000)  # 等待最多2秒
                            if self.single_worker.isRunning():
                                self.single_worker.terminate()  # 强制终止
                    except RuntimeError:
                        # C++对象已被删除，跳过停止操作
                        print("单个配音工作线程的C++对象已被删除，跳过停止操作")
                    
                    self.single_worker.deleteLater()
                    self.single_worker = None
                    print("✅ 单个配音工作线程已停止")
                except Exception as e:
                    print(f"⚠️ 停止单个配音工作线程失败: {e}")
                    self.single_worker = None
            
            # 停止试听工作线程
            if hasattr(self, 'preview_worker') and self.preview_worker:
                try:
                    # 安全检查线程是否正在运行
                    if self.preview_worker.isRunning():
                        # 使用新的停止方法
                        self.preview_worker.stop()
                        self.preview_worker.terminate()
                        
                        # 等待线程结束，但设置超时避免无限等待
                        if not self.preview_worker.wait(3000):  # 等待最多3秒
                            print("⚠️ 试听线程停止超时，强制终止")
                            self.preview_worker.terminate()
                except RuntimeError:
                    # C++对象已被删除，设置为None以避免后续访问
                    print("预览工作线程的C++对象已被删除，清理引用")
                    self.preview_worker = None
                except Exception as e:
                    print(f"⚠️ 停止试听线程时出错: {e}")
                    self.preview_worker = None
            # 停止音频播放
            try:
                if hasattr(self, 'media_player') and self.media_player:
                    if self.media_player.playbackState() == QMediaPlayer.PlayingState:
                        self.media_player.stop()
            except Exception as e:
                print(f"⚠️ 停止音频播放时出错: {e}")
                

            if hasattr(self, 'status_label') and self.status_label and not self.status_label.isHidden():
                try:
                    self.status_label.setText("试听已停止")
                except RuntimeError:
                    pass
                    
            # 恢复按钮状态
            if hasattr(self, 'voice_test_btn') and self.voice_test_btn:
                self.is_testing = False
                self.voice_test_btn.setChecked(False)
                self.voice_test_btn.setText("试听")
                
            print("✅ 试听已安全停止")
            
        except Exception as e:
            print(f"❌ 停止试听时发生错误: {e}")
            # 即使出错也要恢复按钮状态
            try:
                self.is_testing = False
                if hasattr(self, 'voice_test_btn') and self.voice_test_btn:
                    self.voice_test_btn.setChecked(False)
                    self.voice_test_btn.setText("试听")
            except Exception:
                pass

    def _cleanup_media_player_safely(self):
        """安全清理媒体播放器"""
        try:
            if hasattr(self, 'media_player') and self.media_player:
                try:
                    self.media_player.stop()
                    self.media_player.setAudioOutput(None)
                    self.media_player.deleteLater()
                    self.media_player = None
                    print("✅ 媒体播放器已清理")
                except Exception as e:
                    print(f"⚠️ 清理媒体播放器失败: {e}")
            
            if hasattr(self, 'audio_output') and self.audio_output:
                try:
                    self.audio_output.deleteLater()
                    self.audio_output = None
                    print("✅ 音频输出已清理")
                except Exception as e:
                    print(f"⚠️ 清理音频输出失败: {e}")
                    
        except Exception as e:
            print(f"⚠️ 清理媒体组件过程出错: {e}")
    
    def _cleanup_preview_cache_safely(self):
        """安全清理试听缓存"""
        try:
            if hasattr(self, 'preview_cache') and self.preview_cache:
                cache_count = len(self.preview_cache)
                try:
                    self.cleanup_preview_cache()
                    print(f"✅ 试听缓存已清理（{cache_count}个文件）")
                except Exception as e:
                    print(f"⚠️ 清理试听缓存失败: {e}")
                    # 强制清空缓存字典
                    self.preview_cache.clear()
                    
        except Exception as e:
            print(f"⚠️ 清理缓存过程出错: {e}")
    
    def _cleanup_tts_resources_safely(self):
        """安全清理TTS资源"""
        try:
            if hasattr(self, 'tts_manager') and self.tts_manager:
                try:
                    # 如果TTS管理器有清理方法，调用它
                    if hasattr(self.tts_manager, 'cleanup'):
                        self.tts_manager.cleanup()
                    self.tts_manager = None
                    print("✅ TTS管理器已清理")
                except Exception as e:
                    print(f"⚠️ 清理TTS管理器失败: {e}")
                    
        except Exception as e:
            print(f"⚠️ 清理TTS资源过程出错: {e}")
    
    def _cleanup_ui_components_safely(self):
        """安全清理UI组件"""
        try:
            # 清理可能的大型数据
            if hasattr(self, 'subtitles'):
                self.subtitles.clear()
            
            if hasattr(self, 'voiceover_results'):
                self.voiceover_results.clear()
            
            if hasattr(self, 'cached_voices'):
                self.cached_voices = None
            
            if hasattr(self, 'available_engines'):
                self.available_engines = None
            
            print("✅ UI组件数据已清理")
            
        except Exception as e:
            print(f"⚠️ 清理UI组件过程出错: {e}")
    
    def cleanup_orphaned_audio_files(self, output_dir):
        """清理孤立的音频文件"""
        try:
            if not output_dir or not os.path.exists(output_dir):
                return
            
            print(f"🧹 开始清理孤立音频文件，目录: {output_dir}")
            
            # 获取所有当前有效的音频文件路径
            valid_audio_paths = set()
            if hasattr(self, 'subtitle_status'):
                for status_info in self.subtitle_status.values():
                    audio_path = status_info.get('audio_path')
                    if audio_path and status_info.get('status') == 'success':
                        valid_audio_paths.add(os.path.abspath(audio_path))
            
            # 扫描输出目录中的所有音频文件
            audio_extensions = ['.wav', '.mp3', '.m4a']
            orphaned_count = 0
            
            for filename in os.listdir(output_dir):
                if any(filename.lower().endswith(ext) for ext in audio_extensions):
                    file_path = os.path.abspath(os.path.join(output_dir, filename))
                    
                    # 如果文件不在有效路径集合中，删除它
                    if file_path not in valid_audio_paths:
                        try:
                            os.remove(file_path)
                            orphaned_count += 1
                            print(f"🗑️ 删除孤立文件: {filename}")
                        except Exception as e:
                            print(f"⚠️ 删除文件失败 {filename}: {e}")
            
            if orphaned_count > 0:
                print(f"✅ 清理完成，删除了 {orphaned_count} 个孤立音频文件")
            else:
                print("✅ 没有发现孤立的音频文件")
                
        except Exception as e:
            print(f"❌ 清理孤立音频文件失败: {e}")

    def browse_subtitle_file(self):
        """浏览字幕文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "选择字幕文件",
            "",
            "字幕文件 (*.srt *.vtt *.ass);;所有文件 (*)"
        )
        
        if file_path:
            self.file_path_edit.setText(file_path)
            self.load_subtitle_file(file_path)

    def browse_output_dir(self):
        """浏览输出目录"""
        directory = QFileDialog.getExistingDirectory(self, "选择输出目录")
        if directory:
            self.output_dir_edit.setText(directory)

    def load_subtitle_file(self, file_path: str):
        """加载字幕文件"""
        try:
            if file_path.lower().endswith('.srt'):
                self.subtitles = self.parse_srt_file(file_path)
            elif file_path.lower().endswith('.vtt'):
                self.subtitles = self.parse_vtt_file(file_path)
            elif file_path.lower().endswith('.ass'):
                self.subtitles = self.parse_ass_file(file_path)
            else:
                QMessageBox.warning(self, "错误", "不支持的字幕格式")
                return
            
            # 填充字幕表格
            self.populate_subtitles_table()
            
            # 更新字幕条数
            self.subtitle_count_label.setText(f"字幕条数: {len(self.subtitles)} | 双击内容编辑")
            
            # 启用开始配音按钮
            if self.subtitles and self.voice_combo.currentData():
                self.start_btn.setEnabled(True)
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"加载字幕文件失败: {e}")
            self.subtitles = []

    def populate_subtitles_table(self):
        """填充字幕表格 - 统一显示字幕内容和配音状态"""
        try:
            # 检查是否存在字幕表格
            if not hasattr(self, 'subtitles_table'):
                return
            
            # 清空现有内容
            self.subtitles_table.setRowCount(0)
            
            # 如果没有字幕数据，返回
            if not hasattr(self, 'subtitles') or not self.subtitles:
                return
            
            # 设置行数
            self.subtitles_table.setRowCount(len(self.subtitles))
            
            # 初始化状态跟踪
            if not hasattr(self, 'subtitle_status'):
                self.subtitle_status = {}
            
            # 填充数据
            for row, subtitle in enumerate(self.subtitles):
                # 调试输出
                print(f"📋 调试字幕 {row + 1}: {subtitle}")
                
                # 序号列
                index_item = QTableWidgetItem(str(row + 1))
                index_item.setTextAlignment(Qt.AlignCenter)
                index_item.setFlags(Qt.ItemIsSelectable | Qt.ItemIsEnabled)
                index_item.setFont(QFont("Microsoft YaHei", 10, QFont.Bold))
                self.subtitles_table.setItem(row, 0, index_item)
                
                # 开始时间
                start_time_item = QTableWidgetItem(self.seconds_to_time(subtitle['start']))
                start_time_item.setTextAlignment(Qt.AlignCenter)
                start_time_item.setFlags(Qt.ItemIsSelectable | Qt.ItemIsEnabled)
                self.subtitles_table.setItem(row, 1, start_time_item)
                
                # 结束时间  
                end_time_item = QTableWidgetItem(self.seconds_to_time(subtitle['end']))
                end_time_item.setTextAlignment(Qt.AlignCenter)
                end_time_item.setFlags(Qt.ItemIsSelectable | Qt.ItemIsEnabled)
                self.subtitles_table.setItem(row, 2, end_time_item)
                
                # 字幕内容 - 显示完整内容以便编辑
                text = subtitle['text'].replace('\n', ' ').strip()
                # 移除可能的HTML标签
                import re
                text = re.sub(r'<[^>]+>', '', text)
                
                text_item = QTableWidgetItem(text)
                text_item.setFlags(Qt.ItemIsSelectable | Qt.ItemIsEnabled | Qt.ItemIsEditable)  # 允许编辑
                text_item.setToolTip("双击编辑字幕内容")
                # 存储原始完整文本
                text_item.setData(Qt.UserRole + 1, subtitle['text'])
                self.subtitles_table.setItem(row, 3, text_item)
                
                # 初始状态 - 等待配音
                status_item = QTableWidgetItem("⏳ 等待")
                status_item.setTextAlignment(Qt.AlignCenter)
                status_item.setFlags(Qt.ItemIsSelectable | Qt.ItemIsEnabled)
                status_item.setBackground(QColor(100, 100, 100, 50))  # 灰色背景
                self.subtitles_table.setItem(row, 4, status_item)
                
                # 操作按钮组件
                action_widget = SubtitleActionWidget(row)
                action_widget.play_audio.connect(self.on_play_audio_clicked)
                action_widget.stop_audio.connect(self.on_stop_audio_clicked)
                action_widget.retry_voiceover.connect(self.on_retry_voiceover_clicked)
                self.subtitles_table.setCellWidget(row, 5, action_widget)
                
                # 初始化状态记录
                self.subtitle_status[row] = {
                    'status': 'waiting',
                    'audio_path': None,
                    'error_message': None,
                    'action_widget': action_widget  # 保存引用以便后续更新
                }
                
                # 设置行颜色交替（可选）
                if row % 2 == 1:
                    for col in range(5):  # 处理前5列，操作列由自定义组件处理
                        item = self.subtitles_table.item(row, col)
                        if item and col != 4:  # 状态列保持自己的颜色
                            item.setBackground(QColor(25, 30, 36, 50))  # 稍微深一点的背景
            
            print(f"✅ 字幕表格已填充完成，共 {len(self.subtitles)} 条字幕")
            
            # 更新底部信息显示
            self.update_bottom_info()
            
        except Exception as e:
            print(f"❌ 填充字幕表格失败: {e}")
            import traceback
            traceback.print_exc()

    def parse_srt_file(self, file_path: str) -> List[Dict]:
        """解析SRT字幕文件"""
        subtitles = []
        
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 按空行分割字幕块
        blocks = re.split(r'\n\s*\n', content.strip())
        
        for i, block in enumerate(blocks):
            lines = block.strip().split('\n')
            if len(lines) >= 3:
                # 解析时间码
                time_line = lines[1]
                time_match = re.match(r'(\d{2}:\d{2}:\d{2}[,\.]\d{3})\s*-->\s*(\d{2}:\d{2}:\d{2}[,\.]\d{3})', time_line)
                
                if time_match:
                    start_time = self.time_to_seconds(time_match.group(1))
                    end_time = self.time_to_seconds(time_match.group(2))
                    text = '\n'.join(lines[2:])
                    
                    subtitles.append({
                        'index': i + 1,
                        'start': start_time,
                        'end': end_time,
                        'text': text.strip()
                    })
        
        return subtitles

    def parse_vtt_file(self, file_path: str) -> List[Dict]:
        """解析VTT字幕文件"""
        # 简化的VTT解析，可以使用webvtt库来完善
        subtitles = []
        
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        i = 0
        subtitle_index = 1
        
        while i < len(lines):
            line = lines[i].strip()
            
            # 跳过WEBVTT头和空行
            if line.startswith('WEBVTT') or not line:
                i += 1
                continue
            
            # 检查是否是时间行
            time_match = re.match(r'(\d{2}:\d{2}:\d{2}[,\.]\d{3})\s*-->\s*(\d{2}:\d{2}:\d{2}[,\.]\d{3})', line)
            if time_match:
                start_time = self.time_to_seconds(time_match.group(1))
                end_time = self.time_to_seconds(time_match.group(2))
                
                # 收集字幕文本
                i += 1
                text_lines = []
                while i < len(lines) and lines[i].strip():
                    text_lines.append(lines[i].strip())
                    i += 1
                
                if text_lines:
                    subtitles.append({
                        'index': subtitle_index,
                        'start': start_time,
                        'end': end_time,
                        'text': '\n'.join(text_lines)
                    })
                    subtitle_index += 1
            else:
                i += 1
        
        return subtitles

    def parse_ass_file(self, file_path: str) -> List[Dict]:
        """解析ASS字幕文件（简化版）"""
        subtitles = []
        
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        events_section = False
        subtitle_index = 1
        
        for line in lines:
            line = line.strip()
            
            if line == '[Events]':
                events_section = True
                continue
            
            if events_section and line.startswith('Dialogue:'):
                parts = line.split(',', 9)
                if len(parts) >= 10:
                    start_time = self.ass_time_to_seconds(parts[1])
                    end_time = self.ass_time_to_seconds(parts[2])
                    text = parts[9]
                    
                    # 清理ASS格式标记
                    text = re.sub(r'\{[^}]*\}', '', text)
                    text = text.replace('\\N', '\n')
                    
                    subtitles.append({
                        'index': subtitle_index,
                        'start': start_time,
                        'end': end_time,
                        'text': text.strip()
                    })
                    subtitle_index += 1
        
        return subtitles

    def time_to_seconds(self, time_str: str) -> float:
        """将时间字符串转换为秒数"""
        time_str = time_str.replace(',', '.')
        parts = time_str.split(':')
        if len(parts) == 3:
            hours = int(parts[0])
            minutes = int(parts[1])
            seconds = float(parts[2])
            return hours * 3600 + minutes * 60 + seconds
        return 0.0

    def ass_time_to_seconds(self, time_str: str) -> float:
        """将ASS时间格式转换为秒数"""
        # ASS格式：0:00:00.00
        parts = time_str.split(':')
        if len(parts) == 3:
            hours = int(parts[0])
            minutes = int(parts[1])
            seconds = float(parts[2])
            return hours * 3600 + minutes * 60 + seconds
        return 0.0

    def seconds_to_time(self, seconds: float) -> str:
        """将秒数转换为时间格式"""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        seconds = seconds % 60
        return f"{hours:02d}:{minutes:02d}:{seconds:06.3f}"

    def toggle_voice_test(self):
        """切换语音预览状态"""
        if not self._preview_in_progress:
            self.start_voice_preview()
        else:
            self.stop_voice_preview()
    
    def start_voice_preview(self):
        # 检查voice_test_btn是否存在
        if not hasattr(self, 'voice_test_btn') or not self.voice_test_btn:
            print("警告：voice_test_btn 不存在，可能未正确初始化")
            return
        
        # 设置预览状态为进行中
        self._preview_in_progress = True
        self.voice_test_btn.setEnabled(False)
        self.voice_test_btn.setText("生成中...")
        
        # 获取当前设置
        engine = self.engine_combo.currentText()
        voice = self.voice_combo.currentData()  # 修复：使用语音ID而不是显示名称
        speed = self.speed_slider.value() / 100.0  # 修复：使用正确的语速滑块并转换为倍数
        
        # 使用默认试听文本
        text = "欢迎使用FlipTalk Ai，支持多种语音设置。"
            
        # 创建异步任务
        async def preview_task():
            try:
                # 使用TTS管理器的preview_voice方法
                audio_path = await self._generate_preview_audio(text, voice, speed)
                    
                # 在主线程中播放音频
                QMetaObject.invokeMethod(
                    self, 
                    "_play_preview_audio_slot",
                    Qt.QueuedConnection,
                    Q_ARG(str, audio_path)
                )
                
            except Exception as e:
                # 在主线程中显示错误
                QMetaObject.invokeMethod(
                    self,
                    "on_preview_failed",
                    Qt.QueuedConnection,
                    Q_ARG(str, str(e))
                )
        
        # 在新线程中运行异步任务
        def run_async_task():
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            loop.run_until_complete(preview_task())
            loop.close()
            
        preview_thread = Thread(target=run_async_task)
        preview_thread.start()

    @Slot(str)
    def _play_preview_audio_slot(self, audio_path: str):
        """播放预览音频的槽方法"""
        self._play_preview_audio(audio_path)
    
    def _play_preview_audio(self, audio_path: str):
        """播放预览音频"""
        try:
            # 清理之前的音频
            self._cleanup_preview_file()
            
            # 保存新的音频路径
            self._preview_audio_path = audio_path
            
            # 初始化播放器
            if not self._preview_media_player:
                self._preview_media_player = QMediaPlayer()
                self._preview_audio_output = QAudioOutput()
                self._preview_media_player.setAudioOutput(self._preview_audio_output)
                
                # 连接信号
                self._preview_media_player.mediaStatusChanged.connect(self._on_preview_status_changed)
                self._preview_media_player.errorOccurred.connect(self._on_preview_error)
            
            # 设置音频文件
            self._preview_media_player.setSource(QUrl.fromLocalFile(audio_path))
            
            # 开始播放
            self._preview_media_player.play()
            
            # 更新按钮状态
            self.voice_test_btn.setText("停止")
            self.voice_test_btn.setEnabled(True)
            self.voice_test_btn.setChecked(True)  # 设置为选中状态，显示红色背景
            
        except Exception as e:
            self.on_preview_failed(f"播放预览失败: {e}")

    def _on_preview_status_changed(self, status):
        """处理预览状态变化"""
        if status == QMediaPlayer.MediaStatus.EndOfMedia:
            self._reset_preview_state()
            
    def _on_preview_error(self, error, error_string):
        """处理预览错误"""
        self.on_preview_failed(f"预览播放错误: {error_string}")

    def stop_voice_preview(self):
        """停止语音预览"""
        print("🛑 停止语音预览")
        
        # 立即停止媒体播放器
        if hasattr(self, '_preview_media_player') and self._preview_media_player:
            try:
                self._preview_media_player.stop()
                self._preview_media_player.setSource(QUrl())  # 清空媒体源
                print("✅ 媒体播放器已停止")
            except Exception as e:
                print(f"⚠️ 停止媒体播放器时出错: {e}")
        
        # 重置状态
        self._reset_preview_state()
        
        # 清理预览文件（延迟清理以避免文件占用冲突）
        QTimer.singleShot(500, self._cleanup_preview_file)

    def _reset_preview_state(self):
        """重置预览状态"""
        self._preview_in_progress = False
        self.voice_test_btn.setText("试听")
        self.voice_test_btn.setEnabled(True)
        self.voice_test_btn.setChecked(False)  # 重置按钮选中状态，恢复默认背景色

    def _cleanup_preview_file(self):
        """清理预览音频文件"""
        if self._preview_audio_path:
            try:
                if os.path.exists(self._preview_audio_path):
                    os.unlink(self._preview_audio_path)
            except Exception as e:
                logging.warning(f"清理预览文件失败: {e}")
            finally:
                self._preview_audio_path = None

    def on_preview_failed(self, error_message: str):
        """处理预览失败"""
        self._reset_preview_state()
        self.show_error_message(f"预览失败: {error_message}")

    def show_error_message(self, message: str):
        """显示错误消息"""
        from PySide6.QtWidgets import QMessageBox
        QMessageBox.warning(self, "错误", message)
    
    async def _generate_preview_audio(self, text: str, voice: str, speed: float) -> str:
        """生成预览音频"""
        try:
            # 使用TTS管理器的preview_voice方法
            import asyncio
            loop = asyncio.get_event_loop()
            
            # 在线程池中运行同步的preview_voice方法
            audio_path = await loop.run_in_executor(
                None, 
                self.tts_manager.preview_voice,
                text, voice, speed
            )
            
            if not audio_path:
                raise Exception("预览音频生成失败")
                
            return audio_path
            
        except Exception as e:
            raise Exception(f"生成预览音频时出错: {e}")

    def closeEvent(self, event):
        """关闭窗口时清理资源"""
        self.stop_voice_preview()
        super().closeEvent(event)

    def on_media_status_changed(self, status):
        """处理媒体状态变化"""
        try:
            from PySide6.QtMultimedia import QMediaPlayer
            if status == QMediaPlayer.MediaStatus.EndOfMedia:
                print("🔄 音频播放完成，重置播放状态")
                # 自动重置播放状态
                if hasattr(self, 'current_playing_row') and self.current_playing_row is not None:
                    self.reset_playing_state(self.current_playing_row)
        except Exception as e:
            print(f"❌ 处理媒体状态变化失败: {e}")

    def on_playback_state_changed(self, state):
        """处理播放状态变化"""
        try:
            from PySide6.QtMultimedia import QMediaPlayer
            if state == QMediaPlayer.PlaybackState.StoppedState:
                print("⏹️ 播放已停止")
            elif state == QMediaPlayer.PlaybackState.PlayingState:
                print("▶️ 正在播放")
            elif state == QMediaPlayer.PlaybackState.PausedState:
                print("⏸️ 播放已暂停")
        except Exception as e:
            print(f"❌ 处理播放状态变化失败: {e}")

    def on_media_error_occurred(self, error, error_string):
        """处理媒体播放错误（增强错误恢复）"""
        try:
            print(f"❌ 媒体播放错误: {error} - {error_string}")
            
            # 尝试音频设备恢复
            if "audio device" in error_string.lower() or "activate" in error_string.lower():
                print("🔄 检测到音频设备问题，尝试恢复...")
                try:
                    # 重新初始化音频设备
                    if hasattr(self, 'audio_output') and self.audio_output:
                        from PySide6.QtMultimedia import QMediaDevices
                        default_device = QMediaDevices.defaultAudioOutput()
                        if not default_device.isNull():
                            self.audio_output.setDevice(default_device)
                            self.audio_output.setVolume(1.0)
                            print(f"✅ 音频设备已重新设置: {default_device.description()}")
                        else:
                            print("⚠️ 无法找到默认音频设备")
                except Exception as recovery_error:
                    print(f"❌ 音频设备恢复失败: {recovery_error}")
            
            # 重置播放状态
            if hasattr(self, 'current_playing_row') and self.current_playing_row is not None:
                self.reset_playing_state(self.current_playing_row)
            
            # 不显示错误弹窗，只记录错误信息，避免干扰用户体验
            print(f"📝 音频播放错误已记录，继续正常运行")
            
        except Exception as e:
            print(f"❌ 处理媒体播放错误失败: {e}")

    def keep_audio_file_path(self, audio_path, reason=""):
        """保留音频文件路径（不删除文件）"""
        if not audio_path:
            return False
        
        try:
            if os.path.exists(audio_path):
                reason_text = f"（{reason}）" if reason else ""
                print(f"🔄 保留音频文件路径: {os.path.basename(audio_path)}{reason_text}")
                return True
            else:
                print(f"⚠️ 音频文件不存在: {os.path.basename(audio_path)}")
                return False
        except Exception as e:
            print(f"❌ 保留音频文件路径失败: {os.path.basename(audio_path)}, 错误: {e}")
            return False
    
    def _sync_latest_subtitle_texts(self):
        """同步表格中最新的字幕文本到数据结构（确保配音使用最新编辑的文本）"""
        try:
            sync_count = 0
            for row_index in range(len(self.subtitles)):
                # 从表格中获取最新文本
                content_item = self.subtitles_table.item(row_index, 3)  # 字幕内容列
                if content_item:
                    # 获取原始多行文本（如果存在）
                    latest_text = content_item.data(Qt.UserRole + 1)
                    if not latest_text:
                        latest_text = content_item.text()
                    
                    # 检查是否有变化
                    original_text = self.subtitles[row_index]['text']
                    if latest_text != original_text:
                        self.subtitles[row_index]['text'] = latest_text
                        sync_count += 1
                        print(f"📝 同步字幕 {row_index + 1} 文本变更:")
                        print(f"   原文本: {original_text[:30]}...")
                        print(f"   新文本: {latest_text[:30]}...")
            
            if sync_count > 0:
                print(f"✅ 已同步 {sync_count} 条字幕的最新文本")
            else:
                print("ℹ️ 字幕文本无变化，无需同步")
                
        except Exception as e:
            print(f"❌ 同步字幕文本失败: {e}")
            import traceback
            traceback.print_exc()
