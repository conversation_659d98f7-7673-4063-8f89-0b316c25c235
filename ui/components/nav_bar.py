#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FlipTalk AI - 导航栏组件
"""

from PySide6.QtWidgets import QWidget, QVBoxLayout, QPushButton
from PySide6.QtCore import Qt

class NavigationBar(QWidget):
    """导航栏类"""
    def __init__(self):
        super().__init__()
        self.setup_ui()
        
    def setup_ui(self):
        """初始化UI"""
        # 设置固定宽度
        self.setFixedWidth(60)
        
        # 设置背景色
        self.setStyleSheet("""
            QWidget {
                background-color: #2D2D2D;
            }
            QPushButton {
                background-color: transparent;
                border: none;
                color: #FFFFFF;
                padding: 10px;
            }
            QPushButton:hover {
                background-color: #3D3D3D;
            }
        """)
        
        # 创建布局
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)
        layout.setAlignment(Qt.AlignTop)
        
        # 添加导航按钮
        self.add_nav_button("首页", "home")
        self.add_nav_button("设置", "settings")
        self.add_nav_button("帮助", "help")
        
    def add_nav_button(self, text, icon_name):
        """添加导航按钮"""
        button = QPushButton(text)
        button.setFixedHeight(60)
        # TODO: 添加图标
        self.layout().addWidget(button) 