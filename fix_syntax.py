#!/usr/bin/env python
# -*- coding: utf-8 -*-

# 修复ui/fliptalk_ui.py中的语法错误
import re

# 读取文件内容
with open('ui/fliptalk_ui.py', 'r', encoding='utf-8') as f:
    content = f.read()

# 修复SubtitleEditArea类的setup_gpu_detector方法
pattern1 = r'def setup_gpu_detector\(self\):\n\s+"""设置GPU检测器"""\n\s+# 尝试导入GPU检测器\n\s+try:\n\s+from core\.gpu_detector import GPUDetector\n\s+self\.gpu_detector = GPUDetector\(\)\n\s+self\.gpu_detector\.gpu_status_updated\.connect\(self\.on_gpu_status_updated\)\n\s+# 触发立即检测\n\s+self\.detect_and_update_gpu_immediately\(\)\n\s+except ImportError as e:\n\s+print\(f"无法导入GPU检测器: \{e\}"\)'
replacement1 = '''def setup_gpu_detector(self):
        """设置GPU检测器"""
        # 尝试导入GPU检测器
        try:
            from core.gpu_detector import GPUDetector
            self.gpu_detector = GPUDetector()
            self.gpu_detector.gpu_status_updated.connect(self.on_gpu_status_updated)
            # 触发立即检测
            self.detect_and_update_gpu_immediately()
        except ImportError as e:
            print(f"无法导入GPU检测器: {e}")'''

# 修复SubtitleEditArea类的refresh_gpu_status方法
pattern2 = r'def refresh_gpu_status\(self\):\n\s+"""刷新GPU状态"""\n\s+if self\.gpu_detector:\n\s+self\.gpu_detector\.check_gpu_async\(\)'
replacement2 = '''def refresh_gpu_status(self):
        """刷新GPU状态"""
        try:
            if self.gpu_detector:
                self.gpu_detector.check_gpu_async()
        except Exception as e:
            print(f"刷新GPU状态出错: {e}")'''

# 修复SubtitleEditArea类的detect_and_update_gpu_immediately方法
pattern3 = r'def detect_and_update_gpu_immediately\(self\):\n\s+"""立即检测GPU并更新状态"""\n\s+if self\.gpu_detector:\n\s+try:\n\s+is_available, gpu_name = self\.gpu_detector\.check_gpu_sync\(\)\n\s+status = "可用" if is_available else "不可用"\n\s+self\.on_gpu_status_updated\(is_available, gpu_name, status\)\n\s+except Exception as e:\n\s+print\(f"GPU检测失败: \{e\}"\)\n\s+self\.on_gpu_status_updated\(False, "检测失败", "检测失败"\)'
replacement3 = '''def detect_and_update_gpu_immediately(self):
        """立即检测GPU并更新状态"""
        try:
            if self.gpu_detector:
                is_available, gpu_name = self.gpu_detector.check_gpu_sync()
                status = "可用" if is_available else "不可用"
                self.on_gpu_status_updated(is_available, gpu_name, status)
        except Exception as e:
            print(f"GPU检测失败: {e}")
            self.on_gpu_status_updated(False, "检测失败", "检测失败")'''

# 修复FlipTalkMainWindow类的__init__方法中的GPU检测器初始化部分
pattern4 = r'# 初始化GPU检测器\n\s+self\.gpu_is_available = False\n\s+self\.gpu_name = "检测中..."\n\s+try:\n\s+from core\.gpu_detector import GPUDetector\n\s+self\.gpu_detector = GPUDetector\(\)\n\s+self\.gpu_detector\.gpu_status_updated\.connect\(self\.on_gpu_status_updated\)\n\s+except Exception as e:\n\s+print\(f"GPU检测器初始化失败: \{e\}"\)'
replacement4 = '''# 初始化GPU检测器
        self.gpu_is_available = False
        self.gpu_name = "检测中..."
        try:
            from core.gpu_detector import GPUDetector
            self.gpu_detector = GPUDetector()
            self.gpu_detector.gpu_status_updated.connect(self.on_gpu_status_updated)
        except Exception as e:
            print(f"GPU检测器初始化失败: {e}")'''

# 应用所有修复
content = re.sub(pattern1, replacement1, content, flags=re.DOTALL)
content = re.sub(pattern2, replacement2, content, flags=re.DOTALL)
content = re.sub(pattern3, replacement3, content, flags=re.DOTALL)
content = re.sub(pattern4, replacement4, content, flags=re.DOTALL)

# 写入修复后的内容
with open('ui/fliptalk_ui.py', 'w', encoding='utf-8') as f:
    f.write(content)

print("语法错误修复完成！") 