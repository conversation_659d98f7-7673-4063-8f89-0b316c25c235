# GPU检测功能说明

## 概述
本功能为FlipTalk AI主界面的GPU状态栏提供实时GPU检测和状态显示功能。

## 功能特性

### 1. 智能AI加速检测
- 不仅检测GPU硬件，更重要的是检测AI框架的GPU支持
- 支持检测NVIDIA、AMD、Intel GPU硬件
- 重点检测PyTorch GPU支持（Demucs人声分离必需）
- 检测CUDA、cuDNN等AI加速依赖库
- 区分硬件可用性和实际AI加速能力

### 2. 状态栏显示
- **绿色文字**: AI GPU加速真正可用（硬件+软件都支持）
- **黄色文字**: AI GPU加速不可用（硬件有问题或软件配置不当）
- 显示格式说明：
  - `GPU可用，显卡名称`: 完全支持AI加速
  - `GPU硬件可用，但AI加速不可用 - 显卡名称`: 硬件存在但软件配置有问题
  - `AMD GPU检测到，AI加速支持有限 - 显卡名称`: AMD显卡对主流AI框架支持有限
  - `集成显卡，显卡名称`: 集成显卡性能不足以支持AI加速

### 3. 实时更新
- 程序启动时自动检测GPU
- 每60秒自动刷新GPU状态
- 支持手动点击状态栏刷新

### 4. 快速启动
- 程序启动时立即显示GPU状态（约0.05秒）
- 无需等待检测过程
- 后台自动定期刷新

## 技术实现

### 核心模块
- `core/gpu_detector.py`: GPU检测核心模块
- 基于系统命令和Python库进行检测
- 使用Qt信号机制实现异步状态更新

### 检测方式
1. **NVIDIA GPU**: 使用`nvidia-smi`命令
2. **AMD GPU**: 使用`wmic`(Windows)或`lspci`(Linux)
3. **Intel GPU**: 同AMD检测方式
4. **CUDA支持**: 尝试检测PyTorch/pynvml

### 状态判断逻辑
- NVIDIA GPU + PyTorch GPU支持 → 绿色（推荐）
- NVIDIA GPU + 无PyTorch GPU支持 → 黄色（需配置CUDA）
- AMD独立显卡 → 黄色（AI框架支持有限）
- Intel集成显卡 → 黄色（性能不足）
- 未检测到GPU → 黄色（不可用）

## 使用说明

### 查看GPU状态
1. 启动FlipTalk AI主程序
2. 查看右侧字幕编辑区域顶部的GPU状态栏
3. 根据颜色判断GPU加速可用性：
   - 绿色 = 可以启用GPU加速
   - 黄色 = 建议使用CPU模式

### 自动更新
- 启动时立即显示检测结果
- 每60秒后台自动刷新状态

### 状态含义
- `GPU可用，NVIDIA GeForce RTX 3080`: AI GPU加速完全可用，推荐启用
- `GPU硬件可用，但AI加速不可用 - NVIDIA GeForce RTX 3080`: 硬件正常但缺少CUDA配置
- `AMD GPU检测到，AI加速支持有限 - AMD Radeon RX 6800`: AMD显卡对主流AI框架支持有限
- `集成显卡，Intel UHD Graphics`: 集成显卡，不推荐用于AI加速
- `GPU不可用`: 未检测到任何GPU硬件
- `GPU检测失败`: 检测过程出错

## 故障排除

### 常见问题
1. **显示"GPU不可用"但有独立显卡**
   - 检查显卡驱动是否正确安装
   - 确认显卡在设备管理器中正常工作

2. **NVIDIA GPU显示"AI加速不可用"**
   - 说明缺少CUDA运行时库或配置不正确
   - 需要安装CUDA 11.8+和cuDNN 8.6+
   - 参考：https://www.tensorflow.org/install/gpu
   - Demucs人声分离等功能将使用CPU模式，速度较慢

3. **状态更新延迟**
   - GPU检测已优化，启动时立即显示结果
   - 检测速度约0.05秒

### 技术支持
- 功能位置: 主界面右侧GPU状态栏
- 更新频率: 每60秒自动刷新
- 支持平台: Windows、Linux
- 依赖命令: nvidia-smi、wmic、lspci
- AI框架检测: PyTorch GPU支持
- 关键应用: Demucs人声分离GPU加速

## 版本信息
- 功能版本: 1.0.0
- 集成时间: 2025年6月
- 支持的GPU: NVIDIA、AMD、Intel
- 兼容性: PySide6、Qt6 