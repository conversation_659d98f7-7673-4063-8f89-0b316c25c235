# 视频下载功能使用说明

## 功能简介

FlipTalk AI 现已集成完整的视频下载功能，支持从YouTube、Bilibili等主流视频平台下载视频。

## 功能特点

### 🎯 支持平台
- **YouTube** - 全球最大视频平台
- **Bilibili** - 中国领先弹幕视频网站  
- **抖音** - 短视频平台
- **快手** - 短视频平台
- **西瓜视频** - 中长视频平台
- **其他** - 支持500+视频网站

### 📺 下载选项
- **视频质量**: 最佳质量、1080p、720p、480p、360p
- **下载格式**: 视频+音频、仅视频、仅音频
- **文件类型**: MP4、WebM、M4A等
- **自定义路径**: 选择任意保存位置

### 🚀 功能优势
- **实时进度**: 显示下载进度和速度
- **错误处理**: 智能错误检测和提示
- **批量支持**: 支持播放列表下载
- **线程安全**: 多线程下载，不阻塞界面

## 安装依赖

### 自动安装（推荐）
```bash
python install_dependencies.py
```

### 手动安装
```bash
pip install yt-dlp>=2023.7.6
pip install PySide6>=6.4.0
pip install requests>=2.31.0
```

### 完整依赖
```bash
pip install -r requirements.txt
```

## 使用方法

### 1. 启动下载功能
- 打开 FlipTalk AI 主界面
- 点击"视频下载"功能卡片
- 视频下载对话框自动打开

### 2. 输入视频链接
- 在"视频链接"输入框中粘贴视频URL
- 支持的链接格式示例：
  ```
  https://www.youtube.com/watch?v=VIDEO_ID
  https://www.bilibili.com/video/BV1234567890
  https://www.douyin.com/video/1234567890
  ```

### 3. 配置下载设置
- **视频质量**: 选择合适的清晰度
- **下载格式**: 根据需要选择格式类型
- **保存路径**: 点击"浏览"选择下载文件夹

### 4. 开始下载
- 点击"开始下载"按钮
- 观察进度条和状态信息
- 下载完成后会有提示

### 5. 管理下载
- **取消下载**: 点击"取消下载"按钮
- **查看日志**: 在日志区域查看详细信息
- **关闭对话框**: 下载完成后可关闭

## 常见问题

### Q: 下载失败怎么办？
A: 
1. 检查网络连接是否正常
2. 确认视频链接是否有效
3. 尝试降低视频质量设置
4. 查看错误日志获取详细信息

### Q: 支持下载私有视频吗？
A: 
- 仅支持公开视频下载
- 私有或受限视频无法下载
- 请确保视频访问权限

### Q: 下载速度慢怎么办？
A: 
1. 检查网络带宽
2. 选择较低清晰度
3. 尝试在网络较好的时段下载
4. 关闭其他网络应用

### Q: 某些平台无法下载？
A: 
- 不同平台可能有访问限制
- 建议更新到最新版本
- 某些地区可能有网络限制

## 技术支持

### 依赖问题
如果遇到依赖安装问题：
```bash
# 更新pip
python -m pip install --upgrade pip

# 使用国内源
pip install -i https://pypi.tuna.tsinghua.edu.cn/simple/ yt-dlp

# 清除缓存重新安装
pip cache purge
pip install yt-dlp --no-cache-dir
```

### 性能优化
- 建议下载到SSD硬盘以提高速度
- 关闭不必要的后台程序
- 定期清理下载文件夹

## 更新日志

### v1.0.0 (2024-01-XX)
- ✅ 基础视频下载功能
- ✅ 支持主流视频平台
- ✅ 多种质量选项
- ✅ 实时进度显示
- ✅ 错误处理机制

### 计划功能
- 🔄 批量下载支持
- 🔄 下载历史记录
- 🔄 断点续传功能
- 🔄 更多平台支持

## 许可证

本功能基于开源项目 yt-dlp 实现，遵循相关开源协议。请合法使用，尊重视频版权。

---

**注意**: 请遵守各平台的使用条款，仅下载您有权访问的内容。 