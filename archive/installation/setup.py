#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FlipTalk AI - 项目安装配置文件
"""

from setuptools import setup, find_packages
from pathlib import Path

# 读取README文件
readme_file = Path(__file__).parent / "docs" / "README.md"
if readme_file.exists():
    with open(readme_file, "r", encoding="utf-8") as f:
        long_description = f.read()
else:
    long_description = "FlipTalk AI - 智能视频翻译和音频处理工具"

# 读取依赖文件
requirements_file = Path(__file__).parent / "requirements.txt"
if requirements_file.exists():
    with open(requirements_file, "r", encoding="utf-8") as f:
        requirements = [line.strip() for line in f if line.strip() and not line.startswith("#")]
else:
    requirements = []

setup(
    name="fliptalk-ai",
    version="1.0.0",
    description="智能视频翻译和音频处理工具",
    long_description=long_description,
    long_description_content_type="text/markdown",
    author="FlipTalk AI Team",
    author_email="<EMAIL>",
    url="https://github.com/fliptalk/fliptalk-ai",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: End Users/Desktop",
        "Topic :: Multimedia :: Video",
        "Topic :: Multimedia :: Sound/Audio",
        "License :: OSI Approved :: MIT License",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Operating System :: OS Independent",
    ],
    python_requires=">=3.8",
    install_requires=requirements,
    entry_points={
        "console_scripts": [
            "fliptalk=main:main",
        ],
    },
    include_package_data=True,
    package_data={
        "ui": ["*.ui", "*.qrc"],
        "assets": ["*.png", "*.jpg", "*.ico"],
        "docs": ["*.md"],
    },
    project_urls={
        "Bug Reports": "https://github.com/fliptalk/fliptalk-ai/issues",
        "Source": "https://github.com/fliptalk/fliptalk-ai",
        "Documentation": "https://fliptalk.ai/docs",
    },
) 