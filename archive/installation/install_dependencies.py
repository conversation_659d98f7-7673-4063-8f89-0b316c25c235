#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FlipTalk AI 依赖安装脚本
自动安装视频下载功能所需的依赖库
"""

import subprocess
import sys
import os

def install_package(package):
    """安装单个包"""
    try:
        print(f"正在安装 {package}...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"✅ {package} 安装成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {package} 安装失败: {e}")
        return False

def check_package(package_name):
    """检查包是否已安装"""
    try:
        __import__(package_name)
        return True
    except ImportError:
        return False

def main():
    """主函数"""
    print("🚀 FlipTalk AI 依赖检查与安装")
    print("="*50)
    
    # 核心依赖列表
    dependencies = [
        ("PySide6", "PySide6>=6.4.0"),
        ("yt_dlp", "yt-dlp>=2023.7.6"),
        ("requests", "requests>=2.31.0"),
        ("tqdm", "tqdm>=4.65.0"),
    ]
    
    # 可选依赖列表
    optional_dependencies = [
        ("pydub", "pydub>=0.25.1"),
        ("cv2", "opencv-python>=4.8.0"),
        ("moviepy", "moviepy>=1.0.3"),
        ("webvtt", "webvtt-py>=0.4.6"),
        ("pysrt", "pysrt>=1.1.2"),
        ("colorama", "colorama>=0.4.6"),
    ]
    

    
    print("检查核心依赖...")
    missing_core = []
    for import_name, package_name in dependencies:
        if check_package(import_name):
            print(f"✅ {package_name} - 已安装")
        else:
            print(f"❌ {package_name} - 未安装")
            missing_core.append(package_name)
    
    print("\n检查可选依赖...")
    missing_optional = []
    for import_name, package_name in optional_dependencies:
        if check_package(import_name):
            print(f"✅ {package_name} - 已安装")
        else:
            print(f"⚠️ {package_name} - 未安装 (可选)")
            missing_optional.append(package_name)
    

    
    # 安装缺失的核心依赖
    if missing_core:
        print(f"\n发现 {len(missing_core)} 个缺失的核心依赖")
        install_choice = input("是否自动安装核心依赖? (y/n): ").lower().strip()
        
        if install_choice in ['y', 'yes', '是']:
            print("\n开始安装核心依赖...")
            for package in missing_core:
                install_package(package)
        else:
            print("跳过核心依赖安装")
    
    # 安装可选依赖
    if missing_optional:
        print(f"\n发现 {len(missing_optional)} 个缺失的可选依赖")
        install_choice = input("是否安装可选依赖? (y/n): ").lower().strip()
        
        if install_choice in ['y', 'yes', '是']:
            print("\n开始安装可选依赖...")
            for package in missing_optional:
                install_package(package)
        else:
            print("跳过可选依赖安装")
    

    
    print("\n" + "="*50)
    print("依赖检查完成！")
    
    # 最终检查
    print("\n最终检查 - 功能状态:")
    try:
        import yt_dlp
        print("✅ yt-dlp 可用 - 视频下载功能正常")
    except ImportError:
        print("❌ yt-dlp 不可用 - 视频下载功能无法使用")
        print("请手动安装: pip install yt-dlp")
    
    try:
        from PySide6.QtWidgets import QApplication
        print("✅ PySide6 可用 - UI界面正常")
    except ImportError:
        print("❌ PySide6 不可用 - 无法启动图形界面")
        print("请手动安装: pip install PySide6")
    

    
    print("\n如果安装过程中遇到问题，请尝试:")
    print("1. 更新pip: python -m pip install --upgrade pip")
    print("2. 使用清华源: pip install -i https://pypi.tuna.tsinghua.edu.cn/simple/")
    print("3. 手动安装基础功能: pip install yt-dlp PySide6")

    
    input("\n按Enter键退出...")

if __name__ == "__main__":
    main() 