# WhisperX 音频字幕提取功能使用指南

## 🎯 功能概述

WhisperX 音频字幕提取功能是 FlipTalk AI 的核心模块之一，提供高质量的音频转字幕服务。基于 OpenAI Whisper 和 WhisperX 技术，支持多语言识别、说话人分离、时间戳对齐等高级功能。

## ✨ 核心特性

### 🔥 AI 语音识别
- **多模型支持**: 从 tiny (39MB) 到 large-v3 (1.5GB) 多种规格
- **多语言识别**: 支持中文、英文等 99+ 种语言
- **自动语言检测**: 智能识别音频中的语言类型
- **高精度转录**: 基于最新 Whisper 模型，准确率极高

### ⚡ 性能优化
- **GPU 加速**: 自动检测和使用 CUDA GPU
- **批处理**: 支持批量音频文件处理
- **内存优化**: 智能内存管理，避免内存溢出
- **增量下载**: 模型自动下载和缓存管理

### 🎨 丰富输出格式
- **SRT 格式**: 标准字幕格式，兼容性最好
- **VTT 格式**: Web 字幕格式，支持样式
- **JSON 详情**: 包含完整时间戳和元数据
- **音频分段**: 根据字幕自动切分音频片段

### 🔧 高级功能
- **说话人分离**: 识别不同说话人 (需要 HuggingFace 令牌)
- **时间戳对齐**: 精确到词级别的时间戳
- **噪音过滤**: 自动过滤背景噪音
- **置信度评分**: 每个字幕段落的准确度评分

## 📦 安装依赖

### 基础依赖
```bash
# 安装 WhisperX 和相关依赖
pip install whisperx>=3.1.0
pip install openai-whisper>=20230918
pip install torch>=2.0.0
pip install torchaudio>=2.0.0
pip install soundfile>=0.12.1
pip install srt>=3.5.0
```

### 完整依赖 (推荐)
```bash
# 安装项目完整依赖
pip install -r requirements.txt
```

### GPU 支持 (可选)
```bash
# CUDA 支持 (NVIDIA GPU)
pip install torch torchaudio --index-url https://download.pytorch.org/whl/cu118

# 验证 GPU 可用性
python -c "import torch; print(f'CUDA 可用: {torch.cuda.is_available()}')"
```

## 🚀 快速开始

### 1. 通过图形界面使用

1. 启动 FlipTalk AI 主程序
```bash
python main.py
```

2. 点击功能区域的 **"音频提取字幕"** 卡片

3. 在弹出的对话框中：
   - 选择音频文件
   - 选择 AI 模型 (推荐: `medium` 或 `large-v3`)
   - 选择目标语言 (或使用自动检测)
   - 选择输出格式 (SRT/VTT)
   - 点击 **"开始提取字幕"**

### 2. 通过代码使用

```python
from models.whisperx_subtitle import WhisperXExtractor

# 初始化提取器
extractor = WhisperXExtractor(device="auto")

# 提取字幕
result = extractor.extract_subtitle(
    audio_path="path/to/your/audio.wav",
    model_name="medium",
    language=None,  # 自动检测
    output_formats=["srt", "vtt"]
)

# 生成字幕文件
srt_content = extractor.generate_srt(result['segments'], "output.srt")
vtt_content = extractor.generate_vtt(result['segments'], "output.vtt")
```

### 3. 命令行使用
```bash
# 运行示例脚本
cd models/whisperx_subtitle
python example.py

# 测试模型管理
python example.py --test models

# 测试音频验证
python example.py --test validation
```

## 🎛️ 详细配置

### 模型选择指南

| 模型名称 | 大小 | 速度 | 准确度 | 推荐用途 |
|---------|------|------|--------|----------|
| tiny | 39MB | ⭐⭐⭐⭐⭐ | ⭐⭐ | 快速预览、实时转录 |
| base | 74MB | ⭐⭐⭐⭐ | ⭐⭐⭐ | 日常使用、英文内容 |
| small | 244MB | ⭐⭐⭐ | ⭐⭐⭐⭐ | 多语言内容、平衡选择 |
| medium | 769MB | ⭐⭐ | ⭐⭐⭐⭐⭐ | 高质量转录、中文推荐 |
| large-v3 | 1.5GB | ⭐ | ⭐⭐⭐⭐⭐ | 最高质量、专业用途 |

### 语言配置

```python
# 支持的语言代码
SUPPORTED_LANGUAGES = {
    "auto": "自动检测",
    "zh": "中文 (简体)",
    "en": "English",
    "ja": "日本語",
    "ko": "한국어",
    "es": "Español",
    "fr": "Français", 
    "de": "Deutsch",
    "it": "Italiano",
    "pt": "Português",
    "ru": "Русский",
    "ar": "العربية"
}
```

### 高级参数配置

```python
# 高级配置示例
config = {
    "model_name": "large-v3",
    "language": "zh",  # 或 None 表示自动检测
    "batch_size": 16,  # 批处理大小
    "chunk_size": 30,  # 音频分块大小(秒)
    "enable_diarization": True,  # 说话人分离
    "min_speakers": 1,  # 最少说话人数
    "max_speakers": 5,  # 最多说话人数
    "hf_token": "your_huggingface_token"  # 说话人分离需要
}
```

## 🔧 模型管理

### 查看可用模型
```python
from models.whisperx_subtitle import WhisperXModelManager

manager = WhisperXModelManager()
models = manager.get_available_models()
for name, info in models.items():
    print(f"{name}: {info['size']} - {info['description']}")
```

### 下载模型
```python
# 下载指定模型
success = manager.download_model("medium")

# 下载推荐模型
recommended = manager.get_recommended_model("zh")  # 中文推荐
manager.download_model(recommended)
```

### 管理已下载模型
```python
# 查看已下载模型
downloaded = manager.get_downloaded_models()
print(f"已下载: {downloaded}")

# 删除模型
manager.remove_model("tiny")

# 清理缓存
manager.cleanup_cache()
```

## 📁 输出格式说明

### SRT 格式
```srt
1
00:00:00,000 --> 00:00:03,280
大家好，欢迎来到 FlipTalk AI

2
00:00:03,280 --> 00:00:06,560
今天我们来演示音频转字幕功能
```

### VTT 格式
```vtt
WEBVTT

00:00:00.000 --> 00:00:03.280
大家好，欢迎来到 FlipTalk AI

00:00:03.280 --> 00:00:06.560
今天我们来演示音频转字幕功能
```

### JSON 详情
```json
{
  "segments": [
    {
      "start": 0.0,
      "end": 3.28,
      "text": "大家好，欢迎来到 FlipTalk AI",
      "words": [
        {"start": 0.0, "end": 1.0, "word": "大家好"},
        {"start": 1.2, "end": 2.0, "word": "欢迎"},
        {"start": 2.1, "end": 3.28, "word": "来到 FlipTalk AI"}
      ]
    }
  ],
  "language": "zh",
  "model_used": "large-v3"
}
```

## 🎯 使用技巧

### 1. 提高识别准确度
- **音质优化**: 使用清晰、无噪音的音频
- **模型选择**: 中文内容推荐 `large-v3`，英文可用 `medium.en`
- **语言指定**: 明确指定语言比自动检测更准确
- **音频预处理**: 降噪、音量标准化

### 2. 优化处理速度
- **GPU 使用**: 确保 CUDA 可用，速度提升 5-10 倍
- **模型大小**: 根据需求选择合适大小的模型
- **批处理**: 处理多个文件时使用批处理模式
- **内存管理**: 处理大文件时适当调整 `chunk_size`

### 3. 说话人分离设置
```python
# 需要 HuggingFace 账号和令牌
# 1. 注册 https://huggingface.co/
# 2. 获取访问令牌
# 3. 同意 pyannote/speaker-diarization 模型使用条款

config = {
    "enable_diarization": True,
    "hf_token": "hf_xxxxxxxxxxxxxxxxxxxx",
    "min_speakers": 2,
    "max_speakers": 4
}
```

## 🐛 常见问题

### Q1: 模型下载失败
**A1**: 
- 检查网络连接
- 使用代理: `export HF_ENDPOINT=https://hf-mirror.com`
- 手动下载模型到 `models/whisperx_subtitle/weights/`

### Q2: CUDA 内存不足
**A2**:
- 减小 `batch_size` (默认 16 → 8 → 4)
- 使用更小的模型 (`large` → `medium` → `small`)
- 减小 `chunk_size` (默认 30 → 15 → 10)

### Q3: 识别准确度低
**A3**:
- 确保音频质量良好 (采样率 ≥ 16kHz)
- 指定正确的语言代码
- 使用更大的模型
- 检查音频中是否有背景音乐干扰

### Q4: 处理速度慢
**A4**:
- 安装 GPU 版本的 PyTorch
- 使用更小的模型进行快速预览
- 启用批处理模式
- 检查系统资源使用情况

### Q5: 说话人分离无效
**A5**:
- 确保已设置 `hf_token`
- 检查 HuggingFace 账号权限
- 音频中需要有明显的不同说话人
- 调整 `min_speakers` 和 `max_speakers` 参数

## 🔗 相关资源

### 官方文档
- [OpenAI Whisper](https://github.com/openai/whisper)
- [WhisperX](https://github.com/m-bain/whisperX)
- [PyTorch](https://pytorch.org/)

### 模型信息
- [Whisper 模型对比](https://github.com/openai/whisper#available-models-and-languages)
- [HuggingFace 模型库](https://huggingface.co/models?search=whisper)

### 社区支持
- [GitHub Issues](https://github.com/dodin0907/FlipTalk-Ai/issues)
- [讨论区](https://github.com/dodin0907/FlipTalk-Ai/discussions)

## 📈 性能基准

### 测试环境
- **CPU**: Intel i7-12700K
- **GPU**: NVIDIA RTX 3080 (12GB)
- **内存**: 32GB DDR4
- **音频**: 10分钟中文对话 (WAV, 48kHz)

### 处理时间对比

| 模型 | CPU 模式 | GPU 模式 | 准确度 |
|------|----------|----------|--------|
| tiny | 2分30秒 | 45秒 | 85% |
| small | 5分钟 | 1分30秒 | 92% |
| medium | 8分钟 | 2分钟 | 95% |
| large-v3 | 15分钟 | 3分30秒 | 98% |

### 内存使用

| 模型 | CPU 内存 | GPU 内存 |
|------|----------|----------|
| tiny | 500MB | 1GB |
| small | 1GB | 2GB |
| medium | 2GB | 4GB |
| large-v3 | 4GB | 6GB |

## 🎉 结语

WhisperX 音频字幕提取功能为 FlipTalk AI 提供了强大的语音识别能力。通过合理的配置和使用，您可以获得高质量的字幕文件，为后续的翻译、配音等功能奠定基础。

如果您在使用过程中遇到任何问题，请参考本文档的故障排除部分，或在 GitHub 上提交 Issue。我们会持续改进和优化这一功能！

---
*最后更新: 2024年12月* 