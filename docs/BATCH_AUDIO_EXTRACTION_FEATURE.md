# FlipTalk AI - 批量音频提取功能

## 功能概述

现在当您点击功能界面中的"视频提取音频"卡片的"立即使用"按钮时，会直接弹出一个专门的批量音频提取对话框，提供更好的用户体验。

## 新增功能特性

### 🎯 一键启动
- 点击"立即使用"按钮直接弹出专用对话框
- 无需跳转到首页再处理
- 提供专注的批量处理环境

### 📁 批量文件管理
- **拖拽上传**: 支持直接拖拽多个视频文件到对话框
- **文件浏览器**: 点击"添加文件"按钮批量选择视频文件
- **文件预览**: 显示文件名和处理状态
- **文件移除**: 单独移除不需要的文件

### 📊 实时状态监控
- **进度条显示**: 每个文件的处理进度可视化
- **状态更新**: 实时显示处理状态（等待、处理中、完成、失败）
- **统计信息**: 总文件数、已完成数、失败数
- **颜色标识**: 不同状态使用不同颜色区分

### 📝 详细处理日志
- **时间戳**: 每条日志都有精确时间记录
- **操作记录**: 记录文件添加、移除、处理开始、完成等操作
- **错误信息**: 详细的错误信息帮助问题排查
- **自动滚动**: 日志自动滚动到最新内容

### ⚙️ 灵活配置选项
- **输出目录**: 可选择自定义输出目录或使用默认位置
- **输出格式**: 统一输出为高质量WAV格式
- **音频参数**: 采样率44100Hz，16位PCM，立体声

## 技术实现

### 核心组件
1. **BatchAudioExtractionDialog**: 主对话框类
2. **TaskProgressWidget**: 单个任务进度显示组件
3. **核心服务集成**: 与现有音频提取服务无缝集成

### 支持的视频格式
- MP4, AVI, MKV, MOV, WMV, FLV, WebM, M4V
- 基于FFmpeg，支持广泛的视频编码格式

### 异步处理架构
- 多线程并发处理多个文件
- 不阻塞UI界面响应
- 实时进度反馈

## 使用指南

### 启动批量提取
1. 运行FlipTalk AI主程序
2. 点击左侧导航栏"功能"
3. 找到"视频提取音频"功能卡片
4. 点击"立即使用"按钮

### 添加视频文件
**方法一：拖拽上传**
- 直接拖拽视频文件到对话框任意位置
- 支持同时拖拽多个文件

**方法二：文件浏览器**
- 点击"添加文件"按钮
- 在文件对话框中选择一个或多个视频文件
- 支持Ctrl+点击或Shift+点击多选

### 管理文件列表
- **查看状态**: 每个文件显示当前处理状态
- **移除文件**: 点击文件右侧的"移除"按钮
- **清空列表**: 点击"清空列表"按钮移除所有文件

### 开始处理
1. 确认文件列表中有需要处理的视频
2. （可选）点击"选择目录"设置自定义输出目录
3. 点击"开始处理"按钮
4. 观察进度条和日志信息

### 处理结果
- 音频文件默认保存在视频文件同目录
- 文件名格式：`原视频名_audio.wav`
- 处理完成后在日志中显示输出路径

## 界面截图功能说明

### 主要区域
1. **标题区域**: 显示功能名称和描述
2. **文件列表区域**: 显示所有待处理的视频文件
3. **控制区域**: 统计信息、输出设置、处理日志
4. **操作按钮**: 清空列表、开始处理、关闭

### 状态指示
- 🟦 **蓝色**: 正在处理
- 🟢 **绿色**: 处理完成
- 🔴 **红色**: 处理失败
- 🟡 **黄色**: 启动中
- ⚪ **灰色**: 等待处理

## 错误处理

### 常见问题及解决方案
1. **FFmpeg未找到**: 确保FFmpeg已安装并在系统PATH中
2. **文件格式不支持**: 检查是否为支持的视频格式
3. **权限问题**: 确保有读取视频文件和写入输出目录的权限
4. **磁盘空间不足**: 确保输出目录有足够的存储空间

### 日志信息解读
- `[时间戳] 添加文件: xxx.mp4` - 成功添加文件到处理列表
- `[时间戳] 开始提取: xxx.mp4` - 开始处理文件
- `[时间戳] 提取完成: xxx.mp4 -> xxx_audio.wav` - 处理成功完成
- `[时间戳] 提取失败: xxx.mp4 - 错误信息` - 处理失败及原因

## 性能特性

### 并发处理
- 支持多个文件同时处理
- 自动根据CPU核心数调整并发数
- 避免系统资源过度占用

### 内存优化
- 流式处理大视频文件
- 不将整个视频加载到内存
- 动态释放已完成任务的资源

### 进度反馈
- 实时更新处理进度
- 精确的百分比显示
- 平滑的进度条动画

## 扩展功能

### 未来计划
- [ ] 支持更多音频输出格式（MP3、AAC、FLAC）
- [ ] 音频质量参数自定义
- [ ] 音频预处理（降噪、音量标准化）
- [ ] 批量处理模板保存
- [ ] 处理结果导出报告

### 集成能力
- 与现有音频提取核心完全兼容
- 支持插件化扩展
- 可与其他FlipTalk AI功能模块联动

## 开发者信息

### 主要文件
- `batch_audio_extraction_dialog.py` - 批量提取对话框
- `fliptalk_ui.py` - 主界面集成
- `core/services.py` - 核心服务
- `plugins/audio_extractor/plugin.py` - FFmpeg插件

### 依赖项
- PySide6 - Qt界面框架
- FFmpeg - 音视频处理
- pathlib - 路径处理
- threading - 多线程支持

---

**FlipTalk AI** - 让视频音频处理变得简单高效！ 