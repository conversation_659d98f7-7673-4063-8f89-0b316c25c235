# Azure TTS 设置指南

## 概述

Azure TTS (Text-to-Speech) 是微软Azure认知服务提供的高质量语音合成服务，支持多种语言和声音，音质相比免费的Edge TTS更加自然流畅。

## 依赖安装

### 1. 安装Azure SDK

```bash
pip install azure-cognitiveservices-speech
```

### 2. 验证安装

运行字幕配音功能，如果在TTS引擎下拉框中看到"Azure TTS (高级)"选项，说明安装成功。

## 配置Azure TTS

### 1. 获取Azure TTS API密钥

1. 访问 [Azure Portal](https://portal.azure.com/)
2. 创建或登录Azure账户
3. 搜索"认知服务"或"Cognitive Services"
4. 创建新的"语音"服务资源
5. 在资源页面获取以下信息：
   - **密钥1** 或 **密钥2**（任选其一）
   - **区域/位置**（如：eastus, westus2, eastasia等）

### 2. 在FlipTalk AI中配置

1. 打开FlipTalk AI主界面
2. 点击左侧导航栏的"API设置"
3. 找到"🔊 Azure TTS设置"卡片
4. 输入获取到的API密钥和区域
5. 配置会自动保存

## 使用Azure TTS

### 1. 在字幕配音功能中使用

1. 打开字幕配音对话框
2. 在"TTS引擎"下拉框中选择"Azure TTS (高级)"
3. 选择语言和声音
4. 设置语速等参数
5. 开始配音

### 2. 支持的功能

- ✅ **多语言支持**：中文、英文、日文、韩文等
- ✅ **丰富的声音选择**：每种语言提供多种不同风格的声音
- ✅ **高质量音频**：神经网络训练的自然语音
- ✅ **语速控制**：0.5x - 2.0x 语速范围
- ✅ **实时试听**：配音前预览声音效果
- ✅ **批量处理**：支持大量字幕文件的批量配音

### 3. Azure TTS vs Edge TTS

| 特性 | Edge TTS | Azure TTS |
|------|----------|-----------|
| 费用 | 免费 | 付费（有免费额度） |
| 音质 | 标准 | 高质量 |
| 声音数量 | 较多 | 丰富 |
| 稳定性 | 基本稳定 | 企业级稳定 |
| API限制 | 无明确限制 | 有配额限制 |
| 商业使用 | 灰色地带 | 明确支持 |

## 常见问题

### Q: Azure TTS收费吗？

A: Azure TTS提供免费额度（每月50万字符），超出后按用量收费。具体价格请查看[Azure定价页面](https://azure.microsoft.com/pricing/details/cognitive-services/speech-services/)。

### Q: 为什么我看不到Azure TTS选项？

A: 可能的原因：
1. 未安装`azure-cognitiveservices-speech`库
2. 未配置API密钥
3. 网络连接问题

### Q: 支持哪些语言和声音？

A: Azure TTS支持100+种语言和400+种声音，具体列表请查看[官方文档](https://docs.microsoft.com/azure/cognitive-services/speech-service/language-support)。

### Q: 如何选择Azure区域？

A: 建议选择离你最近的区域以获得最佳性能：
- 中国用户：`eastasia`（东亚）
- 美国用户：`eastus`（美国东部）
- 欧洲用户：`westeurope`（西欧）

## 故障排除

### 1. 安装失败

```bash
# 如果pip安装失败，尝试升级pip
python -m pip install --upgrade pip
pip install azure-cognitiveservices-speech
```

### 2. 初始化失败

检查：
- API密钥是否正确
- 网络连接是否正常
- 区域设置是否正确

### 3. 合成失败

可能原因：
- API配额耗尽
- 网络连接中断
- 文本格式问题

## 最佳实践

1. **API密钥安全**：不要在代码中硬编码API密钥
2. **区域选择**：选择离用户最近的区域
3. **错误处理**：实现适当的错误重试机制
4. **成本控制**：监控API使用量，避免意外费用
5. **缓存策略**：对相同文本启用缓存以节省成本

## 更多资源

- [Azure TTS官方文档](https://docs.microsoft.com/azure/cognitive-services/speech-service/)
- [Azure定价计算器](https://azure.microsoft.com/pricing/calculator/)
- [SSML语法参考](https://docs.microsoft.com/azure/cognitive-services/speech-service/speech-synthesis-markup) 