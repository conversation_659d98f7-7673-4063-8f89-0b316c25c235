# FlipTalk AI - 音频提取功能

## 功能概述

已实现的视频音频提取功能，支持从各种视频格式中提取高质量音频。该功能基于插件式架构设计，使用FFmpeg作为底层音频处理引擎。

## 功能特性

- ✅ **多格式支持**: 支持MP4、AVI、MKV、MOV、WMV、FLV等主流视频格式
- ✅ **高质量提取**: 输出16位PCM格式的WAV音频文件
- ✅ **异步处理**: 支持多任务并发处理，不阻塞用户界面
- ✅ **进度监控**: 实时显示处理进度和状态
- ✅ **错误处理**: 完善的错误处理和用户提示
- ✅ **插件架构**: 模块化设计，易于扩展和维护

## 系统要求

### 必需依赖
- Python 3.8+
- PySide6 6.5.0+
- FFmpeg (需要安装并添加到系统PATH)

### FFmpeg安装

#### Windows
1. 从 [FFmpeg官网](https://ffmpeg.org/download.html) 下载Windows版本
2. 解压到 `C:\ffmpeg\` 目录
3. 将 `C:\ffmpeg\bin` 添加到系统PATH环境变量
4. 或者将 `ffmpeg.exe` 复制到项目根目录

#### Linux/Mac
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install ffmpeg

# CentOS/RHEL
sudo yum install ffmpeg

# macOS (使用Homebrew)
brew install ffmpeg
```

## 项目结构

```
FlipTalk_AI/
├── core/                          # 核心模块
│   ├── __init__.py
│   ├── interfaces.py              # 插件接口定义
│   ├── plugin_manager.py          # 插件管理器
│   ├── task_scheduler.py          # 任务调度器
│   └── services.py               # 核心服务
├── plugins/                       # 插件目录
│   ├── __init__.py
│   └── audio_extractor/           # 音频提取插件
│       ├── __init__.py
│       └── plugin.py             # FFmpeg音频提取实现
├── fliptalk_ui.py                # 主界面文件
├── config_window.py              # 配置窗口
├── test_audio_extraction.py      # 测试脚本
└── requirements.txt              # 依赖列表
```

## 使用方法

### 1. 启动应用程序
```bash
python fliptalk_ui.py
```

### 2. 上传视频文件
- **拖拽上传**: 直接将视频文件拖拽到上传区域
- **点击选择**: 点击上传区域，通过文件对话框选择视频

### 3. 开始处理
- 点击"开始处理"按钮启动音频提取任务
- 系统会自动为每个视频文件创建异步处理任务
- 可以在处理列表中查看进度和状态

### 4. 查看结果
- 提取的音频文件保存在原视频文件同目录下
- 文件命名格式: `原文件名_audio.wav`

## 测试功能

运行测试脚本来验证音频提取功能：

```bash
# 1. 准备测试视频文件（命名为test_video.mp4，放在项目根目录）
# 2. 运行测试脚本
python test_audio_extraction.py
```

测试脚本会验证：
- 插件系统加载
- 音频提取器初始化
- 视频音频信息获取
- 音频提取功能
- 异步任务处理

## API接口

### 核心服务接口

```python
from core.services import core_service

# 异步音频提取
task_id = core_service.audio_extraction.extract_audio_async(video_path)

# 同步音频提取
output_path = core_service.audio_extraction.extract_audio_sync(video_path)

# 获取支持的格式
formats = core_service.audio_extraction.get_supported_formats()

# 获取音频信息
audio_info = core_service.audio_extraction.get_audio_info(video_path)
```

### 信号连接

```python
# 连接音频提取事件
core_service.audio_extraction.extraction_started.connect(on_started)
core_service.audio_extraction.extraction_progress.connect(on_progress)
core_service.audio_extraction.extraction_completed.connect(on_completed)
core_service.audio_extraction.extraction_failed.connect(on_failed)
```

## 插件开发

如需扩展其他音频提取方式，可以参考以下接口：

```python
from core.interfaces import IAudioExtractor

class CustomAudioExtractor(IAudioExtractor):
    def get_name(self) -> str:
        return "自定义音频提取器"
    
    def get_version(self) -> str:
        return "1.0.0"
    
    def get_description(self) -> str:
        return "自定义音频提取器描述"
    
    def initialize(self, config: Dict[str, Any]) -> bool:
        # 初始化代码
        return True
    
    def extract(self, video_path: str, output_dir: str = None) -> str:
        # 音频提取实现
        pass
    
    def get_supported_formats(self) -> list:
        return ['.mp4', '.avi']
    
    def get_audio_info(self, video_path: str) -> Dict[str, Any]:
        # 获取音频信息
        pass
    
    def cleanup(self) -> None:
        # 清理资源
        pass
```

## 配置选项

音频提取器支持以下配置参数：

```python
config = {
    'output_format': 'wav',    # 输出格式 (wav, mp3, flac)
    'sample_rate': 44100,      # 采样率 (Hz)
    'channels': 2,             # 声道数 (1=单声道, 2=立体声)
}
```

## 故障排除

### 常见问题

1. **FFmpeg未找到**
   - 检查FFmpeg是否正确安装
   - 确认FFmpeg已添加到系统PATH
   - 可以将ffmpeg.exe放在项目根目录

2. **视频格式不支持**
   - 检查视频文件格式是否在支持列表中
   - 尝试使用转换工具转换为支持的格式

3. **音频提取失败**
   - 检查视频文件是否包含音频流
   - 确认有足够的磁盘空间
   - 查看控制台错误信息

### 调试模式

开启调试信息：

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 扩展计划

后续将实现以下功能：
- 人声分离
- 语音识别
- 字幕翻译
- 语音合成
- 视频合成

## 许可证

MIT License

## 联系方式

如有问题或建议，请提交Issue或联系开发团队。 