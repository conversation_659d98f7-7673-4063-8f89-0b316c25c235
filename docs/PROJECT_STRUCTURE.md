# FlipTalk AI - 项目结构说明

## 项目概述

FlipTalk AI 是一个智能视频翻译和音频处理工具，采用模块化设计，具有清晰的目录结构和良好的代码组织。

## 目录结构

```
FlipTalk AI/
├── main.py                    # 主程序入口文件
├── setup.py                   # 项目安装配置
├── requirements.txt           # 项目依赖
├── run_app.py                # 简化启动脚本
│
├── ui/                       # 用户界面模块
│   ├── __init__.py           # UI模块初始化
│   ├── fliptalk_ui.py        # 主界面UI
│   ├── batch_audio_extraction_dialog.py  # 批量音频提取对话框
│   └── config_window.py      # 配置窗口
│
├── core/                     # 核心功能模块
│   ├── __init__.py           # 核心模块初始化
│   ├── interfaces.py         # 插件接口定义
│   ├── plugin_manager.py     # 插件管理器
│   ├── task_scheduler.py     # 任务调度器
│   └── services.py           # 高级服务接口
│
├── plugins/                  # 插件模块
│   ├── __init__.py           # 插件模块初始化
│   └── audio_extractor/      # 音频提取插件
│       ├── __init__.py
│       └── plugin.py         # FFmpeg音频提取实现
│
├── tests/                    # 测试模块
│   ├── __init__.py           # 测试模块初始化
│   ├── test_audio_extraction.py     # 音频提取测试
│   ├── test_ui_integration.py       # UI集成测试
│   ├── test_config.py              # 配置测试
│   ├── test_config_window.py       # 配置窗口测试
│   ├── test_ui.py                  # UI单元测试
│   └── demo_batch_extraction.py    # 批量提取演示
│
├── docs/                     # 文档模块
│   ├── README.md             # 项目说明文档
│   ├── PROJECT_STRUCTURE.md  # 项目结构说明
│   ├── develop.md            # 开发文档
│   ├── AUDIO_EXTRACTION_README.md      # 音频提取功能说明
│   └── BATCH_AUDIO_EXTRACTION_FEATURE.md # 批量提取功能说明
│
├── output/                   # 输出目录
│   ├── *.wav                 # 提取的音频文件
│   └── *.mp4                 # 测试视频文件
│
├── assets/                   # 资源文件
│   ├── *.png                 # 界面截图
│   └── prompt                # 开发提示文件
│
└── .cursor/                  # IDE配置文件
```

## 模块功能说明

### 🎨 UI模块 (`ui/`)
负责所有用户界面相关的功能，包括：
- **主界面**: 提供完整的应用程序界面框架
- **对话框**: 各种功能的专用对话框
- **样式管理**: 统一的UI样式和主题管理

### 🔧 核心模块 (`core/`)
提供应用程序的核心功能：
- **插件系统**: 可扩展的插件架构
- **任务调度**: 异步任务处理和管理
- **服务层**: 高级功能服务接口

### 🔌 插件模块 (`plugins/`)
实现具体的功能插件：
- **音频提取**: 基于FFmpeg的音频提取功能
- **语音识别**: AI语音转文字功能（规划中）
- **翻译服务**: 多语言翻译功能（规划中）

### 🧪 测试模块 (`tests/`)
包含所有测试代码：
- **单元测试**: 各模块的独立功能测试
- **集成测试**: 模块间交互测试
- **演示脚本**: 功能演示和验证

### 📚 文档模块 (`docs/`)
项目相关文档：
- **用户文档**: 使用说明和功能介绍
- **开发文档**: 架构设计和开发指南
- **API文档**: 接口和插件开发说明

### 📁 输出目录 (`output/`)
存放处理结果：
- **音频文件**: 从视频提取的音频
- **字幕文件**: 生成的字幕文件（规划中）
- **翻译结果**: 翻译后的文件（规划中）

### 🎯 资源目录 (`assets/`)
存放项目资源：
- **图标图片**: 应用程序图标和界面图片
- **配置文件**: 默认配置和模板文件
- **其他资源**: 音效、字体等资源文件

## 设计原则

### 1. 模块化设计
- 每个功能模块独立开发和测试
- 清晰的模块边界和接口定义
- 便于维护和扩展

### 2. 插件化架构
- 核心功能通过插件实现
- 支持动态加载和卸载插件
- 标准化的插件接口

### 3. 异步处理
- 耗时操作使用异步执行
- 不阻塞用户界面
- 提供实时进度反馈

### 4. 配置驱动
- 功能参数可配置
- 支持用户自定义设置
- 配置持久化存储

## 启动方式

### 开发环境启动
```bash
# 方式1：使用主入口文件
python main.py

# 方式2：使用简化启动脚本
python run_app.py

# 方式3：直接运行UI模块
python -m ui.fliptalk_ui
```

### 生产环境安装
```bash
# 安装项目
pip install -e .

# 使用命令行启动
fliptalk
```

## 开发规范

### 文件命名
- 使用小写字母和下划线
- 模块文件以功能命名
- 测试文件以`test_`开头

### 代码风格
- 遵循PEP 8规范
- 使用类型提示
- 完善的文档字符串

### 版本控制
- 使用语义化版本号
- 清晰的提交信息
- 分支管理策略

---

**项目结构整理完成！** 🎉

现在项目具有了专业的目录结构，便于开发、测试和维护。 