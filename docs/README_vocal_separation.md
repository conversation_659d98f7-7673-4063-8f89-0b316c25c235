# 人声分离算法移植项目

## 项目概述

本项目成功将 pytvzhen-1.3.0 中的快速人声分离算法移植到了 FlipTalk AI 项目中，为用户提供了更多的音频处理选择。

## 移植内容

### 1. 核心算法
- **源项目**: pytvzhen-1.3.0
- **算法**: CascadedNet 级联神经网络
- **特点**: 快速、高质量的人声与伴奏分离

### 2. 移植结构

```
models/vocal_separation/
├── __init__.py              # 模块初始化
├── separator.py             # 主要分离器类
├── example.py              # 使用示例
├── requirements.txt        # 依赖包
├── lib/                    # 核心库
│   ├── __init__.py
│   ├── nets.py            # 神经网络模型
│   ├── layers.py          # 网络层定义
│   ├── spec_utils.py      # 频谱处理工具
│   ├── dataset.py         # 数据集处理
│   └── utils.py           # 工具函数
└── weights/               # 模型权重
    ├── README.md
    └── baseline.pth       # 预训练模型
```

### 3. 主要功能

#### 核心类: VocalSeparator
- 快速人声分离
- GPU/CPU 自适应
- 测试时增强 (TTA)
- 批量处理
- 多种质量设置

#### 集成接口: AudioProcessor
- 多算法支持 (CascadedNet + Demucs)
- 统一API接口
- 性能对比
- 格式支持广泛

## 使用方法

### 1. 基础使用

```python
from models.vocal_separation import create_separator

# 创建分离器
separator = create_separator(
    model_path="models/vocal_separation/weights/baseline.pth",
    device="auto"
)

# 分离音频
vocal, instrumental = separator.separate_audio_file(
    "input.wav",
    "output_vocal.wav",
    "output_instrumental.wav"
)
```

### 2. 集成接口使用

```python
from audio_processing import AudioProcessor

# 创建处理器
processor = AudioProcessor()

# 使用 CascadedNet 算法
vocal_path, instrumental_path = processor.separate_vocals(
    "input.wav",
    output_dir="output",
    method="cascaded",
    quality="balanced"
)
```

### 3. 命令行使用

```bash
# 单文件处理
python models/vocal_separation/example.py --mode single --input audio.wav --model models/vocal_separation/weights/baseline.pth

# 批量处理
python models/vocal_separation/example.py --mode batch --input ./audio_folder --model models/vocal_separation/weights/baseline.pth

# 使用集成接口
python audio_processing.py input.wav --method cascaded --quality high
```

## 性能特点

### 算法优势
1. **速度快**: GPU模式下约为实时的3-5倍
2. **质量高**: 级联网络架构，分离效果优秀
3. **内存效率**: 分块处理，支持长音频
4. **易于使用**: 简洁的API设计

### 质量模式
- **fast**: 快速模式，关闭TTA和后处理
- **balanced**: 平衡模式，启用TTA，中等质量
- **high**: 高质量模式，启用TTA和后处理

### 系统要求
- **CPU**: 支持，处理速度较慢
- **GPU**: 推荐，需要2-4GB显存
- **RAM**: 建议8GB以上
- **存储**: 模型文件约250MB

## 技术细节

### 网络架构
- **编码器-解码器结构**: 多尺度特征提取
- **ASPP模块**: 空洞空间金字塔池化
- **LSTM时序建模**: 处理音频时序信息
- **级联设计**: 三阶段渐进式处理

### 频谱处理
- **STFT变换**: 2048点FFT，1024跳跃长度
- **复数处理**: 实虚部分离表示
- **掩码预测**: 软掩码分离方式
- **后处理**: 伪影去除和平滑

### 数据增强
- **测试时增强**: 偏移处理提高鲁棒性
- **随机增益**: 训练时数据增强
- **相位处理**: 复数域增强

## 依赖包

```
torch>=1.10.0
librosa>=0.9.0
soundfile>=0.10.0
numpy>=1.21.0
tqdm>=4.62.0
```

安装命令:
```bash
pip install -r models/vocal_separation/requirements.txt
```

## API 参考

### VocalSeparator 类

#### 构造函数
```python
VocalSeparator(
    model_path=None,     # 模型路径
    device='auto',       # 计算设备
    fft_size=2048,       # FFT大小
    hop_size=1024,       # 跳跃长度
    batchsize=4,         # 批大小
    cropsize=256,        # 裁剪大小
    postprocess=False    # 后处理开关
)
```

#### 主要方法
- `load_model(model_path)`: 加载模型
- `separate_audio_file(...)`: 分离音频文件
- `batch_separate(...)`: 批量处理
- `get_model_info()`: 获取模型信息

### AudioProcessor 类

#### 主要方法
- `separate_vocals(...)`: 统一分离接口
- `batch_separate(...)`: 批量处理
- `compare_methods(...)`: 方法对比
- `get_method_info(...)`: 获取算法信息

## 示例代码

### 完整示例
```python
import os
from models.vocal_separation import create_separator

def demo_vocal_separation():
    # 设置路径
    model_path = "models/vocal_separation/weights/baseline.pth"
    input_file = "demo.wav"
    output_dir = "separated"
    
    # 检查文件存在
    if not os.path.exists(model_path):
        print("错误: 模型文件不存在")
        return
    
    if not os.path.exists(input_file):
        print("错误: 输入文件不存在")
        return
    
    # 创建分离器
    print("正在初始化分离器...")
    separator = create_separator(
        model_path=model_path,
        device="auto",
        postprocess=True
    )
    
    # 显示模型信息
    info = separator.get_model_info()
    print(f"模型信息: {info}")
    
    # 执行分离
    print("正在分离音频...")
    vocal_path, instrumental_path = separator.separate_audio_file(
        input_file,
        os.path.join(output_dir, "vocal.wav"),
        os.path.join(output_dir, "instrumental.wav"),
        use_tta=True
    )
    
    print(f"分离完成!")
    print(f"人声: {vocal_path}")
    print(f"伴奏: {instrumental_path}")

if __name__ == "__main__":
    demo_vocal_separation()
```

## 故障排除

### 常见问题

1. **模型加载失败**
   - 检查模型文件路径
   - 确认模型文件完整性
   - 检查PyTorch版本兼容性

2. **CUDA内存不足**
   - 减小batchsize参数
   - 使用CPU模式
   - 关闭其他GPU程序

3. **音频格式不支持**
   - 转换为WAV格式
   - 检查采样率（推荐44.1kHz）
   - 确认音频文件完整

4. **分离质量不佳**
   - 启用测试时增强
   - 开启后处理
   - 尝试不同质量设置

### 性能优化

1. **GPU加速**
   ```python
   separator = create_separator(device="cuda")
   ```

2. **批量处理优化**
   ```python
   # 适当减小批大小
   separator = create_separator(batchsize=2)
   ```

3. **内存优化**
   ```python
   # 分块处理长音频
   separator = create_separator(cropsize=128)
   ```

## 更新日志

### v1.0.0 (2024-12-19)
- ✅ 完成算法移植
- ✅ 实现完整API
- ✅ 添加使用示例
- ✅ 集成到主项目
- ✅ 模型权重复制
- ✅ 文档完善

## 贡献

本移植项目基于以下开源项目：
- **pytvzhen-1.3.0**: 提供原始算法实现
- **librosa**: 音频处理库
- **PyTorch**: 深度学习框架

## 许可证

本项目遵循原始项目的许可证条款，仅用于学习和研究目的。

---

**FlipTalk AI Team**  
*让AI为每个人的创作赋能* 