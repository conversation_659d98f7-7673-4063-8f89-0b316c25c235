# 字幕配音模块闪退问题排查指南

## 问题现状

虽然我们的测试显示代码本身是稳定的，但在某些特定条件下仍可能出现闪退。本指南提供了全面的排查和解决方案。

## 可能的闪退原因分析

### 1. 环境依赖问题
- **PySide6版本冲突**: 不同版本可能导致兼容性问题
- **系统驱动**: 音频驱动或显卡驱动过旧
- **缺少系统组件**: Windows Media Foundation组件

### 2. 资源竞争问题
- **音频设备占用**: 其他程序正在使用音频设备
- **内存不足**: 系统可用内存低于1GB
- **CPU负载过高**: 系统CPU使用率超过90%

### 3. 特定操作序列
- **快速重复打开**: 短时间内多次打开字幕配音模块
- **大文件处理**: 处理过大的字幕文件
- **网络问题**: TTS服务网络连接异常

### 4. 系统冲突
- **杀毒软件**: 过度保护导致的误杀
- **防火墙**: 阻止了必要的网络连接
- **其他AI程序**: 与其他使用相同资源的程序冲突

## 解决方案

### 方案1: 环境修复

#### 1.1 更新依赖库
```bash
# 更新PySide6到最新版本
pip install --upgrade PySide6

# 更新其他相关依赖
pip install --upgrade edge-tts azure-cognitiveservices-speech
```

#### 1.2 检查系统组件
```powershell
# 检查Windows Media Foundation
dism /online /get-features | findstr "MediaPlayback"

# 如果未安装，可以启用
dism /online /enable-feature /featurename:MediaPlayback
```

### 方案2: 资源优化

#### 2.1 关闭不必要的程序
- 关闭其他音频/视频处理软件
- 释放系统内存（建议保持2GB以上可用）
- 降低其他程序的优先级

#### 2.2 音频设备设置
- 确保音频设备没有被其他程序独占
- 在系统音频设置中切换默认设备再切换回来
- 更新音频驱动程序

### 方案3: 程序设置优化

#### 3.1 启用兼容模式
在程序图标上右键 → 属性 → 兼容性:
- ☑️ 以兼容模式运行这个程序 (Windows 8)
- ☑️ 以管理员身份运行此程序
- ☑️ 禁用全屏优化

#### 3.2 调整TTS设置
在程序的API设置中：
- 优先使用Edge TTS（更稳定）
- 降低并发数量（设置为1）
- 启用缓存模式

### 方案4: 系统级修复

#### 4.1 运行系统检查
```powershell
# 系统文件检查
sfc /scannow

# 内存检查
mdsched.exe
```

#### 4.2 清理系统
- 重启计算机
- 清理临时文件
- 检查磁盘空间（至少保留5GB）

### 方案5: 安全模式测试

#### 5.1 创建测试环境
```python
# 创建最小化测试脚本
import sys
from PySide6.QtWidgets import QApplication

app = QApplication(sys.argv)

try:
    from ui.subtitle_voiceover_dialog import SubtitleVoiceoverDialog
    dialog = SubtitleVoiceoverDialog()
    dialog.show()
    print("✅ 对话框打开成功")
    
    # 等待5秒后自动关闭
    import time
    time.sleep(5)
    dialog.close()
    print("✅ 对话框关闭成功")
    
except Exception as e:
    print(f"❌ 错误: {e}")
    import traceback
    traceback.print_exc()
```

#### 5.2 逐步测试
1. 先测试基础功能（不使用TTS）
2. 再测试Edge TTS功能
3. 最后测试Azure TTS功能

## 高级排查方法

### 方法1: 事件查看器
1. 按Win+R，输入`eventvwr.msc`
2. 导航到 Windows日志 → 应用程序
3. 查找Python或程序相关的错误事件
4. 记录错误代码和详细信息

### 方法2: 进程监控
使用Process Monitor工具：
1. 下载Microsoft Process Monitor
2. 启动监控
3. 重现闪退问题
4. 分析崩溃前的文件/注册表访问

### 方法3: 内存检查
```python
# 内存监控脚本
import psutil
import time

def monitor_memory():
    while True:
        memory = psutil.virtual_memory()
        print(f"内存使用: {memory.percent}%, 可用: {memory.available/1024/1024/1024:.1f}GB")
        time.sleep(1)

# 在另一个终端运行此脚本，然后测试字幕配音模块
```

## 应急解决方案

### 临时方案: 降级运行
如果以上方案都无效，可以使用降级模式：

1. **仅使用Edge TTS**: 禁用Azure TTS功能
2. **单线程模式**: 设置TTS并发数为1
3. **简化界面**: 使用最基本的功能界面

### 备用方案: 外部工具
如果程序持续闪退，可以使用外部TTS工具：

```bash
# 使用命令行Edge TTS
edge-tts --voice zh-CN-XiaoxiaoNeural --text "你好世界" --write-media output.wav
```

## 联系支持

如果所有方案都无效，请提供以下信息：

### 系统信息
- Windows版本和构建号
- Python版本
- PySide6版本
- 系统内存大小
- 显卡型号

### 错误信息
- 具体的错误信息
- 事件查看器中的错误日志
- 闪退时正在进行的操作

### 环境详情
- 是否安装了其他Python GUI程序
- 是否同时运行其他AI/语音程序
- 网络连接状态

## 预防措施

### 日常维护
1. **定期更新**: 保持Python和相关库为最新版本
2. **系统清理**: 定期清理临时文件和缓存
3. **资源监控**: 定期检查系统资源使用情况

### 最佳实践
1. **避免多实例**: 不要同时打开多个字幕配音窗口
2. **文件大小控制**: 字幕文件建议不超过1000条
3. **网络稳定**: 使用稳定的网络连接

通过以上排查步骤，绝大多数闪退问题都可以得到解决。如果问题仍然存在，说明可能存在更深层的系统兼容性问题，建议联系技术支持进行进一步诊断。 