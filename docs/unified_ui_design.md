# 统一字幕配音界面设计

## 概述

根据用户需求，将字幕预览和配音结果合并为一个统一的表格界面。用户上传字幕文件后，即可在同一表格中查看字幕内容并实时跟踪配音状态。

## 设计特点

### 🎯 统一显示
- **单一表格**：将原来分离的字幕预览和配音结果合并
- **实时状态**：字幕内容和配音状态在同一行显示
- **空间优化**：右侧面板完全用于显示字幕表格

### 📊 四列布局

| 列名 | 宽度 | 内容 | 说明 |
|------|------|------|------|
| 开始时间 | 100px | HH:MM:SS.mmm | 字幕开始时间，居中对齐 |
| 结束时间 | 100px | HH:MM:SS.mmm | 字幕结束时间，居中对齐 |
| 字幕内容 | 自适应 | 字幕文本 | 长文本自动截断，悬停显示完整内容 |
| 状态 | 100px | 状态图标+文字 | 实时显示配音状态 |

### 🎨 状态设计

#### 状态类型
- **⏳ 等待**：灰色背景，初始状态
- **🔄 配音中**：黄色背景，正在处理
- **✅ 成功**：绿色背景，配音完成
- **❌ 失败**：红色背景，配音出错

#### 状态变化流程
```
字幕加载 → ⏳ 等待 → 🔄 配音中 → ✅ 成功 / ❌ 失败
```

## 交互功能

### 📱 加载体验
1. **文件上传**：拖拽或点击选择字幕文件
2. **即时显示**：文件加载后立即填充表格
3. **状态初始化**：所有行显示"⏳ 等待"状态

### 🎵 配音体验
1. **状态跟踪**：实时更新每行的配音状态
2. **进度可视化**：当前处理行高亮显示
3. **结果反馈**：成功/失败状态带颜色提示

### 🎧 播放体验
1. **双击播放**：双击成功行播放对应音频
2. **状态提示**：悬停显示详细信息
3. **错误反馈**：失败行显示错误原因

## 技术实现

### 核心组件
```python
# 统一字幕表格
self.subtitles_table = QTableWidget()
self.subtitles_table.setColumnCount(4)
self.subtitles_table.setHorizontalHeaderLabels([
    "开始时间", "结束时间", "字幕内容", "状态"
])
```

### 关键方法
- `populate_subtitles_table()`：加载字幕时填充表格
- `update_subtitle_status()`：更新单行状态
- `on_voiceover_completed()`：配音完成时更新状态
- `on_subtitle_item_double_clicked()`：处理双击播放

### 状态管理
```python
# 状态跟踪字典
self.subtitle_status = {
    row_index: {
        'status': 'waiting|processing|success|failed',
        'audio_path': 'path/to/audio.wav',
        'error_message': 'error description'
    }
}
```

## 样式设计

### 深色主题
- **背景色**：#1B1E24
- **表头色**：#2B9D7C
- **选中色**：#2B9D7C
- **网格线**：#333333

### 状态颜色
- **等待**：rgba(100, 100, 100, 50)
- **处理中**：rgba(255, 193, 7, 80)
- **成功**：rgba(0, 204, 85, 80)
- **失败**：rgba(255, 107, 107, 80)

## 用户流程

### 1. 加载字幕
```
用户操作 → 选择字幕文件 → 表格填充 → 显示"等待"状态
```

### 2. 开始配音
```
点击配音 → 状态重置 → 逐行处理 → 实时状态更新
```

### 3. 查看结果
```
配音完成 → 查看状态 → 双击播放 → 音频试听
```

### 4. 错误处理
```
配音失败 → 红色状态 → 悬停查看 → 了解错误原因
```

## 优势特点

### 🚀 用户体验
- **一目了然**：字幕内容和状态统一显示
- **实时反馈**：配音进度实时可见
- **操作简便**：双击即可播放音频

### 💡 设计优化
- **空间高效**：消除冗余界面元素
- **信息集中**：相关信息就近显示
- **状态清晰**：彩色状态一眼识别

### ⚡ 性能提升
- **单一表格**：减少UI组件开销
- **状态缓存**：优化状态管理
- **增量更新**：仅更新变化的行

## 兼容性

- ✅ 支持所有字幕格式（SRT、VTT、ASS）
- ✅ 兼容所有TTS引擎
- ✅ 保持现有功能完整性
- ✅ 响应式布局适配

## 测试指南

### 基本功能测试
1. 加载不同格式的字幕文件
2. 验证表格正确显示内容和状态
3. 测试配音过程中的状态变化
4. 验证双击播放功能

### 交互测试
1. 测试文件拖拽上传
2. 验证状态颜色显示正确
3. 测试长文本截断和提示
4. 验证错误状态显示

### 性能测试
1. 测试大量字幕条目的加载速度
2. 验证配音过程中的界面响应性
3. 测试内存使用情况 