# FlipTalk AI - 字幕配音功能说明

## 功能概述

字幕配音功能是FlipTalk AI的核心AI功能之一，支持将字幕文件转换为自然流畅的语音文件，实现智能配音生成。

### 主要特性

- 🎤 **多TTS引擎支持**：Edge TTS（免费）+ Azure TTS（高质量）
- 🌍 **多语言支持**：中文、英语、日语、韩语等
- 🎵 **丰富声音选择**：男声、女声，多种风格和情感
- ⚡ **语速调节**：0.5x - 2.0x可调节语速
- 🔊 **试听功能**：实时预览语音效果
- 📋 **批量处理**：一键生成所有字幕的配音
- 🎯 **精准对应**：一条字幕对应一段音频文件

## 支持的文件格式

### 输入格式
- `.srt` - SubRip字幕文件
- `.vtt` - WebVTT字幕文件  
- `.ass` - Advanced SubStation Alpha字幕文件

### 输出格式
- `.wav` - 高质量音频文件（每条字幕一个文件）

## TTS引擎对比

### Edge TTS（推荐入门使用）
- ✅ **完全免费**，无需API密钥
- ✅ **声音质量好**，支持多种语言和声音
- ✅ **安装简单**，开箱即用
- ⚠️ **网络依赖**，需要稳定的网络连接
- ⚠️ **使用限制**，可能有频率限制

### Azure TTS（推荐专业使用）
- ✅ **声音质量极高**，接近真人
- ✅ **稳定可靠**，企业级服务
- ✅ **丰富特性**，支持情感、风格调节
- ✅ **高并发支持**，适合批量处理
- ❌ **需要付费**，需要Azure API密钥
- ❌ **配置复杂**，需要注册Azure账号

## 安装指南

### 方法一：自动安装（推荐）
```bash
# 运行TTS依赖安装脚本
python scripts/install_tts_dependencies.py
```

### 方法二：手动安装
```bash
# 安装Edge TTS
pip install edge-tts>=6.1.7

# 安装Azure TTS（可选）
pip install azure-cognitiveservices-speech>=1.31.0

# 安装音频播放支持
pip install PySide6-Multimedia>=6.4.0
```

## 使用教程

### 1. 启动配音功能
1. 打开FlipTalk AI主程序
2. 在功能区域点击"字幕配音"卡片
3. 等待配音对话框加载完成

### 2. 加载字幕文件
1. 点击"浏览..."按钮选择字幕文件
2. 或直接将字幕文件拖拽到文件路径输入框
3. 支持格式：`.srt`、`.vtt`、`.ass`
4. 字幕内容将自动解析并显示在左侧表格中

### 3. 配置TTS设置
1. **选择TTS引擎**：Edge TTS（免费）或Azure TTS（需配置）
2. **选择语言**：根据字幕内容选择对应语言
3. **选择声音**：从可用声音列表中选择合适的声音
4. **调整语速**：使用滑块调节语速（0.5x - 2.0x）
5. **设置输出目录**：选择音频文件保存位置

### 4. 试听功能
1. 在预览文本框中输入测试文本
2. 或选择字幕表格中的任意条目（会自动填充预览文本）
3. 点击"生成预览"按钮
4. 等待预览音频生成完成
5. 点击"播放预览"试听效果
6. 满意后可开始正式配音

### 5. 开始配音
1. 确认所有设置正确
2. 点击"开始配音"按钮
3. 观察进度条和实时结果
4. 配音过程中可随时点击"停止配音"
5. 完成后可在结果表格中查看详情

### 6. 导出音频
1. 配音完成后，点击"导出音频"按钮
2. 选择目标导出目录
3. 音频文件将按序号命名：`subtitle_0001.wav`、`subtitle_0002.wav`...
4. 每个文件对应一条字幕的配音

## Azure TTS配置

### 1. 注册Azure账号
1. 访问 [Azure Portal](https://portal.azure.com/)
2. 注册或登录Azure账号
3. 创建"认知服务"资源
4. 选择"语音服务"

### 2. 获取API密钥
1. 在Azure Portal中找到你的语音服务资源
2. 点击"密钥和终结点"
3. 复制"密钥1"或"密钥2"
4. 记录"区域"信息（如：eastus）

### 3. 在FlipTalk AI中配置
1. 打开FlipTalk AI配置窗口
2. 找到"TTS设置"区域
3. 输入Azure API密钥
4. 设置正确的Azure区域
5. 保存配置

## 常见问题

### Q: Edge TTS无法使用？
A: 
- 检查网络连接是否正常
- 确认已安装edge-tts库：`pip install edge-tts`
- 尝试重启程序

### Q: Azure TTS提示认证失败？
A: 
- 检查API密钥是否正确
- 确认区域设置是否匹配
- 验证Azure账户是否有足够余额

### Q: 生成的音频文件过大？
A: 
- 考虑分割长字幕为较短片段
- 调整音频质量设置
- 使用压缩格式

### Q: 某些字符无法正确发音？
A: 
- 检查字幕文件编码（建议使用UTF-8）
- 清理字幕中的特殊格式标记
- 选择更合适的语音模型

### Q: 批量配音速度较慢？
A: 
- Edge TTS有频率限制，建议适度使用
- Azure TTS支持更高并发，速度更快
- 避免同时运行多个配音任务

## 最佳实践

### 字幕准备
1. **清理格式**：移除HTML标签、特殊字符
2. **合理分段**：避免单条字幕过长（建议<100字符）
3. **统一编码**：使用UTF-8编码保存字幕文件
4. **检查时间轴**：确保时间格式正确

### 语音选择
1. **匹配内容**：新闻类选择正式声音，娱乐类选择活泼声音
2. **考虑受众**：儿童内容选择年轻声音，商务内容选择成熟声音
3. **保持一致**：整个项目使用相同声音保持风格统一

### 性能优化
1. **分批处理**：大量字幕分批次处理，避免系统过载
2. **网络环境**：确保稳定网络连接，避免中途中断
3. **本地缓存**：相同文本的预览会被缓存，提高效率

## 技术架构

### 插件化设计
- 基于插件接口`ITtsEngine`设计
- 支持热插拔不同TTS引擎
- 便于后续扩展更多TTS服务

### 多线程处理
- 主线程负责UI交互
- 工作线程负责TTS处理
- 避免界面冻结，提升用户体验

### 错误恢复
- 单条失败不影响整体进度
- 支持失败重试机制
- 详细错误日志便于排查

## 版本更新

### v1.0.0 (当前版本)
- ✅ 支持Edge TTS和Azure TTS
- ✅ 多语言多声音支持
- ✅ 试听和批量处理功能
- ✅ SRT/VTT/ASS格式支持

### 计划功能
- 🔄 更多TTS引擎支持（讯飞、百度等）
- 🔄 音频后处理（降噪、音效）
- 🔄 实时语音预览优化
- 🔄 云端配音服务
- 🔄 语音情感和风格调节

## 技术支持

如果在使用过程中遇到问题，可以：

1. 查看本文档的常见问题部分
2. 检查控制台输出的错误信息
3. 运行依赖安装脚本重新安装
4. 提交Issue反馈问题

---

**FlipTalk AI Team**  
*让AI为视频创作赋能* 