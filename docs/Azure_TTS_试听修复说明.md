# Azure TTS 试听修复说明

## 问题描述

用户反馈在使用FlipTalk AI字幕配音功能时，当选择Azure TTS引擎并点击试听按钮时，界面会立即闪退崩溃。

## 问题分析

### 根本原因
1. **配置问题**：Azure TTS未正确配置API密钥，导致插件初始化失败
2. **逻辑缺陷**：界面仍然允许用户选择和试听未正确初始化的Azure TTS引擎
3. **异常处理不当**：当Azure TTS调用失败时，异常没有被正确捕获和处理，导致程序崩溃

### 具体表现
- 日志显示：`Warning: Azure API密钥未配置，请在主界面API设置中配置Azure TTS密钥`
- TTS管理器可用引擎列表：`['edge_tts']`（不包含azure_tts）
- 但界面上可能仍然显示Azure TTS选项
- 点击试听时调用失败的Azure TTS导致程序崩溃

## 修复方案

### 1. 试听前安全检查（主要修复）

在 `ui/subtitle_voiceover_dialog.py` 的 `test_voice()` 方法中添加了引擎可用性检查：

```python
# 检查当前选择的TTS引擎是否可用
current_engine = self.engine_combo.currentData()
if current_engine and hasattr(self.tts_manager, 'get_available_engines'):
    available_engines = self.tts_manager.get_available_engines()
    if current_engine not in available_engines:
        if current_engine == 'azure_tts':
            QMessageBox.warning(self, "配置错误", 
                              "Azure TTS未正确配置！\n\n"
                              "请检查以下设置：\n"
                              "1. Azure API密钥是否正确填写\n"
                              "2. Azure服务区域是否正确\n"
                              "3. 网络连接是否正常\n\n"
                              "建议切换到Edge TTS引擎进行试听。")
        else:
            QMessageBox.warning(self, "引擎不可用", f"当前选择的TTS引擎 '{current_engine}' 不可用，请选择其他引擎。")
        return
```

### 2. PreviewWorker异常处理增强

在 `PreviewWorker` 的 `run()` 方法中增强了异常处理：

```python
# 安全调用TTS预览
try:
    preview_path = self.tts_manager.preview_voice(self.test_text, self.voice_id, self.speed)
except Exception as tts_error:
    # 特别处理Azure TTS的错误
    error_msg = str(tts_error)
    if "Azure" in error_msg and ("API" in error_msg or "密钥" in error_msg or "key" in error_msg.lower()):
        self.preview_failed.emit("Azure TTS配置错误：API密钥未配置或无效。请在设置中配置正确的Azure API密钥，或切换到Edge TTS引擎。")
    elif "Azure" in error_msg:
        self.preview_failed.emit(f"Azure TTS服务错误：{error_msg}。建议切换到Edge TTS引擎。")
    else:
        self.preview_failed.emit(f"TTS服务错误：{error_msg}")
    return
```

### 3. 修复效果

- ✅ **防止崩溃**：当Azure TTS未配置时，点击试听不会导致程序崩溃
- ✅ **友好提示**：显示详细的错误信息和解决建议
- ✅ **引导用户**：提示用户配置Azure TTS或切换到Edge TTS
- ✅ **正常功能**：Edge TTS试听功能不受影响

## 测试验证

### 测试环境
- 操作系统：Windows 10/11
- Python版本：支持的版本
- PyQt6：已安装

### 测试步骤
1. 启动字幕配音对话框：`python launch_subtitle_voiceover_direct.py`
2. 等待界面完全加载
3. 在TTS引擎下拉框中选择Azure TTS（如果可见）
4. 点击试听按钮
5. 观察结果：应显示错误提示而不是程序崩溃

### 预期结果
- **Azure TTS未配置时**：显示配置错误提示，程序不崩溃
- **Azure TTS已配置时**：正常试听功能
- **Edge TTS**：始终正常工作

## 使用建议

### 用户指南
1. **首次使用**：建议使用Edge TTS，无需额外配置
2. **Azure TTS配置**：
   - 在设置中正确填写Azure API密钥
   - 选择正确的服务区域
   - 确保网络连接正常
3. **故障排除**：如遇试听问题，切换到Edge TTS引擎

### 开发者注意事项
1. **异常处理**：所有TTS调用都应包含适当的异常处理
2. **用户体验**：提供清晰的错误信息和解决建议
3. **向下兼容**：保持对现有功能的兼容性

## 相关文件

### 修改的文件
- `ui/subtitle_voiceover_dialog.py` - 主要修复文件
  - `test_voice()` 方法：添加引擎可用性检查
  - `PreviewWorker.run()` 方法：增强异常处理

### 相关文件
- `plugins/tts/azure_tts_plugin.py` - Azure TTS插件
- `plugins/tts/tts_manager_plugin.py` - TTS管理器
- `launch_subtitle_voiceover_direct.py` - 直接启动器（推荐测试用）

## 版本信息

- **修复日期**：2024年12月
- **影响版本**：所有包含字幕配音功能的版本
- **修复类型**：Bug修复 + 用户体验改进
- **向下兼容**：是 