# 字幕编辑功能说明

## 功能概述

字幕配音界面现在支持实时编辑字幕内容，让您可以在配音前调整和优化字幕文本。

## 主要特性

### 1. 在线编辑
- **双击编辑**：双击字幕内容列即可进入编辑模式
- **实时保存**：编辑完成后自动保存到字幕数据
- **可视化提示**：编辑时字幕单元格会高亮显示

### 2. 智能状态管理
- **状态重置**：编辑字幕后，已配音的条目状态自动重置为"等待"
- **实时反馈**：编辑操作会在控制台显示确认信息
- **数据同步**：编辑内容自动同步到原始字幕数据

### 3. 用户界面优化
- **清晰标识**：界面标题更改为"字幕预览与编辑"
- **操作提示**：底部状态栏显示"双击内容编辑"提示
- **视觉反馈**：编辑时提供聚焦样式和边框高亮

## 使用方法

### 编辑字幕内容
1. 加载字幕文件到界面
2. 在字幕表格中找到需要修改的条目
3. 双击"字幕内容"列的文本
4. 直接输入新的字幕内容
5. 按回车键或点击其他位置完成编辑

### 编辑后配音
- 编辑过的字幕条目状态会自动重置为"⏳ 等待"
- 可以选择重新配音单个条目或全部重新配音
- 原有的成功配音不会受到影响（除非被编辑）

## 技术实现

### 表格项标志
```python
# 字幕内容列设置为可编辑
text_item.setFlags(Qt.ItemIsSelectable | Qt.ItemIsEnabled | Qt.ItemIsEditable)
```

### 编辑事件处理
```python
# 双击事件：进入编辑模式
def on_subtitle_item_double_clicked(self, item):
    if item.column() == 2:  # 字幕内容列
        self.subtitles_table.editItem(item)

# 内容变化：同步数据和重置状态  
def on_subtitle_text_changed(self, item):
    if item.column() == 2:
        self.subtitles[row]['text'] = new_text
        # 重置已配音条目的状态
```

### 样式增强
- 添加聚焦边框样式
- 编辑提示工具提示
- 状态栏编辑指导

## 注意事项

1. **仅内容可编辑**：时间戳列和状态列保持只读，确保时间轴准确性
2. **自动数据备份**：原始字幕文本存储在表格项的UserRole数据中
3. **状态一致性**：编辑后的字幕会自动重置配音状态，避免不一致
4. **实时验证**：空文本会自动恢复到原始内容

## 未来扩展

- [ ] 支持批量编辑多个字幕条目
- [ ] 添加撤销/重做功能
- [ ] 实时预览编辑效果
- [ ] 字幕格式标记支持（加粗、斜体等）
- [ ] 拼写检查和语法建议 