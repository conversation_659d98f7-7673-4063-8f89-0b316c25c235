# FlipTalk Ai 设计文档
## 1. 项目概述
FlipTalk Ai 是一款本地运行的视频翻译配音工具，主要功能包括：从视频中提取音频、分离出纯净人声、将人声转录为字幕、翻译字幕并生成目标语言语音，最后合成翻译后的视频输出。系统流程可分为：音频提取→人声分离→语音识别（WhisperX）→字幕翻译→AI 配音→时间轴对齐优化→音轨合并→视频合成。类似的开源视频配音流水线展示了这一常见流程：先提取音频，用 Whisper 进行语音转文本并翻译，然后生成目标语言配音。FlipTalk Ai 目标是提供灵活可扩展的插件式架构，支持批量处理和本地化部署，同时可调用外部服务（如 Azure TTS）以获得高质量合成效果。
## 2. 技术架构
**插件式核心架构**：采用插件（Plugin）机制组织各模块，核心系统负责协调流程，各功能模块以插件形式独立开发并注册到核心。插件架构允许添加额外功能，并使代码高度模块化和可扩展。每个插件遵循统一接口（Contract），通过约定的钩子（Hooks）与核心系统交互，系统在启动时进行插件发现与注册：扫描指定目录、读取配置文件，识别和加载符合接口的模块，然后通过依赖注入或回调将插件集成到主流程中。
**运行平台**：基于 Python + PySide6，仅支持 Windows 系统（后续计划扩展到其他平台）。核心使用面向对象的设计，将流程逻辑与 GUI 界面分离。系统配置、模型参数等使用配置文件或数据库管理，核心通过提供 API 或回调供插件调用。对外集成 Azure、WhisperX 等接口，使用策略模式或适配器模式封装第三方服务。
**资源管理**：支持检测本机 GPU 资源（例如使用 PyTorch torch.cuda.is_available()检测），在有 GPU 时优先使用硬件加速。系统使用任务队列管理并发任务，核心负责线程/进程调度，保证 UI 主线程流畅。
**模块化设计**：各模块（音频提取、分离、识别、翻译、TTS、音频处理等）均可作为独立插件开发，遵循统一的输入/输出格式规范。核心系统提供公共接口供插件注册并通信，确保新增插件后仅需最小改动即可接入。
## 3. 功能模块说明
**视频音频提取模块：**
- **职责**：接收视频文件，调用如 ffmpeg 或 moviepy 提取音频流。
- **输入/输出**：输入视频路径，输出原始音频文件（格式如 WAV）。
- **插件接口**：实现 IAudioExtractor 接口，提供 extract(video_path) -> audio_path 方法。
- **可测性**：给定测试视频文件，验证输出音频时长和内容正确。

**人声分离模块：**

- **职责**：从混合音频中分离出纯净的人声（去除背景音乐、噪声）。项目使用Demucs和CascadedNet（基于PyTorch）。
- **输入/输出**：输入原始音频，输出分离后的人声音频文件。
- **插件接口**：实现 IVoiceSeparator 接口，方法 separate(input_audio) -> vocals_audio。
- **可测性**：使用含噪声/背景音乐的音频样本，检查输出中人声特征增强。

**语音识别模块（WhisperX）：**
- **职责**：将人声音频转录为带时间戳的字幕（SRT）。使用 WhisperX 提供更精细的时间信息。
- **输入/输出**：输入人声音频文件，输出原始语言 SRT 字幕文件。
- **插件接口**：实现 IAsrEngine 接口，方法 transcribe(audio) -> subtitles_file。
- **可测性**：对已知语音测试，检查生成字幕的文本和时间戳是否与预期匹配。

**翻译模块：**
- 职责：将源语言字幕翻译为目标语言。可调用外部翻译 API（例如 deepl、微软翻译、Google freeTranslate）。
- 输入/输出：输入原始 SRT 文件和目标语言设定，输出目标语言 SRT 文件。
- 插件接口：实现 ITranslator 接口，方法 translate(source_srt, target_lang) -> target_srt。
- 可测性：使用固定的源语言字幕和目标语言，对比翻译结果文本是否符合预期。

**AI 配音（TTS）模块：**
- 职责：对每条译文字幕调用 TTS 生成配音音频。支持 Azure TTS 等可插拔引擎。
- 输入/输出：输入单句文本、语速、语音风格参数；输出该句的音频文件。
- 插件接口：实现 ITtsEngine 接口，方法 synthesize(text, voice, speed) -> audio。
- 可测性：对已知文本生成语音，检查语音内容正确并符合时长预期（人工试听或文本对比）。

**音频剪辑模块：**
- **职责**：去除每段配音首尾静音、计算新的实际时长，为时间轴调整做准备。
- **输入/输出**：输入配音音频文件，输出剪辑后的音频及其持续时间。
- **插件接口**：可作为 IAudioProcessor 插件，提供 trim_silence(audio) -> trimmed_audio, duration。
- **可测性**：对带静音段的音频进行处理，验证输出无多余静音且时长正确。

**时间轴冲突检测与加速模块：**
- **职责**：检测当前字幕段结束与下一字幕段开始的时间差，判断是否需要加速当前配音段。
- **输入/输出**：输入当前段起始时间、下段起始时间和当前段配音持续时长，输出加速倍率或标记。
- **插件接口**：实现时间轴逻辑的算法模块，可配置阈值。
- **可测性**：对特定的时长数据进行测试，验证加速倍率计算符合规则；如果倍率超过上限，字幕编辑界面要能标红提示。

**音频合并模块：**
- **职责**：按字幕时间轴顺序合并各段配音音频，生成连续的配音音轨。
- **输入/输出**：输入各段配音文件列表及对应时间轴信息，输出完整的合成音频文件。
- **插件接口**：实现 IAudioMerger 接口，方法 merge(audio_segments) -> final_audio。
- **可测性**：使用两段已知音频，对齐后合并，验证输出时长等于两段之和并无空隙。

**视频合成模块：**
- **职责**：将新生成的配音音轨与原始视频去除音轨的视频合并，生成最终输出视频。
- **输入/输出**：输入原视频文件（去除音轨）和合成音频，输出带有新配音的目标视频。
- **插件接口**：实现 IVideoSynthesizer 接口，方法 combine(video, audio) -> new_video。
- **可测性**：对短视频进行处理，检查输出视频的音频已被替换且长度不变。

各模块间通过统一的数据格式（如 SRT、音频文件路径等）交互。插件接口的明确设计使得每个模块可独立编写和测试，并可用模拟数据验证功能正确性。
## 4. 时间轴冲突检测与加速逻辑说明
针对每条字幕段执行以下逻辑：先去除静音并计算配音实际时长，然后判断是否会与下一字幕段重叠。伪代码示例如下：

```python
def voiceConnect(sourceDir, outputAndPath):
    MAX_SPEED_UP = 1.2  # 最大音频加速
    MIN_SPEED_UP = 1.05  # 最小音频加速
    MIN_GAP_DURATION = 0.1  # 最小间隔时间，单位秒。低于这个间隔时间就认为音频重叠了

    if not os.path.exists(sourceDir):
        return False
    
    srtMapFileName = "voiceMap.srt"
    srtMapFileAndPath = os.path.join(sourceDir, srtMapFileName)
    if not os.path.exists(srtMapFileAndPath):
        return False
    
    voiceMapSrtContent = ""
    with open(srtMapFileAndPath, "r", encoding="utf-8") as f:
        voiceMapSrtContent = f.read()

    # 确定音频长度
    voiceMapSrt = list(srt.parse(voiceMapSrtContent))
    duration = voiceMapSrt[-1].end.total_seconds() * 1000
    finalAudioFileAndPath = os.path.join(sourceDir, voiceMapSrt[-1].content)
    finalAudioEnd = voiceMapSrt[-1].start.total_seconds() * 1000
    finalAudioEnd += AudioSegment.from_wav(finalAudioFileAndPath).duration_seconds * 1000
    duration = max(duration, finalAudioEnd)

    diagnosisLog.write("\n<Voice connect section>", False)

    # 初始化一个空的音频段
    combined = AudioSegment.silent(duration=duration)
    for i in range(len(voiceMapSrt)):
        audioFileAndPath = os.path.join(sourceDir, voiceMapSrt[i].content)
        audio = AudioSegment.from_wav(audioFileAndPath)
        audio = audio.strip_silence(silence_thresh=-40, silence_len=100) # 去除头尾的静音
        audioPosition = voiceMapSrt[i].start.total_seconds() * 1000

        if i != len(voiceMapSrt) - 1:
            # 检查上这一句的结尾到下一句的开头之间是否有静音，如果没有则需要缩小音频
            audioEndPosition = audioPosition + audio.duration_seconds * 1000 + MIN_GAP_DURATION *1000
            audioNextPosition = voiceMapSrt[i+1].start.total_seconds() * 1000
            if audioNextPosition < audioEndPosition:
                speedUp = (audio.duration_seconds * 1000 + MIN_GAP_DURATION *1000) / (audioNextPosition - audioPosition)
                seconds = audioPosition / 1000.0
                timeStr = str(datetime.timedelta(seconds=seconds))
                if speedUp > MAX_SPEED_UP:
                    # 转换为 HH:MM:SS 格式
                    mark_subtitle_red(i)           # 超过 1.2，标红提示人工调整
                    diagnosisLog.write(logStr)
                
                # 音频如果提速一个略大于1，则speedup函数可能会出现一个错误的音频，所以这里确定最小的speedup为1.01
                if speedUp < MIN_SPEED_UP:
                    logStr = f"Warning: The audio {i+1} , at {timeStr} , speed up {speedUp} is too near to 1.0. Set to {MIN_SPEED_UP} forcibly."
                    diagnosisLog.write(logStr)
                    speedUp = MIN_SPEED_UP
                audio = audio.speedup(playback_speed=speedUp)

        combined = combined.overlay(audio, position=audioPosition)
    
    combined.export(outputAndPath, format="wav")
    return True

```
- **去静音**：调用音频处理模块去掉首尾的无声部分，并重新计算段长。
- **倍率计算**：公式 (duration + 0.1) / (start_next - start_current)，保证加入 0.1 秒缓冲。如计算结果低于 1.01，则取 1.01；高于 1.2 则标红字幕行提示需人工校正。
- **后续处理**：对超时或超长的配音段，可提示人工编辑字幕文本或时长。调整后重新生成该段配音并更新合并。
该逻辑确保生成的配音尽量贴合原视频的节奏，同时超出范围时引导人工优化。
## 5. UI 界面设计结构与导航逻辑
- **主界面总体布局**：采用多页 (QStackedWidget) 或选项卡布局。左侧或顶部菜单导航不同功能模块，右侧显示对应界面。支持亮/暗主题切换后自动调整样式和图标颜色。
- **参数配置界面**：提供下拉框/输入框配置关键参数，如选取 ASR 模型、翻译引擎、TTS 语音参数等。可保存为预设。
- **视频上传界面**：允许用户批量选择视频文件或拖拽上传，显示上传列表。用户可为每个任务设置目标语言等参数。
- **任务列表界面**：显示所有待处理和正在处理的任务，用表格或卡片列出任务名称、进度条、状态（排队/进行中/完成/出错）。支持暂停/取消操作。
- **字幕编辑界面**：以表格形式显示当前翻译字幕：字段包括序号、原文、译文、起止时间、加速倍率、状态（正常/需调整）。对于标红提示的行，表格会高亮或文字标红，提醒用户人工校正。支持双击编辑译文或时间。
- **配音试听界面**：选中字幕行后可播放对应原文或译文的语音供试听，或比较原声与合成声。界面提供播放/暂停和音量控制按钮。
- **API/系统配置界面**：集中管理各类系统配置，如 Azure API 密钥、GPU 使用设置、插件路径等。可设置日志等级和常用路径。
- **导航逻辑**：主窗口通过侧边栏或顶部菜单切换不同页面。执行长任务时，通过信号槽更新任务列表和进度条。整个 GUI 使用 MVC/MVVM 思想分离界面与逻辑层，界面仅处理用户交互和视图更新，业务逻辑放入后台模块。

## 6. 图标绘制模块说明（QPainter 亮/暗主题）
所有图标采用 Qt 的 QPainter 绘制或绘制在 *.qrc 资源中，以确保在亮暗主题间一致性。图标模块可包含如下要点：
- **矢量化绘制**：使用 QPainter 绘制可缩放的图形（线条、形状、文字），避免位图模糊。
- **主题适配**：根据当前主题选择前景色和背景色，例如在暗色主题下图标线条使用浅色，在亮色主题下使用深色。可通过查询 QPalette 或应用样式表动态调整颜色。
- **集成与重用**：为常用操作（开始、停止、设置等）定义统一的绘制函数或类，保证风格一致。所有图标都由此模块生成，放在资源(resources/icons)中供界面加载使用。
## 7. 测试设计
- **单元测试**：各模块独立测试。如对音频提取模块、分离模块、ASR 结果解析、翻译结果等编写单元测试，使用 pytest 或 unittest。Qt 文档建议：PySide6 中常规业务逻辑使用 pytest 进行单元测试。测试用例包含正常流程和异常流程，确保接口返回正确结果。
- **端到端测试**：构造小型测试视频，执行完整管道，验证最终输出视频语音、字幕与原视频时长和内容的一致性。可通过对比提取的字幕内容或音频波形检查转换准确性。
- **集成测试**：测试插件注册加载、任务调度与GUI交互。可模拟用户界面操作（如自动化脚本）验证各界面响应。
- **持续集成**：建议使用 CI 工具自动运行所有测试，保证每次提交时都执行测试套件。结合覆盖率工具评估测试完整性。正如最佳实践所示，编写单元测试有助于提高代码正确性，也建议结合 CI 自动化执行。
## 8. 多线程与任务调度策略
- **线程模型**：GUI 主线程负责界面渲染和用户交互。所有耗时操作（音频处理、ASR、翻译、TTS 等）都应在后台线程或线程池中执行，以避免界面卡死。如所述：“解决方案是将耗时任务移出 GUI 线程到其他线程”。可使用 QThreadPool+QRunnable 或 Python 的 concurrent.futures 结合 Qt 的 QThread。
- **任务队列**：维护一个任务队列或线程池，用户添加的新视频任务排入队列。系统根据资源情况（如 CPU、GPU、内存占用）并行处理多个任务。可设置最大并发数以防止资源竞争。
- **进度与状态报告**：后台线程通过信号（Signal/Slot）向主线程报告当前进度和状态变化（开始、完成、错误等），主界面在任务列表中更新进度条和状态文本。
- **多核/多GPU 支持**：如果检测到多个 GPU，可为不同任务分配不同 GPU，通过环境变量或 API 显式指定。对于纯 CPU 模型，可使用 Python 的 multiprocessing 在多核上并行处理。
- **异常处理**：任务线程需捕获异常并返回错误信息，主界面弹窗提示用户或在任务列表标记失败。
## 9. 可扩展性设计
**插件拓展**：新增功能时只需编写满足接口规范的新插件，并将其放入插件目录，无需修改核心。譬如新增一种更佳的人声分离算法，只需实现 IVoiceSeparator 并更新配置即可使用。
**可替换模块**：翻译、TTS 等模块通过接口抽象，可轻松切换实现。例如通过配置切换翻译引擎（Azure、Google、OpenAI 等）或 TTS 引擎（Azure、Coqui、Edge TTS 等）。核心通过工厂模式实例化所选插件。
**动态加载**：应用启动或运行中可动态检测插件目录并热加载插件，或者在配置界面提供“插件管理”功能，允许用户启用/禁用特定插件。
**版本兼容**：为插件接口和数据格式定义版本号，确保升级时能够兼容老插件或提示更新。
**增强功能**：未来可在此架构下增加更多功能模块，如唇形同步、文本校对、AI字幕生成等。只要符合插件设计规范，模块即可无缝集成。
## 10. 初步项目目录结构建议
项目结构应按功能和模块组织。参考常见 PySide 项目布局，可参考下列示例：

    FlipTalk/                        
    ├── main.py                   # 应用程序入口（初始化窗口和核心逻辑）  
    ├── requirements.txt          # 依赖列表 
    ├── models/                   # 下载的模型列表，如whisper模型  
    ├── core/                     # 核心逻辑和框架代码（任务调度、插件管理等）  
    │   ├── plugin_manager.py  
    │   ├── task_scheduler.py  
    │   └── config.py  
    ├── plugins/                  # 各类插件文件夹  
    │   ├── audio_extractor/      # 视频音频提取插件  
    │   ├── voice_separator/      # 人声分离插件  
    │   ├── asr/                  # 语音识别(WhisperX)插件  
    │   ├── translator/           # 翻译引擎插件  
    │   ├── tts/                  # AI 配音引擎插件  
    │   ├── audio_processor/      # 音频剪辑/合并插件  
    │   └── video_synthesizer/    # 视频合成插件  
    ├── ui/                       # UI 界面代码  
    │   ├── main_window.py  
    │   ├── dialogs/              # 弹窗和子窗口  
    │   └── resources/            # Qt Designer .ui 文件或生成代码  
    ├── resources/                # 资源文件（图标、样式表等）  
    │   ├── icons/  
    │   ├── styles/               # .qss 样式表  
    │   └── samples/              # 示例测试视频/音频  
    ├── tests/                    # 测试代码  
    │   ├── unit/                 # 单元测试  
    │   └── integration/          # 集成/端到端测试  
    └── docs/                     # 设计文档、说明文件  