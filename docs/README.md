# FlipTalk AI - 视频翻译软件

## 项目简介

FlipTalk AI 是一款桌面端视频翻译软件，专注于视频内容的自动翻译和配音处理。软件提供了完整的视频处理工作流，从视频上传到最终输出翻译配音后的视频文件。

## 主要功能

### 核心处理流程
1. **视频上传** - 支持拖拽和点击上传多种视频格式（mp4、mov、mkv等）
2. **音频提取** - 从视频中提取音频，去除背景音乐，保留纯人声
3. **语音识别** - 使用先进的AI模型将音频转换为字幕文本
4. **字幕翻译** - 智能翻译字幕内容到目标语言
5. **语音合成** - 为翻译后的字幕生成自然的语音配音
6. **音频处理** - 去除静音、时间轴冲突检测和自动加速
7. **手工精修** - 支持字幕内容和时间戳的手动编辑
8. **视频合成** - 将处理后的音频与原视频合并生成最终文件

### 界面特色
- **现代化UI设计** - 采用深色主题，圆角设计，视觉效果优秀
- **实时处理监控** - 显示文件处理进度和状态
- **GPU加速支持** - 智能检测GPU状态，优化处理性能
- **批量处理能力** - 支持同时处理多个视频文件
- **智能冲突检测** - 自动检测和解决时间轴冲突问题

## 安装说明

### 环境要求
- Python 3.8 或更高版本
- Windows 10/11 操作系统
- NVIDIA GPU（可选，用于加速处理）

### 安装步骤

1. **克隆项目**
```bash
git clone https://github.com/your-repo/fliptalk-ai.git
cd fliptalk-ai
```

2. **安装依赖**
```bash
pip install -r requirements.txt
```

3. **安装FFmpeg**（必需）
下载并安装 FFmpeg，确保添加到系统PATH环境变量

4. **配置GPU支持**（可选）
如果有NVIDIA GPU，请安装CUDA支持的PyTorch版本

## 使用方法

### 启动软件
```bash
python fliptalk_ui.py
```

### 基本操作流程

1. **上传视频**
   - 将视频文件拖拽到上传区域
   - 或点击上传区域选择文件

2. **开始处理**
   - 点击"开始处理"按钮启动自动处理流程
   - 在处理列表中监控进度

3. **字幕编辑**
   - 在右侧字幕编辑区查看和编辑字幕
   - 被标记为红色的字幕需要人工处理
   - 可编辑字幕文本和时间戳

4. **保存输出**
   - 点击"保存字幕"保存字幕文件
   - 点击"合并音频"生成最终视频

## 界面说明

### 导航栏（左侧）
- **首页** - 主要处理界面
- **功能** - 功能设置页面
- **API设置** - 配置AI模型API
- **系统设置** - 系统参数配置
- **运行日志** - 查看处理日志

### 视频处理区（中间）
- **软件标题栏** - 显示软件名称和参数配置入口
- **上传区域** - 拖拽或点击上传视频文件
- **处理列表** - 显示文件处理状态和进度

### 字幕编辑区（右侧）
- **状态栏** - 显示GPU状态和用户信息
- **字幕列表** - 编辑字幕内容和时间戳
- **操作按钮** - 保存字幕和合并音频

## 技术架构

### UI框架
- **PySide6** - 现代化的Python GUI框架
- **自定义组件** - 圆角矩形、拖拽上传等组件

### 核心算法
- **Whisper** - 语音识别模型
- **Transformers** - 文本翻译模型
- **TTS引擎** - 语音合成系统

### 视频处理
- **FFmpeg** - 视频音频处理
- **OpenCV** - 视频分析和处理
- **MoviePy** - Python视频编辑库

## 配色方案

软件采用专业的深色主题配色：

- `#00DD65` - 主色调（按钮、重要元素）
- `#000000` - 模块背景色
- `#1F1F1F` - 基础背景色
- `#FFFFFF` - 文字颜色
- `#FF0000` - 警告标记色

## 开发说明

### 代码结构
```
fliptalk-ai/
├── fliptalk_ui.py          # 主UI界面文件
├── requirements.txt        # 项目依赖
├── README.md              # 项目说明
├── .cursor/               # Cursor编辑器规则
│   └── rules/
│       └── chinese-comments.mdc
└── assets/                # 资源文件目录
```

### 扩展开发
- 所有组件继承自 `RoundedWidget` 基类
- 使用模块化设计，便于功能扩展
- 支持自定义主题和配色方案

## 注意事项

1. **GPU支持** - 软件会自动检测GPU状态，有GPU时处理速度更快
2. **文件格式** - 目前支持mp4、mov、mkv、avi等主流视频格式
3. **处理时间** - 处理时间取决于视频长度和硬件性能
4. **存储空间** - 确保有足够的磁盘空间存储临时文件和输出文件

## 联系我们

如有问题或建议，请联系开发团队。

---

**FlipTalk AI** - 让视频翻译变得简单高效！ 