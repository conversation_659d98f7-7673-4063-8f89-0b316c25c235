# FlipTalk AI - 语速控制修复说明

## 🔍 问题诊断

### 原始问题
用户反馈：即使调整了配音的语速，但实际生成的配音速度依然是默认的1.0。

### 根本原因
通过深入分析 edge-tts-master 项目源码，发现FlipTalk AI中的Edge TTS插件实现存在以下问题：

1. **错误的参数传递方式**: 原代码忽略了语速参数，强制使用默认值
2. **错误的API调用**: 没有使用Edge TTS的正确参数格式
3. **参数格式转换缺失**: 未将数值倍数转换为Edge TTS要求的百分比格式

## 🔧 修复详情

### 关键发现
通过分析edge-tts源码发现，正确的调用方式是：

```python
# 正确的调用方式
communicate = edge_tts.Communicate(
    text=text,
    voice=voice,
    rate="+50%",    # 语速：百分比格式，如 "-50%", "+0%", "+100%"
    volume="+20%",  # 音量：百分比格式  
    pitch="+50Hz"   # 音调：Hz格式，如 "-30Hz", "+0Hz", "+100Hz"
)
```

### 修复内容

#### 1. 参数格式转换函数

新增三个转换函数：

```python
def _convert_speed_to_rate(self, speed: float) -> str:
    """将速度倍数转换为Edge TTS的rate参数格式"""
    # 0.5倍 → "-50%", 1.0倍 → "+0%", 1.5倍 → "+50%", 2.0倍 → "+100%"
    
def _convert_volume_to_volume(self, volume: float) -> str:
    """将音量倍数转换为Edge TTS的volume参数格式"""
    
def _convert_pitch_to_pitch(self, pitch: float) -> str:
    """将音调偏移转换为Edge TTS的pitch参数格式"""
```

#### 2. 修复合成函数

**修复前**:
```python
# 错误：忽略所有参数，只使用默认值
communicate = edge_tts.Communicate(text, voice)
```

**修复后**:
```python
# 正确：传递所有参数
communicate = edge_tts.Communicate(
    text=text,
    voice=voice,
    rate=self._convert_speed_to_rate(speed),
    volume=self._convert_volume_to_volume(volume),
    pitch=self._convert_pitch_to_pitch(pitch)
)
```

#### 3. 修复预览和批量处理

- `preview_voice()`: 现在支持完整的参数调整
- `synthesize_batch()`: 支持批量处理时的参数控制
- 缓存机制更新：包含所有参数的hash值

## ✅ 验证结果

### 测试数据
通过实际测试验证语速控制效果（以文件大小反映音频长度）：

| 语速设置 | 倍数 | Edge TTS格式 | 文件大小 | 效果 |
|---------|------|-------------|----------|------|
| 慢速     | 0.5  | -50%        | 67824字节 | ✅ 音频最长 |
| 正常     | 1.0  | +0%         | 34128字节 | ✅ 标准长度 |
| 快速     | 1.5  | +50%        | 22896字节 | ✅ 音频变短 |
| 更快     | 2.0  | +100%       | 17280字节 | ✅ 音频最短 |

### 综合参数测试
同时验证了语速、音量、音调的组合控制：
- **快速高音**: 语速1.5倍 + 音量1.2倍 + 音调+50Hz ✅
- **慢速低音**: 语速0.7倍 + 音量0.8倍 + 音调-30Hz ✅  
- **正常音量大**: 语速1.0倍 + 音量1.5倍 + 音调0Hz ✅

## 📋 使用说明

### 语速参数格式
- **倍数格式**: `0.5` (慢50%) | `1.0` (正常) | `1.5` (快50%) | `2.0` (快100%)
- **自动转换**: 插件自动转换为Edge TTS要求的百分比格式

### API调用示例

```python
from plugins.tts.edge_tts_plugin import EdgeTtsPlugin

tts = EdgeTtsPlugin()
tts.initialize({})

# 基础语速控制
result = tts.synthesize(
    text="测试语速控制",
    voice="zh-CN-XiaoxiaoNeural", 
    speed=1.5  # 快50%
)

# 完整参数控制  
result = tts.synthesize(
    text="测试综合参数",
    voice="zh-CN-XiaoxiaoNeural",
    speed=1.2,    # 快20%
    volume=1.1,   # 音量增10%
    pitch=20.0    # 音调升高20Hz
)

# 试听功能
preview = tts.preview_voice(
    text="试听测试", 
    voice="zh-CN-XiaoxiaoNeural",
    speed=0.8     # 慢20%
)
```

## 🎯 影响范围

### 修复的功能
1. ✅ **试听功能**: 现在正确反映语速设置
2. ✅ **批量配音**: 支持自定义语速的批量处理  
3. ✅ **实时预览**: UI中的语速滑块现在生效
4. ✅ **参数缓存**: 基于完整参数的智能缓存

### 向后兼容性
- ✅ 保持原有API接口不变
- ✅ 默认参数行为保持一致
- ✅ 配置文件格式无需改动

## 🔄 迁移说明

### 用户操作
1. 无需手动迁移，修复自动生效
2. 重启应用后，语速调节功能正常工作
3. 可以删除之前生成的缓存文件以确保使用新的参数格式

### 开发者注意
如果有其他TTS插件，建议参照此修复方案：
1. 检查参数传递是否正确
2. 确保参数格式转换符合相应TTS服务要求
3. 更新缓存键值包含所有影响音频的参数

## 📊 性能影响

- **计算开销**: 增加的参数转换函数开销极小
- **存储影响**: 缓存文件名包含更多参数，有助于精确缓存
- **网络请求**: 无额外网络开销，仅参数传递优化

## 🚀 后续优化建议

1. **参数验证**: 可增加参数范围验证（如语速0.1-3.0倍）
2. **预设配置**: 可添加语速预设（如"播客模式"、"快读模式"）
3. **UI改进**: 可在UI中显示实际的Edge TTS参数值
4. **错误处理**: 可添加参数超出范围时的友好提示 