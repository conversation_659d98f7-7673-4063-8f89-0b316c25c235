#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试保留背景音乐功能
"""

import sys
import os
import tempfile
import shutil

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def create_test_audio_files():
    """创建测试用的音频文件"""
    try:
        from pydub import AudioSegment
        from pydub.generators import Sine
        
        # 创建临时目录
        temp_dir = tempfile.mkdtemp(prefix="fliptalk_test_")
        print(f"📁 创建测试目录: {temp_dir}")
        
        # 创建子目录
        voice_separation_dir = os.path.join(temp_dir, "voice_separation")
        voiceover_dir = os.path.join(temp_dir, "voiceover")
        synthesis_dir = os.path.join(temp_dir, "synthesis")
        
        os.makedirs(voice_separation_dir, exist_ok=True)
        os.makedirs(voiceover_dir, exist_ok=True)
        os.makedirs(synthesis_dir, exist_ok=True)
        
        # 创建测试背景音乐文件（440Hz正弦波，10秒）
        background_music = Sine(440).to_audio_segment(duration=10000)  # 10秒
        background_music = background_music - 15  # 降低音量到 -15dB
        background_path = os.path.join(voice_separation_dir, "test_background.wav")
        background_music.export(background_path, format="wav")
        print(f"🎵 创建测试背景音乐: {background_path}")
        
        # 创建测试配音文件（880Hz正弦波，5秒）
        voiceover_audio = Sine(880).to_audio_segment(duration=5000)  # 5秒
        voiceover_audio = voiceover_audio - 10  # 降低音量到 -10dB
        voiceover_path = os.path.join(synthesis_dir, "test_voiceover.wav")
        voiceover_audio.export(voiceover_path, format="wav")
        print(f"🎤 创建测试配音文件: {voiceover_path}")
        
        return temp_dir, background_path, voiceover_path
        
    except ImportError:
        print("❌ 缺少 pydub 库，无法创建测试音频文件")
        return None, None, None
    except Exception as e:
        print(f"❌ 创建测试音频文件失败: {e}")
        return None, None, None

def test_background_music_validation():
    """测试背景音乐验证功能"""
    print("🧪 开始测试背景音乐验证功能...")
    
    try:
        from PySide6.QtWidgets import QApplication
        from ui.fliptalk_ui import FlipTalkMainWindow
        
        # 创建应用程序
        app = QApplication(sys.argv)
        
        # 创建主窗口
        main_window = FlipTalkMainWindow()
        
        # 创建测试音频文件
        temp_dir, background_path, voiceover_path = create_test_audio_files()
        if not temp_dir:
            print("❌ 无法创建测试文件，跳过测试")
            return False
        
        # 设置输出路径
        if hasattr(main_window, 'parameter_panel') and hasattr(main_window.parameter_panel, 'output_path_edit'):
            main_window.parameter_panel.output_path_edit.setText(temp_dir)
            print(f"✅ 设置输出路径: {temp_dir}")
        
        # 测试1: 未启用人声分离时的验证
        print("\n📋 测试1: 未启用人声分离时的验证")
        if hasattr(main_window.parameter_panel, 'voice_separation_checkbox'):
            main_window.parameter_panel.voice_separation_checkbox.setChecked(False)
        
        result = main_window.validate_background_music_requirements()
        print(f"验证结果: {result}")
        assert not result['valid'], "应该验证失败（未启用人声分离）"
        print("✅ 测试1通过")
        
        # 测试2: 启用人声分离但无背景音乐文件时的验证
        print("\n📋 测试2: 启用人声分离但无背景音乐文件时的验证")
        if hasattr(main_window.parameter_panel, 'voice_separation_checkbox'):
            main_window.parameter_panel.voice_separation_checkbox.setChecked(True)
        
        # 删除背景音乐文件
        if os.path.exists(background_path):
            os.remove(background_path)
        
        result = main_window.validate_background_music_requirements()
        print(f"验证结果: {result}")
        assert not result['valid'], "应该验证失败（无背景音乐文件）"
        print("✅ 测试2通过")
        
        # 测试3: 条件满足时的验证
        print("\n📋 测试3: 条件满足时的验证")
        
        # 重新创建背景音乐文件
        temp_dir2, background_path2, _ = create_test_audio_files()
        if background_path2:
            # 复制到正确位置
            shutil.copy2(background_path2, background_path)
        
        result = main_window.validate_background_music_requirements()
        print(f"验证结果: {result}")
        assert result['valid'], "应该验证成功（条件满足）"
        print("✅ 测试3通过")
        
        # 测试4: 背景音乐混合功能
        print("\n📋 测试4: 背景音乐混合功能")
        mixed_path = main_window.mix_background_music(voiceover_path, temp_dir)
        if mixed_path and os.path.exists(mixed_path):
            print(f"✅ 背景音乐混合成功: {mixed_path}")
            
            # 验证混合后的文件
            try:
                from pydub import AudioSegment
                mixed_audio = AudioSegment.from_wav(mixed_path)
                print(f"📊 混合音频: 长度={len(mixed_audio)/1000:.2f}s, 音量={mixed_audio.dBFS:.1f}dB")
                assert len(mixed_audio) > 0, "混合音频长度应该大于0"
                print("✅ 测试4通过")
            except Exception as e:
                print(f"❌ 验证混合音频失败: {e}")
                return False
        else:
            print("❌ 背景音乐混合失败")
            return False
        
        # 清理测试文件
        try:
            shutil.rmtree(temp_dir)
            if temp_dir2:
                shutil.rmtree(temp_dir2)
            print("🧹 测试文件已清理")
        except Exception as e:
            print(f"⚠️ 清理测试文件失败: {e}")
        
        print("\n✅ 所有测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_checkbox_interaction(main_window):
    """测试复选框交互功能"""
    print("\n🧪 开始测试复选框交互功能...")

    try:
        # 检查复选框是否存在
        if not hasattr(main_window, 'composition_checkboxes'):
            print("❌ 未找到合成设置复选框")
            return False

        if 'keep_background_music' not in main_window.composition_checkboxes:
            print("❌ 未找到保留背景音乐复选框")
            return False

        checkbox = main_window.composition_checkboxes['keep_background_music']
        print("✅ 找到保留背景音乐复选框")

        # 测试复选框状态变化
        print("📋 测试复选框状态变化...")

        # 模拟勾选（应该失败，因为条件不满足）
        checkbox.setChecked(True)

        # 检查是否被自动取消勾选
        if not checkbox.isChecked():
            print("✅ 复选框正确地被自动取消勾选（条件不满足）")
        else:
            print("⚠️ 复选框未被自动取消勾选")

        print("✅ 复选框交互测试完成")
        return True

    except Exception as e:
        print(f"❌ 复选框交互测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 开始测试保留背景音乐功能")
    print("=" * 50)
    
    # 测试背景音乐验证功能
    validation_success = test_background_music_validation()

    # 如果验证测试成功，使用同一个应用实例测试复选框交互
    interaction_success = False
    if validation_success:
        try:
            from ui.fliptalk_ui import FlipTalkMainWindow
            # 重用已有的应用实例
            main_window = FlipTalkMainWindow()
            interaction_success = test_checkbox_interaction(main_window)
        except Exception as e:
            print(f"❌ 复选框交互测试初始化失败: {e}")
            interaction_success = False
    
    print("\n" + "=" * 50)
    print("📋 测试总结:")
    print(f"  背景音乐验证功能: {'✅ 通过' if validation_success else '❌ 失败'}")
    print(f"  复选框交互功能: {'✅ 通过' if interaction_success else '❌ 失败'}")
    
    if validation_success and interaction_success:
        print("\n🎉 所有测试通过！保留背景音乐功能已成功实现。")
        print("\n💡 使用说明:")
        print("1. 在参数设置中启用'人声分离'")
        print("2. 上传视频并执行人声分离")
        print("3. 在合成设置中勾选'保留背景音乐'")
        print("4. 执行视频合成，系统会自动混合背景音乐")
        return 0
    else:
        print("\n❌ 部分测试失败，请检查实现。")
        return 1

if __name__ == "__main__":
    sys.exit(main())
