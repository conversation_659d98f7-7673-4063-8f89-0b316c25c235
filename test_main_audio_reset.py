#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试主程序中的音频播放完成后自动重置功能
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_audio_reset_functionality():
    """测试音频自动重置功能"""
    print("🧪 开始测试音频播放完成后自动重置功能...")
    
    try:
        from PySide6.QtWidgets import QApplication
        from PySide6.QtMultimedia import QMediaPlayer
        from ui.fliptalk_ui import FlipTalkMainWindow
        
        # 创建应用程序
        app = QApplication(sys.argv)
        
        # 创建主窗口
        main_window = FlipTalkMainWindow()
        
        # 检查字幕音频播放器是否存在
        if hasattr(main_window, 'video_upload_area') and hasattr(main_window.video_upload_area, 'subtitle_audio_player'):
            player = main_window.video_upload_area.subtitle_audio_player
            print("✅ 找到字幕音频播放器")
            
            # 检查信号连接
            print("🔍 检查信号连接...")
            
            # 检查 mediaStatusChanged 信号连接
            status_connections = player.mediaStatusChanged.receivers()
            print(f"📡 mediaStatusChanged 信号连接数: {status_connections}")
            
            # 检查 playbackStateChanged 信号连接
            playback_connections = player.playbackStateChanged.receivers()
            print(f"📡 playbackStateChanged 信号连接数: {playback_connections}")
            
            # 检查主窗口是否有对应的处理方法
            if hasattr(main_window, 'on_subtitle_audio_status_changed'):
                print("✅ 主窗口有 on_subtitle_audio_status_changed 方法")
            else:
                print("❌ 主窗口缺少 on_subtitle_audio_status_changed 方法")
                
            if hasattr(main_window, 'on_subtitle_audio_playback_state_changed'):
                print("✅ 主窗口有 on_subtitle_audio_playback_state_changed 方法")
            else:
                print("❌ 主窗口缺少 on_subtitle_audio_playback_state_changed 方法")
                
            if hasattr(main_window, 'reset_all_play_buttons'):
                print("✅ 主窗口有 reset_all_play_buttons 方法")
            else:
                print("❌ 主窗口缺少 reset_all_play_buttons 方法")
            
            # 模拟播放完成事件
            print("\n🎵 模拟音频播放完成事件...")
            try:
                # 直接调用状态变化处理方法
                main_window.on_subtitle_audio_status_changed(QMediaPlayer.MediaStatus.EndOfMedia)
                print("✅ 成功调用 on_subtitle_audio_status_changed(EndOfMedia)")
                
                # 调用播放状态变化处理方法
                main_window.on_subtitle_audio_playback_state_changed(QMediaPlayer.PlaybackState.StoppedState)
                print("✅ 成功调用 on_subtitle_audio_playback_state_changed(StoppedState)")
                
            except Exception as e:
                print(f"❌ 模拟事件调用失败: {e}")
                import traceback
                traceback.print_exc()
            
            # 检查 VideoUploadArea 的信号转发方法
            if hasattr(main_window.video_upload_area, 'get_main_window'):
                test_main_window = main_window.video_upload_area.get_main_window()
                if test_main_window == main_window:
                    print("✅ VideoUploadArea 能正确找到主窗口")
                else:
                    print("❌ VideoUploadArea 无法正确找到主窗口")
            else:
                print("❌ VideoUploadArea 缺少 get_main_window 方法")
            
        else:
            print("❌ 未找到字幕音频播放器")
            
        print("\n📋 测试总结:")
        print("1. 音频播放完成后自动重置功能的实现已经存在")
        print("2. 信号连接机制已经建立")
        print("3. 主窗口有相应的处理方法")
        print("4. 如果功能不工作，可能的原因:")
        print("   - 信号连接时机问题")
        print("   - 音频播放器状态检测问题")
        print("   - UI更新时机问题")
        
        print("\n🔧 建议的解决方案:")
        print("1. 确保在主窗口初始化完成后重新建立信号连接")
        print("2. 添加更多调试日志来跟踪信号传递")
        print("3. 检查音频播放器的状态变化是否正确触发")
        
        # 不显示窗口，直接退出
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    success = test_audio_reset_functionality()
    
    if success:
        print("\n✅ 测试完成")
        print("\n💡 要验证实际功能，请:")
        print("1. 启动主程序")
        print("2. 上传视频并提取字幕")
        print("3. 生成字幕配音")
        print("4. 点击播放按钮播放音频")
        print("5. 观察音频播放完成后按钮是否自动恢复到默认状态")
    else:
        print("\n❌ 测试失败")
        
    return 0 if success else 1


if __name__ == "__main__":
    sys.exit(main())
