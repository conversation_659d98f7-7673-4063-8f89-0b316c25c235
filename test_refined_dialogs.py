#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试精致版苹果风格弹窗
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_refined_dialogs():
    """测试精致版苹果风格弹窗"""
    try:
        from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton
        from PySide6.QtCore import Qt
        from ui.fliptalk_ui import AppleMessageBox
        
        # 创建应用程序
        app = QApplication(sys.argv)
        
        # 创建测试窗口
        window = QMainWindow()
        window.setWindowTitle("精致版苹果风格弹窗测试")
        window.setFixedSize(350, 280)
        window.setStyleSheet("""
            QMainWindow {
                background-color: #1C1C1E;
            }
            QPushButton {
                background-color: #0A84FF;
                color: white;
                border: none;
                border-radius: 6px;
                font-size: 13px;
                font-weight: 500;
                padding: 8px 16px;
                margin: 4px;
                min-height: 28px;
            }
            QPushButton:hover {
                background-color: #0056CC;
            }
        """)
        
        # 创建中央部件
        central_widget = QWidget()
        window.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        layout.setSpacing(8)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # 创建测试按钮
        info_btn = QPushButton("📘 信息提示 (精致版)")
        info_btn.clicked.connect(lambda: AppleMessageBox.show_info(
            window, "FlipTalk AI 已优化", 
            "弹窗设计已成功优化！\n\n"
            "• 更紧凑的尺寸\n"
            "• 精致的按钮设计\n"
            "• 优雅的间距布局"
        ))
        
        warning_btn = QPushButton("⚠️ 警告提示 (精致版)")
        warning_btn.clicked.connect(lambda: AppleMessageBox.show_warning(
            window, "注意事项",
            "请注意以下重要信息：\n\n"
            "弹窗尺寸已优化为更合适的大小，\n"
            "提供更好的用户体验。"
        ))
        
        error_btn = QPushButton("❌ 错误提示 (精致版)")
        error_btn.clicked.connect(lambda: AppleMessageBox.show_error(
            window, "操作失败",
            "操作执行失败。\n\n"
            "请检查设置后重试。"
        ))
        
        success_btn = QPushButton("✅ 成功提示 (精致版)")
        success_btn.clicked.connect(lambda: AppleMessageBox.show_success(
            window, "操作成功",
            "操作已成功完成！\n\n"
            "弹窗设计更加精致美观。"
        ))
        
        question_btn = QPushButton("❓ 确认对话框 (精致版)")
        question_btn.clicked.connect(lambda: test_refined_question(window))
        
        # 添加按钮到布局
        layout.addWidget(info_btn)
        layout.addWidget(warning_btn)
        layout.addWidget(error_btn)
        layout.addWidget(success_btn)
        layout.addWidget(question_btn)
        layout.addStretch()
        
        # 显示窗口
        window.show()
        
        print("🎨 精致版苹果风格弹窗测试程序已启动")
        print("点击按钮测试优化后的弹窗效果")
        print("\n✨ 优化内容:")
        print("• 弹窗尺寸: 320-480px → 280-360px")
        print("• 按钮高度: 44px → 32px")
        print("• 圆角半径: 14px → 12px")
        print("• 内边距: 更紧凑的间距")
        print("• 字体大小: 更合适的尺寸")
        
        return app.exec()
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return 1

def test_refined_question(parent):
    """测试精致版确认对话框"""
    try:
        from ui.fliptalk_ui import AppleMessageBox
        
        result = AppleMessageBox.show_question(
            parent, "确认操作",
            "您确定要应用这些优化吗？\n\n"
            "这将使弹窗更加精致美观。",
            "确定", "取消"
        )
        
        if result:
            AppleMessageBox.show_success(parent, "优化完成", "弹窗设计已成功优化！")
        else:
            AppleMessageBox.show_info(parent, "操作取消", "已取消优化操作。")
            
    except Exception as e:
        print(f"❌ 确认对话框测试失败: {e}")

def compare_sizes():
    """对比优化前后的尺寸"""
    print("📏 弹窗尺寸对比:")
    print("=" * 40)
    print("项目                 优化前      优化后")
    print("-" * 40)
    print("弹窗最小宽度         320px       280px")
    print("弹窗最大宽度         480px       360px")
    print("按钮最小高度         44px        32px")
    print("圆角半径             14px        12px")
    print("标题字体大小         16px        15px")
    print("内容字体大小         14px        13px")
    print("按钮字体大小         16px        14px")
    print("标题内边距           20-24px     18-20px")
    print("内容内边距           24px        20px")
    print("按钮内边距           12-24px     8-16px")
    print("按钮外边距           8-12px      6-8px")
    print("-" * 40)
    print("总体效果: 更紧凑、更精致、更美观")

def main():
    """主函数"""
    print("🚀 精致版苹果风格弹窗测试")
    print("=" * 50)
    
    # 显示尺寸对比
    compare_sizes()
    
    print("\n" + "=" * 50)
    print("🎨 启动弹窗测试...")
    
    # 测试弹窗
    result = test_refined_dialogs()
    
    print("\n" + "=" * 50)
    if result == 0:
        print("✅ 精致版弹窗测试完成")
        print("\n💡 优化亮点:")
        print("• 弹窗尺寸更加合理，不会过大")
        print("• 按钮高度适中，符合现代设计")
        print("• 间距布局更加紧凑精致")
        print("• 字体大小层次分明")
        print("• 整体视觉更加协调美观")
    else:
        print("❌ 测试过程中出现问题")
    
    return result

if __name__ == "__main__":
    sys.exit(main())
