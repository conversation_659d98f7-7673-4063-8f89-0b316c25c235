# 字幕配音功能测试

## 概述

字幕配音功能提供AI智能语音合成，支持多种TTS引擎和语音效果。

## 文件结构

- `ui/subtitle_voiceover_dialog.py` - 字幕配音对话框UI组件（主要实现）
- `tests/test_subtitle_voiceover.py` - 独立测试脚本

## 使用方式

### 1. 在主应用程序中使用

通过FlipTalk主界面的"功能区域" -> "字幕配音"功能卡片进入。

### 2. 独立测试运行

```bash
# 在项目根目录下运行
python tests/test_subtitle_voiceover.py
```

## 功能特性

- ✅ 支持多种TTS引擎（Edge TTS、Azure TTS等）
- ✅ 多语言多声音选择
- ✅ 语速调节和实时试听
- ✅ 异步初始化，快速启动
- ✅ 缓存机制，提升试听体验
- ✅ 拖拽上传字幕文件
- ✅ 批量生成配音文件
- ✅ 支持SRT、VTT、ASS格式

## 依赖要求

```
edge-tts
azure-cognitiveservices-speech
PySide6
```

## 注意事项

1. 首次启动可能需要下载语音模型，请保持网络连接
2. Azure TTS需要配置有效的API密钥
3. 生成的音频文件默认保存在output/voiceover目录 