def create_status_card(self, icon, title, value, accent_color):
    """创建美观的状态卡片"""
    card = QFrame()
    card.setFixedSize(320, 100)
    card.setStyleSheet(f"""
        QFrame {{
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 rgba(42, 42, 42, 0.9), 
                stop:0.5 rgba(35, 35, 35, 0.8), 
                stop:1 rgba(27, 30, 36, 0.9));
            border: 0px solid rgba(255, 255, 255, 0.1);
            border-radius: 15px;
        }}
    """)

    card_layout = QHBoxLayout(card)
    card_layout.setContentsMargins(15, 12, 15, 12)
    card_layout.setSpacing(12)

    # 左侧图标区域
    icon_container = QFrame()
    icon_container.setFixedSize(50, 50)
    icon_container.setStyleSheet(f"""
        QFrame {{
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 {accent_color}, 
                stop:1 rgba({StyleManager.hex_to_rgb(accent_color)}, 0.7));
            border-radius: 25px;
            border: none;
        }}
    """)

    icon_layout = QVBoxLayout(icon_container)
    icon_layout.setContentsMargins(0, 0, 0, 0)

    icon_label = QLabel(icon)
    icon_label.setStyleSheet(StyleManager.get_label_style(color='#FFFFFF', font_size=24))
    icon_label.setAlignment(Qt.AlignCenter)
    icon_layout.addWidget(icon_label)

    card_layout.addWidget(icon_container)

    # 右侧文本区域
    text_layout = QVBoxLayout(text_container)
    text_layout.setContentsMargins(0, 0, 0, 0)
    text_layout.setSpacing(2)

    # 标题
    title_label = QLabel(title)
    title_label.setStyleSheet(StyleManager.get_label_style(color='rgba(255, 255, 255, 0.7)', font_size=14))

    # 值
    value_label = QLabel(value)
    value_label.setStyleSheet(StyleManager.get_label_style(color=accent_color, font_size=16))
    value_label.setProperty("font-weight", "bold")

    text_layout.addWidget(title_label)
    text_layout.addWidget(value_label)

    card_layout.addWidget(text_container)

    return card