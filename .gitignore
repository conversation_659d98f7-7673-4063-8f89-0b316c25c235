# =============================================================================
# FlipTalk AI 项目 .gitignore 配置文件
# 用于忽略大型文件，避免上传到Git仓库
# =============================================================================

# -----------------------------------------------------------------------------
# 音频和视频文件 (通常文件较大)
# -----------------------------------------------------------------------------
# 音频文件格式
*.mp3       # MP3音频文件
*.wav       # WAV音频文件  
*.flac      # FLAC无损音频文件
*.aac       # AAC音频文件
*.ogg       # OGG音频文件
*.wma       # WMA音频文件
*.m4a       # M4A音频文件
*.aiff      # AIFF音频文件
*.au        # AU音频文件
*.ra        # RealAudio音频文件

# 视频文件格式
*.mp4       # MP4视频文件
*.avi       # AVI视频文件
*.mov       # QuickTime视频文件
*.wmv       # Windows Media视频文件
*.flv       # Flash视频文件
*.mkv       # Matroska视频文件
*.webm      # WebM视频文件
*.m4v       # M4V视频文件
*.3gp       # 3GP视频文件
*.ts        # 传输流视频文件
*.vob       # DVD视频文件

# -----------------------------------------------------------------------------
# AI模型和机器学习相关大文件
# -----------------------------------------------------------------------------
# 预训练模型文件
*.pth       # PyTorch模型文件
*.pt        # PyTorch检查点文件
*.ckpt      # 模型检查点文件
*.h5        # Keras/HDF5模型文件
*.hdf5      # HDF5数据文件
*.pkl       # Pickle序列化文件
*.pickle    # Pickle序列化文件
*.pb        # TensorFlow protobuf模型文件
*.tflite    # TensorFlow Lite模型文件
*.onnx      # ONNX模型文件
*.bin       # 二进制模型文件
*.safetensors # SafeTensors格式模型文件

# Hugging Face 缓存和模型
.cache/     # Hugging Face缓存目录
huggingface_hub/  # Hugging Face Hub缓存
transformers_cache/  # Transformers缓存目录

# 模型权重和参数文件
*weights*   # 包含weights的文件
*checkpoint* # 包含checkpoint的文件
model_*.bin # 模型二进制文件
pytorch_model.bin # PyTorch模型文件

# -----------------------------------------------------------------------------
# 数据集和大型数据文件
# -----------------------------------------------------------------------------
# 数据文件格式
*.csv       # CSV数据文件（如果很大）
*.json      # 大型JSON文件
*.jsonl     # JSON Lines文件
*.parquet   # Parquet数据文件
*.arrow     # Arrow数据文件
*.feather   # Feather数据文件
*.npz       # NumPy压缩数组文件
*.npy       # NumPy数组文件

# 数据集目录
datasets/   # 数据集目录
data/       # 数据目录
raw_data/   # 原始数据目录
processed_data/ # 处理后的数据目录

# -----------------------------------------------------------------------------
# 项目特定的输出和临时文件
# -----------------------------------------------------------------------------
# 输出文件目录
output/     # 输出文件目录
outputs/    # 输出文件目录
results/    # 结果文件目录
generated/  # 生成文件目录
temp/       # 临时文件目录
tmp/        # 临时文件目录

# 预训练模型目录
pretrained_models/ # 预训练模型目录
models/large/      # 大型模型目录
models/demucs_cache/ # Demucs模型缓存目录

# 语音分离输出文件
output/voice_separation/ # 语音分离输出目录

# 缓存目录
cache/      # 缓存目录
.cache/     # 隐藏缓存目录

# -----------------------------------------------------------------------------
# 压缩文件和归档文件
# -----------------------------------------------------------------------------
*.zip       # ZIP压缩文件
*.rar       # RAR压缩文件
*.7z        # 7-Zip压缩文件
*.tar       # TAR归档文件
*.tar.gz    # Gzip压缩的TAR文件
*.tgz       # Gzip压缩的TAR文件（简写）
*.tar.bz2   # Bzip2压缩的TAR文件
*.tar.xz    # XZ压缩的TAR文件
*.gz        # Gzip压缩文件
*.bz2       # Bzip2压缩文件
*.xz        # XZ压缩文件

# -----------------------------------------------------------------------------
# 系统和开发环境文件
# -----------------------------------------------------------------------------
# Python相关
__pycache__/    # Python字节码缓存
*.py[cod]       # Python编译文件
*$py.class      # Python类文件
*.so            # 共享对象文件

# Jupyter Notebook检查点
.ipynb_checkpoints/ # Jupyter检查点目录

# 虚拟环境
venv/           # 虚拟环境目录
env/            # 环境目录
.env/           # 环境配置目录

# IDE和编辑器配置
.vscode/        # Visual Studio Code配置
.idea/          # IntelliJ IDEA配置
*.swp           # Vim交换文件
*.swo           # Vim交换文件
*~              # 备份文件

# 操作系统生成的文件
.DS_Store       # macOS文件夹配置文件
Thumbs.db       # Windows缩略图缓存
*.tmp           # 临时文件
*.temp          # 临时文件

# -----------------------------------------------------------------------------
# 日志和调试文件
# -----------------------------------------------------------------------------
*.log           # 日志文件
logs/           # 日志目录
debug/          # 调试文件目录
*.out           # 输出文件
*.err           # 错误文件

# -----------------------------------------------------------------------------
# 特殊的大文件（按文件大小过滤）
# -----------------------------------------------------------------------------
# 注意：Git LFS（Large File Storage）可以用于跟踪大文件
# 如果需要版本控制大文件，建议使用 Git LFS
# 这里忽略超过100MB的文件（Git默认限制）

# -----------------------------------------------------------------------------
# 自定义大文件目录
# -----------------------------------------------------------------------------
# 根据项目需要，可以添加特定的大文件目录
large_files/    # 大文件目录
downloads/      # 下载文件目录
assets/large/   # 大型资源文件目录

# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/
cover/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
.pybuilder/
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
Pipfile.lock

# poetry
poetry.lock

# pdm
pdm.lock
.pdm.toml

# PEP 582
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# pytype static type analyzer
.pytype/

# Cython debug symbols
cython_debug/

# history and output
output/
history/
_model_cache/

# large files
/ffmpeg.exe
/ffmpeg
/ffprobe.exe
/ffprobe
.DS_Store
_config.py
config.py

# runtime
runtime/
venv/
dev/

# 音频视频文件
*.wav
*.mp3
*.mp4
*.m4a
*.flac

# 图片文件
*.jpg
*.png
*.jpeg
*.gif
*.bmp

# 模型文件
models--Systran--faster-whisper-*
*.pt
*.pth
*.onnx
*.bin

# 临时文件
temp_*
*.tmp
*.temp

# -----------------------------------------------------------------------------
# 项目特定的临时目录
# -----------------------------------------------------------------------------
# UI相关临时目录
ui/models/           # UI目录下的模型文件（应使用根目录下的models）
ui/Downloads/        # UI目录下的下载文件
**/Downloads/        # 任意位置的Downloads目录
**/models/whisperx_subtitle/  # 重复的whisperx模型目录
%APPDATA%/          # Windows应用数据目录

# 结束配置文件 