#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
清理项目中的测试文件和临时文件
"""

import os
import shutil
from pathlib import Path

def clean_project(project_root: str):
    """
    清理项目中的测试文件和临时文件
    
    Args:
        project_root: 项目根目录
    """
    # 要删除的文件模式
    patterns_to_delete = [
        "test_*.py",
        "debug_*.py",
        "*.wav",
        "*.pyc",
        "__pycache__",
        ".cursor",
        ".pytest_cache",
        ".coverage",
        "htmlcov",
        "*.log"
    ]
    
    # 要保留的重要文件
    files_to_keep = [
        "main.py",
        "README.md",
        "requirements.txt",
        "setup.py",
        "config.py"
    ]
    
    root_path = Path(project_root)
    
    # 遍历所有文件
    for pattern in patterns_to_delete:
        for file_path in root_path.rglob(pattern):
            if file_path.is_file():
                if file_path.name in files_to_keep:
                    continue
                try:
                    file_path.unlink()
                    print(f"已删除文件: {file_path}")
                except Exception as e:
                    print(f"删除文件失败 {file_path}: {e}")
            elif file_path.is_dir():
                try:
                    shutil.rmtree(file_path)
                    print(f"已删除目录: {file_path}")
                except Exception as e:
                    print(f"删除目录失败 {file_path}: {e}")

if __name__ == "__main__":
    # 获取当前脚本所在目录的父目录作为项目根目录
    project_root = str(Path(__file__).parent.parent)
    clean_project(project_root) 