#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TTS依赖安装脚本
用于安装字幕配音功能所需的依赖库
"""

import subprocess
import sys
import os
from pathlib import Path

def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 8):
        print("❌ 错误：需要Python 3.8或更高版本")
        print(f"当前版本：{sys.version}")
        return False
    
    print(f"✅ Python版本检查通过：{sys.version}")
    return True

def install_package(package_name, description=""):
    """安装单个包"""
    print(f"\n📦 正在安装 {package_name}...")
    if description:
        print(f"   {description}")
    
    try:
        result = subprocess.run(
            [sys.executable, "-m", "pip", "install", package_name],
            capture_output=True,
            text=True,
            check=True
        )
        print(f"✅ {package_name} 安装成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {package_name} 安装失败")
        print(f"错误信息：{e.stderr}")
        return False

def check_package_installation(package_name, import_name=None):
    """检查包是否已安装"""
    if import_name is None:
        import_name = package_name
    
    try:
        __import__(import_name)
        print(f"✅ {package_name} 已正确安装")
        return True
    except ImportError:
        print(f"❌ {package_name} 未安装或导入失败")
        return False

def main():
    """主安装流程"""
    print("🎤 FlipTalk AI - TTS依赖安装程序")
    print("=" * 50)
    
    # 检查Python版本
    if not check_python_version():
        return False
    
    # 升级pip
    print("\n🔄 升级pip...")
    try:
        subprocess.run([sys.executable, "-m", "pip", "install", "--upgrade", "pip"], 
                      capture_output=True, check=True)
        print("✅ pip升级完成")
    except subprocess.CalledProcessError:
        print("⚠️  pip升级失败，继续安装...")
    
    # TTS依赖包列表
    tts_packages = [
        {
            "name": "edge-tts>=6.1.7",
            "import_name": "edge_tts",
            "description": "微软Edge TTS免费语音合成"
        },
        {
            "name": "azure-cognitiveservices-speech>=1.31.0",
            "import_name": "azure.cognitiveservices.speech",
            "description": "微软Azure TTS高质量语音合成"
        },
        {
            "name": "PySide6-Multimedia>=6.4.0",
            "import_name": "PySide6.QtMultimedia",
            "description": "Qt多媒体支持（音频播放）"
        }
    ]
    
    # 安装依赖
    success_count = 0
    total_count = len(tts_packages)
    
    print(f"\n📋 开始安装 {total_count} 个TTS相关依赖包...")
    
    for package in tts_packages:
        if install_package(package["name"], package["description"]):
            success_count += 1
    
    print(f"\n📊 安装完成统计：")
    print(f"   成功：{success_count}/{total_count}")
    print(f"   失败：{total_count - success_count}/{total_count}")
    
    # 验证安装
    print(f"\n🔍 验证安装结果...")
    verification_success = 0
    
    for package in tts_packages:
        if check_package_installation(package["name"].split(">=")[0], package["import_name"]):
            verification_success += 1
    
    print(f"\n📋 验证结果：")
    print(f"   验证通过：{verification_success}/{total_count}")
    
    # 安装结果总结
    if verification_success == total_count:
        print("\n🎉 恭喜！所有TTS依赖安装成功！")
        print("💡 提示：")
        print("   • Edge TTS：免费使用，无需API密钥")
        print("   • Azure TTS：需要Azure API密钥，质量更高")
        print("   • 现在可以使用FlipTalk AI的字幕配音功能了")
        return True
    else:
        print("\n⚠️  部分依赖安装失败")
        print("💡 建议：")
        print("   • 检查网络连接")
        print("   • 尝试使用管理员权限运行")
        print("   • 手动安装失败的包")
        return False

def show_usage_guide():
    """显示使用指南"""
    print("\n📚 TTS功能使用指南：")
    print("1. 启动FlipTalk AI主程序")
    print("2. 点击'字幕配音'功能卡片")
    print("3. 加载字幕文件（支持.srt, .vtt, .ass格式）")
    print("4. 选择TTS引擎和语音")
    print("5. 调整语速并试听效果")
    print("6. 开始批量配音生成")
    print("7. 导出配音文件")
    
    print("\n🔧 配置Azure TTS（可选）：")
    print("1. 注册Azure认知服务账号")
    print("2. 获取Speech Service API密钥")
    print("3. 在FlipTalk AI设置中配置API密钥")

if __name__ == "__main__":
    try:
        success = main()
        if success:
            show_usage_guide()
        
        input("\n按回车键退出...")
    except KeyboardInterrupt:
        print("\n\n⚠️  安装被用户中断")
    except Exception as e:
        print(f"\n❌ 安装过程中出现错误：{e}")
        input("\n按回车键退出...") 