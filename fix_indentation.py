#!/usr/bin/env python
# -*- coding: utf-8 -*-

# 修复ui/fliptalk_ui.py中的缩进问题

with open('ui/fliptalk_ui.py', 'r', encoding='utf-8') as f:
    content = f.read()

# 修复show_home_page方法
content = content.replace(
    '''def show_home_page(self):
        """显示首页"""
        try:
        # 清空右侧区域布局中的所有组件''',
    '''def show_home_page(self):
        """显示首页"""
        try:
            # 清空右侧区域布局中的所有组件'''
)

# 修复reset_right_area_layout方法
content = content.replace(
    '''def reset_right_area_layout(self):
        """重置右侧区域布局"""
        try:
        # 清空右侧区域布局中的所有组件''',
    '''def reset_right_area_layout(self):
        """重置右侧区域布局"""
        try:
            # 清空右侧区域布局中的所有组件'''
)

# 修复缩进错误的行
content = content.replace('        self.subtitle_area.show()', '                    self.subtitle_area.show()')
content = content.replace('        self.parameter_panel.show()', '                    self.parameter_panel.show()')

# 检查并修复其他可能的try语句缩进问题
lines = content.split('\n')
fixed_lines = []
in_try_block = False

for i, line in enumerate(lines):
    if "try:" in line and not line.strip().endswith('except') and i < len(lines) - 1:
        next_line = lines[i+1]
        if not next_line.startswith(line.split('try:')[0] + '    '):
            # 这是一个缩进错误的try块
            indent = line.split('try:')[0]
            fixed_lines.append(line)
            in_try_block = True
            continue
    
    if in_try_block and line.strip() and not line.startswith(fixed_lines[-1].split('try:')[0] + '    '):
        # 修复缩进
        indent = fixed_lines[-1].split('try:')[0] + '    '
        fixed_lines.append(indent + line.lstrip())
        if 'except' in line:
            in_try_block = False
    else:
        fixed_lines.append(line)
        if in_try_block and 'except' in line:
            in_try_block = False

# 写入修复后的内容
with open('ui/fliptalk_ui.py', 'w', encoding='utf-8') as f:
    f.write('\n'.join(fixed_lines))

print("缩进问题修复完成！") 