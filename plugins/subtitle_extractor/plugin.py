#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FlipTalk AI - WhisperX字幕提取插件
使用WhisperX库提供高质量音频转字幕功能
"""

import os
import sys
import json
from pathlib import Path
from typing import Dict, Any, List, Optional, Callable

# 添加core和models模块到路径
current_dir = Path(__file__).parent
sys.path.append(str(current_dir.parent.parent))

try:
    from core.interfaces import IPlugin
    CORE_AVAILABLE = True
except ImportError:
    print("警告：无法导入核心接口，将使用基础接口")
    CORE_AVAILABLE = False
    # 定义基础接口
    class IPlugin:
        def get_name(self) -> str: pass
        def get_version(self) -> str: pass
        def get_description(self) -> str: pass
        def initialize(self, config: Dict[str, Any]) -> bool: pass
        def cleanup(self) -> None: pass

try:
    from models.whisperx_subtitle.extractor import WhisperXExtractor
    from models.whisperx_subtitle.model_manager import WhisperXModelManager
    WHISPERX_AVAILABLE = True
except ImportError as e:
    print(f"警告：无法导入WhisperX模块 - {e}")
    WHISPERX_AVAILABLE = False
    # 创建占位符类以避免导入错误
    class WhisperXExtractor:
        def __init__(self, *args, **kwargs): pass
    class WhisperXModelManager:
        def __init__(self, *args, **kwargs): pass


class WhisperXSubtitleExtractor(IPlugin):
    """
    WhisperX字幕提取插件
    提供音频转字幕的完整功能
    """
    
    def __init__(self):
        self.name = "WhisperX字幕提取器"
        self.version = "1.0.0"
        self.description = "使用WhisperX AI技术将音频转换为高质量字幕文件"
        
        # 核心组件
        self.extractor = None
        self.model_manager = None
        
        # 配置参数 - 基于最新优化
        self.config = {
            "default_model": "large-v3",  # 使用更精确的模型
            "default_language": "auto",   # 自动语言检测
            "enable_uvr5": True,          # 启用人声分离
            "target_duration": 20*60,     # 智能分割时长(秒)
            "use_api": False,             # 默认使用本地模式
            "api_token": None,            # API令牌
            "output_formats": ["srt", "vtt", "txt"],
            "device": "auto"
        }
        
        # 支持的音频格式
        self.supported_formats = [
            '.wav', '.mp3', '.flac', '.m4a', '.aac', 
            '.ogg', '.wma', '.aiff', '.au'
        ]
        
        # 支持的语言
        self.supported_languages = {
            "auto": "自动检测",
            "zh": "中文",
            "en": "English",
            "ja": "日本語", 
            "ko": "한국어",
            "es": "Español",
            "fr": "Français",
            "de": "Deutsch",
            "it": "Italiano",
            "pt": "Português",
            "ru": "Русский",
            "ar": "العربية",
            "hi": "हिन्दी"
        }
    
    def get_name(self) -> str:
        """获取插件名称"""
        return self.name
    
    def get_version(self) -> str:
        """获取插件版本"""
        return self.version
    
    def get_description(self) -> str:
        """获取插件描述"""
        return self.description
    
    def initialize(self, config: Dict[str, Any]) -> bool:
        """
        初始化插件
        
        Args:
            config: 配置参数字典
            
        Returns:
            bool: 初始化是否成功
        """
        try:
            if not WHISPERX_AVAILABLE:
                print("错误：WhisperX模块不可用")
                return False
            
            # 应用配置参数
            if config:
                self.config.update(config)
            
            # 设置模型目录 - 指向weights子目录
            models_dir = self.config.get("models_dir")
            if not models_dir:
                # 默认使用项目models/whisperx_subtitle/weights目录
                project_root = Path(__file__).parent.parent.parent
                models_dir = project_root / "models" / "whisperx_subtitle" / "weights"
            
            # 初始化模型管理器
            self.model_manager = WhisperXModelManager(str(models_dir))
            
            # 初始化提取器 - 使用优化后的接口，指向weights目录
            device = config.get("device", self.config.get("device", "auto"))
            self.extractor = WhisperXExtractor(
                models_dir=str(models_dir),  # 已经指向weights目录
                device=device,
                use_api=self.config.get("use_api", False),
                api_token=self.config.get("api_token")
            )
            
            # 更新配置中的设备信息
            self.config["device"] = device
            
            print(f"WhisperX字幕提取插件初始化完成")
            print(f"模型目录: {models_dir}")
            print(f"设备: {self.extractor.device}")
            
            return True
            
        except Exception as e:
            print(f"WhisperX字幕提取插件初始化失败: {e}")
            return False
    
    def cleanup(self) -> None:
        """清理插件资源"""
        try:
            if self.extractor:
                del self.extractor
                self.extractor = None
            
            if self.model_manager:
                del self.model_manager
                self.model_manager = None
                
        except Exception as e:
            print(f"清理WhisperX插件资源时出错: {e}")
    
    def extract_subtitle(self, 
                        audio_path: str = None,
                        video_path: str = None,
                        output_dir: str = None,
                        model_name: str = None,
                        language: str = None,
                        output_formats: List[str] = None,
                        enable_uvr5: bool = None,
                        target_duration: int = None,
                        progress_callback: Callable = None) -> Dict[str, Any]:
        """
        从音频或视频文件提取字幕 - 使用经过验证的优化算法
        
        Args:
            audio_path: 音频文件路径
            video_path: 视频文件路径（会自动转换为音频）
            output_dir: 输出目录，如果为None则使用输入文件同目录
            model_name: 使用的模型名称
            language: 目标语言（auto, zh, en等）
            output_formats: 输出格式列表 ["srt", "vtt", "txt"]
            enable_uvr5: 是否启用UVR5人声分离
            target_duration: 音频分割目标时长（秒）
            progress_callback: 进度回调函数
            
        Returns:
            Dict: 包含提取结果的字典
        """
        try:
            if not self.extractor:
                raise Exception("插件未初始化")
            
            # 验证输入参数
            if not audio_path and not video_path:
                raise ValueError("必须提供audio_path或video_path")
            
            # 检查输入文件
            input_path = Path(audio_path) if audio_path else Path(video_path)
            if not input_path.exists():
                raise FileNotFoundError(f"输入文件不存在: {input_path}")
            
            # 使用默认参数
            model_name = model_name or self.config["default_model"]
            language = language or self.config["default_language"]
            output_formats = output_formats or self.config["output_formats"]
            if enable_uvr5 is None:
                enable_uvr5 = self.config["enable_uvr5"]
            if target_duration is None:
                target_duration = self.config["target_duration"]
            
            # 确定输出目录
            if output_dir is None:
                output_dir = input_path.parent
            else:
                output_dir = Path(output_dir)
                output_dir.mkdir(parents=True, exist_ok=True)
            
            # 使用新的优化接口提取字幕
            if progress_callback:
                progress_callback("开始字幕提取...", 0)
            
            result = self.extractor.extract_subtitle(
                audio_path=audio_path,
                video_path=video_path,
                model_name=model_name,
                language=language,
                enable_uvr5=enable_uvr5,
                target_duration=target_duration,
                progress_callback=progress_callback
            )
            
            # 生成输出文件
            output_files = {}
            base_name = input_path.stem
            
            if "srt" in output_formats:
                srt_path = output_dir / f"{base_name}_subtitle.srt"
                srt_content = self.extractor.generate_srt(
                    result["segments"], 
                    str(srt_path)
                )
                output_files["srt"] = str(srt_path)
            
            if "vtt" in output_formats:
                vtt_path = output_dir / f"{base_name}_subtitle.vtt"
                vtt_content = self.extractor.generate_vtt(
                    result["segments"], 
                    str(vtt_path)
                )
                output_files["vtt"] = str(vtt_path)
            
            if "txt" in output_formats:
                txt_path = output_dir / f"{base_name}_subtitle.txt"
                with open(txt_path, 'w', encoding='utf-8') as f:
                    for segment in result["segments"]:
                        f.write(f"{segment.get('text', '')}\n")
                output_files["txt"] = str(txt_path)
            
            # 保存详细结果和统计信息
            json_path = output_dir / f"{base_name}_subtitle_details.json"
            with open(json_path, 'w', encoding='utf-8') as f:
                json.dump({
                    "input_path": str(input_path),
                    "model_used": result["model_used"],
                    "language": result["language"],
                    "processing_time": result.get("processing_time", 0),
                    "segments_count": result.get("segments_count", 0),
                    "use_api": result.get("use_api", False),
                    "segments": result["segments"],
                    "output_files": output_files
                }, f, indent=2, ensure_ascii=False)
            
            return {
                "success": True,
                "input_path": str(input_path),
                "model_used": result["model_used"],
                "language": result["language"],
                "processing_time": result.get("processing_time", 0),
                "segments_count": len(result["segments"]),
                "use_api": result.get("use_api", False),
                "output_files": output_files,
                "details_file": str(json_path)
            }
            
        except Exception as e:
            error_msg = f"字幕提取失败: {e}"
            print(error_msg)
            return {
                "success": False,
                "error": error_msg
            }
    
    def extract_audio_segments(self, 
                             audio_path: str,
                             segments_data: str,
                             output_dir: str) -> Dict[str, Any]:
        """
        根据字幕段落提取音频片段
        
        Args:
            audio_path: 原始音频文件路径
            segments_data: 字幕段落数据（JSON文件路径或段落列表）
            output_dir: 输出目录
            
        Returns:
            Dict: 提取结果
        """
        try:
            if not self.extractor:
                raise Exception("插件未初始化")
            
            # 加载段落数据
            if isinstance(segments_data, str):
                if os.path.exists(segments_data):
                    # 从JSON文件加载
                    with open(segments_data, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                        segments = data.get("segments", [])
                else:
                    raise FileNotFoundError(f"段落数据文件不存在: {segments_data}")
            else:
                segments = segments_data
            
            # 提取音频片段
            output_files = self.extractor.extract_audio_segments(
                segments=segments,
                audio_path=audio_path,
                output_dir=output_dir
            )
            
            return {
                "success": True,
                "segments_count": len(segments),
                "output_files": output_files,
                "output_dir": output_dir
            }
            
        except Exception as e:
            error_msg = f"音频片段提取失败: {e}"
            print(error_msg)
            return {
                "success": False,
                "error": error_msg
            }
    
    def get_available_models(self) -> Dict[str, Any]:
        """获取可用模型列表"""
        if not self.model_manager:
            return {}
        
        return self.model_manager.get_available_models()
    
    def get_downloaded_models(self) -> List[str]:
        """获取已下载的模型列表"""
        if not self.model_manager:
            return []
        
        return self.model_manager.get_downloaded_models()
    
    def download_model(self, model_name: str, progress_callback: Callable = None) -> bool:
        """
        下载指定模型
        
        Args:
            model_name: 模型名称
            progress_callback: 进度回调函数
            
        Returns:
            bool: 下载是否成功
        """
        if not self.model_manager:
            return False
        
        try:
            return self.model_manager.download_model(model_name, progress_callback)
        except Exception as e:
            print(f"下载模型失败: {e}")
            return False
    
    def remove_model(self, model_name: str) -> bool:
        """删除指定模型"""
        if not self.model_manager:
            return False
        
        try:
            return self.model_manager.remove_model(model_name)
        except Exception as e:
            print(f"删除模型失败: {e}")
            return False
    
    def get_supported_formats(self) -> List[str]:
        """获取支持的音频格式列表"""
        return self.supported_formats.copy()
    
    def get_supported_languages(self) -> Dict[str, str]:
        """获取支持的语言列表"""
        return self.supported_languages.copy()
    
    def get_model_info(self) -> Dict[str, Any]:
        """获取当前模型信息"""
        if not self.extractor:
            return {}
        
        return self.extractor.get_model_info()
    
    def validate_audio_file(self, audio_path: str) -> Dict[str, Any]:
        """
        验证音频文件
        
        Args:
            audio_path: 音频文件路径
            
        Returns:
            Dict: 验证结果
        """
        try:
            audio_path = Path(audio_path)
            
            # 检查文件存在性
            if not audio_path.exists():
                return {
                    "valid": False,
                    "error": f"文件不存在: {audio_path}"
                }
            
            # 检查文件格式
            if audio_path.suffix.lower() not in self.supported_formats:
                return {
                    "valid": False,
                    "error": f"不支持的文件格式: {audio_path.suffix}"
                }
            
            # 检查文件大小
            file_size = audio_path.stat().st_size
            if file_size == 0:
                return {
                    "valid": False,
                    "error": "文件为空"
                }
            
            # 尝试加载音频信息
            try:
                import soundfile as sf
                info = sf.info(str(audio_path))
                
                return {
                    "valid": True,
                    "duration": info.duration,
                    "sample_rate": info.samplerate,
                    "channels": info.channels,
                    "file_size": file_size,
                    "format": info.format
                }
                
            except Exception as e:
                return {
                    "valid": False,
                    "error": f"无法读取音频文件: {e}"
                }
                
        except Exception as e:
            return {
                "valid": False,
                "error": f"验证音频文件时出错: {e}"
            }
    
    def get_recommended_model(self, language: str = "auto") -> str:
        """
        根据语言获取推荐模型
        
        Args:
            language: 目标语言
            
        Returns:
            str: 推荐的模型名称
        """
        if not self.model_manager:
            return "medium"
        
        return self.model_manager.get_recommended_model(language)
    
    def change_device(self, device: str) -> bool:
        """
        动态切换计算设备
        
        Args:
            device: 新的设备类型 ("auto", "cuda", "cpu")
            
        Returns:
            bool: 切换是否成功
        """
        try:
            if not self.extractor:
                print("错误：提取器未初始化")
                return False
            
            success = self.extractor.change_device(device)
            if success:
                self.config["device"] = device
                print(f"设备切换成功: {device}")
            else:
                print(f"设备切换失败: {device}")
            
            return success
            
        except Exception as e:
            print(f"设备切换出错: {e}")
            return False

    def get_current_device(self) -> str:
        """获取当前使用的设备"""
        if self.extractor:
            return self.extractor.device
        return "unknown" 