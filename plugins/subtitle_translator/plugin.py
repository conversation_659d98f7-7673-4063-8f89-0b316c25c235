#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FlipTalk AI - 字幕翻译插件
功能：提供多种翻译服务的字幕翻译功能
"""

import os
import re
import json
import time
import requests
import hashlib
import uuid
from typing import List, Dict, Optional, Tuple
from abc import ABC, abstractmethod
import concurrent.futures
import random


class ISubtitleTranslator(ABC):
    """字幕翻译器接口"""
    
    @abstractmethod
    def get_name(self) -> str:
        """获取翻译器名称"""
        pass
    
    @abstractmethod
    def get_description(self) -> str:
        """获取翻译器描述"""
        pass
    
    @abstractmethod
    def get_supported_languages(self) -> Dict[str, str]:
        """获取支持的语言列表"""
        pass
    
    @abstractmethod
    def translate_text(self, text: str, source_lang: str, target_lang: str, **kwargs) -> Optional[str]:
        """翻译单个文本"""
        pass
    



class SubtitleItem:
    """字幕条目类"""
    def __init__(self, start_time: str = "00:00:00,000", end_time: str = "00:00:03,000", text: str = ""):
        self.start_time = start_time
        self.end_time = end_time
        self.text = text
        self.translated = ""
    
    def to_srt_format(self, index: int) -> str:
        """转换为SRT格式"""
        return f"{index}\n{self.start_time} --> {self.end_time}\n{self.text}\n"
    
    def to_translated_srt_format(self, index: int) -> str:
        """转换为翻译后的SRT格式"""
        text = self.translated if self.translated else self.text
        return f"{index}\n{self.start_time} --> {self.end_time}\n{text}\n"
    
    @staticmethod
    def parse_srt_time(time_str: str) -> float:
        """解析SRT时间格式"""
        try:
            time_parts = time_str.split(':')
            hours = int(time_parts[0])
            minutes = int(time_parts[1])
            seconds_parts = time_parts[2].split(',')
            seconds = int(seconds_parts[0])
            milliseconds = int(seconds_parts[1])
            return hours * 3600 + minutes * 60 + seconds + milliseconds / 1000
        except:
            return 0
    
    @staticmethod
    def format_srt_time(total_seconds: float) -> str:
        """格式化为SRT时间格式"""
        hours = int(total_seconds // 3600)
        minutes = int((total_seconds % 3600) // 60)
        seconds = int(total_seconds % 60)
        milliseconds = int((total_seconds % 1) * 1000)
        return f"{hours:02d}:{minutes:02d}:{seconds:02d},{milliseconds:03d}"


class MicrosoftTranslator(ISubtitleTranslator):
    """微软翻译器 - 使用官方API"""
    
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.base_url = "https://api.cognitive.microsofttranslator.com"
    
    def get_name(self) -> str:
        return "Microsoft Translator"
    
    def get_description(self) -> str:
        return "微软认知服务翻译API，支持多种语言翻译"
    
    def get_supported_languages(self) -> Dict[str, str]:
        return {
            "auto": "自动检测",
            "zh": "中文",
            "en": "英语",
            "ja": "日语",
            "ko": "韩语",
            "fr": "法语",
            "de": "德语",
            "es": "西班牙语",
            "ru": "俄语",
            "ar": "阿拉伯语",
            "th": "泰语",
            "vi": "越南语",
            "it": "意大利语",
            "pt": "葡萄牙语"
        }
    
    def translate_text(self, text: str, source_lang: str, target_lang: str, **kwargs) -> Optional[str]:
        """翻译单个文本"""
        try:
            url = f"{self.base_url}/translate"
            params = {
                'api-version': '3.0',
                'from': source_lang if source_lang != 'auto' else None,
                'to': target_lang
            }
            
            headers = {
                'Ocp-Apim-Subscription-Key': self.api_key,
                'Content-type': 'application/json',
                'X-ClientTraceId': str(uuid.uuid4())
            }
            
            body = [{'text': text}]
            
            response = requests.post(url, params=params, headers=headers, json=body, timeout=10)
            
            if response.status_code == 200:
                result = response.json()
                if result and len(result) > 0 and 'translations' in result[0]:
                    return result[0]['translations'][0]['text']
            
            return None
        except Exception as e:
            print(f"微软翻译错误: {e}")
            return None




class GoogleTranslator(ISubtitleTranslator):
    """谷歌翻译器（免费版）"""
    
    def get_name(self) -> str:
        return "Google Translate"
    
    def get_description(self) -> str:
        return "谷歌翻译免费API，支持多种语言翻译"
    
    def get_supported_languages(self) -> Dict[str, str]:
        return {
            "auto": "自动检测",
            "zh": "中文",
            "en": "英语",
            "ja": "日语",
            "ko": "韩语",
            "fr": "法语",
            "de": "德语",
            "es": "西班牙语",
            "ru": "俄语",
            "ar": "阿拉伯语",
            "th": "泰语",
            "vi": "越南语",
            "it": "意大利语",
            "pt": "葡萄牙语"
        }
    
    def translate_text(self, text: str, source_lang: str, target_lang: str, **kwargs) -> Optional[str]:
        """翻译单个文本"""
        try:
            url = "https://translate.googleapis.com/translate_a/single"
            params = {
                'client': 'gtx',
                'sl': source_lang,
                'tl': target_lang,
                'dt': 't',
                'q': text
            }
            
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }
            
            response = requests.get(url, params=params, headers=headers, timeout=10)
            
            if response.status_code == 200:
                result = response.json()
                if result and len(result) > 0 and len(result[0]) > 0:
                    translated_text = ""
                    for item in result[0]:
                        if item[0]:
                            translated_text += item[0]
                    return translated_text
            
            return None
        except Exception as e:
            print(f"谷歌翻译错误: {e}")
            return None
    



class DeepLTranslator(ISubtitleTranslator):
    """DeepL翻译器"""
    
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.base_url = "https://api-free.deepl.com/v2"
    
    def get_name(self) -> str:
        return "DeepL Translator"
    
    def get_description(self) -> str:
        return "DeepL Pro翻译API，提供高质量翻译服务"
    
    def get_supported_languages(self) -> Dict[str, str]:
        return {
            "ZH": "中文",
            "EN": "英语",
            "JA": "日语",
            "KO": "韩语",
            "FR": "法语",
            "DE": "德语",
            "ES": "西班牙语",
            "RU": "俄语",
            "IT": "意大利语",
            "PT": "葡萄牙语"
        }
    
    def translate_text(self, text: str, source_lang: str, target_lang: str, **kwargs) -> Optional[str]:
        """翻译单个文本"""
        try:
            url = f"{self.base_url}/translate"
            
            headers = {
                'Authorization': f'DeepL-Auth-Key {self.api_key}',
                'Content-Type': 'application/x-www-form-urlencoded'
            }
            
            data = {
                'text': text,
                'target_lang': target_lang.upper()
            }
            
            if source_lang and source_lang.upper() != 'AUTO':
                data['source_lang'] = source_lang.upper()
            
            response = requests.post(url, headers=headers, data=data, timeout=10)
            
            if response.status_code == 200:
                result = response.json()
                if 'translations' in result and len(result['translations']) > 0:
                    return result['translations'][0]['text']
            
            return None
        except Exception as e:
            print(f"DeepL翻译错误: {e}")
            return None
    



class SubtitleTranslatorPlugin:
    """字幕翻译插件主类"""
    
    def __init__(self):
        self.translators = {}
        self.current_translator = None
        self._initialize_translators()
    
    def _initialize_translators(self):
        """初始化翻译器"""
        # 注册可用的翻译器
        self.translators["google"] = GoogleTranslator()
        # Microsoft和DeepL需要API Key，在使用时动态创建
    
    def get_name(self) -> str:
        """获取插件名称"""
        return "字幕翻译器"
    
    def get_description(self) -> str:
        """获取插件描述"""
        return "支持多种翻译服务的字幕翻译插件，包括微软、谷歌、DeepL等"
    
    def get_version(self) -> str:
        """获取插件版本"""
        return "1.0.0"
    
    def get_available_translators(self) -> Dict[str, str]:
        """获取可用的翻译器列表"""
        return {
            "google": "谷歌翻译 (免费)",
            "microsoft": "微软翻译 (需要API Key)",
            "deepl": "DeepL Pro (需要API Key)"
        }
    
    def set_translator(self, translator_type: str, api_key: str = "") -> bool:
        """设置当前使用的翻译器"""
        try:
            if translator_type == "google":
                self.current_translator = self.translators["google"]
            elif translator_type == "microsoft":
                if not api_key:
                    raise ValueError("微软翻译需要API Key")
                self.current_translator = MicrosoftTranslator(api_key)
            elif translator_type == "deepl":
                if not api_key:
                    raise ValueError("DeepL翻译需要API Key")
                self.current_translator = DeepLTranslator(api_key)
            else:
                raise ValueError(f"不支持的翻译器类型: {translator_type}")
            
            return True
        except Exception as e:
            print(f"设置翻译器失败: {e}")
            return False
    
    def get_supported_languages(self) -> Dict[str, str]:
        """获取当前翻译器支持的语言"""
        if self.current_translator:
            return self.current_translator.get_supported_languages()
        return {}
    
    def parse_srt_file(self, file_path: str) -> List[SubtitleItem]:
        """解析SRT字幕文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            return self.parse_srt_content(content)
        except Exception as e:
            print(f"解析SRT文件失败: {e}")
            return []
    
    def parse_srt_content(self, content: str) -> List[SubtitleItem]:
        """解析SRT内容"""
        subtitle_items = []
        
        # 分割字幕块
        blocks = re.split(r'\n\s*\n', content.strip())
        
        for block in blocks:
            lines = block.strip().split('\n')
            if len(lines) >= 3:
                # 跳过序号行
                time_line = lines[1]
                text_lines = lines[2:]
                
                # 解析时间
                time_match = re.match(r'(\d{2}:\d{2}:\d{2},\d{3})\s*-->\s*(\d{2}:\d{2}:\d{2},\d{3})', time_line)
                if time_match:
                    start_time = time_match.group(1)
                    end_time = time_match.group(2)
                    text = '\n'.join(text_lines)
                    
                    subtitle_item = SubtitleItem(start_time, end_time, text)
                    subtitle_items.append(subtitle_item)
        
        return subtitle_items
    
    def translate_subtitle(self, subtitle_item: SubtitleItem, source_lang: str, target_lang: str) -> bool:
        """翻译单个字幕条目"""
        if not self.current_translator:
            print("未设置翻译器")
            return False
        
        try:
            translated_text = self.current_translator.translate_text(
                subtitle_item.text, source_lang, target_lang
            )
            
            if translated_text:
                subtitle_item.translated = translated_text
                return True
            
            return False
        except Exception as e:
            print(f"翻译字幕失败: {e}")
            return False
    

    
    def save_translated_srt(self, subtitle_items: List[SubtitleItem], output_path: str) -> bool:
        """保存翻译后的SRT文件"""
        try:
            content = ""
            index = 1
            
            for item in subtitle_items:
                if item.translated:
                    content += item.to_translated_srt_format(index)
                    content += "\n"
                    index += 1
            
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            return True
        except Exception as e:
            print(f"保存翻译文件失败: {e}")
            return False
    
    def translate_file(self, input_path: str, output_path: str, 
                      source_lang: str, target_lang: str, 
                      translator_type: str, api_key: str = "") -> Tuple[bool, str]:
        """翻译整个字幕文件"""
        try:
            # 设置翻译器
            if not self.set_translator(translator_type, api_key):
                return False, "设置翻译器失败"
            
            # 解析字幕文件
            subtitle_items = self.parse_srt_file(input_path)
            if not subtitle_items:
                return False, "解析字幕文件失败或文件为空"
            
            # 逐个翻译字幕
            success_count = 0
            total_count = len(subtitle_items)
            
            for item in subtitle_items:
                if self.translate_subtitle(item, source_lang, target_lang):
                    success_count += 1
                time.sleep(0.1)  # 避免请求过于频繁
            
            if success_count == 0:
                return False, "翻译失败，没有成功翻译任何字幕"
            
            # 保存翻译结果
            if not self.save_translated_srt(subtitle_items, output_path):
                return False, "保存翻译文件失败"
            
            return True, f"翻译完成，成功翻译 {success_count}/{total_count} 条字幕"
            
        except Exception as e:
            return False, f"翻译过程出错: {str(e)}"


# 插件实例
subtitle_translator_plugin = SubtitleTranslatorPlugin()


def get_plugin():
    """获取插件实例"""
    return subtitle_translator_plugin


if __name__ == "__main__":
    # 测试代码
    plugin = SubtitleTranslatorPlugin()
    print(f"插件名称: {plugin.get_name()}")
    print(f"插件描述: {plugin.get_description()}")
    print(f"可用翻译器: {plugin.get_available_translators()}") 