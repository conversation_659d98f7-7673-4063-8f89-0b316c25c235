#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FlipTalk AI - 语音识别插件
功能：提供多种语音识别服务的音频转文字功能
"""

import os
import json
import time
import wave
import tempfile
from typing import List, Dict, Optional, Tuple, Union
from abc import ABC, abstractmethod


class ISpeechRecognizer(ABC):
    """语音识别器接口"""
    
    @abstractmethod
    def get_name(self) -> str:
        """获取识别器名称"""
        pass
    
    @abstractmethod
    def get_description(self) -> str:
        """获取识别器描述"""
        pass
    
    @abstractmethod
    def get_supported_languages(self) -> Dict[str, str]:
        """获取支持的语言列表"""
        pass
    
    @abstractmethod
    def get_supported_formats(self) -> List[str]:
        """获取支持的音频格式"""
        pass
    
    @abstractmethod
    def recognize_audio(self, audio_path: str, language: str = "auto", **kwargs) -> Optional[str]:
        """识别音频文件"""
        pass
    
    @abstractmethod
    def recognize_audio_with_timestamps(self, audio_path: str, language: str = "auto", **kwargs) -> List[Dict]:
        """识别音频文件并返回时间戳信息"""
        pass


class WhisperRecognizer(ISpeechRecognizer):
    """Whisper语音识别器"""
    
    def __init__(self, model_size: str = "base"):
        self.model_size = model_size
        self.model = None
        self._load_model()
    
    def _load_model(self):
        """加载Whisper模型"""
        try:
            import whisper
            self.model = whisper.load_model(self.model_size)
        except ImportError:
            print("Whisper未安装，请运行: pip install openai-whisper")
        except Exception as e:
            print(f"加载Whisper模型失败: {e}")
    
    def get_name(self) -> str:
        return f"OpenAI Whisper ({self.model_size})"
    
    def get_description(self) -> str:
        return "OpenAI Whisper本地语音识别，支持多语言高精度识别"
    
    def get_supported_languages(self) -> Dict[str, str]:
        return {
            "auto": "自动检测",
            "zh": "中文",
            "en": "英语",
            "ja": "日语",
            "ko": "韩语",
            "fr": "法语",
            "de": "德语",
            "es": "西班牙语",
            "ru": "俄语",
            "ar": "阿拉伯语",
            "th": "泰语",
            "vi": "越南语",
            "it": "意大利语",
            "pt": "葡萄牙语"
        }
    
    def get_supported_formats(self) -> List[str]:
        return [".wav", ".mp3", ".m4a", ".flac", ".ogg", ".wma"]
    
    def recognize_audio(self, audio_path: str, language: str = "auto", **kwargs) -> Optional[str]:
        """识别音频文件"""
        if not self.model:
            return None
        
        try:
            # 设置语言参数
            lang_param = None if language == "auto" else language
            
            # 执行识别
            result = self.model.transcribe(audio_path, language=lang_param)
            return result["text"].strip()
        except Exception as e:
            print(f"Whisper识别失败: {e}")
            return None
    
    def recognize_audio_with_timestamps(self, audio_path: str, language: str = "auto", **kwargs) -> List[Dict]:
        """识别音频文件并返回时间戳信息"""
        if not self.model:
            return []
        
        try:
            # 设置语言参数
            lang_param = None if language == "auto" else language
            
            # 执行识别
            result = self.model.transcribe(audio_path, language=lang_param)
            
            # 转换为标准格式
            segments = []
            for segment in result["segments"]:
                segments.append({
                    "start": segment["start"],
                    "end": segment["end"],
                    "text": segment["text"].strip()
                })
            
            return segments
        except Exception as e:
            print(f"Whisper识别失败: {e}")
            return []


class WhisperXRecognizer(ISpeechRecognizer):
    """WhisperX语音识别器（更高精度）"""
    
    def __init__(self, model_size: str = "base", device: str = "auto"):
        self.model_size = model_size
        self.device = device
        self.model = None
        self.align_model = None
        self.metadata = None
        self._load_model()
    
    def _load_model(self):
        """加载WhisperX模型"""
        try:
            import whisperx
            import torch
            
            # 自动检测设备
            if self.device == "auto":
                self.device = "cuda" if torch.cuda.is_available() else "cpu"
            
            # 加载模型
            self.model = whisperx.load_model(self.model_size, self.device)
        except ImportError:
            print("WhisperX未安装，请参考官方文档安装")
        except Exception as e:
            print(f"加载WhisperX模型失败: {e}")
    
    def get_name(self) -> str:
        return f"WhisperX ({self.model_size})"
    
    def get_description(self) -> str:
        return "WhisperX高精度语音识别，支持说话人分离和精确时间戳"
    
    def get_supported_languages(self) -> Dict[str, str]:
        return {
            "auto": "自动检测",
            "zh": "中文",
            "en": "英语",
            "ja": "日语",
            "ko": "韩语",
            "fr": "法语",
            "de": "德语",
            "es": "西班牙语",
            "ru": "俄语",
            "ar": "阿拉伯语",
            "th": "泰语",
            "vi": "越南语",
            "it": "意大利语",
            "pt": "葡萄牙语"
        }
    
    def get_supported_formats(self) -> List[str]:
        return [".wav", ".mp3", ".m4a", ".flac", ".ogg", ".wma"]
    
    def recognize_audio(self, audio_path: str, language: str = "auto", **kwargs) -> Optional[str]:
        """识别音频文件"""
        if not self.model:
            return None
        
        try:
            import whisperx
            
            # 加载音频
            audio = whisperx.load_audio(audio_path)
            
            # 执行识别
            result = self.model.transcribe(audio, batch_size=16)
            
            # 提取文本
            text = ""
            for segment in result["segments"]:
                text += segment["text"] + " "
            
            return text.strip()
        except Exception as e:
            print(f"WhisperX识别失败: {e}")
            return None
    
    def recognize_audio_with_timestamps(self, audio_path: str, language: str = "auto", **kwargs) -> List[Dict]:
        """识别音频文件并返回时间戳信息"""
        if not self.model:
            return []
        
        try:
            import whisperx
            
            # 加载音频
            audio = whisperx.load_audio(audio_path)
            
            # 执行识别
            result = self.model.transcribe(audio, batch_size=16)
            
            # 检测语言
            detected_language = result.get("language", language)
            if detected_language == "auto":
                detected_language = "en"
            
            # 加载对齐模型
            if not self.align_model or self.metadata is None:
                self.align_model, self.metadata = whisperx.load_align_model(
                    language_code=detected_language, device=self.device
                )
            
            # 执行对齐
            result = whisperx.align(result["segments"], self.align_model, 
                                 self.metadata, audio, self.device, 
                                 return_char_alignments=False)
            
            # 转换为标准格式
            segments = []
            for segment in result["segments"]:
                segments.append({
                    "start": segment.get("start", 0),
                    "end": segment.get("end", 0),
                    "text": segment["text"].strip()
                })
            
            return segments
        except Exception as e:
            print(f"WhisperX识别失败: {e}")
            return []


class SpeechRecognitionPlugin:
    """语音识别插件主类"""
    
    def __init__(self):
        self.recognizers = {}
        self.current_recognizer = None
        self._initialize_recognizers()
    
    def _initialize_recognizers(self):
        """初始化识别器"""
        # 尝试加载Whisper
        try:
            self.recognizers["whisper_tiny"] = WhisperRecognizer("tiny")
            self.recognizers["whisper_base"] = WhisperRecognizer("base")
            self.recognizers["whisper_small"] = WhisperRecognizer("small")
            self.recognizers["whisper_medium"] = WhisperRecognizer("medium")
            self.recognizers["whisper_large"] = WhisperRecognizer("large")
        except Exception as e:
            print(f"初始化Whisper识别器失败: {e}")
        
        # 尝试加载WhisperX
        try:
            self.recognizers["whisperx_base"] = WhisperXRecognizer("base")
            self.recognizers["whisperx_small"] = WhisperXRecognizer("small")
            self.recognizers["whisperx_medium"] = WhisperXRecognizer("medium")
            self.recognizers["whisperx_large"] = WhisperXRecognizer("large")
        except Exception as e:
            print(f"初始化WhisperX识别器失败: {e}")
    
    def get_name(self) -> str:
        """获取插件名称"""
        return "语音识别器"
    
    def get_description(self) -> str:
        """获取插件描述"""
        return "支持多种语音识别引擎的音频转文字插件，包括Whisper和WhisperX"
    
    def get_version(self) -> str:
        """获取插件版本"""
        return "1.0.0"
    
    def get_available_recognizers(self) -> Dict[str, str]:
        """获取可用的识别器列表"""
        available = {}
        for key, recognizer in self.recognizers.items():
            if recognizer.model is not None:  # 只返回成功加载的识别器
                available[key] = recognizer.get_name()
        return available
    
    def set_recognizer(self, recognizer_type: str) -> bool:
        """设置当前使用的识别器"""
        if recognizer_type in self.recognizers:
            recognizer = self.recognizers[recognizer_type]
            if recognizer.model is not None:
                self.current_recognizer = recognizer
                return True
        return False
    
    def get_supported_languages(self) -> Dict[str, str]:
        """获取当前识别器支持的语言"""
        if self.current_recognizer:
            return self.current_recognizer.get_supported_languages()
        return {}
    
    def get_supported_formats(self) -> List[str]:
        """获取当前识别器支持的格式"""
        if self.current_recognizer:
            return self.current_recognizer.get_supported_formats()
        return []
    
    def recognize_audio_file(self, audio_path: str, language: str = "auto") -> Tuple[bool, str]:
        """识别音频文件"""
        if not self.current_recognizer:
            return False, "未设置识别器"
        
        if not os.path.exists(audio_path):
            return False, "音频文件不存在"
        
        try:
            result = self.current_recognizer.recognize_audio(audio_path, language)
            if result:
                return True, result
            else:
                return False, "识别失败"
        except Exception as e:
            return False, f"识别过程出错: {str(e)}"
    
    def recognize_audio_with_timestamps(self, audio_path: str, language: str = "auto") -> Tuple[bool, Union[List[Dict], str]]:
        """识别音频文件并返回时间戳信息"""
        if not self.current_recognizer:
            return False, "未设置识别器"
        
        if not os.path.exists(audio_path):
            return False, "音频文件不存在"
        
        try:
            result = self.current_recognizer.recognize_audio_with_timestamps(audio_path, language)
            if result:
                return True, result
            else:
                return False, "识别失败"
        except Exception as e:
            return False, f"识别过程出错: {str(e)}"
    
    def convert_to_srt(self, segments: List[Dict], output_path: str) -> bool:
        """将时间戳段落转换为SRT字幕文件"""
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                for i, segment in enumerate(segments, 1):
                    start_time = self._seconds_to_srt_time(segment['start'])
                    end_time = self._seconds_to_srt_time(segment['end'])
                    text = segment['text'].strip()
                    
                    f.write(f"{i}\n")
                    f.write(f"{start_time} --> {end_time}\n")
                    f.write(f"{text}\n\n")
            
            return True
        except Exception as e:
            print(f"转换SRT失败: {e}")
            return False
    
    def _seconds_to_srt_time(self, seconds: float) -> str:
        """将秒数转换为SRT时间格式"""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = int(seconds % 60)
        milliseconds = int((seconds % 1) * 1000)
        return f"{hours:02d}:{minutes:02d}:{secs:02d},{milliseconds:03d}"
    
    def get_audio_info(self, audio_path: str) -> Dict:
        """获取音频文件信息"""
        try:
            import librosa
            
            # 加载音频
            y, sr = librosa.load(audio_path, sr=None)
            duration = len(y) / sr
            
            return {
                "duration": duration,
                "sample_rate": sr,
                "channels": 1 if len(y.shape) == 1 else y.shape[0],
                "format": os.path.splitext(audio_path)[1].lower()
            }
        except ImportError:
            # 如果没有librosa，使用wave库（仅支持wav格式）
            try:
                with wave.open(audio_path, 'rb') as wav_file:
                    frames = wav_file.getnframes()
                    sample_rate = wav_file.getframerate()
                    duration = frames / sample_rate
                    channels = wav_file.getnchannels()
                    
                    return {
                        "duration": duration,
                        "sample_rate": sample_rate,
                        "channels": channels,
                        "format": ".wav"
                    }
            except Exception:
                return {}
        except Exception as e:
            print(f"获取音频信息失败: {e}")
            return {}


# 插件实例（延迟初始化）
speech_recognition_plugin = None


def get_plugin():
    """获取插件实例"""
    global speech_recognition_plugin
    if speech_recognition_plugin is None:
        speech_recognition_plugin = SpeechRecognitionPlugin()
    return speech_recognition_plugin


if __name__ == "__main__":
    # 测试代码
    plugin = SpeechRecognitionPlugin()
    print(f"插件名称: {plugin.get_name()}")
    print(f"插件描述: {plugin.get_description()}")
    print(f"可用识别器: {plugin.get_available_recognizers()}") 