# FlipTalk AI 插件系统

FlipTalk AI 采用插件化架构，将各种功能模块化，便于维护、扩展和复用。

## 🚀 可用插件

### 1. 音频提取插件 (`audio_extractor`)
- **功能**: 从视频文件中提取音频
- **实现**: 基于FFmpeg
- **支持格式**: MP4, AVI, MOV, MKV等
- **输出格式**: WAV, MP3等

### 2. 字幕提取插件 (`subtitle_extractor`)
- **功能**: 音频转字幕（语音识别）
- **实现**: 基于WhisperX
- **支持语言**: 多语言自动识别
- **输出格式**: SRT字幕文件

### 3. 字幕翻译插件 (`subtitle_translator`)
- **功能**: 字幕文件翻译
- **支持服务**: 
  - 谷歌翻译（免费）
  - 微软翻译（需API Key）
  - DeepL Pro（需API Key）
- **支持语言**: 14种主要语言
- **支持格式**: SRT字幕文件

### 4. 语音识别插件 (`speech_recognition`)
- **功能**: 音频文件转文字
- **支持引擎**:
  - OpenAI Whisper（多个模型大小）
  - WhisperX（高精度版本）
- **支持格式**: WAV, MP3, M4A, FLAC等
- **输出格式**: 纯文本或带时间戳的SRT

## 📖 使用方法

### 基本使用

```python
# 导入插件系统
from plugins import get_available_plugins

# 获取所有可用插件
plugins = get_available_plugins()
print(f"可用插件: {list(plugins.keys())}")

# 使用字幕翻译插件
from plugins.subtitle_translator import get_plugin
translator = get_plugin()

# 设置翻译服务
translator.set_translator("google")  # 使用谷歌翻译

# 翻译字幕文件
success, message = translator.translate_file(
    input_path="input.srt",
    output_path="output_translated.srt", 
    source_lang="en",
    target_lang="zh",
    translator_type="google"
)

print(f"翻译结果: {message}")
```

### 语音识别示例

```python
# 使用语音识别插件
from plugins.speech_recognition import get_plugin
recognizer = get_plugin()

# 设置识别器
recognizer.set_recognizer("whisper_base")

# 识别音频文件
success, result = recognizer.recognize_audio_file("audio.wav", "zh")
if success:
    print(f"识别结果: {result}")

# 识别并生成字幕文件
success, segments = recognizer.recognize_audio_with_timestamps("audio.wav", "zh")
if success:
    recognizer.convert_to_srt(segments, "output.srt")
```

## 🔧 插件开发

### 插件结构

每个插件都遵循以下目录结构：

```
plugins/
├── plugin_name/
│   ├── __init__.py          # 插件导出
│   └── plugin.py            # 插件主要实现
└── __init__.py              # 插件系统初始化
```

### 创建新插件

1. **创建插件目录**
```bash
mkdir plugins/my_plugin
```

2. **实现插件类**
```python
# plugins/my_plugin/plugin.py
class MyPlugin:
    def get_name(self) -> str:
        return "我的插件"
    
    def get_description(self) -> str:
        return "插件描述"
    
    def get_version(self) -> str:
        return "1.0.0"

def get_plugin():
    return MyPlugin()
```

3. **创建初始化文件**
```python
# plugins/my_plugin/__init__.py
from .plugin import MyPlugin, get_plugin
__all__ = ["MyPlugin", "get_plugin"]
```

4. **注册插件**
```python
# 在 plugins/__init__.py 中添加
try:
    from .my_plugin import MyPlugin
    plugins["my_plugin"] = MyPlugin
except ImportError as e:
    print(f"警告：无法导入我的插件 - {e}")
```

## 🌟 插件特性

### 优势

1. **模块化设计**: 每个功能独立实现，便于维护
2. **热插拔**: 支持动态加载和卸载插件
3. **统一接口**: 所有插件遵循相同的接口规范
4. **错误隔离**: 单个插件错误不影响其他功能
5. **易于扩展**: 可以轻松添加新的功能插件

### 依赖管理

- **按需加载**: 只有使用时才加载插件依赖
- **优雅降级**: 依赖缺失时插件仍可加载但功能受限
- **错误处理**: 完善的异常处理和错误提示

## 📋 依赖要求

### 基础依赖
```bash
pip install requests  # 用于网络请求
```

### 语音识别依赖（可选）
```bash
# OpenAI Whisper
pip install openai-whisper

# WhisperX（更高精度）
pip install whisperx

# 音频处理
pip install librosa
```

### 注意事项

1. **NumPy版本**: 某些依赖可能需要特定版本的NumPy
2. **GPU支持**: WhisperX支持CUDA加速（需要适当的GPU驱动）
3. **API配额**: 某些翻译服务有使用限制

## 🚀 快速开始

1. **检查可用插件**
```python
from plugins import get_available_plugins
print(get_available_plugins())
```

2. **使用字幕翻译功能**
- 在主界面点击"字幕翻译"功能卡片
- 选择翻译服务和语言
- 导入SRT文件并开始翻译

3. **扩展新功能**
- 基于现有插件接口实现新功能
- 注册到插件系统
- 在UI中集成调用

通过插件系统，FlipTalk AI 实现了功能的模块化管理，为后续功能扩展和代码复用提供了良好的基础架构。 