#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FlipTalk AI - 插件模块
包含各种功能插件的实现
"""

# 使用延迟导入避免循环导入问题
def get_available_plugins():
    """获取可用插件字典"""
    plugins = {}
    
    try:
        from .audio_extractor import FFmpegAudioExtractor
        plugins["audio_extractor"] = FFmpegAudioExtractor
    except ImportError as e:
        print(f"警告：无法导入音频提取插件 - {e}")
    
    try:
        from .voice_separator import VoiceSeparatorPlugin
        plugins["voice_separator"] = VoiceSeparatorPlugin
    except ImportError as e:
        print(f"警告：无法导入人声分离插件 - {e}")
    
    try:
        from .subtitle_extractor import WhisperXSubtitleExtractor
        plugins["subtitle_extractor"] = WhisperXSubtitleExtractor
    except ImportError as e:
        print(f"警告：无法导入字幕提取插件 - {e}")
    
    try:
        from .subtitle_translator import SubtitleTranslatorPlugin
        plugins["subtitle_translator"] = SubtitleTranslatorPlugin
    except ImportError as e:
        print(f"警告：无法导入字幕翻译插件 - {e}")
    
    try:
        from .speech_recognition import SpeechRecognitionPlugin
        plugins["speech_recognition"] = SpeechRecognitionPlugin
    except ImportError as e:
        print(f"警告：无法导入语音识别插件 - {e}")
    
    try:
        from .tts.tts_manager_plugin import TtsManagerPlugin
        plugins["tts_manager"] = TtsManagerPlugin
    except ImportError as e:
        print(f"警告：无法导入TTS管理插件 - {e}")
    
    return plugins

# 插件注册表（延迟加载）
AVAILABLE_PLUGINS = get_available_plugins()

# 导出接口
__all__ = ["AVAILABLE_PLUGINS", "get_available_plugins"]