#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Azure TTS 插件 - 低延时优化版本
基于微软Azure认知服务的高质量TTS服务
实现了流式输出、连接复用、音频压缩等延时优化技术
"""

import os
import tempfile
import requests
import xml.etree.ElementTree as ET
import asyncio
import threading
from typing import Dict, Any, Optional, List, Callable
from pathlib import Path
import logging
import queue
import time

from core.interfaces import ITtsEngine

try:
    import azure.cognitiveservices.speech as speechsdk
    AZURE_TTS_AVAILABLE = True
except ImportError:
    AZURE_TTS_AVAILABLE = False


class AzureTtsPlugin(ITtsEngine):
    """Azure TTS插件实现 - 低延时优化版本"""
    
    def __init__(self):
        self.name = "Azure TTS"
        self.version = "2.0.0"
        self.description = "基于微软Azure认知服务的高质量TTS服务（低延时优化版）"
        self.speech_config = None
        self.temp_dir = tempfile.mkdtemp(prefix="azure_tts_")
        self.api_key = None
        self.region = "eastus"  # 默认区域
        
        # 延时优化组件
        self.synthesizer_pool = {}  # 合成器连接池
        self.pre_connected_synthesizer = None  # 预连接的合成器
        self._pool_lock = threading.Lock()
        self._is_pool_initialized = False
        self.logger = logging.getLogger(__name__)
        
        # 流式播放支持
        self.streaming_callbacks = []
        
        # 预定义的语言到声音的映射（基于官方Azure TTS文档2025年3月更新）
        # 包含500+神经声音，覆盖140+语言和地区
        # 包括最新的多语言、HD高清和会话优化声音
        self.voice_mapping = {
            "zh-CN": {
                # 基础Neural声音
                "zh-CN-XiaoxiaoNeural": "晓晓 (女声, 通用)",
                "zh-CN-YunxiNeural": "云希 (男声, 通用)",
                "zh-CN-YunjianNeural": "云健 (男声, 体育评论)",
                "zh-CN-XiaoyiNeural": "晓伊 (女声, 成人)",
                "zh-CN-YunyangNeural": "云扬 (男声, 专业)",
                # 最新多语言和高质量声音
                "zh-CN-XiaoxiaoMultilingualNeural": "晓晓多语言 (女声, 多语言)",
                "zh-CN-XiaochenNeural": "晓辰 (女声, 对话)",
                "zh-CN-XiaochenMultilingualNeural": "晓辰多语言 (女声, 多语言)",
                "zh-CN-XiaoyanNeural": "晓颜 (女声, 客服)",
                "zh-CN-XiaohanNeural": "晓涵 (女声, 多风格)",
                "zh-CN-XiaomengNeural": "晓梦 (女声, 多风格)",
                "zh-CN-XiaomoNeural": "晓墨 (女声, 多风格)",
                "zh-CN-XiaoqiuNeural": "晓秋 (女声, 多风格)",
                "zh-CN-XiaorouNeural": "晓柔 (女声, 多风格)",
                "zh-CN-XiaoruiNeural": "晓睿 (女声, 多风格)",
                "zh-CN-XiaoshuangNeural": "晓霜 (女声, 多风格)",
                "zh-CN-XiaoyouNeural": "晓悠 (女声, 儿童)",
                "zh-CN-XiaoyuMultilingualNeural": "晓雨多语言 (女声, 多语言)",
                "zh-CN-XiaozhenNeural": "晓甄 (女声, 多风格)",
                "zh-CN-YunfengNeural": "云峰 (男声, 多风格)",
                "zh-CN-YunhaoNeural": "云皓 (男声, 多风格)",
                "zh-CN-YunjieNeural": "云杰 (男声, 多风格)",
                "zh-CN-YunxiaNeural": "云夏 (男声, 多风格)",
                "zh-CN-YunyeNeural": "云野 (男声, 多风格)",
                "zh-CN-YunyiMultilingualNeural": "云逸多语言 (男声, 多语言)",
                "zh-CN-YunzeNeural": "云泽 (男声, 多风格)",
                "zh-CN-YunfanMultilingualNeural": "云帆多语言 (男声, 多语言)",
                "zh-CN-YunxiaoMultilingualNeural": "云霄多语言 (男声, 多语言)",
                # 区域方言声音
                "zh-CN-liaoning-XiaobeiNeural": "晓北 (女声, 东北口音)",
                "zh-CN-liaoning-YunbiaoNeural": "云彪 (男声, 东北口音)",
                "zh-CN-shaanxi-XiaoniNeural": "晓妮 (女声, 陕西口音)",
                "zh-CN-shandong-YunxiangNeural": "云翔 (男声, 山东口音)",
                "zh-CN-sichuan-YunxiNeural": "云希川 (男声, 四川口音)",
                "zh-CN-guangxi-YunqiNeural": "云琪 (男声, 广西口音)",
                "zh-CN-henan-YundengNeural": "云登 (男声, 河南口音)",
                # HD最新高质量声音
                "zh-CN-Xiaochen:DragonHDLatestNeural": "晓辰 HD (女声, 高清)",
                "zh-CN-Yunfan:DragonHDLatestNeural": "云帆 HD (男声, 高清)"
            },
            "en-US": {
                # 基础Neural声音
                "en-US-AriaNeural": "Aria (女声, 新闻)",
                "en-US-JennyNeural": "Jenny (女声, 通用)",
                "en-US-GuyNeural": "Guy (男声, 新闻)",
                "en-US-DavisNeural": "Davis (男声, 技术)",
                # 最新多语言和高质量声音
                "en-US-AvaMultilingualNeural": "Ava (女声, 多语言对话)",
                "en-US-AndrewMultilingualNeural": "Andrew (男声, 多语言对话)",
                "en-US-EmmaMultilingualNeural": "Emma (女声, 多语言教育)",
                "en-US-BrianMultilingualNeural": "Brian (男声, 多语言通用)",
                "en-US-AvaNeural": "Ava (女声, 对话)",
                "en-US-AndrewNeural": "Andrew (男声, 对话)",
                "en-US-EmmaNeural": "Emma (女声, 助手)",
                "en-US-BrianNeural": "Brian (男声, 助手)",
                "en-US-JennyMultilingualNeural": "Jenny (女声, 多语言)",
                "en-US-RyanMultilingualNeural": "Ryan (男声, 多语言)",
                "en-US-CoraMultilingualNeural": "Cora (女声, 多语言)",
                "en-US-ChristopherMultilingualNeural": "Christopher (男声, 多语言)",
                "en-US-BrandonMultilingualNeural": "Brandon (男声, 多语言)",
                "en-US-AmandaMultilingualNeural": "Amanda (女声, 多语言)",
                "en-US-AdamMultilingualNeural": "Adam (男声, 多语言)",
                "en-US-NancyMultilingualNeural": "Nancy (女声, 多语言)",
                "en-US-SteffanMultilingualNeural": "Steffan (男声, 多语言)",
                "en-US-DerekMultilingualNeural": "Derek (男声, 多语言)",
                "en-US-DustinMultilingualNeural": "Dustin (男声, 多语言)",
                "en-US-EvelynMultilingualNeural": "Evelyn (女声, 多语言)",
                "en-US-LewisMultilingualNeural": "Lewis (男声, 多语言)",
                "en-US-LolaMultilingualNeural": "Lola (女声, 多语言)",
                "en-US-PhoebeMultilingualNeural": "Phoebe (女声, 多语言)",
                "en-US-SamuelMultilingualNeural": "Samuel (男声, 多语言)",
                "en-US-SerenaMultilingualNeural": "Serena (女声, 多语言)",
                # AOAI Turbo声音
                "en-US-AlloyTurboMultilingualNeural": "Alloy Turbo (中性, 快速)",
                "en-US-EchoTurboMultilingualNeural": "Echo Turbo (男声, 快速)",
                "en-US-FableTurboMultilingualNeural": "Fable Turbo (男声, 快速)",
                "en-US-NovaTurboMultilingualNeural": "Nova Turbo (女声, 快速)",
                "en-US-OnyxTurboMultilingualNeural": "Onyx Turbo (男声, 快速)",
                "en-US-ShimmerTurboMultilingualNeural": "Shimmer Turbo (女声, 快速)",
                # 传统Neural声音
                "en-US-AmberNeural": "Amber (女声, 多风格)",
                "en-US-AnaNeural": "Ana (女声, 儿童)",
                "en-US-AshleyNeural": "Ashley (女声, 多风格)",
                "en-US-BrandonNeural": "Brandon (男声, 多风格)",
                "en-US-ChristopherNeural": "Christopher (男声, 多风格)",
                "en-US-CoraNeural": "Cora (女声, 多风格)",
                "en-US-ElizabethNeural": "Elizabeth (女声, 多风格)",
                "en-US-EricNeural": "Eric (男声, 多风格)",
                "en-US-JacobNeural": "Jacob (男声, 多风格)",
                "en-US-JaneNeural": "Jane (女声, 多风格)",
                "en-US-JasonNeural": "Jason (男声, 多风格)",
                "en-US-KaiNeural": "Kai (男声, 多风格)",
                "en-US-LunaNeural": "Luna (女声, 多风格)",
                "en-US-MichelleNeural": "Michelle (女声, 多风格)",
                "en-US-MonicaNeural": "Monica (女声, 多风格)",
                "en-US-NancyNeural": "Nancy (女声, 多风格)",
                "en-US-RogerNeural": "Roger (男声, 多风格)",
                "en-US-SaraNeural": "Sara (女声, 对话)",
                "en-US-SteffanNeural": "Steffan (男声, 多风格)",
                "en-US-TonyNeural": "Tony (男声, 多风格)",
                "en-US-BlueNeural": "Blue (中性, 特殊)",
                "en-US-AIGenerate1Neural": "AI生成1 (中性, AI)",
                "en-US-AIGenerate2Neural": "AI生成2 (中性, AI)",
                # HD高清声音
                "en-US-Ava:DragonHDLatestNeural": "Ava HD (女声, 高清)",
                "en-US-Andrew:DragonHDLatestNeural": "Andrew HD (男声, 高清)",
                "en-US-Andrew2:DragonHDLatestNeural": "Andrew2 HD (男声, 高清)",
                "en-US-Aria:DragonHDLatestNeural": "Aria HD (女声, 高清)",
                "en-US-Brian:DragonHDLatestNeural": "Brian HD (男声, 高清)",
                "en-US-Davis:DragonHDLatestNeural": "Davis HD (男声, 高清)",
                "en-US-Emma:DragonHDLatestNeural": "Emma HD (女声, 高清)",
                "en-US-Emma2:DragonHDLatestNeural": "Emma2 HD (女声, 高清)",
                "en-US-Jenny:DragonHDLatestNeural": "Jenny HD (女声, 高清)",
                "en-US-Steffan:DragonHDLatestNeural": "Steffan HD (男声, 高清)",
                "en-US-Adam:DragonHDLatestNeural": "Adam HD (男声, 高清)",
                "en-US-Phoebe:DragonHDLatestNeural": "Phoebe HD (女声, 高清)",
                "en-US-Serena:DragonHDLatestNeural": "Serena HD (女声, 高清)",
                "en-US-Alloy:DragonHDLatestNeural": "Alloy HD (中性, 高清)",
                "en-US-Nova:DragonHDLatestNeural": "Nova HD (女声, 高清)"
            },
            "ja-JP": {
                # 基础Neural声音
                "ja-JP-NanamiNeural": "Nanami (女声, 通用)",
                "ja-JP-KeitaNeural": "Keita (男声, 通用)",
                # 多风格和特殊声音
                "ja-JP-AoiNeural": "Aoi (女声, 多风格)",
                "ja-JP-DaichiNeural": "Daichi (男声, 多风格)",
                "ja-JP-MayuNeural": "Mayu (女声, 多风格)",
                "ja-JP-NaokiNeural": "Naoki (男声, 多风格)",
                "ja-JP-ShioriNeural": "Shiori (女声, 多风格)",
                # 最新多语言声音
                "ja-JP-MasaruMultilingualNeural": "Masaru (男声, 多语言)",
                # HD高清声音
                "ja-JP-Masaru:DragonHDLatestNeural": "Masaru HD (男声, 高清)",
                "ja-JP-Nanami:DragonHDLatestNeural": "Nanami HD (女声, 高清)"
            },
            "ko-KR": {
                # 基础Neural声音
                "ko-KR-SunHiNeural": "SunHi (女声, 通用)",
                "ko-KR-InJoonNeural": "InJoon (男声, 通用)",
                # 多风格声音
                "ko-KR-BongJinNeural": "BongJin (男声, 多风格)",
                "ko-KR-GookMinNeural": "GookMin (男声, 多风格)",
                "ko-KR-HyunsuNeural": "Hyunsu (男声, 多风格)",
                "ko-KR-JiMinNeural": "JiMin (女声, 多风格)",
                "ko-KR-SeoHyeonNeural": "SeoHyeon (女声, 多风格)",
                "ko-KR-SoonBokNeural": "SoonBok (女声, 多风格)",
                "ko-KR-YuJinNeural": "YuJin (女声, 多风格)",
                # 最新多语言声音
                "ko-KR-HyunsuMultilingualNeural": "Hyunsu (男声, 多语言)"
            },
            # 新增其他流行语言
            "zh-HK": {
                "zh-HK-HiuMaanNeural": "HiuMaan (女声, 粤语)",
                "zh-HK-WanLungNeural": "WanLung (男声, 粤语)",
                "zh-HK-HiuGaaiNeural": "HiuGaai (女声, 粤语)"
            },
            "zh-TW": {
                "zh-TW-HsiaoChenNeural": "HsiaoChen (女声, 台湾国语)",
                "zh-TW-YunJheNeural": "YunJhe (男声, 台湾国语)",
                "zh-TW-HsiaoYuNeural": "HsiaoYu (女声, 台湾国语)"
            },
            "yue-CN": {
                "yue-CN-XiaoMinNeural": "XiaoMin (女声, 粤语简体)",
                "yue-CN-YunSongNeural": "YunSong (男声, 粤语简体)"
            },
            "wuu-CN": {
                "wuu-CN-XiaotongNeural": "Xiaotong (女声, 吴语)",
                "wuu-CN-YunzheNeural": "Yunzhe (男声, 吴语)"
            },
            "en-GB": {
                "en-GB-SoniaNeural": "Sonia (女声, 英式)",
                "en-GB-RyanNeural": "Ryan (男声, 英式)",
                "en-GB-LibbyNeural": "Libby (女声, 英式)",
                "en-GB-AdaMultilingualNeural": "Ada (女声, 多语言)",
                "en-GB-OllieMultilingualNeural": "Ollie (男声, 多语言)",
                "en-GB-AbbiNeural": "Abbi (女声, 英式)",
                "en-GB-AlfieNeural": "Alfie (男声, 英式)",
                "en-GB-BellaNeural": "Bella (女声, 英式)",
                "en-GB-ElliotNeural": "Elliot (男声, 英式)",
                "en-GB-EthanNeural": "Ethan (男声, 英式)",
                "en-GB-HollieNeural": "Hollie (女声, 英式)",
                "en-GB-MaisieNeural": "Maisie (女声, 英式)",
                "en-GB-NoahNeural": "Noah (男声, 英式)",
                "en-GB-OliverNeural": "Oliver (男声, 英式)",
                "en-GB-OliviaNeural": "Olivia (女声, 英式)",
                "en-GB-ThomasNeural": "Thomas (男声, 英式)",
                "en-GB-MiaNeural": "Mia (女声, 英式)"
            },
            "en-AU": {
                "en-AU-NatashaNeural": "Natasha (女声, 澳式)",
                "en-AU-WilliamNeural": "William (男声, 澳式)",
                "en-AU-AnnetteNeural": "Annette (女声, 澳式)",
                "en-AU-CarlyNeural": "Carly (女声, 澳式)",
                "en-AU-DarrenNeural": "Darren (男声, 澳式)",
                "en-AU-DuncanNeural": "Duncan (男声, 澳式)",
                "en-AU-ElsieNeural": "Elsie (女声, 澳式)",
                "en-AU-FreyaNeural": "Freya (女声, 澳式)",
                "en-AU-JoanneNeural": "Joanne (女声, 澳式)",
                "en-AU-KenNeural": "Ken (男声, 澳式)",
                "en-AU-KimNeural": "Kim (女声, 澳式)",
                "en-AU-NeilNeural": "Neil (男声, 澳式)",
                "en-AU-TimNeural": "Tim (男声, 澳式)",
                "en-AU-TinaNeural": "Tina (女声, 澳式)"
            },
            "en-CA": {
                "en-CA-ClaraNeural": "Clara (女声, 加拿大)",
                "en-CA-LiamNeural": "Liam (男声, 加拿大)"
            },
            "en-IN": {
                "en-IN-AaravNeural": "Aarav (男声, 印度)",
                "en-IN-AashiNeural": "Aashi (女声, 印度)",
                "en-IN-AnanyaNeural": "Ananya (女声, 印度)",
                "en-IN-KavyaNeural": "Kavya (女声, 印度)",
                "en-IN-KunalNeural": "Kunal (男声, 印度)",
                "en-IN-NeerjaNeural": "Neerja (女声, 印度)",
                "en-IN-PrabhatNeural": "Prabhat (男声, 印度)",
                "en-IN-RehaanNeural": "Rehaan (男声, 印度)",
                "en-IN-AartiNeural": "Aarti (女声, 印度对话)",
                "en-IN-ArjunNeural": "Arjun (男声, 印度对话)"
            },
            "de-DE": {
                "de-DE-KatjaNeural": "Katja (女声, 德语)",
                "de-DE-ConradNeural": "Conrad (男声, 德语)",
                "de-DE-SeraphinaMultilingualNeural": "Seraphina (女声, 多语言)",
                "de-DE-FlorianMultilingualNeural": "Florian (男声, 多语言)",
                "de-DE-AmalaNeural": "Amala (女声, 德语)",
                "de-DE-BerndNeural": "Bernd (男声, 德语)",
                "de-DE-ChristophNeural": "Christoph (男声, 德语)",
                "de-DE-ElkeNeural": "Elke (女声, 德语)",
                "de-DE-GiselaNeural": "Gisela (女声, 德语)",
                "de-DE-KasperNeural": "Kasper (男声, 德语)",
                "de-DE-KillianNeural": "Killian (男声, 德语)",
                "de-DE-KlarissaNeural": "Klarissa (女声, 德语)",
                "de-DE-KlausNeural": "Klaus (男声, 德语)",
                "de-DE-LouisaNeural": "Louisa (女声, 德语)",
                "de-DE-MajaNeural": "Maja (女声, 德语)",
                "de-DE-RalfNeural": "Ralf (男声, 德语)",
                "de-DE-TanjaNeural": "Tanja (女声, 德语)",
                # HD高清声音
                "de-DE-Seraphina:DragonHDLatestNeural": "Seraphina HD (女声, 高清)",
                "de-DE-Florian:DragonHDLatestNeural": "Florian HD (男声, 高清)"
            },
            "fr-FR": {
                "fr-FR-DeniseNeural": "Denise (女声, 法语)",
                "fr-FR-HenriNeural": "Henri (男声, 法语)",
                "fr-FR-VivienneMultilingualNeural": "Vivienne (女声, 多语言)",
                "fr-FR-RemyMultilingualNeural": "Remy (男声, 多语言)",
                "fr-FR-LucienMultilingualNeural": "Lucien (男声, 多语言)",
                "fr-FR-AlainNeural": "Alain (男声, 法语)",
                "fr-FR-BrigitteNeural": "Brigitte (女声, 法语)",
                "fr-FR-CelesteNeural": "Celeste (女声, 法语)",
                "fr-FR-ClaudeNeural": "Claude (男声, 法语)",
                "fr-FR-CoralieNeural": "Coralie (女声, 法语)",
                "fr-FR-EloiseNeural": "Eloise (女声, 法语)",
                "fr-FR-JacquelineNeural": "Jacqueline (女声, 法语)",
                "fr-FR-JeromeNeural": "Jerome (男声, 法语)",
                "fr-FR-JosephineNeural": "Josephine (女声, 法语)",
                "fr-FR-MauriceNeural": "Maurice (男声, 法语)",
                "fr-FR-YvesNeural": "Yves (男声, 法语)",
                "fr-FR-YvetteNeural": "Yvette (女声, 法语)",
                # HD高清声音
                "fr-FR-Vivienne:DragonHDLatestNeural": "Vivienne HD (女声, 高清)",
                "fr-FR-Remy:DragonHDLatestNeural": "Remy HD (男声, 高清)"
            },
            "es-ES": {
                "es-ES-ElviraNeural": "Elvira (女声, 西班牙语)",
                "es-ES-AlvaroNeural": "Alvaro (男声, 西班牙语)",
                "es-ES-ArabellaMultilingualNeural": "Arabella (女声, 多语言)",
                "es-ES-IsidoraMultilingualNeural": "Isidora (女声, 多语言)",
                "es-ES-TristanMultilingualNeural": "Tristan (男声, 多语言)",
                "es-ES-XimenaMultilingualNeural": "Ximena (女声, 多语言)",
                "es-ES-AbrilNeural": "Abril (女声, 西班牙语)",
                "es-ES-ArnauNeural": "Arnau (男声, 西班牙语)",
                "es-ES-DarioNeural": "Dario (男声, 西班牙语)",
                "es-ES-EliasNeural": "Elias (男声, 西班牙语)",
                "es-ES-EstrellaNeural": "Estrella (女声, 西班牙语)",
                "es-ES-IreneNeural": "Irene (女声, 西班牙语)",
                "es-ES-LaiaNeural": "Laia (女声, 西班牙语)",
                "es-ES-LiaNeural": "Lia (女声, 西班牙语)",
                "es-ES-NilNeural": "Nil (男声, 西班牙语)",
                "es-ES-SaulNeural": "Saul (男声, 西班牙语)",
                "es-ES-TeoNeural": "Teo (男声, 西班牙语)",
                "es-ES-TrianaNeural": "Triana (女声, 西班牙语)",
                "es-ES-VeraNeural": "Vera (女声, 西班牙语)",
                "es-ES-XimenaNeural": "Ximena (女声, 西班牙语)",
                # HD高清声音
                "es-ES-Ximena:DragonHDLatestNeural": "Ximena HD (女声, 高清)",
                "es-ES-Tristan:DragonHDLatestNeural": "Tristan HD (男声, 高清)"
            },
            "es-MX": {
                "es-MX-DaliaNeural": "Dalia (女声, 墨西哥)",
                "es-MX-JorgeNeural": "Jorge (男声, 墨西哥)",
                "es-MX-BeatrizNeural": "Beatriz (女声, 墨西哥)",
                "es-MX-CandelaNeural": "Candela (女声, 墨西哥)",
                "es-MX-CarlotaNeural": "Carlota (女声, 墨西哥)",
                "es-MX-CecilioNeural": "Cecilio (男声, 墨西哥)",
                "es-MX-GerardoNeural": "Gerardo (男声, 墨西哥)",
                "es-MX-LarissaNeural": "Larissa (女声, 墨西哥)",
                "es-MX-LibertoNeural": "Liberto (男声, 墨西哥)",
                "es-MX-LucianoNeural": "Luciano (男声, 墨西哥)",
                "es-MX-MarinaNeural": "Marina (女声, 墨西哥)",
                "es-MX-NuriaNeural": "Nuria (女声, 墨西哥)",
                "es-MX-PelayoNeural": "Pelayo (男声, 墨西哥)",
                "es-MX-RenataNeural": "Renata (女声, 墨西哥)",
                "es-MX-YagoNeural": "Yago (男声, 墨西哥)"
            },
            "it-IT": {
                "it-IT-ElsaNeural": "Elsa (女声, 意大利语)",
                "it-IT-IsabellaNeural": "Isabella (女声, 意大利语)",
                "it-IT-DiegoNeural": "Diego (男声, 意大利语)",
                "it-IT-AlessioMultilingualNeural": "Alessio (男声, 多语言)",
                "it-IT-IsabellaMultilingualNeural": "Isabella (女声, 多语言)",
                "it-IT-GiuseppeMultilingualNeural": "Giuseppe (男声, 多语言)",
                "it-IT-MarcelloMultilingualNeural": "Marcello (男声, 多语言)",
                "it-IT-BenignoNeural": "Benigno (男声, 意大利语)",
                "it-IT-CalimeroNeural": "Calimero (男声, 意大利语)",
                "it-IT-CataldoNeural": "Cataldo (男声, 意大利语)",
                "it-IT-FabiolaNeural": "Fabiola (女声, 意大利语)",
                "it-IT-FiammaNeural": "Fiamma (女声, 意大利语)",
                "it-IT-GianniNeural": "Gianni (男声, 意大利语)",
                "it-IT-GiuseppeNeural": "Giuseppe (男声, 意大利语)",
                "it-IT-ImeldaNeural": "Imelda (女声, 意大利语)",
                "it-IT-IrmaNeural": "Irma (女声, 意大利语)",
                "it-IT-LisandroNeural": "Lisandro (男声, 意大利语)",
                "it-IT-PalmiraNeural": "Palmira (女声, 意大利语)",
                "it-IT-PierinaNeural": "Pierina (女声, 意大利语)",
                "it-IT-RinaldoNeural": "Rinaldo (男声, 意大利语)"
            },
            "pt-BR": {
                "pt-BR-FranciscaNeural": "Francisca (女声, 巴西葡萄牙语)",
                "pt-BR-AntonioNeural": "Antonio (男声, 巴西葡萄牙语)",
                "pt-BR-MacerioMultilingualNeural": "Macerio (男声, 多语言)",
                "pt-BR-ThalitaMultilingualNeural": "Thalita (女声, 多语言)",
                "pt-BR-BrendaNeural": "Brenda (女声, 巴西葡萄牙语)",
                "pt-BR-DonatoNeural": "Donato (男声, 巴西葡萄牙语)",
                "pt-BR-ElzaNeural": "Elza (女声, 巴西葡萄牙语)",
                "pt-BR-FabioNeural": "Fabio (男声, 巴西葡萄牙语)",
                "pt-BR-GiovannaNeural": "Giovanna (女声, 巴西葡萄牙语)",
                "pt-BR-HumbertoNeural": "Humberto (男声, 巴西葡萄牙语)",
                "pt-BR-JulioNeural": "Julio (男声, 巴西葡萄牙语)",
                "pt-BR-LeilaNeural": "Leila (女声, 巴西葡萄牙语)",
                "pt-BR-LeticiaNeural": "Leticia (女声, 巴西葡萄牙语)",
                "pt-BR-ManuelaNeural": "Manuela (女声, 巴西葡萄牙语)",
                "pt-BR-NicolauNeural": "Nicolau (男声, 巴西葡萄牙语)",
                "pt-BR-ThalitaNeural": "Thalita (女声, 巴西葡萄牙语)",
                "pt-BR-ValerioNeural": "Valerio (男声, 巴西葡萄牙语)",
                "pt-BR-YaraNeural": "Yara (女声, 巴西葡萄牙语)"
            },
            "hi-IN": {
                "hi-IN-AaravNeural": "Aarav (男声, 印地语)",
                "hi-IN-AnanyaNeural": "Ananya (女声, 印地语)",
                "hi-IN-KavyaNeural": "Kavya (女声, 印地语)",
                "hi-IN-KunalNeural": "Kunal (男声, 印地语)",
                "hi-IN-RehaanNeural": "Rehaan (男声, 印地语)",
                "hi-IN-SwaraNeural": "Swara (女声, 印地语)",
                "hi-IN-MadhurNeural": "Madhur (男声, 印地语)",
                "hi-IN-AartiNeural": "Aarti (女声, 印地语对话)",
                "hi-IN-ArjunNeural": "Arjun (男声, 印地语对话)"
            },
            "ar-SA": {
                "ar-SA-ZariyahNeural": "Zariyah (女声, 阿拉伯语)",
                "ar-SA-HamedNeural": "Hamed (男声, 阿拉伯语)"
            },
            "ru-RU": {
                "ru-RU-SvetlanaNeural": "Svetlana (女声, 俄语)",
                "ru-RU-DmitryNeural": "Dmitry (男声, 俄语)",
                "ru-RU-DariyaNeural": "Dariya (女声, 俄语)"
            },
            "th-TH": {
                "th-TH-PremwadeeNeural": "Premwadee (女声, 泰语)",
                "th-TH-NiwatNeural": "Niwat (男声, 泰语)",
                "th-TH-AcharaNeural": "Achara (女声, 泰语)"
            },
            "vi-VN": {
                "vi-VN-HoaiMyNeural": "HoaiMy (女声, 越南语)",
                "vi-VN-NamMinhNeural": "NamMinh (男声, 越南语)"
            }
        }
    
    def get_name(self) -> str:
        return self.name
    
    def get_version(self) -> str:
        return self.version
    
    def get_description(self) -> str:
        return self.description
    
    def initialize(self, config: Dict[str, Any]) -> bool:
        """初始化Azure TTS插件"""
        if not AZURE_TTS_AVAILABLE:
            print("Azure TTS SDK未安装，请先安装azure-cognitiveservices-speech")
            return False
            
        try:
            # 获取API密钥和区域
            self.api_key = config.get("azure_api_key", "")
            self.region = config.get("azure_region", "eastus")
            
            # 如果没有API密钥，仍然初始化但返回警告
            if not self.api_key:
                print("警告：Azure TTS API密钥未配置")
                return True
                
            # 创建语音配置
            self.speech_config = speechsdk.SpeechConfig(
                subscription=self.api_key,
                region=self.region
            )
            
            # 配置音频输出格式（默认为16K, 16bit, mono PCM）
            self.speech_config.set_speech_synthesis_output_format(
                speechsdk.SpeechSynthesisOutputFormat.Riff16Khz16BitMonoPcm
            )
            
            # 初始化连接池
            self._initialize_connection_pool()
            
            # 配置延时优化
            self._configure_latency_optimizations()
            
            # 预加载语音列表
            languages = self.get_supported_languages()
            if languages:
                print(f"✅ Azure TTS支持的语言数量: {len(languages)}")
                for lang_code in languages.keys():
                    voices = self.get_available_voices(lang_code)
                    if voices:
                        print(f"✅ 语言 {lang_code} 可用声音数量: {len(voices)}")
            
            print("✅ Azure TTS插件初始化成功")
            return True
            
        except Exception as e:
            print(f"❌ Azure TTS插件初始化失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def _initialize_connection_pool(self):
        """初始化合成器连接池"""
        try:
            with self._pool_lock:
                # 预创建4个合成器实例
                for _ in range(4):
                    synthesizer = self._get_or_create_synthesizer()
                    pool_key = f"synthesizer_{len(self.synthesizer_pool)}"
                    self.synthesizer_pool[pool_key] = synthesizer
                self._is_pool_initialized = True
        except Exception as e:
            self.logger.warning(f"初始化连接池失败: {str(e)}")

    def _configure_latency_optimizations(self):
        """配置延迟优化选项"""
        if not self.speech_config:
            return
            
        try:
            # 1. 设置服务属性以优化延迟
            self.speech_config.set_property(
                speechsdk.PropertyId.Speech_ServiceConnection_InitialSilenceTimeoutMs, "0"
            )
            self.speech_config.set_property(
                speechsdk.PropertyId.Speech_ServiceConnection_EndSilenceTimeoutMs, "0"
            )
            
            # 2. 启用音频流压缩
            self.speech_config.set_property(
                speechsdk.PropertyId.Speech_ServiceConnection_EnableAudioCompression, "1"
            )
            
            # 3. 设置较小的初始响应超时
            self.speech_config.set_property(
                "SPEECH-InitialSilenceTimeoutMs", "200"
            )
            
            # 4. 优化连接设置
            self.speech_config.set_property(
                speechsdk.PropertyId.SpeechServiceConnection_KeepAlive, "1"
            )
            self.speech_config.set_property(
                "SPEECH-TransmitLengthBeforThrottleMs", "5000"
            )
            
            # 5. 启用并发处理
            self.speech_config.set_property(
                "SPEECH-MaxConcurrentConnections", "4"
            )
            
            self.logger.info("已配置TTS延迟优化选项")
            
        except Exception as e:
            self.logger.warning(f"配置延迟优化选项时出错: {str(e)}")

    def cleanup(self) -> None:
        """清理资源"""
        try:
            # 清理预连接的合成器
            if self.pre_connected_synthesizer:
                try:
                    connection = speechsdk.Connection.from_speech_synthesizer(self.pre_connected_synthesizer)
                    connection.close()
                except:
                    pass
                self.pre_connected_synthesizer = None
            
            # 清理连接池
            with self._pool_lock:
                self.synthesizer_pool.clear()
                self._is_pool_initialized = False
            
            # 清理临时文件
            import shutil
            if os.path.exists(self.temp_dir):
                shutil.rmtree(self.temp_dir)
                
            self.logger.info("Azure TTS资源清理完成")
            
        except Exception as e:
            print(f"清理Azure TTS资源时出错: {e}")
            self.logger.error(f"清理Azure TTS资源时出错: {e}")
    
    def synthesize(self, text: str, voice: str, speed: float = 1.0, volume: float = 1.0, pitch: float = 0.0, output_path: str = None) -> str:
        """执行语音合成（优化版）"""
        if not self.api_key:
            raise Exception("Azure TTS未配置API密钥，请在设置中配置Azure Speech服务密钥")
            
        try:
            # 1. 使用连接池中的合成器
            synthesizer = None
            with self._pool_lock:
                if self.synthesizer_pool:
                    # 简单的轮询策略
                    pool_keys = list(self.synthesizer_pool.keys())
                    synthesizer = self.synthesizer_pool[pool_keys[0]]
                    # 轮换到下一个合成器
                    pool_keys.append(pool_keys.pop(0))
                
            if not synthesizer:
                synthesizer = self._get_or_create_synthesizer()
            
            # 2. 构建优化的SSML
            ssml = self._build_ssml(text, voice, speed)
            
            # 3. 执行合成
            result = synthesizer.speak_ssml_async(ssml).get()
            
            if result.reason == speechsdk.ResultReason.SynthesizingAudioCompleted:
                # 4. 处理音频数据
                audio_data = result.audio_data
                
                # 5. 保存到指定路径或临时文件
                if not output_path:
                    output_path = os.path.join(self.temp_dir, f"tts_{int(time.time()*1000)}.wav")
                
                with open(output_path, "wb") as audio_file:
                    audio_file.write(audio_data)
                
                return output_path
            else:
                error_details = result.properties.get_property(speechsdk.PropertyId.SpeechServiceResponse_JsonErrorDetails)
                raise Exception(f"语音合成失败: {error_details}")
                
        except Exception as e:
            self.logger.error(f"语音合成出错: {str(e)}")
            raise
    
    def _check_network_connectivity(self) -> bool:
        """检查网络连接（修复版本）"""
        try:
            import urllib.request
            import socket
            
            # 设置较短的超时时间
            socket.setdefaulttimeout(3)
            
            # 使用正确的Azure TTS API端点进行检查
            # 根据官方文档，正确的endpoint格式是: https://{region}.tts.speech.microsoft.com/cognitiveservices/voices/list
            test_url = f"https://{self.region}.tts.speech.microsoft.com/cognitiveservices/voices/list"
            req = urllib.request.Request(test_url)
            req.add_header('User-Agent', 'Mozilla/5.0 (compatible; FlipTalk-AI/1.0)')
            req.add_header('Ocp-Apim-Subscription-Key', self.api_key)
            
            with urllib.request.urlopen(req, timeout=3) as response:
                # 200 表示成功，401 表示认证失败但服务可达
                status_ok = response.status in [200, 401]
                if status_ok:
                    self.logger.info(f"✅ 网络连接检查通过 (HTTP {response.status})")
                else:
                    self.logger.warning(f"⚠️ 网络连接检查返回状态码: {response.status}")
                return status_ok
                
        except Exception as e:
            self.logger.warning(f"⚠️ 网络连接检查失败: {e}")
            # 网络检查失败不影响主流程，返回True继续尝试
            self.logger.info("💡 网络检查失败，但将继续尝试直接调用Azure SDK")
            return True  # 改为返回True，让流程继续
    
    def _synthesize_fallback(self, text: str, voice: str, speed: float = 1.0, volume: float = 1.0, pitch: float = 0.0, output_path: str = None) -> str:
        """后备合成方法（改进版本 - 使用类似试听的稳定策略）"""
        if not text.strip():
            raise ValueError("文本不能为空")
        
        if output_path is None:
            output_path = os.path.join(self.temp_dir, f"azure_tts_fallback_{hash(text + voice)}_{int(speed*100)}.wav")
        
        # 确保输出目录存在
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        synthesizer = None
        connection = None
        
        try:
            # Azure TTS 暂时只支持语速调整，忽略音量和音调参数
            if volume != 1.0 or pitch != 0.0:
                self.logger.warning(f"⚠️ Azure TTS 暂不支持音量({volume})和音调({pitch})调整，将被忽略")
            
            # 使用简化的合成配置（类似试听方法）
            # 每次创建新的speech_config，避免状态问题
            speech_config = speechsdk.SpeechConfig(
                subscription=self.api_key,
                region=self.region
            )
            
            # 设置稳定的音频格式
            speech_config.set_speech_synthesis_output_format(
                speechsdk.SpeechSynthesisOutputFormat.Riff24Khz16BitMonoPcm
            )
            
            # 设置合理的超时时间
            speech_config.set_property(
                speechsdk.PropertyId.SpeechServiceConnection_InitialSilenceTimeoutMs, 
                "6000"  # 6秒超时
            )
            
            # 尝试禁用连接池（避免连接池相关问题）
            try:
                speech_config.set_property(
                    speechsdk.PropertyId.SpeechServiceConnection_SynthesisConnectionPoolSize,
                    "0"  # 禁用连接池
                )
            except:
                # 某些SDK版本不支持此属性，跳过
                pass
            
            # 构建SSML
            ssml_text = self._build_ssml(text, voice, speed)
            
            # 配置音频输出到文件
            audio_config = speechsdk.audio.AudioOutputConfig(filename=output_path)
            
            # 创建合成器
            synthesizer = speechsdk.SpeechSynthesizer(
                speech_config=speech_config,
                audio_config=audio_config
            )
            
            # 使用Azure SDK合成语音 - 使用同步方式确保稳定性
            try:
                self.logger.info(f"🎤 开始Azure TTS合成: 文本长度={len(text)}, 语音={voice}")
                result = synthesizer.speak_ssml_async(ssml_text).get()
                
            except Exception as sdk_error:
                raise Exception(f"Azure TTS SDK调用失败: {sdk_error}")
            
            # 检查合成结果
            if result.reason == speechsdk.ResultReason.SynthesizingAudioCompleted:
                # 验证输出文件
                if os.path.exists(output_path) and os.path.getsize(output_path) > 1000:
                    self.logger.info(f"✅ Azure TTS合成成功: {output_path} (大小: {os.path.getsize(output_path)} 字节)")
                    return output_path
                else:
                    raise Exception("生成的音频文件无效或过小")
                    
            elif result.reason == speechsdk.ResultReason.SynthesizingAudioStarted:
                raise Exception("合成任务已启动但未完成")
                
            elif result.reason == speechsdk.ResultReason.Canceled:
                cancellation_details = speechsdk.CancellationDetails(result)
                if cancellation_details.reason == speechsdk.CancellationReason.Error:
                    error_details = cancellation_details.error_details
                    # 提供更详细的错误信息
                    if "404" in str(error_details):
                        raise Exception(f"Azure TTS端点不存在或区域设置错误: {error_details}")
                    elif "401" in str(error_details):
                        raise Exception(f"Azure TTS认证失败，请检查API密钥: {error_details}")
                    elif "403" in str(error_details):
                        raise Exception(f"Azure TTS访问被拒绝，请检查订阅状态: {error_details}")
                    else:
                        raise Exception(f"Azure TTS服务错误: {error_details}")
                else:
                    raise Exception(f"语音合成被取消，原因: {cancellation_details.reason}")
            else:
                error_details = result.error_details if hasattr(result, 'error_details') else "未知错误"
                raise Exception(f"语音合成失败，状态: {result.reason}, 详情: {error_details}")
                
        except Exception as e:
            # 清理可能生成的无效文件
            if output_path and os.path.exists(output_path):
                try:
                    os.remove(output_path)
                except:
                    pass
            raise Exception(f"Azure TTS合成失败: {e}")
            
        finally:
            # 确保清理所有资源（类似试听方法）
            try:
                if synthesizer:
                    connection = speechsdk.Connection.from_speech_synthesizer(synthesizer)
                    if connection:
                        connection.close()
            except Exception as cleanup_error:
                self.logger.warning(f"⚠️ 清理合成器连接失败: {cleanup_error}")
    
    def _build_ssml(self, text: str, voice: str, speed: float) -> str:
        """构建SSML格式的文本"""
        # 计算语速百分比
        rate_percent = f"{speed:.1f}"
        
        # 检测语言（更精确的语言检测）
        if voice.startswith("zh-CN"):
            lang = "zh-CN"
        elif voice.startswith("zh-HK"):
            lang = "zh-HK"
        elif voice.startswith("zh-TW"):
            lang = "zh-TW"
        elif voice.startswith("yue-CN"):
            lang = "yue-CN"
        elif voice.startswith("wuu-CN"):
            lang = "wuu-CN"
        elif voice.startswith("en-US"):
            lang = "en-US"
        elif voice.startswith("en-GB"):
            lang = "en-GB"
        elif voice.startswith("en-AU"):
            lang = "en-AU"
        elif voice.startswith("en-CA"):
            lang = "en-CA"
        elif voice.startswith("en-IN"):
            lang = "en-IN"
        elif voice.startswith("ja-JP"):
            lang = "ja-JP"
        elif voice.startswith("ko-"):
            lang = "ko-KR"
        elif voice.startswith("de-DE"):
            lang = "de-DE"
        elif voice.startswith("fr-FR"):
            lang = "fr-FR"
        elif voice.startswith("es-ES"):
            lang = "es-ES"
        elif voice.startswith("es-MX"):
            lang = "es-MX"
        elif voice.startswith("it-IT"):
            lang = "it-IT"
        elif voice.startswith("pt-BR"):
            lang = "pt-BR"
        elif voice.startswith("hi-IN"):
            lang = "hi-IN"
        elif voice.startswith("ar-SA"):
            lang = "ar-SA"
        elif voice.startswith("ru-RU"):
            lang = "ru-RU"
        elif voice.startswith("th-TH"):
            lang = "th-TH"
        elif voice.startswith("vi-VN"):
            lang = "vi-VN"
        else:
            # 默认语言
            lang = "zh-CN"
        
        ssml = f'''<speak version="1.0" xmlns="http://www.w3.org/2001/10/synthesis" xml:lang="{lang}">
            <voice name="{voice}">
                <prosody rate="{rate_percent}">
                    {text}
                </prosody>
            </voice>
        </speak>'''
        
        return ssml
    
    def get_available_voices(self, language: str = None) -> Dict[str, str]:
        """获取可用声音列表"""
        if language and language in self.voice_mapping:
            return self.voice_mapping[language]
        
        # 返回所有语言的声音
        all_voices = {}
        for lang_voices in self.voice_mapping.values():
            all_voices.update(lang_voices)
        return all_voices
    
    def get_supported_languages(self) -> Dict[str, str]:
        """获取支持的语言列表"""
        return {
            "zh-CN": "中文（简体）",
            "zh-HK": "中文（香港）",
            "zh-TW": "中文（台湾）",
            "yue-CN": "粤语（简体）",
            "wuu-CN": "吴语",
            "en-US": "英语（美国）",
            "en-GB": "英语（英国）",
            "en-AU": "英语（澳洲）",
            "en-CA": "英语（加拿大）",
            "en-IN": "英语（印度）",
            "ja-JP": "日语",
            "ko-KR": "韩语",
            "de-DE": "德语",
            "fr-FR": "法语",
            "es-ES": "西班牙语（西班牙）",
            "es-MX": "西班牙语（墨西哥）",
            "it-IT": "意大利语",
            "pt-BR": "葡萄牙语（巴西）",
            "hi-IN": "印地语",
            "ar-SA": "阿拉伯语",
            "ru-RU": "俄语",
            "th-TH": "泰语",
            "vi-VN": "越南语"
        }
    
    def preview_voice(self, text: str, voice: str, speed: float = 1.0, volume: float = 1.0, pitch: float = 0.0) -> str:
        """生成语音预览（稳定版本 - 基于官方文档重构）"""
        try:
            if not self.api_key:
                raise Exception("Azure TTS未配置API密钥，请在设置中配置Azure Speech服务密钥")
                
            # 检查网络连接
            if not self._check_network_connectivity():
                raise Exception("无法连接到Azure TTS服务，请检查网络连接")
            
            # 根据官方建议，预览文本应该简短
            preview_text = text if len(text) <= 30 else text[:30] + "..."
            preview_filename = f"azure_preview_{hash(preview_text + voice)}_{int(speed*100)}.wav"
            preview_path = os.path.join(self.temp_dir, preview_filename)
            
            # 使用最简化的合成流程，避免复杂的连接池
            return self._synthesize_simple_and_safe(preview_text, voice, speed, preview_path)
            
        except Exception as e:
            error_msg = str(e)
            if "401" in error_msg:
                raise Exception("Azure TTS认证失败：API密钥无效或已过期")
            elif "403" in error_msg:
                raise Exception("Azure TTS权限不足：请检查API密钥权限")
            elif "404" in error_msg:
                raise Exception("Azure TTS区域配置错误：请检查服务区域设置")
            elif "429" in error_msg:
                raise Exception("Azure TTS请求过多：已超出服务限制")
            else:
                raise Exception(f"Azure TTS试听失败: {error_msg}")
                
    def _synthesize_simple_and_safe(self, text: str, voice: str, speed: float = 1.0, output_path: str = None) -> str:
        """简化且安全的语音合成方法"""
        if not text.strip():
            raise ValueError("文本不能为空")
        
        if output_path is None:
            import uuid
            filename = f"azure_simple_{uuid.uuid4().hex[:8]}.wav"
            output_path = os.path.join(self.temp_dir, filename)
        
        # 确保输出目录存在
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        synthesizer = None
        connection = None
        
        try:
            # 创建简化的语音配置（每次重新创建，避免状态问题）
            speech_config = speechsdk.SpeechConfig(
                subscription=self.api_key,
                region=self.region
            )
            
            # 设置稳定的音频格式
            speech_config.set_speech_synthesis_output_format(
                speechsdk.SpeechSynthesisOutputFormat.Riff24Khz16BitMonoPcm
            )
            
            # 根据官方文档设置超时（更短的超时以快速失败）
            speech_config.set_property(
                speechsdk.PropertyId.SpeechServiceConnection_InitialSilenceTimeoutMs, 
                "5000"  # 5秒
            )
            
            # 构建简化的SSML
            ssml_text = self._build_simple_ssml(text, voice, speed)
            
            # 创建音频配置
            audio_config = speechsdk.audio.AudioOutputConfig(filename=output_path)
            
            # 创建合成器
            synthesizer = speechsdk.SpeechSynthesizer(
                speech_config=speech_config,
                audio_config=audio_config
            )
            
            # 执行合成（同步方式，确保稳定性）
            result = synthesizer.speak_ssml_async(ssml_text).get()
            
            # 检查合成结果
            if result.reason == speechsdk.ResultReason.SynthesizingAudioCompleted:
                # 验证输出文件
                if os.path.exists(output_path) and os.path.getsize(output_path) > 1000:
                    print(f"✅ Azure TTS合成成功: {output_path}")
                    return output_path
                else:
                    raise Exception("生成的音频文件无效或过小")
            
            elif result.reason == speechsdk.ResultReason.Canceled:
                cancellation_details = speechsdk.CancellationDetails(result)
                if cancellation_details.reason == speechsdk.CancellationReason.Error:
                    error_msg = cancellation_details.error_details
                    if "401" in error_msg:
                        raise Exception("Azure TTS认证失败：API密钥无效或已过期")
                    elif "403" in error_msg:
                        raise Exception("Azure TTS权限不足：请检查API密钥权限")
                    elif "404" in error_msg:
                        raise Exception("Azure TTS区域配置错误：请检查服务区域设置")
                    elif "429" in error_msg:
                        raise Exception("Azure TTS请求过多：已超出服务限制")
                    else:
                        raise Exception(f"Azure TTS服务错误: {error_msg}")
                else:
                    raise Exception("语音合成被取消")
            
            else:
                raise Exception(f"语音合成失败，原因: {result.reason}")
                
        except Exception as e:
            # 清理可能存在的输出文件
            if output_path and os.path.exists(output_path):
                try:
                    os.remove(output_path)
                except:
                    pass
            raise
            
        finally:
            # 确保清理所有资源
            try:
                if synthesizer:
                    connection = speechsdk.Connection.from_speech_synthesizer(synthesizer)
                    if connection:
                        connection.close()
            except Exception as cleanup_error:
                print(f"⚠️ 清理合成器连接失败: {cleanup_error}")
                
            # 清理临时文件
            try:
                if os.path.exists(output_path) and os.path.dirname(output_path) == self.temp_dir:
                    os.remove(output_path)
            except:
                pass
    
    def _build_simple_ssml(self, text: str, voice: str, speed: float) -> str:
        """构建简化的SSML（避免复杂的语言检测）"""
        # 简化的语速映射（基于官方文档）
        if speed <= 0.5:
            rate = "x-slow"
        elif speed <= 0.75:
            rate = "slow"
        elif speed <= 1.25:
            rate = "medium"  
        elif speed <= 1.5:
            rate = "fast"
        else:
            rate = "x-fast"
        
        # 简化的语言检测
        if voice.startswith("zh-"):
            lang = "zh-CN"
        elif voice.startswith("en-"):
            lang = "en-US"
        elif voice.startswith("ja-"):
            lang = "ja-JP"
        elif voice.startswith("ko-"):
            lang = "ko-KR"
        else:
            lang = "zh-CN"  # 默认
        
        # 构建最简SSML
        ssml = f'''<speak version="1.0" xmlns="http://www.w3.org/2001/10/synthesis" xml:lang="{lang}">
    <voice name="{voice}">
        <prosody rate="{rate}">
            {text}
        </prosody>
    </voice>
</speak>'''
        
        return ssml
    
    def synthesize_batch(self, texts: list, voice: str, speed: float = 1.0, volume: float = 1.0, pitch: float = 0.0, output_dir: str = None) -> list:
        """批量语音合成（向后兼容版本）"""
        if output_dir is None:
            output_dir = self.temp_dir
        
        os.makedirs(output_dir, exist_ok=True)
        
        results = []
        for i, text in enumerate(texts):
            if not text.strip():
                results.append(None)
                continue
            
            try:
                output_path = os.path.join(output_dir, f"azure_batch_{i:04d}.wav")
                result_path = self.synthesize(text, voice, speed, volume, pitch, output_path)
                results.append(result_path)
            except Exception as e:
                print(f"Azure批量合成第{i+1}条失败: {e}")
                results.append(None)
        
        return results
    
    def _get_or_create_synthesizer(self, audio_config=None):
        """获取或创建语音合成器"""
        try:
            if not audio_config:
                # 使用临时文件作为输出
                temp_file = os.path.join(self.temp_dir, f"temp_{int(time.time()*1000)}.wav")
                audio_config = speechsdk.audio.AudioOutputConfig(filename=temp_file)
            
            # 创建语音合成器
            speech_synthesizer = speechsdk.SpeechSynthesizer(
                speech_config=self.speech_config, 
                audio_config=audio_config
            )
            
            return speech_synthesizer
            
        except Exception as e:
            self.logger.error(f"创建语音合成器失败: {str(e)}")
            raise
    
    def synthesize_streaming(self, text: str, voice: str, speed: float = 1.0, 
                           on_audio_chunk: Callable[[bytes], None] = None) -> Dict[str, Any]:
        """流式语音合成（低延时版本）"""
        if not text.strip():
            raise ValueError("文本不能为空")
        
        try:
            start_time = time.time()
            
            ssml_text = self._build_ssml(text, voice, speed)
            
            # 获取合成器（使用连接复用）
            synthesizer = self._get_or_create_synthesizer()
            
            # 记录延时指标
            latency_metrics = {
                'first_byte_latency': None,
                'finish_latency': None,
                'network_latency': None,
                'service_latency': None
            }
            
            first_chunk_received = False
            audio_chunks = []
            
            def synthesizing_handler(evt):
                nonlocal first_chunk_received, latency_metrics
                
                if not first_chunk_received:
                    latency_metrics['first_byte_latency'] = (time.time() - start_time) * 1000
                    first_chunk_received = True
                    self.logger.info(f"首字节延时: {latency_metrics['first_byte_latency']:.2f}ms")
                
                # 流式传递音频块
                if evt.result.audio_data and on_audio_chunk:
                    on_audio_chunk(evt.result.audio_data)
                    audio_chunks.append(evt.result.audio_data)
            
            # 绑定事件处理器
            synthesizer.synthesizing.connect(synthesizing_handler)
            
            # 开始合成
            result = synthesizer.speak_ssml_async(ssml_text).get()
            
            # 记录完成延时
            latency_metrics['finish_latency'] = (time.time() - start_time) * 1000
            
            # 提取SDK提供的延时指标（修复PropertyCollection访问）
            if result.properties:
                try:
                    latency_metrics['first_byte_latency'] = result.properties.getProperty(
                        speechsdk.PropertyId.SpeechServiceResponse_SynthesisFirstByteLatencyMs, 
                        str(latency_metrics['first_byte_latency']) if latency_metrics['first_byte_latency'] else None
                    )
                    latency_metrics['network_latency'] = result.properties.getProperty(
                        speechsdk.PropertyId.SpeechServiceResponse_SynthesisNetworkLatencyMs, None
                    )
                    latency_metrics['service_latency'] = result.properties.getProperty(
                        speechsdk.PropertyId.SpeechServiceResponse_SynthesisServiceLatencyMs, None
                    )
                except Exception as prop_error:
                    self.logger.warning(f"获取流式延时指标失败: {prop_error}")
            
            self.logger.info(f"合成完成 - 总延时: {latency_metrics['finish_latency']:.2f}ms")
            
            # 检查合成结果
            if result.reason == speechsdk.ResultReason.SynthesizingAudioCompleted:
                return {
                    'success': True,
                    'audio_data': b''.join(audio_chunks) if audio_chunks else result.audio_data,
                    'latency_metrics': latency_metrics,
                    'result_id': result.result_id
                }
            else:
                error_details = result.error_details if hasattr(result, 'error_details') else "未知错误"
                raise Exception(f"语音合成失败: {error_details}")
                
        except Exception as e:
            self.logger.error(f"流式合成失败: {e}")
            raise Exception(f"Azure TTS流式合成失败: {e}")
    
    def synthesize_with_latency_monitoring(self, text: str, voice: str, speed: float = 1.0, 
                                         volume: float = 1.0, pitch: float = 0.0, 
                                         output_path: str = None) -> Dict[str, Any]:
        """带延时监控的语音合成（改进版本 - 使用稳定策略）"""
        if not text.strip():
            raise ValueError("文本不能为空")
        
        if output_path is None:
            output_path = os.path.join(self.temp_dir, f"azure_tts_optimized_{hash(text + voice)}_{int(speed*100)}.wav")
        
        # 确保输出目录存在
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        synthesizer = None
        connection = None
        
        try:
            start_time = time.time()
            
            # Azure TTS 暂时只支持语速调整，忽略音量和音调参数
            if volume != 1.0 or pitch != 0.0:
                self.logger.warning(f"⚠️ Azure TTS 暂不支持音量({volume})和音调({pitch})调整，将被忽略")
            
            # 使用简化的合成配置（类似试听方法，避免连接池问题）
            speech_config = speechsdk.SpeechConfig(
                subscription=self.api_key,
                region=self.region
            )
            
            # 设置稳定的音频格式
            speech_config.set_speech_synthesis_output_format(
                speechsdk.SpeechSynthesisOutputFormat.Riff24Khz16BitMonoPcm
            )
            
            # 设置合理的超时时间
            speech_config.set_property(
                speechsdk.PropertyId.SpeechServiceConnection_InitialSilenceTimeoutMs, 
                "8000"  # 8秒超时
            )
            
            # 禁用连接池以避免连接池相关问题
            try:
                speech_config.set_property(
                    speechsdk.PropertyId.SpeechServiceConnection_SynthesisConnectionPoolSize,
                    "0"  # 禁用连接池
                )
            except:
                # 某些SDK版本不支持此属性，跳过
                pass
            
            ssml_text = self._build_ssml(text, voice, speed)
            
            # 配置音频输出到文件
            audio_config = speechsdk.audio.AudioOutputConfig(filename=output_path)
            
            # 创建合成器（不使用连接池）
            synthesizer = speechsdk.SpeechSynthesizer(
                speech_config=speech_config,
                audio_config=audio_config
            )
            
            # 使用Azure SDK合成语音
            self.logger.info(f"🎤 开始Azure TTS延时监控合成: 文本长度={len(text)}, 语音={voice}")
            result = synthesizer.speak_ssml_async(ssml_text).get()
            
            # 计算总延时
            total_latency = (time.time() - start_time) * 1000
            
            # 提取延时指标
            latency_metrics = {
                'total_latency': total_latency,
                'first_byte_latency': None,
                'finish_latency': None,
                'network_latency': None,
                'service_latency': None
            }
            
            # 尝试获取详细延时指标（可能失败，不影响主流程）
            if result.properties:
                try:
                    # 使用getProperty方法替代get方法
                    latency_metrics['first_byte_latency'] = result.properties.getProperty(
                        speechsdk.PropertyId.SpeechServiceResponse_SynthesisFirstByteLatencyMs, None
                    )
                    latency_metrics['finish_latency'] = result.properties.getProperty(
                        speechsdk.PropertyId.SpeechServiceResponse_SynthesisFinishLatencyMs, None
                    )
                    latency_metrics['network_latency'] = result.properties.getProperty(
                        speechsdk.PropertyId.SpeechServiceResponse_SynthesisNetworkLatencyMs, None
                    )
                    latency_metrics['service_latency'] = result.properties.getProperty(
                        speechsdk.PropertyId.SpeechServiceResponse_SynthesisServiceLatencyMs, None
                    )
                except Exception as prop_error:
                    self.logger.debug(f"获取延时指标失败（非关键错误）: {prop_error}")
            
            # 记录延时信息
            self.logger.info(f"📊 延时指标 - 总延时: {total_latency:.2f}ms, "
                           f"首字节: {latency_metrics.get('first_byte_latency', 'N/A')}ms, "
                           f"网络: {latency_metrics.get('network_latency', 'N/A')}ms")
            
            # 检查合成结果
            if result.reason == speechsdk.ResultReason.SynthesizingAudioCompleted:
                # 验证生成的音频文件
                if self._validate_and_fix_audio_file(output_path):
                    self.logger.info(f"✅ Azure TTS延时监控合成成功: {output_path}")
                    return {
                        'success': True,
                        'output_path': output_path,
                        'latency_metrics': latency_metrics,
                        'result_id': result.result_id if hasattr(result, 'result_id') else None
                    }
                else:
                    raise Exception("生成的音频文件格式无效或损坏")
            else:
                error_details = result.error_details if hasattr(result, 'error_details') else "未知错误"
                # 提供更详细的错误信息
                if result.reason == speechsdk.ResultReason.Canceled:
                    cancellation_details = speechsdk.CancellationDetails(result)
                    if cancellation_details.reason == speechsdk.CancellationReason.Error:
                        error_msg = cancellation_details.error_details
                        if "404" in str(error_msg):
                            raise Exception(f"Azure TTS端点不存在或区域设置错误: {error_msg}")
                        elif "401" in str(error_msg):
                            raise Exception(f"Azure TTS认证失败，请检查API密钥: {error_msg}")
                        else:
                            raise Exception(f"Azure TTS服务错误: {error_msg}")
                    else:
                        raise Exception(f"语音合成被取消: {cancellation_details.reason}")
                else:
                    raise Exception(f"语音合成失败: {result.reason} - {error_details}")
                
        except Exception as e:
            self.logger.error(f"❌ Azure TTS延时监控合成失败: {e}")
            # 清理可能生成的无效文件
            if output_path and os.path.exists(output_path):
                try:
                    os.remove(output_path)
                except:
                    pass
            return {
                'success': False,
                'error': str(e),
                'output_path': None,
                'latency_metrics': None
            }
            
        finally:
            # 确保清理所有资源
            try:
                if synthesizer:
                    connection = speechsdk.Connection.from_speech_synthesizer(synthesizer)
                    if connection:
                        connection.close()
            except Exception as cleanup_error:
                self.logger.warning(f"⚠️ 清理合成器连接失败: {cleanup_error}")
    
    def _validate_and_fix_audio_file(self, audio_path):
        """验证并修复音频文件（增强版：添加重试机制）"""
        if not os.path.exists(audio_path):
            return False
            
        try:
            # 检查文件大小
            file_size = os.path.getsize(audio_path)
            if file_size == 0:
                return False
                
            # 读取WAV文件头
            with open(audio_path, 'rb') as f:
                header = f.read(44)  # WAV头部通常是44字节
                
            # 基本的WAV头部验证
            if len(header) < 44:
                return False
                
            # 检查WAV标识符
            if header[0:4] != b'RIFF' or header[8:12] != b'WAVE':
                # 尝试修复文件头
                try:
                    with open(audio_path, 'rb') as f:
                        content = f.read()
                    
                    # 构建标准WAV头
                    wav_header = bytearray(44)
                    wav_header[0:4] = b'RIFF'  # ChunkID
                    wav_header[4:8] = (len(content) - 8).to_bytes(4, 'little')  # ChunkSize
                    wav_header[8:12] = b'WAVE'  # Format
                    wav_header[12:16] = b'fmt '  # Subchunk1ID
                    wav_header[16:20] = (16).to_bytes(4, 'little')  # Subchunk1Size (16 for PCM)
                    wav_header[20:22] = (1).to_bytes(2, 'little')  # AudioFormat (1 for PCM)
                    wav_header[22:24] = (1).to_bytes(2, 'little')  # NumChannels (1 for mono)
                    wav_header[24:28] = (24000).to_bytes(4, 'little')  # SampleRate
                    wav_header[28:32] = (48000).to_bytes(4, 'little')  # ByteRate
                    wav_header[32:34] = (2).to_bytes(2, 'little')  # BlockAlign
                    wav_header[34:36] = (16).to_bytes(2, 'little')  # BitsPerSample
                    wav_header[36:40] = b'data'  # Subchunk2ID
                    wav_header[40:44] = (len(content) - 44).to_bytes(4, 'little')  # Subchunk2Size
                    
                    # 写入修复后的文件
                    with open(audio_path, 'wb') as f:
                        f.write(wav_header)
                        f.write(content[44:])
                    
                    return True
                except Exception as e:
                    self.logger.error(f"修复音频文件失败: {e}")
                    return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"验证音频文件失败: {e}")
            return False

    def synthesize_with_retry(self, text: str, voice: str, speed: float = 1.0, volume: float = 1.0, pitch: float = 0.0, output_path: str = None, max_retries: int = 3) -> str:
        """带重试机制的语音合成"""
        last_error = None
        
        for retry in range(max_retries):
            try:
                result = self.synthesize(text, voice, speed, volume, pitch, output_path)
                
                # 验证音频文件
                if self._validate_and_fix_audio_file(result):
                    return result
                    
                # 如果验证失败，继续重试
                self.logger.warning(f"音频文件验证失败，正在重试 ({retry + 1}/{max_retries})")
                continue
                
            except Exception as e:
                last_error = e
                self.logger.warning(f"语音合成失败，正在重试 ({retry + 1}/{max_retries}): {e}")
                
                # 短暂等待后重试
                import time
                time.sleep(1)
                
                # 重新初始化连接
                try:
                    self._initialize_connection_pool()
                except:
                    pass
        
        # 所有重试都失败
        raise Exception(f"语音合成失败，已重试{max_retries}次: {last_error}")