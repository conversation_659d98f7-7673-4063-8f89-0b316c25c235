#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TTS管理插件
统一管理和调用不同的TTS引擎
"""

import os
import tempfile
import re
from typing import Dict, Any, Optional, List, Tuple
from pathlib import Path

from core.interfaces import ITtsEngine
from .edge_tts_plugin import EdgeTtsPlugin
from .azure_tts_plugin import AzureTtsPlugin


class TtsManagerPlugin(ITtsEngine):
    """TTS管理器插件，统一管理多个TTS引擎"""
    
    def __init__(self):
        self.name = "TTS Manager"
        self.version = "1.0.0"
        self.description = "TTS引擎管理器，支持多种TTS服务的统一调用"
        
        # TTS引擎实例
        self.tts_engines = {}
        self.current_engine = None
        self.config = {}
        
        # 临时目录
        self.temp_dir = tempfile.mkdtemp(prefix="tts_manager_")
        
    def get_name(self) -> str:
        return self.name
    
    def get_version(self) -> str:
        return self.version
    
    def get_description(self) -> str:
        return self.description
    
    def initialize(self, config: Dict[str, Any]) -> bool:
        """初始化TTS管理器"""
        self.config = config
        fast_mode = config.get("fast_mode", False)
        
        try:
            # 初始化Edge TTS（免费）
            edge_tts = EdgeTtsPlugin()
            edge_config = config.copy()
            if fast_mode:
                edge_config["skip_voice_list"] = False  # 修改：必须加载声音列表
                edge_config["use_cache"] = True
            
            if edge_tts.initialize(edge_config):
                self.tts_engines["edge_tts"] = edge_tts
                print("Edge TTS引擎加载成功")
            
            # 初始化Azure TTS（需要API密钥）
            azure_tts = AzureTtsPlugin()
            # 从配置中获取Azure TTS的配置
            azure_config = {
                "azure_api_key": config.get("azure_api_key", ""),  # 直接从根配置获取
                "azure_region": config.get("azure_region", "eastus"),  # 直接从根配置获取
                "fast_mode": False,  # 修改：禁用快速模式以确保加载声音列表
                "use_cache": config.get("use_cache", True)
            }
            
            # 总是尝试初始化Azure TTS，即使没有API密钥
            if azure_tts.initialize(azure_config):
                self.tts_engines["azure_tts"] = azure_tts
                if azure_config["azure_api_key"]:
                    print("Azure TTS引擎加载成功")
                else:
                    print("Azure TTS引擎已加载（需要配置API密钥）")
            
            # 设置默认引擎
            if "edge_tts" in self.tts_engines:
                self.current_engine = "edge_tts"
            elif "azure_tts" in self.tts_engines:
                self.current_engine = "azure_tts"
            else:
                print("警告：没有可用的TTS引擎")
                return False
            
            # 预加载语音列表
            for engine_name, engine in self.tts_engines.items():
                try:
                    languages = engine.get_supported_languages()
                    if languages:
                        print(f"✅ {engine_name} 支持的语言数量: {len(languages)}")
                        for lang_code in languages.keys():
                            voices = engine.get_available_voices(lang_code)
                            if voices:
                                print(f"✅ {engine_name} - {lang_code} 可用声音数量: {len(voices)}")
                except Exception as e:
                    print(f"⚠️ {engine_name} 加载语音列表失败: {e}")
            
            print(f"TTS管理器初始化成功，可用引擎: {list(self.tts_engines.keys())}")
            print(f"当前引擎: {self.current_engine}")
            return True
            
        except Exception as e:
            print(f"TTS管理器初始化失败: {e}")
            return False
    
    def cleanup(self) -> None:
        """清理资源"""
        try:
            # 清理所有引擎
            for engine in self.tts_engines.values():
                engine.cleanup()
            
            # 清理临时文件
            import shutil
            if os.path.exists(self.temp_dir):
                shutil.rmtree(self.temp_dir)
                
        except Exception as e:
            print(f"清理TTS管理器资源时出错: {e}")
    
    def set_current_engine(self, engine_name: str) -> bool:
        """设置当前使用的TTS引擎"""
        try:
            print(f"🔄 正在切换到TTS引擎: {engine_name}")
            
            # 检查引擎是否存在
            if engine_name not in self.tts_engines:
                print(f"❌ TTS引擎不存在: {engine_name}")
                return False
                
            # 特别处理Azure TTS的情况
            if engine_name == "azure_tts":
                engine = self.tts_engines[engine_name]
                if not hasattr(engine, 'api_key') or not engine.api_key:
                    print("❌ Azure TTS未配置API密钥，请先配置API密钥")
                    return False
            
            # 保存旧引擎名称
            old_engine = self.current_engine
            
            # 更新当前引擎
            self.current_engine = engine_name
            engine = self.tts_engines[engine_name]
            
            # 获取新引擎的声音列表
            try:
                # 获取支持的语言
                supported_languages = engine.get_supported_languages()
                if not supported_languages:
                    raise Exception("获取支持的语言列表失败")
                print(f"✅ 支持的语言数量: {len(supported_languages)}")
                
                # 获取可用声音
                for lang_code in supported_languages.keys():
                    voices = engine.get_available_voices(lang_code)
                    if voices:
                        print(f"✅ 语言 {lang_code} 可用声音数量: {len(voices)}")
                    else:
                        print(f"⚠️ 语言 {lang_code} 没有可用声音")
                
                print(f"✅ 切换到TTS引擎成功: {engine_name}")
                return True
                
            except Exception as e:
                print(f"❌ 获取引擎信息失败: {e}")
                # 如果获取信息失败，回退到之前的引擎
                self.current_engine = old_engine
                print(f"⚠️ 已回退到之前的引擎: {old_engine}")
                return False
                
        except Exception as e:
            print(f"❌ 切换TTS引擎时出错: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def get_available_engines(self) -> List[str]:
        """获取可用的TTS引擎列表"""
        return list(self.tts_engines.keys())
    
    def get_current_engine(self) -> str:
        """获取当前使用的TTS引擎"""
        return self.current_engine
    
    def get_engine_info(self, engine_name: str = None) -> Dict[str, str]:
        """获取TTS引擎信息"""
        if engine_name is None:
            engine_name = self.current_engine
        
        if engine_name in self.tts_engines:
            engine = self.tts_engines[engine_name]
            return {
                "name": engine.get_name(),
                "version": engine.get_version(),
                "description": engine.get_description()
            }
        return {}
    
    def synthesize(self, text: str, voice: str, speed: float = 1.0, volume: float = 1.0, pitch: float = 0.0, output_path: str = None) -> str:
        """合成语音（增强版）"""
        if not self.current_engine or self.current_engine not in self.tts_engines:
            raise Exception("没有可用的TTS引擎")
        
        engine = self.tts_engines[self.current_engine]
        # 检查引擎是否支持新参数
        try:
            return engine.synthesize(text, voice, speed, volume, pitch, output_path)
        except TypeError:
            # 回退到旧接口（仅支持语速）
            return engine.synthesize(text, voice, speed, output_path)
    
    # ---------- 新增：向后兼容 generate_speech ----------
    def generate_speech(self, text: str, voice_id: str, output_path: str = None, 
                       speed: float = 1.0, volume: float = 1.0, pitch: float = 0.0, 
                       engine_name: str = None):
        """
        旧代码期望的接口，返回 (success, result)：
        success 为 True  → result 是音频文件路径
        success 为 False → result 是错误信息
        """
        if engine_name is None:
            engine_name = self.current_engine

        # 引擎有效性检查
        if engine_name not in self.tts_engines:
            return False, f"TTS引擎不存在: {engine_name}"

        try:
            audio_path = self.synthesize(text, voice_id, speed, volume, pitch, output_path)
            # ----------- 新增：验证音频文件是否真正生成 -----------
            try:
                if not audio_path or not os.path.exists(audio_path):
                    raise FileNotFoundError("音频文件未生成")

                file_size = os.path.getsize(audio_path)
                # 小于 1KB 视为失败（空文件或合成异常）
                if file_size < 1024:
                    # 删除无效文件，避免后续误用
                    try:
                        os.remove(audio_path)
                    except OSError:
                        pass
                    raise IOError("生成的音频文件无效或过小")

            except Exception as verify_err:
                return False, f"语音合成校验失败: {verify_err}"

            # 校验通过，返回成功
            return True, audio_path
        except Exception as e:
            return False, str(e)
    
    def get_available_voices(self, language: str = None, engine_name: str = None) -> Dict[str, str]:
        """获取可用声音列表"""
        if engine_name is None:
            engine_name = self.current_engine
        
        if engine_name not in self.tts_engines:
            return {}
        
        engine = self.tts_engines[engine_name]
        return engine.get_available_voices(language)
    
    def get_supported_languages(self, engine_name: str = None) -> Dict[str, str]:
        """获取支持的语言列表"""
        if engine_name is None:
            engine_name = self.current_engine
        
        if engine_name not in self.tts_engines:
            return {}
        
        engine = self.tts_engines[engine_name]
        return engine.get_supported_languages()
    
    def preview_voice(self, text: str, voice: str, speed: float = 1.0, volume: float = 1.0, pitch: float = 0.0, engine_name: str = None) -> str:
        """生成语音预览（增强版）"""
        if engine_name is None:
            engine_name = self.current_engine
        
        if engine_name not in self.tts_engines:
            raise Exception(f"TTS引擎不存在: {engine_name}")
        
        engine = self.tts_engines[engine_name]
        # 检查引擎是否支持新参数
        try:
            return engine.preview_voice(text, voice, speed, volume, pitch)
        except TypeError:
            # 回退到旧接口（仅支持语速）
            return engine.preview_voice(text, voice, speed)
    
    def synthesize_batch(self, texts: list, voice: str, speed: float = 1.0, volume: float = 1.0, pitch: float = 0.0, output_dir: str = None, engine_name: str = None) -> list:
        """批量语音合成（增强版）"""
        if engine_name is None:
            engine_name = self.current_engine
        
        if engine_name not in self.tts_engines:
            raise Exception(f"TTS引擎不存在: {engine_name}")
        
        engine = self.tts_engines[engine_name]
        # 检查引擎是否支持新参数
        try:
            return engine.synthesize_batch(texts, voice, speed, volume, pitch, output_dir)
        except TypeError:
            # 回退到旧接口（仅支持语速）
            return engine.synthesize_batch(texts, voice, speed, output_dir)
    
    def synthesize_subtitle_segments(self, subtitles: List[Dict], voice: str, speed: float = 1.0, 
                                   output_dir: str = None, engine_name: str = None, 
                                   progress_callback: callable = None) -> List[Dict]:
        """
        为字幕片段生成配音（支持实时进度更新）
        
        Args:
            subtitles: 字幕列表，每个元素包含 {'text': str, 'start': float, 'end': float, 'index': int}
            voice: 声音ID
            speed: 语速
            output_dir: 输出目录
            engine_name: TTS引擎名称
            progress_callback: 进度回调函数，参数为 (current_index, total_count, result_dict)
            
        Returns:
            List[Dict]: 配音结果，每个元素包含 {'subtitle': dict, 'audio_path': str, 'success': bool, 'error': str}
        """
        if engine_name is None:
            engine_name = self.current_engine
        
        if engine_name not in self.tts_engines:
            raise Exception(f"TTS引擎不存在: {engine_name}")
        
        if output_dir is None:
            output_dir = os.path.join(self.temp_dir, "subtitle_audio")
        
        os.makedirs(output_dir, exist_ok=True)
        
        results = []
        engine = self.tts_engines[engine_name]
        total_count = len(subtitles)
        
        for i, subtitle in enumerate(subtitles):
            result = {
                "subtitle": subtitle,
                "audio_path": None,
                "success": False,
                "error": None
            }
            
            try:
                text = subtitle.get("text", "").strip()
                if not text:
                    result["error"] = "字幕文本为空"
                else:
                    # 清理文本（移除HTML标签、特殊字符等）
                    cleaned_text = self._clean_subtitle_text(text)
                    if not cleaned_text:
                        result["error"] = "清理后文本为空"
                    else:
                        # 生成音频文件名
                        subtitle_index = subtitle.get("index", i)
                        audio_filename = f"subtitle_{subtitle_index:04d}_{engine_name}.wav"
                        audio_path = os.path.join(output_dir, audio_filename)
                        
                        # 合成语音
                        try:
                            generated_path = engine.synthesize(cleaned_text, voice, speed, 1.0, 0.0, audio_path)
                        except TypeError:
                            # 回退到旧接口
                            generated_path = engine.synthesize(cleaned_text, voice, speed, audio_path)
                        
                        result["audio_path"] = generated_path
                        result["success"] = True
                
            except Exception as e:
                result["error"] = str(e)
            
            results.append(result)
            
            # 实时调用进度回调
            if progress_callback:
                try:
                    progress_callback(i, total_count, result)
                except Exception as e:
                    print(f"进度回调出错: {e}")
        
        return results
    
    def _clean_subtitle_text(self, text: str) -> str:
        """清理字幕文本"""
        if not text:
            return ""
        
        # 移除HTML标签
        text = re.sub(r'<[^>]+>', '', text)
        
        # 移除字幕格式标记
        text = re.sub(r'\{[^}]+\}', '', text)
        text = re.sub(r'\[[^\]]+\]', '', text)
        
        # 移除多余的空白字符
        text = re.sub(r'\s+', ' ', text)
        text = text.strip()
        
        # 移除时间戳等
        text = re.sub(r'\d{2}:\d{2}:\d{2}[,\.]\d{3}', '', text)
        
        # 移除字幕序号
        text = re.sub(r'^\d+\s*', '', text)
        
        return text.strip()
    
    def get_engine_status(self) -> Dict[str, Dict[str, Any]]:
        """获取所有引擎的状态信息"""
        status = {}
        
        for engine_name, engine in self.tts_engines.items():
            try:
                # 尝试获取基本信息来测试引擎状态
                voices = engine.get_available_voices()
                languages = engine.get_supported_languages()
                
                status[engine_name] = {
                    "name": engine.get_name(),
                    "version": engine.get_version(),
                    "description": engine.get_description(),
                    "available": True,
                    "voice_count": len(voices),
                    "language_count": len(languages),
                    "is_current": engine_name == self.current_engine
                }
            except Exception as e:
                status[engine_name] = {
                    "name": getattr(engine, 'name', engine_name),
                    "available": False,
                    "error": str(e),
                    "is_current": engine_name == self.current_engine
                }
        
        return status 