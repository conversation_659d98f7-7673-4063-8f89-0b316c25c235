#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FlipTalk AI - TTS（文本转语音）插件模块
支持多种TTS引擎，用于字幕配音功能
"""

# 使用延迟导入避免循环导入问题
def get_available_tts_plugins():
    """获取可用的TTS插件字典"""
    plugins = {}
    
    try:
        from .edge_tts_plugin import EdgeTtsPlugin
        plugins["edge_tts"] = EdgeTtsPlugin
    except ImportError as e:
        print(f"警告：无法导入Edge TTS插件 - {e}")
    
    try:
        from .azure_tts_plugin import AzureTtsPlugin
        plugins["azure_tts"] = AzureTtsPlugin
    except ImportError as e:
        print(f"警告：无法导入Azure TTS插件 - {e}")
    
    return plugins

# TTS插件注册表（延迟加载）
AVAILABLE_TTS_PLUGINS = get_available_tts_plugins()

# 导出接口
__all__ = ["AVAILABLE_TTS_PLUGINS", "get_available_tts_plugins"] 