#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Edge TTS 插件 - 修复语速控制问题
支持完整的语速、音量、音调控制
"""

import os
import asyncio
import tempfile
from typing import Dict, Any

# 检查 edge-tts 是否可用
try:
    import edge_tts
    EDGE_TTS_AVAILABLE = True
except ImportError:
    EDGE_TTS_AVAILABLE = False

from core.interfaces import ITtsEngine


class EdgeTtsPlugin(ITtsEngine):
    """Edge TTS 插件 - 修复版本，支持完整语速控制"""
    
    def __init__(self):
        self.name = "Edge TTS"
        self.version = "2.1.0"
        self.description = "Microsoft Edge Text-to-Speech 支持完整语速控制"
        self.temp_dir = None
        
        # 预定义的声音映射（缓存，减少在线查询）
        # 更新：2024年1月 - 基于当前可用的Edge TTS声音
        self.voice_mapping = {
            "zh-CN": {
                "zh-CN-XiaoxiaoNeural": "晓晓 (女声)",
                "zh-CN-YunxiNeural": "云希 (男声)", 
                "zh-CN-YunjianNeural": "云健 (男声)",
                "zh-CN-XiaoyiNeural": "晓伊 (女声)",
                "zh-CN-YunyangNeural": "云扬 (男声)",
                "zh-CN-YunxiaNeural": "云夏 (男声)",
                "zh-CN-liaoning-XiaobeiNeural": "晓北 (女声, 东北口音)",
                "zh-CN-shaanxi-XiaoniNeural": "晓妮 (女声, 陕西口音)"
            },
            "en-US": {
                "en-US-AriaNeural": "Aria (女声)",
                "en-US-JennyNeural": "Jenny (女声)",
                "en-US-GuyNeural": "Guy (男声)",
                "en-US-DavisNeural": "Davis (男声)"
            }
        }
    
    def get_name(self) -> str:
        return self.name
    
    def get_version(self) -> str:
        return self.version
    
    def get_description(self) -> str:
        return self.description
    
    def initialize(self, config: Dict[str, Any]) -> bool:
        """初始化Edge TTS插件"""
        if not EDGE_TTS_AVAILABLE:
            print("❌ edge-tts 库未安装，请运行: pip install edge-tts")
            return False
        
        try:
            # 创建临时目录
            self.temp_dir = tempfile.mkdtemp(prefix="edge_tts_")
            
            print("✅ Edge TTS 插件初始化成功（支持完整语速控制）")
            return True
            
        except Exception as e:
            print(f"❌ Edge TTS 插件初始化失败: {e}")
            return False
    
    def cleanup(self) -> None:
        """清理资源"""
        try:
            if self.temp_dir and os.path.exists(self.temp_dir):
                import shutil
                shutil.rmtree(self.temp_dir)
        except Exception as e:
            print(f"⚠️ 清理Edge TTS资源时出错: {e}")
    
    def _convert_speed_to_rate(self, speed: float) -> str:
        """
        将速度倍数转换为Edge TTS的rate参数格式
        
        参数:
            speed: 速度倍数 (0.5 = 慢50%, 1.0 = 正常, 1.5 = 快50%, 2.0 = 快100%)
        
        返回:
            str: Edge TTS的rate参数格式，如 "-50%", "+0%", "+50%", "+100%"
        """
        if speed <= 0:
            speed = 1.0
        
        # 将倍数转换为百分比变化
        if speed == 1.0:
            return "+0%"
        elif speed < 1.0:
            # 慢于正常速度
            percentage = int((1.0 - speed) * 100)
            return f"-{percentage}%"
        else:
            # 快于正常速度
            percentage = int((speed - 1.0) * 100)
            return f"+{percentage}%"
    
    def _convert_volume_to_volume(self, volume: float) -> str:
        """
        将音量倍数转换为Edge TTS的volume参数格式
        
        参数:
            volume: 音量倍数 (0.5 = 减50%, 1.0 = 正常, 1.5 = 增50%)
        
        返回:
            str: Edge TTS的volume参数格式
        """
        if volume <= 0:
            volume = 1.0
        
        if volume == 1.0:
            return "+0%"
        elif volume < 1.0:
            percentage = int((1.0 - volume) * 100)
            return f"-{percentage}%"
        else:
            percentage = int((volume - 1.0) * 100)
            return f"+{percentage}%"
    
    def _convert_pitch_to_pitch(self, pitch: float) -> str:
        """
        将音调偏移转换为Edge TTS的pitch参数格式
        
        参数:
            pitch: 音调偏移（单位：Hz）
        
        返回:
            str: Edge TTS的pitch参数格式
        """
        if pitch == 0.0:
            return "+0Hz"
        elif pitch > 0:
            return f"+{int(pitch)}Hz"
        else:
            return f"{int(pitch)}Hz"
    
    def synthesize(self, text: str, voice: str, speed: float = 1.0, volume: float = 1.0, pitch: float = 0.0, output_path: str = None) -> str:
        """
        合成语音 - 修复版本，正确支持语速控制
        
        参数:
            text: 要合成的文本
            voice: 声音ID
            speed: 语速倍数 (0.5=慢50%, 1.0=正常, 1.5=快50%, 2.0=快100%)
            volume: 音量倍数 (0.5=减50%, 1.0=正常, 1.5=增50%)
            pitch: 音调偏移 (单位：Hz，-100到+100)
            output_path: 输出文件路径
        """
        if not text.strip():
            raise ValueError("文本不能为空")
        
        if output_path is None:
            # 基于文本、声音和参数生成唯一文件名
            content_hash = hash(f"{text}{voice}{speed}{volume}{pitch}")
            output_path = os.path.join(self.temp_dir, f"edge_tts_{abs(content_hash)}.wav")
        
        # 确保输出目录存在
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        # 转换参数格式
        rate_param = self._convert_speed_to_rate(speed)
        volume_param = self._convert_volume_to_volume(volume)
        pitch_param = self._convert_pitch_to_pitch(pitch)
        
        try:
            # 使用正确的Edge TTS API调用
            print(f"🎤 开始合成: {text[:50]}...")
            print(f"🔊 使用声音: {voice}")
            print(f"⚡ 语速设置: {speed}倍 ({rate_param})")
            if volume != 1.0:
                print(f"🔉 音量设置: {volume}倍 ({volume_param})")
            if pitch != 0.0:
                print(f"🎵 音调设置: {pitch}Hz ({pitch_param})")
            
            asyncio.run(self._synthesize_with_params(
                text, voice, rate_param, volume_param, pitch_param, output_path
            ))
            return output_path
        except Exception as e:
            raise Exception(f"Edge TTS语音合成失败: {e}")
    
    async def _synthesize_with_params(self, text: str, voice: str, rate: str, volume: str, pitch: str, output_path: str):
        """
        使用参数进行异步语音合成（增强版本）
        """
        try:
            # 创建Edge TTS通信对象
            communicate = edge_tts.Communicate(
                text=text,
                voice=voice,
                rate=rate,
                volume=volume,
                pitch=pitch
            )
            
            # 执行语音合成并保存
            await communicate.save(output_path)
            
            # 验证文件是否生成
            if not os.path.exists(output_path):
                raise Exception("音频文件未生成")
            
            # 检查文件大小
            file_size = os.path.getsize(output_path)
            if file_size == 0:
                raise Exception("生成的音频文件为空")
                
            print(f"✅ 语音合成完成: {os.path.basename(output_path)} ({file_size/1024:.1f}KB)")
            
        except Exception as e:
            print(f"❌ 语音合成失败: {e}")
            if os.path.exists(output_path):
                try:
                    os.remove(output_path)
                except:
                    pass
            raise e
    
    def get_available_voices(self, language: str = None) -> Dict[str, str]:
        """获取可用声音列表"""
        if language and language in self.voice_mapping:
            return self.voice_mapping[language]
        
        # 返回所有语言的声音
        all_voices = {}
        for lang_voices in self.voice_mapping.values():
            all_voices.update(lang_voices)
        return all_voices
    
    def get_supported_languages(self) -> Dict[str, str]:
        """获取支持的语言列表"""
        return {
            "zh-CN": "中文（简体）",
            "en-US": "英语（美国）",
            "ja-JP": "日语",
            "ko-KR": "韩语"
        }
    
    def preview_voice(self, text: str, voice: str, speed: float = 1.0, volume: float = 1.0, pitch: float = 0.0) -> str:
        """
        生成语音预览 - 修复版本，支持参数调整
        """
        # 截断预览文本
        preview_text = text if len(text) <= 100 else text[:100] + "..."
        
        # 生成缓存文件名（包含所有参数）
        content_hash = hash(f"{preview_text}{voice}{speed}{volume}{pitch}")
        preview_filename = f"preview_{abs(content_hash)}.wav"
        preview_path = os.path.join(self.temp_dir, preview_filename)
        
        # 检查缓存
        if os.path.exists(preview_path):
            try:
                if os.path.getsize(preview_path) > 1024:  # 至少1KB
                    print(f"🎵 使用缓存的试听文件: {preview_filename}")
                    return preview_path
                else:
                    os.remove(preview_path)
            except OSError:
                pass
        
        try:
            print(f"🎤 生成新的试听音频: {preview_filename}")
            print(f"🎛️ 参数: 语速={speed}, 音量={volume}, 音调={pitch}")
            
            # 使用完整参数进行合成
            return self.synthesize(preview_text, voice, speed, volume, pitch, preview_path)
        except Exception as e:
            raise Exception(f"预览生成失败: {e}")
    
    def synthesize_batch(self, texts: list, voice: str, speed: float = 1.0, volume: float = 1.0, pitch: float = 0.0, output_dir: str = None) -> list:
        """
        批量语音合成 - 修复版本，支持参数调整
        """
        if output_dir is None:
            output_dir = self.temp_dir
        
        os.makedirs(output_dir, exist_ok=True)
        
        print(f"🎬 开始批量合成，参数: 语速={speed}, 音量={volume}, 音调={pitch}")
        
        results = []
        for i, text in enumerate(texts):
            if not text.strip():
                results.append(None)
                continue
            
            try:
                output_path = os.path.join(output_dir, f"batch_{i:04d}.wav")
                result_path = self.synthesize(text, voice, speed, volume, pitch, output_path)
                results.append(result_path)
                print(f"✅ 第{i+1}/{len(texts)}条完成")
            except Exception as e:
                print(f"❌ 批量合成第{i+1}条失败: {e}")
                results.append(None)
        
        return results
    
    def synthesize_with_retry(self, text: str, voice: str, speed: float = 1.0, volume: float = 1.0, pitch: float = 0.0, output_path: str = None, max_retries: int = 3) -> str:
        """
        带重试机制的语音合成
        
        参数:
            text: 要合成的文本
            voice: 声音ID
            speed: 语速倍数 (0.5=慢50%, 1.0=正常, 1.5=快50%, 2.0=快100%)
            volume: 音量倍数 (0.5=减50%, 1.0=正常, 1.5=增50%)
            pitch: 音调偏移 (单位：Hz，-100到+100)
            output_path: 输出文件路径
            max_retries: 最大重试次数
            
        返回:
            str: 生成的音频文件路径
        """
        last_error = None
        for attempt in range(max_retries):
            try:
                return self.synthesize(text, voice, speed, volume, pitch, output_path)
            except Exception as e:
                last_error = e
                if attempt < max_retries - 1:
                    print(f"语音合成失败，正在重试 ({attempt + 1}/{max_retries}): {e}")
                    # 短暂延迟后重试
                    import time
                    time.sleep(1)  # 1秒延迟
                else:
                    print(f"❌ 达到最大重试次数 ({max_retries})，语音合成失败")
                    raise last_error 