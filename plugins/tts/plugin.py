#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TTS插件入口文件
为插件管理器提供TTS插件的标准化接口
"""

import sys
from pathlib import Path
from typing import Dict, Any

# 导入核心接口
sys.path.append(str(Path(__file__).parent.parent.parent))
from core.interfaces import ITtsEngine

# 导入TTS管理器插件
from .tts_manager_plugin import TtsManagerPlugin

class TtsPlugin(ITtsEngine):
    """
    TTS插件的统一入口
    封装TTS管理器插件，为插件管理器提供标准接口
    """
    
    def __init__(self):
        self.name = "TTS Engine Manager"
        self.version = "1.0.0" 
        self.description = "文本转语音插件管理器，支持多种TTS引擎（Edge TTS、Azure TTS等）"
        self.tts_manager = None
        self.is_initialized = False
    
    def get_name(self) -> str:
        """获取插件名称"""
        return self.name
    
    def get_version(self) -> str:
        """获取插件版本"""
        return self.version
    
    def get_description(self) -> str:
        """获取插件描述"""
        return self.description
    
    def initialize(self, config: Dict[str, Any]) -> bool:
        """初始化TTS插件"""
        try:
            print("🚀 开始初始化TTS插件...")
            
            # 创建TTS管理器
            self.tts_manager = TtsManagerPlugin()
            
            # 初始化TTS管理器
            if self.tts_manager.initialize(config):
                self.is_initialized = True
                print("✅ TTS插件初始化成功")
                return True
            else:
                print("❌ TTS管理器初始化失败")
                return False
                
        except Exception as e:
            print(f"❌ TTS插件初始化失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def cleanup(self) -> None:
        """清理资源"""
        try:
            if self.tts_manager:
                self.tts_manager.cleanup()
                self.tts_manager = None
            self.is_initialized = False
            print("✅ TTS插件清理完成")
        except Exception as e:
            print(f"⚠️ TTS插件清理时出错: {e}")
    
    def synthesize(self, text: str, voice: str, speed: float = 1.0, volume: float = 1.0, pitch: float = 0.0, output_path: str = None) -> str:
        """合成语音"""
        if not self.is_initialized or not self.tts_manager:
            raise RuntimeError("TTS插件未正确初始化")
        
        return self.tts_manager.synthesize(text, voice, speed, volume, pitch, output_path)
    
    def get_available_voices(self, language: str = None) -> Dict[str, str]:
        """获取可用声音列表"""
        if not self.is_initialized or not self.tts_manager:
            return {}
        
        return self.tts_manager.get_available_voices(language)
    
    def get_supported_languages(self) -> Dict[str, str]:
        """获取支持的语言列表"""
        if not self.is_initialized or not self.tts_manager:
            return {}
        
        return self.tts_manager.get_supported_languages()
    
    def preview_voice(self, text: str, voice: str, speed: float = 1.0, volume: float = 1.0, pitch: float = 0.0) -> str:
        """生成语音预览"""
        if not self.is_initialized or not self.tts_manager:
            raise RuntimeError("TTS插件未正确初始化")
        
        return self.tts_manager.preview_voice(text, voice, speed, volume, pitch)
    
    def synthesize_batch(self, texts: list, voice: str, speed: float = 1.0, volume: float = 1.0, pitch: float = 0.0, output_dir: str = None) -> list:
        """批量语音合成"""
        if not self.is_initialized or not self.tts_manager:
            raise RuntimeError("TTS插件未正确初始化")
        
        return self.tts_manager.synthesize_batch(texts, voice, speed, volume, pitch, output_dir)
    
    def get_tts_manager(self):
        """获取内部的TTS管理器实例"""
        return self.tts_manager


def create_plugin() -> ITtsEngine:
    """创建TTS插件实例"""
    return TtsPlugin()


def get_plugin_info():
    """获取插件信息"""
    return {
        'name': 'TTS Engine Manager',
        'version': '1.0.0',
        'description': '文本转语音插件管理器，支持多种TTS引擎',
        'type': 'ITtsEngine',
        'engines': ['Edge TTS', 'Azure TTS']
    } 