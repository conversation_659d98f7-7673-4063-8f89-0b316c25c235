#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CascadedNet人声分离插件实现
基于CascadedNet算法实现快速高质量人声分离功能
"""

import os
import sys
from pathlib import Path
from typing import Dict, Any, Optional, Callable, List
import time

# 导入核心接口
sys.path.append(str(Path(__file__).parent.parent.parent))
from core.interfaces import IVoiceSeparator

# 设置项目根目录
PROJECT_ROOT = Path(__file__).parent.parent.parent
MODELS_DIR = PROJECT_ROOT / "models" / "vocal_separation" / "weights"

class CascadedNetVoiceSeparationPlugin(IVoiceSeparator):
    """
    基于CascadedNet的快速人声分离插件
    
    功能特性：
    - 快速处理速度
    - 高质量音频分离
    - GPU加速支持
    - 测试时增强(TTA)支持
    - 支持批量处理
    """
    
    def __init__(self):
        self.name = "CascadedNet Voice Separation"
        self.version = "1.0.0"
        self.description = "基于CascadedNet的快速AI人声分离插件"
        self.is_initialized = False
        self.separator = None
        self.device = "auto"
        self.config = {}
        self.model_path = None
        
        # 质量设置
        self.quality_settings = {
            'fast': {'batchsize': 8, 'postprocess': False},
            'balanced': {'batchsize': 4, 'postprocess': False},
            'high': {'batchsize': 2, 'postprocess': True}
        }
        self.current_quality = 'balanced'
        
        # 确保模型目录存在
        MODELS_DIR.mkdir(parents=True, exist_ok=True)
    
    # IPlugin接口方法
    def get_name(self) -> str:
        """获取插件名称"""
        return self.name
    
    def get_version(self) -> str:
        """获取插件版本"""
        return self.version
    
    def get_description(self) -> str:
        """获取插件描述"""
        return self.description
    
    def initialize(self, config: Dict[str, Any]) -> bool:
        """初始化插件"""
        try:
            self.config = config or {}
            
            # 查找CascadedNet模型文件
            self.model_path = self._find_model_file()
            if not self.model_path:
                print("❌ CascadedNet插件初始化失败: 未找到模型文件")
                print(f"请确保baseline.pth存在于{MODELS_DIR}目录中")
                return False
            
            # 检查依赖
            try:
                import torch
                import librosa
                import soundfile as sf
                import numpy as np
                
                # 检查models.vocal_separation模块
                sys.path.append(str(PROJECT_ROOT))
                from models.vocal_separation import create_separator
                
            except ImportError as e:
                print(f"❌ CascadedNet插件初始化失败: 缺少依赖 - {e}")
                print("请安装依赖: pip install torch librosa soundfile numpy")
                return False
            
            # 设备检测
            import torch
            if self.device == "auto":
                self.device = "cuda" if torch.cuda.is_available() else "cpu"
            
            print(f"✅ CascadedNet插件初始化成功")
            print(f"📁 模型文件: {self.model_path}")
            print(f"🔧 使用设备: {self.device}")
            
            self.is_initialized = True
            return True
            
        except Exception as e:
            print(f"❌ CascadedNet插件初始化失败: {e}")
            return False
    
    def cleanup(self) -> None:
        """清理资源"""
        if hasattr(self, 'separator') and self.separator:
            del self.separator
            self.separator = None

        # 清理GPU内存
        try:
            import torch
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
        except ImportError:
            pass
    
    def _find_model_file(self) -> Optional[str]:
        """查找CascadedNet模型文件"""
        # 优先查找baseline.pth
        baseline_model = MODELS_DIR / "baseline.pth"
        if baseline_model.exists():
            return str(baseline_model)
        
        # 查找其他.pth文件
        pth_files = list(MODELS_DIR.glob("*.pth"))
        if pth_files:
            return str(pth_files[0])
        
        return None
    
    # IVoiceSeparator接口方法
    def separate(self, audio_path: str, output_dir: str = None, output_options: Dict[str, bool] = None) -> Dict[str, str]:
        """
        分离音频中的人声和背景音乐

        Args:
            audio_path: 输入音频文件路径
            output_dir: 输出目录，如果为None则使用默认目录
            output_options: 输出选项字典，指定要保存哪些轨道

        Returns:
            Dict[str, str]: 分离结果，包含各音轨的文件路径
        """
        if not self.is_initialized:
            raise RuntimeError("CascadedNet插件未正确初始化")

        audio_path = Path(audio_path)
        if not audio_path.exists():
            raise FileNotFoundError(f"音频文件不存在: {audio_path}")

        if output_dir is None:
            output_dir = PROJECT_ROOT / "output" / "voice_separation"
        output_dir = Path(output_dir)

        try:
            # 导入必要的模块
            sys.path.append(str(PROJECT_ROOT))
            from models.vocal_separation import create_separator
            
            # 创建分离器
            quality_config = self.quality_settings.get(self.current_quality, self.quality_settings['balanced'])
            separator = create_separator(
                model_path=self.model_path,
                device=self.device,
                batchsize=quality_config['batchsize'],
                postprocess=quality_config['postprocess']
            )
            
            # 确保输出目录存在
            output_dir.mkdir(parents=True, exist_ok=True)
            
            # 设置默认输出选项（如果未提供）
            if output_options is None:
                output_options = {
                    'vocals': True,
                    'background': True
                }
            
            # 生成输出文件路径
            file_stem = audio_path.stem
            output_files = {}
            
            vocal_path = None
            instrumental_path = None
            
            if output_options.get('vocals', False):
                vocal_path = output_dir / f"{file_stem}_vocals.wav"
                output_files['vocals'] = str(vocal_path)
            
            if output_options.get('background', False):
                instrumental_path = output_dir / f"{file_stem}_background.wav"
                output_files['background'] = str(instrumental_path)
            
            # 执行CascadedNet人声分离
            print(f"正在使用CascadedNet分离音频: {audio_path.name}")
            
            # 使用TTA（测试时增强）
            use_tta = True
            
            # 调用分离器
            result_vocal, result_instrumental = separator.separate_audio_file(
                input_path=str(audio_path),
                output_vocal_path=str(vocal_path) if vocal_path else None,
                output_instrumental_path=str(instrumental_path) if instrumental_path else None,
                use_tta=use_tta,
                sr=44100
            )
            
            # 验证输出文件是否生成
            verified_output = {}
            for key, path in output_files.items():
                if path and os.path.exists(path) and os.path.getsize(path) > 0:
                    verified_output[key] = path
            
            print(f"CascadedNet分离完成，生成文件: {list(verified_output.keys())}")
            return verified_output

        except Exception as e:
            error_msg = f"CascadedNet音频分离失败: {str(e)}"
            print(error_msg)
            raise RuntimeError(error_msg)
    
    def get_supported_formats(self) -> list:
        """获取支持的音频格式"""
        return [".wav", ".mp3", ".m4a", ".flac", ".aac", ".ogg"]
    
    def get_quality_settings(self) -> Dict[str, Any]:
        """获取质量设置选项"""
        return {
            "qualities": {
                "fast": {
                    "name": "快速模式",
                    "description": "最快处理速度，适合预览",
                    "quality": "⭐⭐⭐ 中等质量",
                    "speed": "⚡⚡⚡⚡⚡ 最快",
                    "use_case": "快速预览、批量处理",
                    "batchsize": 8,
                    "postprocess": False
                },
                "balanced": {
                    "name": "平衡模式",
                    "description": "质量和速度平衡，推荐使用",
                    "quality": "⭐⭐⭐⭐ 高质量",
                    "speed": "⚡⚡⚡ 中等速度",
                    "use_case": "日常使用，推荐设置",
                    "batchsize": 4,
                    "postprocess": False
                },
                "high": {
                    "name": "高质量模式",
                    "description": "最高质量，处理较慢",
                    "quality": "⭐⭐⭐⭐⭐ 最高质量",
                    "speed": "⚡⚡ 较慢",
                    "use_case": "追求最佳音质",
                    "batchsize": 2,
                    "postprocess": True
                }
            },
            "devices": ["auto", "cuda", "cpu"],
            "current_quality": self.current_quality,
            "current_device": self.device,
            "features": ["TTA增强", "GPU加速", "批量处理"]
        }

    # 扩展方法（非接口要求）
    def is_available(self) -> bool:
        """检查插件是否可用"""
        return self.is_initialized

    def get_info(self) -> Dict[str, Any]:
        """获取插件信息"""
        return {
            "name": self.name,
            "version": self.version,
            "description": self.description,
            "author": "FlipTalk AI Team",
            "technology": "CascadedNet",
            "supported_formats": self.get_supported_formats(),
            "quality_modes": list(self.quality_settings.keys()),
            "current_quality": self.current_quality,
            "device": self.device,
            "model_path": self.model_path,
            "is_gpu_accelerated": self.device == "cuda",
            "features": ["快速处理", "高质量分离", "TTA增强", "GPU加速"]
        }

    def set_config(self, config: Dict[str, Any]) -> bool:
        """设置插件配置"""
        try:
            if "quality" in config and config["quality"] in self.quality_settings:
                self.current_quality = config["quality"]
                print(f"🔧 已切换到质量模式: {self.current_quality}")

            if "device" in config:
                valid_devices = ["auto", "cuda", "cpu"]
                if config["device"] in valid_devices:
                    self.device = config["device"]
                    print(f"🔧 已设置设备: {self.device}")

            return True
        except Exception as e:
            print(f"❌ 配置设置失败: {e}")
            return False

    def separate_audio(self, audio_path: Path, output_dir: Path,
                      progress_callback: Optional[Callable[[int], None]] = None,
                      status_callback: Optional[Callable[[str], None]] = None,
                      output_options: Dict[str, bool] = None) -> Dict[str, Path]:
        """
        执行音频分离（带进度回调的版本）
        这是为UI对话框提供的扩展方法
        """
        if status_callback:
            status_callback(f"正在加载CascadedNet模型...")
        if progress_callback:
            progress_callback(10)

        if status_callback:
            status_callback(f"正在分离音频: {audio_path.name}")
        if progress_callback:
            progress_callback(30)

        # 调用标准的separate方法
        result = self.separate(str(audio_path), str(output_dir), output_options)

        if progress_callback:
            progress_callback(90)
        if status_callback:
            status_callback("CascadedNet分离完成")

        if progress_callback:
            progress_callback(100)

        # 转换字符串路径为Path对象
        return {k: Path(v) for k, v in result.items()}

    def batch_separate(self, file_paths: List[str], output_dir: str,
                      progress_callback: Optional[Callable[[int], None]] = None,
                      status_callback: Optional[Callable[[str], None]] = None,
                      file_callback: Optional[Callable[[int, Dict[str, str]], None]] = None,
                      output_options: Dict[str, bool] = None) -> List[Dict[str, Any]]:
        """
        批量分离音频文件

        Args:
            file_paths: 音频文件路径列表
            output_dir: 输出目录
            progress_callback: 总体进度回调
            status_callback: 状态更新回调
            file_callback: 单个文件完成回调
            output_options: 输出选项

        Returns:
            List[Dict]: 处理结果列表
        """
        results = []
        total_files = len(file_paths)

        for i, file_path in enumerate(file_paths):
            try:
                file_name = Path(file_path).name
                if status_callback:
                    status_callback(f"正在处理: {file_name} ({i+1}/{total_files})")

                # 分离单个文件
                output_files = self.separate(file_path, output_dir, output_options)

                result = {
                    'file_path': file_path,
                    'success': True,
                    'output_files': output_files,
                    'error': None
                }

                if file_callback:
                    file_callback(i, output_files)

            except Exception as e:
                result = {
                    'file_path': file_path,
                    'success': False,
                    'output_files': {},
                    'error': str(e)
                }

            results.append(result)

            # 更新总体进度
            if progress_callback:
                progress = int((i + 1) / total_files * 100)
                progress_callback(progress)

        return results

# 插件工厂函数
def create_plugin() -> IVoiceSeparator:
    """创建插件实例"""
    return CascadedNetVoiceSeparationPlugin()

# 插件入口点
def get_plugin_info():
    """获取插件基本信息"""
    return {
        "name": "CascadedNet Voice Separation",
        "version": "1.0.0",
        "description": "基于CascadedNet的快速AI人声分离插件",
        "author": "FlipTalk AI Team",
        "type": "voice_separation",
        "main_class": "CascadedNetVoiceSeparationPlugin"
    }

if __name__ == "__main__":
    # 测试插件
    plugin = create_plugin()

    print("CascadedNet插件信息:")
    info = plugin.get_info()
    for key, value in info.items():
        print(f"  {key}: {value}")

    print(f"\n初始化结果: {plugin.initialize({})}")
    print(f"插件可用性: {plugin.is_available()}")

    # 显示质量设置
    quality_settings = plugin.get_quality_settings()
    print(f"\n质量设置:")
    for quality, settings in quality_settings['qualities'].items():
        print(f"  {quality}: {settings['name']} - {settings['description']}")
