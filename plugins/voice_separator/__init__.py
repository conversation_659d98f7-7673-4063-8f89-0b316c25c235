#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
人声分离插件模块

提供多种人声分离算法的插件实现：
- DemucsVoiceSeparationPlugin: 基于Facebook Demucs的专业人声分离
- CascadedNetVoiceSeparationPlugin: 基于CascadedNet的快速人声分离

使用示例:
    from plugins.voice_separator import DemucsVoiceSeparationPlugin, CascadedNetVoiceSeparationPlugin
    
    # 使用Demucs插件
    demucs_plugin = DemucsVoiceSeparationPlugin()
    demucs_plugin.initialize({})
    result = demucs_plugin.separate("input.wav", "output_dir")
    
    # 使用CascadedNet插件
    cascadednet_plugin = CascadedNetVoiceSeparationPlugin()
    cascadednet_plugin.initialize({})
    result = cascadednet_plugin.separate("input.wav", "output_dir")
"""

from .plugin import DemucsVoiceSeparationPlugin, CascadedNetVoiceSeparationPlugin

__version__ = '1.0.0'
__author__ = 'FlipTalk AI Team'

# 导出主要类
__all__ = [
    'DemucsVoiceSeparationPlugin',
    'CascadedNetVoiceSeparationPlugin',
    'create_demucs_plugin',
    'create_cascadednet_plugin',
    'get_available_plugins'
]

def create_demucs_plugin():
    """创建Demucs人声分离插件实例"""
    return DemucsVoiceSeparationPlugin()

def create_cascadednet_plugin():
    """创建CascadedNet人声分离插件实例"""
    return CascadedNetVoiceSeparationPlugin()

def get_available_plugins():
    """获取所有可用的人声分离插件"""
    return {
        'demucs': {
            'name': 'Demucs Voice Separation',
            'description': '基于Facebook Demucs的专业AI人声分离插件',
            'class': DemucsVoiceSeparationPlugin,
            'factory': create_demucs_plugin
        },
        'cascadednet': {
            'name': 'CascadedNet Voice Separation', 
            'description': '基于CascadedNet的快速AI人声分离插件',
            'class': CascadedNetVoiceSeparationPlugin,
            'factory': create_cascadednet_plugin
        }
    }
