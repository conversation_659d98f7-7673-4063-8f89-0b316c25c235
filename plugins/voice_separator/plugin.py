#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
人声分离插件实现
支持多种人声分离算法：
1. CascadedNet - 快速高质量人声分离（推荐）
2. Facebook Demucs - 专业级多轨道分离
"""

import os
import sys
import shutil
from pathlib import Path
from typing import Dict, Any, Optional, Callable
import json
import time

# 导入核心接口
sys.path.append(str(Path(__file__).parent.parent.parent))
from core.interfaces import IVoiceSeparator

# 设置项目根目录的模型缓存路径
PROJECT_ROOT = Path(__file__).parent.parent.parent
MODELS_CACHE_DIR = PROJECT_ROOT / "models" / "demucs_cache"

class DemucsVoiceSeparationPlugin(IVoiceSeparator):
    """
    基于Demucs的专业人声分离插件
    
    功能特性：
    - 支持多种预训练模型（htdemucs, mdx等）
    - GPU加速支持
    - 高质量音频分离
    - 多格式音频文件支持
    """
    
    def __init__(self):
        self.name = "Demucs Voice Separation"
        self.version = "1.0.0"
        self.description = "基于Facebook Demucs的专业AI人声分离插件"
        self.is_initialized = False
        self.separator = None
        self.available_models = [
            "htdemucs",
            "htdemucs_ft", 
            "mdx_extra",
            "mdx",
            "hdemucs_mmi"
        ]
        self.current_model = "htdemucs"
        self.device = "auto"
        self.config = {}
        
        # 确保模型缓存目录存在
        MODELS_CACHE_DIR.mkdir(parents=True, exist_ok=True)

        # 应用PyTorch兼容性修复
        self._pytorch_fix_applied = False
    
    # IPlugin接口方法
    def get_name(self) -> str:
        """获取插件名称"""
        return self.name
    
    def get_version(self) -> str:
        """获取插件版本"""
        return self.version
    
    def get_description(self) -> str:
        """获取插件描述"""
        return self.description
    
    def initialize(self, config: Dict[str, Any]) -> bool:
        """初始化插件"""
        try:
            self.config = config or {}
            
            # 检查Demucs和PyTorch是否可用
            import torch
            import demucs
            from demucs.pretrained import get_model
            
            # 设置模型缓存目录
            os.environ['TORCH_HOME'] = str(MODELS_CACHE_DIR)
            
            # 检测设备
            if self.device == "auto":
                self.device = "cuda" if torch.cuda.is_available() else "cpu"
            
            print(f"✅ Demucs插件初始化成功，使用设备: {self.device}")
            print(f"📁 模型缓存目录: {MODELS_CACHE_DIR}")
            
            self.is_initialized = True
            return True
            
        except ImportError as e:
            print(f"❌ Demucs插件初始化失败: 缺少依赖 - {e}")
            print("请安装Demucs: pip install demucs torch")
            return False
        except Exception as e:
            print(f"❌ Demucs插件初始化失败: {e}")
            return False
    
    def cleanup(self) -> None:
        """清理资源"""
        if hasattr(self, 'separator') and self.separator:
            del self.separator

        # 清理GPU内存
        try:
            import torch
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
        except ImportError:
            pass

    def _apply_pytorch_compatibility_fix(self):
        """应用PyTorch兼容性修复"""
        if self._pytorch_fix_applied:
            return

        try:
            import typing

            # 保存原始的_type_check函数
            if not hasattr(typing, '_original_type_check'):
                typing._original_type_check = typing._type_check

            def patched_type_check(arg, msg, is_argument=True, module=None):
                """修复Self类型检查的问题"""
                # 如果是Self类型，直接返回
                if hasattr(arg, '__name__') and arg.__name__ == 'Self':
                    return arg

                # 对于其他类型，使用原始的检查函数
                try:
                    # 尝试使用新的签名（包含module参数）
                    return typing._original_type_check(arg, msg, is_argument, module)
                except TypeError:
                    # 如果失败，使用旧的签名
                    return typing._original_type_check(arg, msg, is_argument)

            # 应用补丁
            typing._type_check = patched_type_check
            self._pytorch_fix_applied = True
            print("✅ PyTorch Self类型兼容性补丁已应用")

        except Exception as e:
            print(f"⚠️ 无法应用PyTorch兼容性补丁: {e}")
            # 继续执行，可能仍然可以工作
    
    # IVoiceSeparator接口方法
    def separate(self, audio_path: str, output_dir: str = None, output_options: Dict[str, bool] = None) -> Dict[str, str]:
        """
        分离音频中的人声和背景音乐

        Args:
            audio_path: 输入音频文件路径
            output_dir: 输出目录，如果为None则使用默认目录
            output_options: 输出选项字典，指定要保存哪些轨道

        Returns:
            Dict[str, str]: 分离结果，包含各音轨的文件路径
        """
        # 临时调试日志
        print(f"🔍 [DEBUG] Demucs.separate() 接收到的参数:")
        print(f"   audio_path: {audio_path}")
        print(f"   output_dir: {output_dir}")
        print(f"   output_options: {output_options}")
        if not self.is_initialized:
            raise RuntimeError("Demucs插件未正确初始化")

        audio_path = Path(audio_path)
        if not audio_path.exists():
            raise FileNotFoundError(f"音频文件不存在: {audio_path}")

        if output_dir is None:
            output_dir = PROJECT_ROOT / "output" / "voice_separation"
        output_dir = Path(output_dir)

        try:
            # 应用PyTorch兼容性修复
            self._apply_pytorch_compatibility_fix()

            # 导入必要的模块
            import torch
            from demucs.pretrained import get_model
            from demucs.apply import apply_model
            from demucs.audio import AudioFile

            # 设置模型缓存目录
            os.environ['TORCH_HOME'] = str(MODELS_CACHE_DIR)

            # 加载模型
            model = get_model(self.current_model)

            # 确定设备
            if self.device == "auto":
                device = "cuda" if torch.cuda.is_available() else "cpu"
            else:
                device = self.device

            model.to(device)

            # 加载音频文件
            wav = AudioFile(str(audio_path)).read(
                streams=0,
                samplerate=model.samplerate,
                channels=model.audio_channels
            )

            # 音频预处理
            ref = wav.mean(0)
            wav = (wav - ref.mean()) / ref.std()

            # 执行分离
            with torch.no_grad():
                sources = apply_model(model, wav[None], device=device)[0]

            # 音频后处理
            sources = sources * ref.std() + ref.mean()

            # 确保输出目录存在
            output_dir.mkdir(parents=True, exist_ok=True)

            # 设置默认输出选项（如果未提供）
            if output_options is None:
                output_options = {
                    'vocals': True,
                    'background': True,
                    'drums': True,
                    'bass': True,
                    'other': True
                }

            # 保存分离后的音频
            output_files = {}
            labels = model.sources

            # 获取原文件名（不含扩展名）
            original_name = audio_path.stem

            # 只保存用户选择的轨道
            for source_name, source_audio in zip(labels, sources):
                if output_options.get(source_name, False):
                    # 使用原文件名 + 后缀的命名方式
                    output_filename = f"{original_name}_{source_name}.wav"
                    output_path = output_dir / output_filename

                    # 音频归一化和增益补偿
                    source_audio_normalized = self._normalize_and_boost_audio(
                        source_audio.cpu().numpy(),
                        source_name
                    )

                    # 使用正确的方法保存音频文件
                    import torchaudio
                    torchaudio.save(
                        str(output_path),
                        torch.from_numpy(source_audio_normalized),
                        model.samplerate
                    )
                    output_files[source_name] = str(output_path)

            # 创建背景音乐文件（如果用户选择了background但没有选择other）
            if output_options.get('background', False) and 'background' not in output_files:
                # 需要检查是否有other轨道可以作为background
                if 'other' in labels:
                    other_index = labels.index('other')
                    other_audio = sources[other_index]
                    
                    background_filename = f"{original_name}_background.wav"
                    background_path = output_dir / background_filename

                    import torchaudio
                    torchaudio.save(
                        str(background_path),
                        other_audio.cpu(),
                        model.samplerate
                    )
                    output_files['background'] = str(background_path)
                elif 'other' in output_files:
                    # 如果other文件已经存在，复制为background
                    background_filename = f"{original_name}_background.wav"
                    background_path = output_dir / background_filename

                    import shutil
                    shutil.copy2(output_files['other'], str(background_path))
                    output_files['background'] = str(background_path)

            return output_files

        except Exception as e:
            error_msg = f"音频分离失败: {str(e)}"
            raise RuntimeError(error_msg)
    
    def _normalize_and_boost_audio(self, audio_data, source_name):
        """
        根据音频类型进行归一化和增益补偿

        Args:
            audio_data: 音频数据 (numpy数组)
            source_name: 音频类型 ('vocals', 'other', 'drums', 'bass')

        Returns:
            numpy.ndarray: 处理后的音频数据
        """
        try:
            import numpy as np

            # 确保音频数据不为空
            if audio_data.size == 0:
                return audio_data

            # 根据音频类型设置不同的目标音量和增益
            if source_name in ['other', 'background']:
                # 背景音乐需要更大的增益补偿
                target_db = -12
                boost_db = 8  # 增加8dB增益
            elif source_name == 'vocals':
                # 人声适中增益
                target_db = -12
                boost_db = 3
            elif source_name in ['drums', 'bass']:
                # 鼓和贝斯适中增益
                target_db = -12
                boost_db = 5
            else:
                # 默认设置
                target_db = -12
                boost_db = 4

            # 计算当前音频的RMS值
            if audio_data.ndim == 2:
                rms = np.sqrt(np.mean(audio_data ** 2))
            elif audio_data.ndim == 3:
                rms = np.sqrt(np.mean(audio_data ** 2, axis=(1, 2), keepdims=True))
            else:
                rms = np.sqrt(np.mean(audio_data ** 2))

            # 避免除零错误
            if np.any(rms == 0):
                print(f"⚠️ 检测到静音音频 ({source_name})，跳过归一化")
                return audio_data

            # 计算目标RMS值 (从dB转换)
            target_rms = 10 ** (target_db / 20)

            # 计算增益系数
            gain = target_rms / rms

            # 应用额外增益
            if boost_db != 0:
                boost_gain = 10 ** (boost_db / 20)
                gain *= boost_gain

            # 应用增益
            normalized_audio = audio_data * gain

            # 防止削波 - 如果峰值超过0.95，进行限制
            max_val = np.max(np.abs(normalized_audio))
            if max_val > 0.95:
                limiter_gain = 0.95 / max_val
                normalized_audio *= limiter_gain
                print(f"🔧 {source_name} 应用限制器，增益系数: {limiter_gain:.3f}")

            # 计算最终音量
            final_rms = np.sqrt(np.mean(normalized_audio ** 2))
            final_db = 20 * np.log10(final_rms + 1e-10)
            print(f"🔊 {source_name} 归一化完成: 目标={target_db}dB, 增益={boost_db}dB, 实际={final_db:.1f}dB")

            return normalized_audio

        except Exception as e:
            print(f"❌ {source_name} 音频归一化失败: {e}")
            return audio_data

    def get_supported_formats(self) -> list:
        """获取支持的音频格式"""
        return [".wav", ".mp3", ".m4a", ".flac", ".aac", ".ogg"]
    
    def get_quality_settings(self) -> Dict[str, Any]:
        """获取质量设置选项"""
        return {
            "models": {
                "htdemucs": {
                    "name": "HTDemucs - 高质量",
                    "description": "推荐使用，质量和速度平衡",
                    "quality": "⭐⭐⭐⭐⭐ 高质量",
                    "speed": "⚡⚡⚡ 中等速度",
                    "use_case": "适合大部分音频文件",
                    "model_size": "约 260MB"
                },
                "mdx_extra": {
                    "name": "MDX-Net Extra - 超高质量",
                    "description": "最高质量，处理较慢",
                    "quality": "⭐⭐⭐⭐⭐⭐ 超高质量",
                    "speed": "⚡ 慢速",
                    "use_case": "追求最佳音质",
                    "model_size": "约 350MB"
                },
                "htdemucs_ft": {
                    "name": "HTDemucs Fine-tuned - 微调版",
                    "description": "针对专业音乐优化",
                    "quality": "⭐⭐⭐⭐⭐ 高质量",
                    "speed": "⚡⚡ 较慢",
                    "use_case": "复杂音乐、多乐器",
                    "model_size": "约 280MB"
                },
                "mdx": {
                    "name": "MDX-Net - 高质量",
                    "description": "快速处理，质量不错",
                    "quality": "⭐⭐⭐⭐ 高质量",
                    "speed": "⚡⚡⚡⚡ 快速",
                    "use_case": "批量处理、快速预览",
                    "model_size": "约 190MB"
                },
                "hdemucs_mmi": {
                    "name": "Hybrid Demucs MMI - 中等质量",
                    "description": "最快速度，中等质量",
                    "quality": "⭐⭐⭐ 中等质量",
                    "speed": "⚡⚡⚡⚡⚡ 最快",
                    "use_case": "快速预览、低配置",
                    "model_size": "约 150MB"
                }
            },
            "devices": ["auto", "cuda", "cpu"],
            "current_model": self.current_model,
            "current_device": self.device
        }
    
    # 扩展方法（非接口要求）
    def is_available(self) -> bool:
        """检查插件是否可用"""
        return self.is_initialized
    
    def get_info(self) -> Dict[str, Any]:
        """获取插件信息"""
        return {
            "name": self.name,
            "version": self.version,
            "description": self.description,
            "author": "FlipTalk AI Team",
            "technology": "Facebook Demucs",
            "supported_formats": self.get_supported_formats(),
            "available_models": self.available_models,
            "current_model": self.current_model,
            "device": self.device,
            "cache_directory": str(MODELS_CACHE_DIR),
            "is_gpu_accelerated": self.device == "cuda"
        }
    
    def set_config(self, config: Dict[str, Any]) -> bool:
        """设置插件配置"""
        try:
            if "model" in config and config["model"] in self.available_models:
                self.current_model = config["model"]
                print(f"🔧 已切换到模型: {self.current_model}")
            
            if "device" in config:
                valid_devices = ["auto", "cuda", "cpu"]
                if config["device"] in valid_devices:
                    self.device = config["device"]
                    print(f"🔧 已设置设备: {self.device}")
            
            return True
        except Exception as e:
            print(f"❌ 配置设置失败: {e}")
            return False
    
    def separate_audio(self, audio_path: Path, output_dir: Path, 
                      progress_callback: Optional[Callable[[int], None]] = None,
                      status_callback: Optional[Callable[[str], None]] = None,
                      output_options: Dict[str, bool] = None) -> Dict[str, Path]:
        """
        执行音频分离（带进度回调的版本）
        这是为UI对话框提供的扩展方法
        """
        if status_callback:
            status_callback(f"正在加载模型: {self.current_model}")
        if progress_callback:
            progress_callback(10)
        
        # 调用标准的separate方法，传递输出选项
        result = self.separate(str(audio_path), str(output_dir), output_options)
        
        if progress_callback:
            progress_callback(100)
        if status_callback:
            status_callback("音频分离完成")
        
        # 转换字符串路径为Path对象
        return {k: Path(v) for k, v in result.items()}
    
    def get_cache_info(self) -> Dict[str, Any]:
        """获取缓存信息"""
        cache_info = {
            "cache_directory": str(MODELS_CACHE_DIR),
            "cache_exists": MODELS_CACHE_DIR.exists(),
            "models": {}
        }
        
        if MODELS_CACHE_DIR.exists():
            # 计算缓存目录大小
            total_size = 0
            model_files = []
            
            for file_path in MODELS_CACHE_DIR.rglob("*"):
                if file_path.is_file():
                    size = file_path.stat().st_size
                    total_size += size
                    model_files.append({
                        "name": file_path.name,
                        "size": size,
                        "path": str(file_path.relative_to(MODELS_CACHE_DIR))
                    })
            
            cache_info["total_size"] = total_size
            cache_info["total_size_mb"] = round(total_size / (1024 * 1024), 2)
            cache_info["model_files"] = model_files
            cache_info["file_count"] = len(model_files)
        
        return cache_info
    
    def clear_cache(self) -> bool:
        """清理模型缓存"""
        try:
            if MODELS_CACHE_DIR.exists():
                shutil.rmtree(MODELS_CACHE_DIR)
                MODELS_CACHE_DIR.mkdir(parents=True, exist_ok=True)
                print(f"✅ 已清理模型缓存: {MODELS_CACHE_DIR}")
                return True
            return True
        except Exception as e:
            print(f"❌ 清理缓存失败: {e}")
            return False

# 模拟人声分离（演示模式）
def simulate_voice_separation(audio_path: Path, output_dir: Path, 
                            progress_callback=None, status_callback=None):
    """
    模拟人声分离（演示模式）
    当Demucs不可用时使用此函数进行演示
    """
    import time
    import random
    
    print(f"🎭 使用模拟模式进行人声分离: {audio_path.name}")
    
    file_output_dir = output_dir / audio_path.stem
    file_output_dir.mkdir(parents=True, exist_ok=True)
    
    # 模拟分离过程
    steps = [
        ("初始化Demucs模型", 10),
        ("加载音频文件", 25),
        ("执行人声分离", 70),
        ("保存分离结果", 85),
        ("完成处理", 100)
    ]
    
    output_files = {}
    
    for step_name, progress in steps:
        if status_callback:
            status_callback(step_name)
        if progress_callback:
            progress_callback(progress)
        
        # 模拟处理时间
        time.sleep(random.uniform(0.5, 1.5))
    
    # 创建模拟输出文件（空文件）
    sources = ["vocals", "drums", "bass", "other"]
    for source in sources:
        output_path = file_output_dir / f"{source}.wav"
        output_path.touch()  # 创建空文件
        output_files[source] = output_path
    
    return output_files

# 插件工厂函数
def create_plugin() -> IVoiceSeparator:
    """创建插件实例"""
    return DemucsVoiceSeparationPlugin()

# 插件入口点
def get_plugin_info():
    """获取插件基本信息"""
    return {
        "name": "Demucs Voice Separation",
        "version": "1.0.0",
        "description": "基于Facebook Demucs的专业AI人声分离插件",
        "author": "FlipTalk AI Team",
        "type": "voice_separation",
        "main_class": "DemucsVoiceSeparationPlugin"
    }

if __name__ == "__main__":
    # 测试插件
    plugin = create_plugin()
    
    print("插件信息:")
    info = plugin.get_info()
    for key, value in info.items():
        print(f"  {key}: {value}")
    
    print(f"\n初始化结果: {plugin.initialize({})}")
    print(f"插件可用性: {plugin.is_available()}")
    
    # 显示缓存信息
    cache_info = plugin.get_cache_info()
    print(f"\n缓存信息:")
    print(f"  缓存目录: {cache_info['cache_directory']}")
    print(f"  目录存在: {cache_info['cache_exists']}")
    if cache_info['cache_exists']:
        print(f"  文件数量: {cache_info.get('file_count', 0)}")
        print(f"  总大小: {cache_info.get('total_size_mb', 0)} MB")


class CascadedNetVoiceSeparationPlugin(IVoiceSeparator):
    """
    基于CascadedNet的快速人声分离插件

    功能特性：
    - 快速高质量人声分离
    - GPU加速支持
    - 测试时增强(TTA)支持
    - 轻量级模型，快速加载
    """

    def __init__(self):
        self.name = "CascadedNet Voice Separation"
        self.version = "1.0.0"
        self.description = "基于CascadedNet的快速AI人声分离插件"
        self.is_initialized = False
        self.separator = None
        self.device = "auto"
        self.config = {}

        # CascadedNet模型路径
        self.project_root = Path(__file__).parent.parent.parent
        self.model_dir = self.project_root / "models" / "vocal_separation" / "weights"
        self.model_path = None

        # 质量设置
        self.quality_settings = {
            'fast': {'batchsize': 8, 'postprocess': False},
            'balanced': {'batchsize': 4, 'postprocess': False},
            'high': {'batchsize': 2, 'postprocess': True}
        }
        self.current_quality = 'balanced'

    # IPlugin接口方法
    def get_name(self) -> str:
        """获取插件名称"""
        return self.name

    def get_version(self) -> str:
        """获取插件版本"""
        return self.version

    def get_description(self) -> str:
        """获取插件描述"""
        return self.description

    def initialize(self, config: Dict[str, Any]) -> bool:
        """初始化插件"""
        try:
            self.config = config or {}

            # 查找CascadedNet模型文件
            self._find_model_file()

            if not self.model_path:
                print("❌ CascadedNet插件初始化失败: 未找到模型文件")
                print(f"请确保baseline.pth存在于 {self.model_dir} 目录中")
                return False

            # 检查依赖
            try:
                import torch
                import librosa
                import soundfile as sf
                import numpy as np
                from tqdm import tqdm
            except ImportError as e:
                print(f"❌ CascadedNet插件初始化失败: 缺少依赖 - {e}")
                print("请安装依赖: pip install torch librosa soundfile numpy tqdm")
                return False

            # 设置设备
            if self.device == "auto":
                self.device = "cuda" if torch.cuda.is_available() else "cpu"

            print(f"✅ CascadedNet插件初始化成功，使用设备: {self.device}")
            print(f"📁 模型文件: {self.model_path}")

            self.is_initialized = True
            return True

        except Exception as e:
            print(f"❌ CascadedNet插件初始化失败: {e}")
            return False

    def cleanup(self) -> None:
        """清理资源"""
        if hasattr(self, 'separator') and self.separator:
            del self.separator

        # 清理GPU内存
        try:
            import torch
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
        except ImportError:
            pass

    def _find_model_file(self):
        """查找CascadedNet模型文件"""
        if not self.model_dir.exists():
            return

        # 优先查找baseline.pth
        baseline_model = self.model_dir / "baseline.pth"
        if baseline_model.exists():
            self.model_path = str(baseline_model)
            return

        # 查找其他.pth文件
        pth_files = list(self.model_dir.glob("*.pth"))
        if pth_files:
            self.model_path = str(pth_files[0])

    # IVoiceSeparator接口方法
    def separate(self, audio_path: str, output_dir: str = None, output_options: Dict[str, bool] = None) -> Dict[str, str]:
        """
        分离音频中的人声和背景音乐

        Args:
            audio_path: 输入音频文件路径
            output_dir: 输出目录，如果为None则使用默认目录
            output_options: 输出选项字典，指定要保存哪些轨道

        Returns:
            Dict[str, str]: 分离结果，包含各音轨的文件路径
        """
        if not self.is_initialized:
            raise RuntimeError("CascadedNet插件未正确初始化")

        audio_path = Path(audio_path)
        if not audio_path.exists():
            raise FileNotFoundError(f"音频文件不存在: {audio_path}")

        if output_dir is None:
            output_dir = self.project_root / "output" / "voice_separation"
        output_dir = Path(output_dir)

        try:
            # 导入CascadedNet分离器
            from models.vocal_separation import create_separator

            # 获取质量配置
            quality_config = self.quality_settings.get(self.current_quality, self.quality_settings['balanced'])

            # 创建分离器
            separator = create_separator(
                model_path=self.model_path,
                device=self.device,
                batchsize=quality_config['batchsize'],
                postprocess=quality_config['postprocess']
            )

            # 确保输出目录存在
            output_dir.mkdir(parents=True, exist_ok=True)

            # 设置默认输出选项
            if output_options is None:
                output_options = {
                    'vocals': True,
                    'background': True
                }

            # 生成输出文件路径
            file_stem = audio_path.stem
            output_files = {}

            vocal_path = None
            instrumental_path = None

            if output_options.get('vocals', False):
                vocal_path = output_dir / f"{file_stem}_vocals.wav"
                output_files['vocals'] = str(vocal_path)

            if output_options.get('background', False):
                instrumental_path = output_dir / f"{file_stem}_background.wav"
                output_files['background'] = str(instrumental_path)

            # 执行CascadedNet人声分离
            print(f"正在分离音频: {audio_path.name}")

            # TTA默认启用
            use_tta = True

            # 使用分离器接口
            result_vocal, result_instrumental = separator.separate_audio_file(
                input_path=str(audio_path),
                output_vocal_path=str(vocal_path) if vocal_path else None,
                output_instrumental_path=str(instrumental_path) if instrumental_path else None,
                use_tta=use_tta,
                sr=44100
            )

            print("✅ CascadedNet 人声分离完成，音频已自动归一化和增益补偿")

            # 验证输出文件是否生成
            verified_output = {}
            for key, path in output_files.items():
                if path and Path(path).exists() and Path(path).stat().st_size > 0:
                    verified_output[key] = path

            return verified_output

        except Exception as e:
            error_msg = f"CascadedNet音频分离失败: {str(e)}"
            raise RuntimeError(error_msg)

    def get_supported_formats(self) -> list:
        """获取支持的音频格式"""
        return [".wav", ".mp3", ".m4a", ".flac", ".aac", ".ogg"]

    def get_quality_settings(self) -> Dict[str, Any]:
        """获取质量设置选项"""
        return {
            "qualities": {
                "fast": {
                    "name": "快速模式",
                    "description": "最快速度，适合预览",
                    "quality": "⭐⭐⭐ 中等质量",
                    "speed": "⚡⚡⚡⚡⚡ 最快",
                    "use_case": "快速预览、批量处理",
                    "batchsize": 8,
                    "postprocess": False
                },
                "balanced": {
                    "name": "平衡模式",
                    "description": "质量和速度平衡，推荐使用",
                    "quality": "⭐⭐⭐⭐ 高质量",
                    "speed": "⚡⚡⚡ 中等速度",
                    "use_case": "日常使用，推荐设置",
                    "batchsize": 4,
                    "postprocess": False
                },
                "high": {
                    "name": "高质量模式",
                    "description": "最高质量，处理较慢",
                    "quality": "⭐⭐⭐⭐⭐ 最高质量",
                    "speed": "⚡⚡ 较慢",
                    "use_case": "追求最佳音质",
                    "batchsize": 2,
                    "postprocess": True
                }
            },
            "devices": ["auto", "cuda", "cpu"],
            "current_quality": self.current_quality,
            "current_device": self.device,
            "model_path": self.model_path
        }

    # 扩展方法
    def is_available(self) -> bool:
        """检查插件是否可用"""
        return self.is_initialized and self.model_path is not None

    def get_info(self) -> Dict[str, Any]:
        """获取插件信息"""
        return {
            "name": self.name,
            "version": self.version,
            "description": self.description,
            "author": "FlipTalk AI Team",
            "technology": "CascadedNet (pytvzhen-1.3.0)",
            "supported_formats": self.get_supported_formats(),
            "current_quality": self.current_quality,
            "device": self.device,
            "model_path": self.model_path,
            "model_exists": self.model_path is not None and Path(self.model_path).exists(),
            "is_gpu_accelerated": self.device == "cuda"
        }

    def set_config(self, config: Dict[str, Any]) -> bool:
        """设置插件配置"""
        try:
            if "quality" in config and config["quality"] in self.quality_settings:
                self.current_quality = config["quality"]
                print(f"🔧 已切换到质量模式: {self.current_quality}")

            if "device" in config:
                valid_devices = ["auto", "cuda", "cpu"]
                if config["device"] in valid_devices:
                    self.device = config["device"]
                    print(f"🔧 已设置设备: {self.device}")

            return True
        except Exception as e:
            print(f"❌ 配置设置失败: {e}")
            return False

    def separate_audio(self, audio_path: Path, output_dir: Path,
                      progress_callback: Optional[Callable[[int], None]] = None,
                      status_callback: Optional[Callable[[str], None]] = None,
                      output_options: Dict[str, bool] = None) -> Dict[str, Path]:
        """
        执行音频分离（带进度回调的版本）
        这是为UI对话框提供的扩展方法
        """
        if status_callback:
            status_callback("正在加载CascadedNet模型...")
        if progress_callback:
            progress_callback(10)

        if status_callback:
            status_callback("正在执行人声分离...")
        if progress_callback:
            progress_callback(50)

        # 调用标准的separate方法
        result = self.separate(str(audio_path), str(output_dir), output_options)

        if progress_callback:
            progress_callback(100)
        if status_callback:
            status_callback("CascadedNet分离完成")

        # 转换字符串路径为Path对象
        return {k: Path(v) for k, v in result.items()}


# 插件工厂函数 - 更新以支持多种算法
def create_plugin(algorithm: str = "demucs") -> IVoiceSeparator:
    """
    创建插件实例

    Args:
        algorithm: 算法类型 ("demucs" 或 "cascadednet")

    Returns:
        IVoiceSeparator: 插件实例
    """
    if algorithm.lower() == "cascadednet":
        return CascadedNetVoiceSeparationPlugin()
    else:
        return DemucsVoiceSeparationPlugin()


def create_cascadednet_plugin() -> IVoiceSeparator:
    """创建CascadedNet插件实例的便捷函数"""
    return CascadedNetVoiceSeparationPlugin()


def create_demucs_plugin() -> IVoiceSeparator:
    """创建Demucs插件实例的便捷函数"""
    return DemucsVoiceSeparationPlugin()


# 插件入口点 - 更新以支持多种算法
def get_plugin_info():
    """获取插件基本信息"""
    return {
        "name": "Voice Separation Plugins",
        "version": "1.0.0",
        "description": "支持多种算法的AI人声分离插件集合",
        "author": "FlipTalk AI Team",
        "type": "voice_separation",
        "algorithms": {
            "cascadednet": {
                "name": "CascadedNet Voice Separation",
                "class": "CascadedNetVoiceSeparationPlugin",
                "description": "快速高质量人声分离，推荐使用"
            },
            "demucs": {
                "name": "Demucs Voice Separation",
                "class": "DemucsVoiceSeparationPlugin",
                "description": "专业级多轨道分离"
            }
        }
    }