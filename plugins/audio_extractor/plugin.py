#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FlipTalk AI - FFmpeg音频提取插件
使用FFmpeg从视频文件中提取音频流
"""

import os
import subprocess
import shutil
from pathlib import Path
from typing import Dict, Any
import json

# 添加core模块到路径
import sys
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

from core.interfaces import IAudioExtractor


class FFmpegAudioExtractor(IAudioExtractor):
    """
    基于FFmpeg的音频提取插件
    支持多种视频格式，提取高质量音频
    """
    
    def __init__(self):
        self.name = "FFmpeg音频提取器"
        self.version = "1.0.0"
        self.description = "使用FFmpeg从视频文件中提取音频流，支持多种格式"
        self.ffmpeg_path = None
        self.output_format = "wav"  # 默认输出格式
        self.sample_rate = 44100    # 默认采样率
        self.channels = 2           # 默认声道数
        
        # 支持的视频格式
        self.supported_formats = [
            '.mp4', '.avi', '.mkv', '.mov', '.wmv', '.flv', 
            '.webm', '.m4v', '.3gp', '.asf', '.rmvb', '.rm'
        ]
    
    def get_name(self) -> str:
        """获取插件名称"""
        return self.name
    
    def get_version(self) -> str:
        """获取插件版本"""
        return self.version
    
    def get_description(self) -> str:
        """获取插件描述"""
        return self.description
    
    def initialize(self, config: Dict[str, Any]) -> bool:
        """
        初始化插件
        
        Args:
            config: 配置参数字典
            
        Returns:
            bool: 初始化是否成功
        """
        try:
            # 查找FFmpeg可执行文件
            self.ffmpeg_path = self._find_ffmpeg()
            if not self.ffmpeg_path:
                print("错误：未找到FFmpeg可执行文件")
                return False
            
            # 应用配置参数
            if config:
                self.output_format = config.get('output_format', self.output_format)
                self.sample_rate = config.get('sample_rate', self.sample_rate)
                self.channels = config.get('channels', self.channels)
            
            print(f"FFmpeg路径: {self.ffmpeg_path}")
            print(f"输出格式: {self.output_format}, 采样率: {self.sample_rate}, 声道: {self.channels}")
            
            return True
            
        except Exception as e:
            print(f"FFmpeg音频提取器初始化失败: {e}")
            return False
    
    def cleanup(self) -> None:
        """清理插件资源"""
        pass
    
    def extract(self, video_path: str, output_dir: str = None) -> str:
        """
        从视频文件提取音频
        
        Args:
            video_path (str): 输入视频文件路径
            output_dir (str, optional): 输出目录，如果为None则使用视频文件同目录
            
        Returns:
            str: 提取的音频文件路径
            
        Raises:
            FileNotFoundError: 视频文件不存在
            Exception: 提取过程中的其他错误
        """
        # 检查输入文件
        video_path = Path(video_path)
        if not video_path.exists():
            raise FileNotFoundError(f"视频文件不存在: {video_path}")
        
        # 检查文件格式
        if video_path.suffix.lower() not in self.supported_formats:
            print(f"警告：文件格式 {video_path.suffix} 可能不被支持")
        
        # 确定输出目录和文件名
        if output_dir is None:
            output_dir = video_path.parent
        else:
            output_dir = Path(output_dir)
            output_dir.mkdir(parents=True, exist_ok=True)
        
        # 生成输出文件路径，处理中文文件名
        safe_stem = self._sanitize_filename(video_path.stem)
        output_filename = f"{safe_stem}_audio.{self.output_format}"
        output_path = output_dir / output_filename
        
        # 构建FFmpeg命令
        cmd = [
            self.ffmpeg_path,
            '-i', str(video_path),           # 输入文件
            '-vn',                           # 禁用视频流
            '-acodec', 'pcm_s16le',         # 音频编码器
            '-ar', str(self.sample_rate),    # 采样率
            '-ac', str(self.channels),       # 声道数
            '-y',                            # 覆盖输出文件
            str(output_path)                 # 输出文件
        ]
        
        try:
            print(f"开始提取音频: {video_path.name}")
            print(f"FFmpeg命令: {' '.join(cmd)}")
            
            # 执行FFmpeg命令，添加编码支持
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                check=True,
                encoding='utf-8',
                errors='ignore'
            )
            
            # 检查输出文件是否生成
            if output_path.exists() and output_path.stat().st_size > 0:
                print(f"音频提取成功: {output_path}")
                return str(output_path)
            else:
                raise Exception("音频文件生成失败或文件为空")
                
        except subprocess.CalledProcessError as e:
            error_msg = f"FFmpeg执行失败: {e.stderr}"
            print(error_msg)
            raise Exception(error_msg)
        except Exception as e:
            error_msg = f"音频提取失败: {e}"
            print(error_msg)
            raise Exception(error_msg)
    
    def get_supported_formats(self) -> list:
        """
        获取支持的视频格式列表
        
        Returns:
            list: 支持的视频格式扩展名列表
        """
        return self.supported_formats.copy()
    
    def get_audio_info(self, video_path: str) -> Dict[str, Any]:
        """
        获取视频中音频流的信息
        
        Args:
            video_path (str): 视频文件路径
            
        Returns:
            Dict[str, Any]: 音频信息，包含采样率、声道数、时长等
        """
        try:
            # 使用ffprobe获取音频信息
            cmd = [
                'ffprobe',
                '-v', 'quiet',
                '-print_format', 'json',
                '-show_streams',
                '-select_streams', 'a:0',  # 选择第一个音频流
                str(video_path)
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            data = json.loads(result.stdout)
            
            if 'streams' in data and len(data['streams']) > 0:
                stream = data['streams'][0]
                return {
                    'codec': stream.get('codec_name', 'unknown'),
                    'sample_rate': int(stream.get('sample_rate', 0)),
                    'channels': int(stream.get('channels', 0)),
                    'duration': float(stream.get('duration', 0)),
                    'bit_rate': int(stream.get('bit_rate', 0)),
                    'channel_layout': stream.get('channel_layout', 'unknown')
                }
            else:
                return {'error': '未找到音频流'}
                
        except Exception as e:
            return {'error': f'获取音频信息失败: {e}'}
    
    def _find_ffmpeg(self) -> str:
        """
        查找FFmpeg可执行文件
        
        Returns:
            str: FFmpeg可执行文件路径，如果未找到则返回None
        """
        # 常见的FFmpeg可执行文件名
        ffmpeg_names = ['ffmpeg', 'ffmpeg.exe']
        
        # 首先尝试在PATH中查找
        for name in ffmpeg_names:
            path = shutil.which(name)
            if path and self._test_ffmpeg(path):
                return path
        
        # 在常见安装目录中查找
        common_paths = [
            # Windows常见路径
            r'C:\ffmpeg\bin\ffmpeg.exe',
            r'C:\Program Files\ffmpeg\bin\ffmpeg.exe',
            r'C:\Program Files (x86)\ffmpeg\bin\ffmpeg.exe',
            # 当前目录及子目录
            './ffmpeg.exe',
            './bin/ffmpeg.exe',
            '../ffmpeg/bin/ffmpeg.exe',
            # Linux/Mac路径
            '/usr/bin/ffmpeg',
            '/usr/local/bin/ffmpeg',
            '/opt/ffmpeg/bin/ffmpeg'
        ]
        
        for path in common_paths:
            if os.path.exists(path) and self._test_ffmpeg(path):
                return path
        
        print("提示：请确保FFmpeg已安装并添加到系统PATH，或将ffmpeg.exe放在程序目录中")
        return None
    
    def _test_ffmpeg(self, ffmpeg_path: str) -> bool:
        """
        测试FFmpeg是否可用
        
        Args:
            ffmpeg_path: FFmpeg可执行文件路径
            
        Returns:
            bool: 是否可用
        """
        try:
            result = subprocess.run(
                [ffmpeg_path, '-version'],
                capture_output=True,
                text=True,
                timeout=10
            )
            return result.returncode == 0
        except:
            return False
    
    def _sanitize_filename(self, filename: str) -> str:
        """
        清理文件名，确保在不同系统上都能正常工作
        
        Args:
            filename: 原始文件名
            
        Returns:
            str: 清理后的安全文件名
        """
        import re
        
        # 保持中文字符，只替换不安全的字符
        unsafe_chars = r'[<>:"/\\|?*]'
        safe_filename = re.sub(unsafe_chars, '_', filename)
        
        # 去除首尾空格和点号
        safe_filename = safe_filename.strip(' .')
        
        # 如果文件名为空，使用默认名称
        if not safe_filename:
            safe_filename = "audio_file"
        
        # 限制长度（Windows文件名长度限制）
        if len(safe_filename) > 200:
            safe_filename = safe_filename[:200]
        
        return safe_filename 