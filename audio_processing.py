"""
音频处理模块

集成了多种音频处理算法，包括：
1. Demucs 人声分离（原有）
2. CascadedNet 人声分离（新增，来自 pytvzhen）

用户可以选择不同的算法进行人声分离处理。
"""

import os
import sys
import time
from pathlib import Path
from typing import Optional, Tuple, List, Dict, Any

# 添加模型路径
sys.path.append(str(Path(__file__).parent))

try:
    from models.vocal_separation import create_separator, get_model_info
    VOCAL_SEPARATION_AVAILABLE = True
except ImportError as e:
    print(f"警告: 无法导入人声分离模块: {e}")
    VOCAL_SEPARATION_AVAILABLE = False


class AudioProcessor:
    """
    音频处理器主类
    
    支持多种人声分离算法：
    - demucs: Facebook的Demucs算法（原有）
    - cascaded: CascadedNet算法（新增，速度更快）
    """
    
    def __init__(self):
        self.available_methods = self._check_available_methods()
        print(f"可用的音频分离方法: {list(self.available_methods.keys())}")
    
    def _check_available_methods(self) -> Dict[str, bool]:
        """检查可用的分离方法"""
        methods = {}
        
        # 检查 Demucs（原有方法）
        try:
            import demucs
            methods['demucs'] = True
        except ImportError:
            methods['demucs'] = False
        
        # 检查 CascadedNet（新增方法）
        methods['cascaded'] = VOCAL_SEPARATION_AVAILABLE
        
        return methods
    
    def separate_vocals(self, 
                       input_path: str,
                       output_dir: str = "output",
                       method: str = "cascaded",
                       quality: str = "balanced",
                       **kwargs) -> Tuple[Optional[str], Optional[str]]:
        """
        分离音频中的人声和伴奏
        
        Args:
            input_path: 输入音频文件路径
            output_dir: 输出目录
            method: 分离方法 ('demucs' 或 'cascaded')
            quality: 质量设置 ('fast', 'balanced', 'high')
            **kwargs: 其他参数
            
        Returns:
            tuple: (人声文件路径, 伴奏文件路径)
        """
        if not os.path.exists(input_path):
            raise FileNotFoundError(f"输入文件不存在: {input_path}")
        
        os.makedirs(output_dir, exist_ok=True)
        
        if method == "cascaded":
            return self._separate_with_cascaded(input_path, output_dir, quality, **kwargs)
        elif method == "demucs":
            return self._separate_with_demucs(input_path, output_dir, quality, **kwargs)
        else:
            raise ValueError(f"不支持的分离方法: {method}")
    
    def _separate_with_cascaded(self, 
                               input_path: str, 
                               output_dir: str, 
                               quality: str,
                               **kwargs) -> Tuple[str, str]:
        """使用 CascadedNet 算法分离"""
        if not VOCAL_SEPARATION_AVAILABLE:
            raise ImportError("CascadedNet 人声分离模块不可用")
        
        print("使用 CascadedNet 算法进行人声分离...")
        
        # 质量设置映射
        quality_configs = {
            'fast': {
                'use_tta': True,  # TTA默认启用
                'postprocess': False,
                'batchsize': 8
            },
            'balanced': {
                'use_tta': True,
                'postprocess': False,
                'batchsize': 4
            },
            'high': {
                'use_tta': True,
                'postprocess': True,
                'batchsize': 2
            }
        }
        
        config = quality_configs.get(quality, quality_configs['balanced'])
        config.update(kwargs)  # 用户自定义参数覆盖默认值
        
        # 模型路径
        model_path = os.path.join(os.path.dirname(__file__), "models/vocal_separation/weights/baseline.pth")
        if not os.path.exists(model_path):
            raise FileNotFoundError(f"模型文件不存在: {model_path}")
        
        # 创建分离器
        separator = create_separator(
            model_path=model_path,
            device="auto",
            batchsize=config['batchsize'],
            postprocess=config['postprocess']
        )
        
        # 生成输出文件路径
        base_name = os.path.splitext(os.path.basename(input_path))[0]
        vocal_path = os.path.join(output_dir, f"{base_name}_vocal.wav")
        instrumental_path = os.path.join(output_dir, f"{base_name}_instrumental.wav")
        
        # 执行分离
        start_time = time.time()
        separator.separate_audio_file(
            input_path=input_path,
            output_vocal_path=vocal_path,
            output_instrumental_path=instrumental_path,
            use_tta=config['use_tta'],
            sr=44100
        )
        end_time = time.time()
        
        print(f"CascadedNet 分离完成，用时: {end_time - start_time:.2f}秒")
        return vocal_path, instrumental_path
    
    def _separate_with_demucs(self, 
                             input_path: str, 
                             output_dir: str, 
                             quality: str,
                             **kwargs) -> Tuple[str, str]:
        """使用 Demucs 算法分离"""
        if not self.available_methods.get('demucs'):
            raise ImportError("Demucs 不可用")
        
        print("使用 Demucs 算法进行人声分离...")
        
        # 这里需要根据您的原有 Demucs 集成代码进行实现
        # 以下是示例代码框架
        
        import subprocess
        
        # Demucs 命令行调用示例
        cmd = [
            "python", "-m", "demucs.separate",
            "--out", output_dir,
            input_path
        ]
        
        start_time = time.time()
        result = subprocess.run(cmd, capture_output=True, text=True)
        end_time = time.time()
        
        if result.returncode != 0:
            raise RuntimeError(f"Demucs 处理失败: {result.stderr}")
        
        # 生成输出文件路径（需要根据 Demucs 的实际输出格式调整）
        base_name = os.path.splitext(os.path.basename(input_path))[0]
        vocal_path = os.path.join(output_dir, f"{base_name}_vocals.wav")
        instrumental_path = os.path.join(output_dir, f"{base_name}_no_vocals.wav")
        
        print(f"Demucs 分离完成，用时: {end_time - start_time:.2f}秒")
        return vocal_path, instrumental_path
    
    def batch_separate(self, 
                      input_files: List[str],
                      output_dir: str = "batch_output",
                      method: str = "cascaded",
                      quality: str = "balanced",
                      **kwargs) -> List[Tuple[str, str]]:
        """
        批量分离音频文件
        
        Args:
            input_files: 输入文件列表
            output_dir: 输出目录
            method: 分离方法
            quality: 质量设置
            **kwargs: 其他参数
            
        Returns:
            list: [(人声文件路径, 伴奏文件路径), ...]
        """
        results = []
        total = len(input_files)
        
        for i, input_file in enumerate(input_files):
            print(f"\n处理文件 {i+1}/{total}: {os.path.basename(input_file)}")
            
            try:
                vocal_path, instrumental_path = self.separate_vocals(
                    input_file, output_dir, method, quality, **kwargs
                )
                results.append((vocal_path, instrumental_path))
            except Exception as e:
                print(f"处理文件 {input_file} 时出错: {e}")
                results.append((None, None))
        
        return results
    
    def compare_methods(self, 
                       input_path: str, 
                       output_dir: str = "comparison") -> Dict[str, Any]:
        """
        比较不同分离方法的效果和性能
        
        Args:
            input_path: 输入音频文件
            output_dir: 输出目录
            
        Returns:
            dict: 比较结果
        """
        os.makedirs(output_dir, exist_ok=True)
        results = {}
        
        available_methods = [m for m, available in self.available_methods.items() if available]
        
        for method in available_methods:
            print(f"\n测试方法: {method}")
            
            method_output_dir = os.path.join(output_dir, method)
            os.makedirs(method_output_dir, exist_ok=True)
            
            try:
                start_time = time.time()
                vocal_path, instrumental_path = self.separate_vocals(
                    input_path, method_output_dir, method, "balanced"
                )
                end_time = time.time()
                
                results[method] = {
                    'success': True,
                    'processing_time': end_time - start_time,
                    'vocal_path': vocal_path,
                    'instrumental_path': instrumental_path
                }
                
            except Exception as e:
                results[method] = {
                    'success': False,
                    'error': str(e),
                    'processing_time': None
                }
        
        return results
    
    def get_supported_formats(self) -> List[str]:
        """获取支持的音频格式"""
        return ['.wav', '.mp3', '.flac', '.ogg', '.m4a', '.aac']
    
    def get_method_info(self, method: str = None) -> Dict[str, Any]:
        """获取方法信息"""
        if method == "cascaded" and VOCAL_SEPARATION_AVAILABLE:
            return get_model_info()
        elif method == "demucs":
            return {
                "算法名称": "Demucs",
                "来源": "Facebook Research",
                "架构": "U-Net based",
                "特点": ["高质量分离", "多源分离", "预训练模型"]
            }
        else:
            return {
                "可用方法": list(self.available_methods.keys()),
                "推荐方法": "cascaded" if VOCAL_SEPARATION_AVAILABLE else "demucs"
            }


# 便捷函数
def separate_audio(input_path: str, 
                  output_dir: str = "output",
                  method: str = "cascaded",
                  quality: str = "balanced") -> Tuple[str, str]:
    """
    便捷的音频分离函数
    
    Args:
        input_path: 输入音频文件路径
        output_dir: 输出目录
        method: 分离方法 ('cascaded' 或 'demucs')
        quality: 质量设置 ('fast', 'balanced', 'high')
        
    Returns:
        tuple: (人声文件路径, 伴奏文件路径)
    """
    processor = AudioProcessor()
    return processor.separate_vocals(input_path, output_dir, method, quality)


def get_available_methods() -> List[str]:
    """获取可用的分离方法"""
    processor = AudioProcessor()
    return [method for method, available in processor.available_methods.items() if available]


if __name__ == "__main__":
    # 简单的命令行界面
    import argparse
    
    parser = argparse.ArgumentParser(description="音频人声分离工具")
    parser.add_argument("input", help="输入音频文件")
    parser.add_argument("--output", default="output", help="输出目录")
    parser.add_argument("--method", choices=["cascaded", "demucs"], 
                       default="cascaded", help="分离方法")
    parser.add_argument("--quality", choices=["fast", "balanced", "high"],
                       default="balanced", help="质量设置")
    
    args = parser.parse_args()
    
    try:
        vocal, instrumental = separate_audio(
            args.input, args.output, args.method, args.quality
        )
        print(f"\n分离完成!")
        print(f"人声文件: {vocal}")
        print(f"伴奏文件: {instrumental}")
    except Exception as e:
        print(f"处理失败: {e}") 