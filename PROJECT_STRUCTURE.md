# FlipTalk AI 项目结构说明

## 📁 核心目录结构

```
03 FlipTalk Ai/
├── 📄 main.py                    # 项目主入口文件
├── 📄 run_app.py                 # 应用启动脚本
├── 📄 requirements.txt           # Python依赖包列表
│
├── 📂 ui/                        # 用户界面组件目录
│   ├── 📄 fliptalk_ui.py        # 主界面UI组件（已优化字体排版）
│   ├── 📄 config_window.py      # 参数配置窗口
│   ├── 📄 voice_separation_dialog.py  # 人声分离对话框
│   ├── 📄 video_download_dialog.py    # 视频下载对话框
│   ├── 📄 batch_audio_extraction_dialog.py  # 批量音频提取对话框
│   └── 📄 __init__.py           # 包初始化文件
│
├── 📂 core/                      # 核心功能模块
│   ├── 📄 gpu_help_guide.py     # GPU配置指南（已优化排版）
│   └── ... (其他核心模块)
│
├── 📂 models/                    # AI模型存储目录
├── 📂 pretrained_models/         # 预训练模型目录
├── 📂 plugins/                   # 插件扩展目录
├── 📂 scripts/                   # 辅助脚本目录
├── 📂 assets/                    # 静态资源目录
├── 📂 tests/                     # 测试文件目录
├── 📂 docs/                      # 项目文档目录
├── 📂 output/                    # 处理结果输出目录
├── 📂 Downloads/                 # 下载文件存储目录
│
└── 📂 archive/                   # 归档文件目录
    ├── 📂 documentation/         # 说明文档归档
    │   ├── 📄 CUDA_INSTALLATION_GUIDE.md
    │   ├── 📄 GPU_DETECTION_README.md
    │   ├── 📄 VOICE_SEPARATION_README.md
    │   ├── 📄 VIDEO_DOWNLOAD_README.md
    │   └── 📄 AUDIO_EXTRACTION_README.md
    │
    ├── 📂 installation/          # 安装脚本归档
    │   ├── 📄 (已删除install_spleeter.py - 项目不使用Spleeter)
    │   ├── 📄 install_dependencies.py
    │   └── 📄 setup.py
    │
    ├── 📂 temp_files/           # 临时文件归档
    │   └── 📄 temp_dxdiag.txt
    │
    └── 📂 test_results/         # 测试结果归档
        ├── 📂 final_test_output/
        └── 📂 test_output/
```

## 🔧 最近优化内容

### GPU配置指南字体排版优化
- ✅ 修复了 `core/gpu_help_guide.py` 中的字体层次问题
- ✅ 建立了清晰的字体大小层次：20px → 18px → 17px → 13px
- ✅ 移除了UI中覆盖HTML字体设置的样式
- ✅ 小标题现在明显大于正文内容，解决了用户反馈的问题

### 文件结构整理
- ✅ 删除了重复的 `fliptalk_ui.py` 文件，保留最新版本
- ✅ 将相关文件归档到对应目录，提高项目组织性
- ✅ 创建清晰的目录分类，便于维护和查找

## 📋 文件说明

### 主要执行文件
- **main.py**: 项目的主入口点
- **run_app.py**: 应用程序启动脚本

### UI组件
- **ui/fliptalk_ui.py**: 主界面组件，包含完整的用户界面功能
- **ui/config_window.py**: 参数配置窗口
- **ui/*_dialog.py**: 各种功能对话框

### 核心功能
- **core/gpu_help_guide.py**: GPU配置指南，提供详细的硬件配置帮助

### 归档内容
- **archive/documentation/**: 各种README和说明文档
- **archive/installation/**: 安装和设置相关脚本
- **archive/temp_files/**: 临时生成的文件
- **archive/test_results/**: 测试输出结果

## 🚀 快速开始

1. 安装依赖：`pip install -r requirements.txt`
2. 运行应用：`python run_app.py` 或 `python main.py`

## 📖 文档位置

所有详细的说明文档已移动到 `archive/documentation/` 目录下，包括：
- CUDA安装指南
- GPU检测说明
- 各功能模块的使用说明

---

📅 最后更新：2025年6月8日  
🔧 优化内容：GPU配置指南字体排版 + 项目文件结构整理 