#!/usr/bin/env python
# -*- coding: utf-8 -*-

# 修复ui/fliptalk_ui.py中的语法错误
import os
import tempfile
import shutil

# 定义要修复的方法
fixes = {
    # 修复SubtitleEditArea类的setup_gpu_detector方法
    "def setup_gpu_detector(self):\n        \"\"\"设置GPU检测器\"\"\"\n        # 尝试导入GPU检测器\n        try:\n            from core.gpu_detector import GPUDetector\n            self.gpu_detector = GPUDetector()\n        self.gpu_detector.gpu_status_updated.connect(self.on_gpu_status_updated)":
    "def setup_gpu_detector(self):\n        \"\"\"设置GPU检测器\"\"\"\n        # 尝试导入GPU检测器\n        try:\n            from core.gpu_detector import GPUDetector\n            self.gpu_detector = GPUDetector()\n            self.gpu_detector.gpu_status_updated.connect(self.on_gpu_status_updated)",
    
    # 修复SubtitleEditArea类的refresh_gpu_status方法
    "def refresh_gpu_status(self):\n        \"\"\"刷新GPU状态\"\"\"\n        if self.gpu_detector:\n        self.gpu_detector.check_gpu_async()":
    "def refresh_gpu_status(self):\n        \"\"\"刷新GPU状态\"\"\"\n        if self.gpu_detector:\n            self.gpu_detector.check_gpu_async()",
    
    # 修复SubtitleEditArea类的detect_and_update_gpu_immediately方法
    "def detect_and_update_gpu_immediately(self):\n        \"\"\"立即检测GPU并更新状态\"\"\"\n        if self.gpu_detector:\n            try:\n                is_available, gpu_name = self.gpu_detector.check_gpu_sync()\n                status = \"可用\" if is_available else \"不可用\"\n                self.on_gpu_status_updated(is_available, gpu_name, status)\n        except Exception as e:\n                print(f\"GPU检测失败: {e}\")\n                self.on_gpu_status_updated(False, \"检测失败\", \"检测失败\")":
    "def detect_and_update_gpu_immediately(self):\n        \"\"\"立即检测GPU并更新状态\"\"\"\n        if self.gpu_detector:\n            try:\n                is_available, gpu_name = self.gpu_detector.check_gpu_sync()\n                status = \"可用\" if is_available else \"不可用\"\n                self.on_gpu_status_updated(is_available, gpu_name, status)\n            except Exception as e:\n                print(f\"GPU检测失败: {e}\")\n                self.on_gpu_status_updated(False, \"检测失败\", \"检测失败\")",
    
    # 修复FlipTalkMainWindow类的__init__方法
    "# 初始化GPU检测器\n        from core.gpu_detector import GPUDetector\n        self.gpu_detector = GPUDetector()\n        self.gpu_detector.gpu_status_updated.connect(self.on_gpu_status_updated)\n        self.gpu_is_available = False\n        self.gpu_name = \"检测中...\"":
    "# 初始化GPU检测器\n        self.gpu_is_available = False\n        self.gpu_name = \"检测中...\"\n        try:\n            from core.gpu_detector import GPUDetector\n            self.gpu_detector = GPUDetector()\n            self.gpu_detector.gpu_status_updated.connect(self.on_gpu_status_updated)\n        except Exception as e:\n            print(f\"GPU检测器初始化失败: {e}\")"
}

# 读取原始文件内容
file_path = "ui/fliptalk_ui.py"
with open(file_path, "r", encoding="utf-8") as file:
    content = file.read()

# 应用所有修复
for old, new in fixes.items():
    content = content.replace(old, new)

# 创建临时文件写入修复后的内容
with tempfile.NamedTemporaryFile(mode="w", encoding="utf-8", delete=False) as temp_file:
    temp_file.write(content)
    temp_path = temp_file.name

# 替换原文件
shutil.copy2(temp_path, file_path)
os.unlink(temp_path)

print("文件已修复！") 