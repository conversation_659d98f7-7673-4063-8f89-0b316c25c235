# FlipTalk AI

FlipTalk AI 是一个强大的视频处理工具，支持视频字幕提取、翻译和配音功能。

## 主要功能

1. 视频处理
   - 视频下载
   - 音频提取
   - 声音分离

2. 字幕处理
   - 语音识别
   - 字幕提取
   - 字幕翻译
   - 字幕编辑

3. 配音功能
   - Azure TTS配音
   - Edge TTS配音
   - 语速控制
   - 音频合成

## 项目结构

```
FlipTalk AI/
├── assets/          # 资源文件
├── core/            # 核心功能模块
├── docs/            # 文档
│   ├── guides/      # 使用指南
│   └── reports/     # 开发报告
├── models/          # AI模型
├── plugins/         # 插件模块
├── scripts/         # 工具脚本
├── tests/           # 测试文件
└── ui/             # 用户界面
```

## 安装说明

1. 环境要求
   - Python 3.8+
   - CUDA 11.0+ (可选，用于GPU加速)
   - FFmpeg

2. 安装步骤
   ```bash
   # 克隆项目
   git clone https://github.com/your-username/fliptalk-ai.git
   cd fliptalk-ai

   # 安装依赖
   pip install -r requirements.txt

   # 安装TTS依赖
   python scripts/install_tts_dependencies.py
   ```

## 使用说明

1. 启动程序
   ```bash
   python main.py
   ```

2. 主要功能
   - 视频下载：支持YouTube等平台
   - 字幕提取：使用WhisperX进行语音识别
   - 字幕翻译：支持多语言翻译
   - 视频配音：支持Azure TTS和Edge TTS

## 配置说明

1. 创建配置文件
   ```bash
   cp config.example.py config.py
   ```

2. 配置项说明
   - API密钥设置
   - 模型参数配置
   - 输出路径设置

## 开发说明

1. 代码规范
   - 遵循PEP 8规范
   - 使用类型注解
   - 编写单元测试

2. 提交规范
   - 遵循Angular提交规范
   - 每次提交前运行测试
   - 更新相关文档

## 许可证

本项目采用 MIT 许可证。详见 [LICENSE](LICENSE) 文件。

## 贡献指南

欢迎提交Issue和Pull Request。在贡献代码前，请先阅读[贡献指南](CONTRIBUTING.md)。
