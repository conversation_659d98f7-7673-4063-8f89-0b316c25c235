import re

def remove_unused_classes():
    # 读取原始文件
    with open('ui/fliptalk_ui.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 找到VideoFrameThread类的开始和结束位置
    thread_start_pattern = r'class VideoFrameThread\(QThread\):'
    thread_start_match = re.search(thread_start_pattern, content)
    
    if thread_start_match:
        thread_start_pos = thread_start_match.start()
        
        # 找到下一个类定义位置
        next_class_pattern = r'class RoundedWidget\(QWidget\):'
        next_class_match = re.search(next_class_pattern, content)
        
        if next_class_match:
            thread_end_pos = next_class_match.start()
            
            # 从内容中删除VideoFrameThread类
            content = content[:thread_start_pos] + content[thread_end_pos:]
            print("成功删除VideoFrameThread类")
        else:
            print("找不到RoundedWidget类，无法确定VideoFrameThread类的结束位置。")
    else:
        print("找不到VideoFrameThread类的定义，可能已经被删除。")
    
    # 找到ClickableAvatar类的开始和结束位置
    avatar_start_pattern = r'class ClickableAvatar\(QLabel\):'
    avatar_start_match = re.search(avatar_start_pattern, content)
    
    if avatar_start_match:
        avatar_start_pos = avatar_start_match.start()
        
        # 找到下一个类定义位置
        next_class_pattern = r'class NavigationBar\(QWidget\):'
        next_class_match = re.search(next_class_pattern, content)
        
        if next_class_match:
            avatar_end_pos = next_class_match.start()
            
            # 从内容中删除ClickableAvatar类
            content = content[:avatar_start_pos] + content[avatar_end_pos:]
            print("成功删除ClickableAvatar类")
        else:
            print("找不到NavigationBar类，无法确定ClickableAvatar类的结束位置。")
    else:
        print("找不到ClickableAvatar类的定义，可能已经被删除。")
    
    # 保存修改后的内容
    with open('ui/fliptalk_ui.py', 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("文件保存成功")

if __name__ == "__main__":
    remove_unused_classes() 