"""
WhisperX 音频字幕提取器
基于经过验证的可靠方法，支持本地模型和API两种模式
整合了VideoLingo项目中的优化算法
"""

import os
import json
import time
import base64
import subprocess
from typing import Dict, List, Optional, Callable, Any, Tu<PERSON>
from pathlib import Path
import pandas as pd
import librosa
import soundfile as sf
from moviepy.editor import AudioFileClip
from rich import print as rprint
from rich.progress import Progress, SpinnerColumn, TextColumn, TimeElapsedColumn


class WhisperXExtractor:
    """WhisperX音频字幕提取器 - 经过验证的可靠实现"""
    
    def __init__(self, models_dir: str = None, device: str = "auto", use_api: bool = False, api_token: str = None):
        """
        初始化提取器
        
        Args:
            models_dir: 模型目录
            device: 计算设备 ("auto", "cuda", "cpu")
            use_api: 是否使用API模式
            api_token: API令牌（API模式必需）
        """
        self.models_dir = Path(models_dir) if models_dir else Path("models/whisperx_subtitle/weights")
        self.device = self._determine_device(device)
        self.use_api = use_api
        self.api_token = api_token
        self.current_model = None
        self.model_name = None
        
        # 根据设备优化配置
        if self.device == "cuda":
            try:
                import torch
                gpu_mem = torch.cuda.get_device_properties(0).total_memory / (1024**3)
                self.batch_size = 4 if gpu_mem <= 8 else 16
                self.compute_type = "float16"
                rprint(f"[cyan]GPU内存:[/cyan] {gpu_mem:.2f} GB, [cyan]批处理大小:[/cyan] {self.batch_size}")
            except ImportError:
                self.batch_size = 4
                self.compute_type = "int8"
        else:
            self.batch_size = 4
            self.compute_type = "int8"
        
        # API模式配置
        if self.use_api:
            if not api_token:
                raise ValueError("API模式需要提供api_token")
            os.environ["REPLICATE_API_TOKEN"] = api_token
        
        rprint(f"[green]WhisperX提取器初始化完成[/green] - 设备: {self.device}, 模式: {'API' if use_api else '本地'}")
    
    def _determine_device(self, device: str) -> str:
        """确定使用的计算设备"""
        if device == "auto":
            try:
                import torch
                return "cuda" if torch.cuda.is_available() else "cpu"
            except ImportError:
                return "cpu"
        return device
    
    def convert_video_to_audio(self, input_file: str, output_dir: str = "output/audio") -> str:
        """
        视频转音频 - 使用librosa优化版本
        
        Args:
            input_file: 输入视频文件
            output_dir: 输出目录
            
        Returns:
            str: 音频文件路径
        """
        os.makedirs(output_dir, exist_ok=True)
        audio_file = os.path.join(output_dir, 'raw_full_audio.wav')

        if not os.path.exists(audio_file):
            try:
                rprint(f"[green]🎬➡️🎵 使用librosa转换音频...[/green]")
                # 使用librosa加载音频，采样率16kHz
                y, sr = librosa.load(input_file, sr=16000)
                
                # 保存为WAV文件
                sf.write(audio_file, y, sr, subtype='PCM_16')
                
                rprint(f"[green]🎬➡️🎵 转换完成: {input_file} → {audio_file}[/green]")
            except Exception as e:
                rprint(f"[red]❌ 音频转换失败: {e}[/red]")
                raise

        return audio_file
    
    def split_audio_intelligent(self, audio_file: str, target_duration: int = 20*60, window: int = 60) -> List[Tuple[float, float]]:
        """
        智能音频分割 - 基于静音检测
        
        Args:
            audio_file: 音频文件路径
            target_duration: 目标分割时长（秒）
            window: 检测窗口（秒）
            
        Returns:
            List[Tuple[float, float]]: 分割点列表 (开始时间, 结束时间)
        """
        rprint("[green]🔪 开始智能音频分割...[/green]")
        
        # 获取音频时长
        with AudioFileClip(audio_file) as audio:
            duration = audio.duration
        
        segments = []
        start = 0
        
        while start < duration:
            end = min(start + target_duration + window, duration)
            if end - start < target_duration:
                segments.append((start, end))
                break
            
            # 分析窗口内的静音
            window_start = start + target_duration - window
            window_end = min(window_start + 2 * window, duration)
            
            ffmpeg_cmd = [
                'ffmpeg',
                '-y',
                '-i', audio_file,
                '-ss', str(window_start),
                '-to', str(window_end),
                '-af', 'silencedetect=n=-30dB:d=0.5',
                '-f', 'null',
                '-'
            ]
            
            try:
                output = subprocess.run(ffmpeg_cmd, capture_output=True, text=True, timeout=60).stderr
                
                # 解析静音检测结果
                silence_end_times = [
                    float(line.split('silence_end: ')[1].split(' ')[0]) 
                    for line in output.split('\n') 
                    if 'silence_end' in line
                ]
                
                if silence_end_times:
                    # 找到目标时长后的第一个静音点
                    split_point = next((t for t in silence_end_times if t > target_duration), None)
                    if split_point:
                        segments.append((start, start + split_point))
                        start += split_point
                        continue
            except subprocess.TimeoutExpired:
                rprint(f"[yellow]⚠️ 静音检测超时，使用固定分割[/yellow]")
            
            # 如果没有找到合适的分割点，使用固定时长
            segments.append((start, start + target_duration))
            start += target_duration
        
        rprint(f"[green]🔪 音频分割完成，共 {len(segments)} 个片段[/green]")
        return segments

    def load_model(self, model_name: str, force_cpu: bool = False) -> bool:
        """
        加载指定模型
        
        Args:
            model_name: 模型名称
            
        Returns:
            bool: 加载是否成功
        """
        try:
            # 检查是否需要重新加载
            if self.current_model and self.model_name == model_name:
                return True
            
            # 导入faster-whisper（更可靠的实现）
            from .model_manager import WhisperXModelManager
            
            rprint(f"[green]正在加载模型: {model_name}[/green]")
            
            # 使用模型管理器加载
            manager = WhisperXModelManager(str(self.models_dir))
            # 如果当前设备是CPU或强制CPU，则传递相应参数
            device_to_use = "cpu" if (self.device == "cpu" or force_cpu) else self.device
            compute_type_to_use = "int8" if device_to_use == "cpu" else "float16"
            
            self.current_model = manager.load_model(
                model_name, 
                device=device_to_use,
                compute_type=compute_type_to_use,
                force_cpu=force_cpu
            )
            
            if self.current_model:
                self.model_name = model_name
                rprint(f"[green]模型 {model_name} 加载成功[/green]")
                return True
            else:
                rprint(f"[red]模型 {model_name} 加载失败[/red]")
                return False
            
        except Exception as e:
            rprint(f"[red]加载模型 {model_name} 失败: {e}[/red]")
            return False
    
    def transcribe_segment_local(self, audio_file: str, start: float, end: float, 
                               model_name: str = "large-v3", language: str = None) -> Dict:
        """
        本地模型转录单个音频段 - 经过验证的方法
        
        Args:
            audio_file: 音频文件路径
            start: 开始时间
            end: 结束时间
            model_name: 模型名称
            language: 目标语言
            
        Returns:
            Dict: 转录结果
        """
        try:
            import faster_whisper
            import torch
            
            rprint(f"[green]🎙️ 转录片段: {start:.2f}s - {end:.2f}s[/green]")
            
            # 中文特殊模型处理
            if language == 'zh':
                rprint("[yellow]⚠️ 中文转录建议使用专用模型[/yellow]")
            
            # 加载模型
            if not self.load_model(model_name):
                raise Exception(f"无法加载模型: {model_name}")
            
            # 加载音频片段（faster-whisper方式）
            # 创建临时片段文件
            segment_file = f'temp_segment_{start:.2f}_{end:.2f}.wav'
            ffmpeg_cmd = [
                'ffmpeg', '-y', '-i', audio_file,
                '-ss', str(start), '-to', str(end),
                '-ar', '16000', '-ac', '1',  # 16kHz单声道
                segment_file
            ]
            
            subprocess.run(ffmpeg_cmd, check=True, stderr=subprocess.PIPE)
            
            # 使用faster-whisper转录
            with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                TimeElapsedColumn(),
                transient=True
            ) as progress:
                task = progress.add_task("[cyan]转录中...", total=None)
                
                try:
                    segments, info = self.current_model.transcribe(
                        segment_file,
                        language=language if language != 'auto' else None,
                        beam_size=5,
                        word_timestamps=True
                    )
                    
                    # 转换为列表
                    segments_list = list(segments)
                    progress.update(task, completed=True)
                    
                    # 检查是否有转录结果
                    if not segments_list:
                        rprint("[yellow]⚠️ 警告：该片段没有检测到语音内容[/yellow]")
                    
                except Exception as e:
                    progress.update(task, completed=True)
                    if "cudnn" in str(e).lower():
                        rprint("[yellow]⚠️ CUDA/CuDNN错误，尝试使用CPU模式...[/yellow]")
                        # 重新加载为CPU模式
                        self.load_model(model_name, force_cpu=True)
                        segments, info = self.current_model.transcribe(
                            segment_file,
                            language=language if language != 'auto' else None,
                            beam_size=5,
                            word_timestamps=True
                        )
                        segments_list = list(segments)
                    else:
                        raise e
            
            # 删除临时文件
            os.remove(segment_file)
            
            # 调整时间戳
            result_segments = []
            for segment in segments_list:
                adjusted_segment = {
                    'start': segment.start + start,
                    'end': segment.end + start,
                    'text': segment.text,
                    'words': []
                }
                
                # 调整词级时间戳
                for word in segment.words:
                    adjusted_word = {
                        'word': word.word,
                        'start': word.start + start,
                        'end': word.end + start
                    }
                    adjusted_segment['words'].append(adjusted_word)
                
                result_segments.append(adjusted_segment)
            
            return {
                'segments': result_segments,
                'language': info.language,
                'language_probability': info.language_probability
            }
            
        except Exception as e:
            if os.path.exists(segment_file):
                os.remove(segment_file)
            rprint(f"[red]本地转录失败:[/red] {e}")
            raise
    
    def transcribe_segment_api(self, audio_file: str, start: float, end: float, 
                             language: str = None) -> Dict:
        """
        API模式转录单个音频段
        
        Args:
            audio_file: 音频文件路径
            start: 开始时间
            end: 结束时间
            language: 目标语言
            
        Returns:
            Dict: 转录结果
        """
        if language == 'zh':
            raise Exception("WhisperX API 不支持中文，请使用本地模式进行中文转录")
        
        try:
            import replicate
            
            rprint(f"[green]🎙️ API转录片段: {start:.2f}s - {end:.2f}s[/green]")
            
            # 创建音频片段
            segment_file = f'temp_segment_{start:.2f}_{end:.2f}.wav'
            ffmpeg_cmd = [
                'ffmpeg', '-y', '-i', audio_file,
                '-ss', str(start), '-to', str(end),
                '-ar', '16000', '-ac', '1',
                segment_file
            ]
            
            try:
                subprocess.run(ffmpeg_cmd, check=True, stderr=subprocess.PIPE, timeout=300)
            except subprocess.TimeoutExpired:
                rprint("[yellow]⚠️ ffmpeg超时，重试...[/yellow]")
                subprocess.run(ffmpeg_cmd, check=True, stderr=subprocess.PIPE)
            
            time.sleep(1)  # 等待文件写入完成
            
            # 编码为base64
            with open(segment_file, 'rb') as file:
                audio_base64 = base64.b64encode(file.read()).decode('utf-8')
            
            segment_size = len(audio_base64) / (1024 * 1024)
            rprint(f"[cyan]片段大小:[/cyan] {segment_size:.2f} MB")
            
            # API转录
            rprint("[green]🚀 调用WhisperX API...[/green]")
            input_params = {
                "debug": False,
                "vad_onset": 0.5,
                "audio_file": f"data:audio/wav;base64,{audio_base64}",
                "batch_size": 64,
                "vad_offset": 0.363,
                "diarization": False,
                "temperature": 0,
                "align_output": True,
                "language_detection_min_prob": 0,
                "language_detection_max_tries": 5
            }
            
            if language and language != 'auto':
                input_params["language"] = language
            
            result = replicate.run(
                "victor-upmeet/whisperx:84d2ad2d6194fe98a17d2b60bef1c7f910c46b2f6fd38996ca457afd9c8abfcb",
                input=input_params
            )
            
            # 删除临时文件
            os.remove(segment_file)
            
            # 调整时间戳
            for segment in result['segments']:
                segment['start'] += start
                segment['end'] += start
                for word in segment.get('words', []):
                    if 'start' in word:
                        word['start'] += start
                    if 'end' in word:
                        word['end'] += start
            
            return result
            
        except Exception as e:
            if os.path.exists(segment_file):
                os.remove(segment_file)
            rprint(f"[red]API转录失败:[/red] {e}")
            raise

    def extract_subtitle(self,
                        audio_path: str = None,
                        video_path: str = None,
                        model_name: str = "large-v3",
                        language: str = "auto",
                        enable_uvr5: bool = True,
                        target_duration: int = 20*60,
                        progress_callback: Callable = None) -> Dict[str, Any]:
        """
        从音频或视频提取字幕 - 完整的经过验证的流程
        
        Args:
            audio_path: 音频文件路径
            video_path: 视频文件路径（如果提供，会先转换为音频）
            model_name: 使用的模型名称
            language: 目标语言（auto, zh, en等）
            enable_uvr5: 是否启用UVR5人声分离
            target_duration: 音频分割目标时长（秒）
            progress_callback: 进度回调函数
            
        Returns:
            Dict: 提取结果
        """
        try:
            start_time = time.time()
            
            if progress_callback:
                progress_callback("开始字幕提取...", 5)
            
            # Step 1: 处理输入文件
            if video_path:
                if progress_callback:
                    progress_callback("正在转换视频为音频...", 10)
                audio_path = self.convert_video_to_audio(video_path)
            elif not audio_path:
                raise ValueError("必须提供audio_path或video_path")
            
            # Step 2: UVR5人声分离（可选）
            if enable_uvr5:
                if progress_callback:
                    progress_callback("正在进行人声分离...", 20)
                
                vocal_path = 'output/audio/original_vocal.wav'
                if not os.path.exists(vocal_path):
                    try:
                        # 这里需要UVR5模块，如果没有则跳过
                        rprint("[yellow]⚠️ UVR5模块未集成，跳过人声分离[/yellow]")
                    except Exception as e:
                        rprint(f"[yellow]⚠️ UVR5处理失败，使用原始音频: {e}[/yellow]")
                else:
                    audio_path = vocal_path
                    rprint("[green]✅ 使用UVR5分离后的人声[/green]")
            
            # Step 3: 智能音频分割
            if progress_callback:
                progress_callback("正在分割音频...", 30)
            segments = self.split_audio_intelligent(audio_path, target_duration)
            
            # Step 4: 逐段转录
            all_results = []
            total_segments = len(segments)
            
            for i, (start, end) in enumerate(segments):
                if progress_callback:
                    progress = 40 + (i / total_segments) * 50
                    progress_callback(f"转录进度: {i+1}/{total_segments}", progress)
                
                try:
                    if self.use_api:
                        result = self.transcribe_segment_api(audio_path, start, end, language)
                    else:
                        result = self.transcribe_segment_local(audio_path, start, end, model_name, language)
                    
                    all_results.append(result)
                    
                except Exception as e:
                    rprint(f"[red]片段 {i+1} 转录失败: {e}[/red]")
                    continue
            
            # Step 5: 合并结果
            if progress_callback:
                progress_callback("正在合并转录结果...", 90)
            
            combined_segments = []
            detected_language = 'unknown'
            
            for result in all_results:
                if 'language' in result:
                    detected_language = result['language']
                combined_segments.extend(result.get('segments', []))
            
            # Step 6: 生成最终结果
            if progress_callback:
                progress_callback("字幕提取完成", 100)
            
            end_time = time.time()
            processing_time = end_time - start_time
            
            rprint(f"[green]🎉 字幕提取完成！总耗时: {processing_time:.2f}秒[/green]")
            
            return {
                "segments": combined_segments,
                "language": detected_language,
                "model_used": model_name,
                "processing_time": processing_time,
                "segments_count": len(combined_segments),
                "use_api": self.use_api
            }
            
        except Exception as e:
            error_msg = f"字幕提取失败: {e}"
            rprint(f"[red]{error_msg}[/red]")
            raise Exception(error_msg)
    
    def process_transcription_result(self, result: Dict) -> pd.DataFrame:
        """
        处理转录结果为DataFrame格式 - 经过验证的处理方法
        
        Args:
            result: 转录结果
            
        Returns:
            pd.DataFrame: 处理后的结果
        """
        all_words = []
        
        for segment in result.get('segments', []):
            for word in segment.get('words', []):
                # 处理特殊字符（法语等）
                word_text = word.get("word", "").replace('»', '').replace('«', '')
                
                if 'start' not in word and 'end' not in word:
                    if all_words:
                        # 使用前一个词的结束时间
                        word_dict = {
                            'text': word_text,
                            'start': all_words[-1]['end'],
                            'end': all_words[-1]['end'],
                        }
                        all_words.append(word_dict)
                    else:
                        # 查找下一个有时间戳的词
                        next_word = next(
                            (w for w in segment['words'] if 'start' in w and 'end' in w), 
                            None
                        )
                        if next_word:
                            word_dict = {
                                'text': word_text,
                                'start': next_word["start"],
                                'end': next_word["end"],
                            }
                            all_words.append(word_dict)
                        else:
                            rprint(f"[yellow]⚠️ 无法为词语找到时间戳: {word_text}[/yellow]")
                            continue
                else:
                    # 正常情况，有开始和结束时间
                    word_dict = {
                        'text': word_text,
                        'start': word.get('start', all_words[-1]['end'] if all_words else 0),
                        'end': word['end'],
                    }
                    all_words.append(word_dict)
        
        return pd.DataFrame(all_words)
    
    def save_results(self, df: pd.DataFrame, output_dir: str = 'output/log'):
        """
        保存转录结果 - 兼容VideoLingo格式
        
        Args:
            df: 转录结果DataFrame
            output_dir: 输出目录
        """
        os.makedirs(output_dir, exist_ok=True)
        excel_path = os.path.join(output_dir, "cleaned_chunks.xlsx")
        
        # 格式化文本
        df['text'] = df['text'].apply(lambda x: f'"{x}"')
        df.to_excel(excel_path, index=False)
        
        rprint(f"[green]📊 结果已保存到: {excel_path}[/green]")
        return excel_path
    
    def save_language(self, language: str, output_dir: str = 'output/log'):
        """
        保存检测到的语言信息
        
        Args:
            language: 检测到的语言
            output_dir: 输出目录
        """
        os.makedirs(output_dir, exist_ok=True)
        language_file = os.path.join(output_dir, 'transcript_language.json')
        with open(language_file, 'w', encoding='utf-8') as f:
            json.dump({"language": language}, f, ensure_ascii=False, indent=4)
        return language_file

    def generate_srt(self, segments: List[Dict], output_path: str = None) -> str:
        """
        生成SRT格式字幕
        
        Args:
            segments: 字幕段落列表
            output_path: 输出文件路径
            
        Returns:
            str: SRT格式字幕内容
        """
        try:
            import srt
            from datetime import timedelta
            
            srt_subtitles = []
            
            for i, segment in enumerate(segments, 1):
                start_time = timedelta(seconds=segment.get("start", 0))
                end_time = timedelta(seconds=segment.get("end", 0))
                text = segment.get("text", "").strip()
                
                # 添加说话人信息（如果有）
                if "speaker" in segment:
                    text = f"[{segment['speaker']}] {text}"
                
                subtitle = srt.Subtitle(
                    index=i,
                    start=start_time,
                    end=end_time,
                    content=text
                )
                srt_subtitles.append(subtitle)
            
            # 生成SRT内容
            srt_content = srt.compose(srt_subtitles)
            
            # 保存文件
            if output_path:
                with open(output_path, 'w', encoding='utf-8') as f:
                    f.write(srt_content)
                print(f"SRT字幕已保存: {output_path}")
            
            return srt_content
            
        except Exception as e:
            print(f"生成SRT字幕失败: {e}")
            return ""
    
    def generate_vtt(self, segments: List[Dict], output_path: str = None) -> str:
        """
        生成VTT格式字幕
        
        Args:
            segments: 字幕段落列表
            output_path: 输出文件路径
            
        Returns:
            str: VTT格式字幕内容
        """
        try:
            def format_time(seconds):
                """格式化时间为VTT格式"""
                hours = int(seconds // 3600)
                minutes = int((seconds % 3600) // 60)
                secs = seconds % 60
                return f"{hours:02d}:{minutes:02d}:{secs:06.3f}"
            
            vtt_lines = ["WEBVTT", ""]
            
            for segment in segments:
                start_time = format_time(segment.get("start", 0))
                end_time = format_time(segment.get("end", 0))
                text = segment.get("text", "").strip()
                
                # 添加说话人信息（如果有）
                if "speaker" in segment:
                    text = f"<v {segment['speaker']}>{text}"
                
                vtt_lines.append(f"{start_time} --> {end_time}")
                vtt_lines.append(text)
                vtt_lines.append("")
            
            vtt_content = "\n".join(vtt_lines)
            
            # 保存文件
            if output_path:
                with open(output_path, 'w', encoding='utf-8') as f:
                    f.write(vtt_content)
                print(f"VTT字幕已保存: {output_path}")
            
            return vtt_content
            
        except Exception as e:
            print(f"生成VTT字幕失败: {e}")
            return ""
    
    def extract_audio_segments(self,
                             segments: List[Dict],
                             audio_path: str,
                             output_dir: str) -> List[str]:
        """
        根据字幕段落提取音频片段
        
        Args:
            segments: 字幕段落列表
            audio_path: 原始音频文件路径
            output_dir: 输出目录
            
        Returns:
            List[str]: 输出的音频文件路径列表
        """
        try:
            import soundfile as sf
            
            # 读取原始音频
            audio_data, sample_rate = sf.read(audio_path)
            
            output_files = []
            output_dir = Path(output_dir)
            output_dir.mkdir(parents=True, exist_ok=True)
            
            base_name = Path(audio_path).stem
            
            for i, segment in enumerate(segments):
                start_time = segment.get("start", 0)
                end_time = segment.get("end", 0)
                text = segment.get("text", "").strip()
                
                # 计算样本索引
                start_sample = int(start_time * sample_rate)
                end_sample = int(end_time * sample_rate)
                
                # 提取音频片段
                segment_audio = audio_data[start_sample:end_sample]
                
                # 生成输出文件名
                safe_text = "".join(c for c in text[:20] if c.isalnum() or c in (' ', '-', '_')).strip()
                output_file = output_dir / f"{base_name}_segment_{i+1:03d}_{safe_text}.wav"
                
                # 保存音频片段
                sf.write(str(output_file), segment_audio, sample_rate)
                output_files.append(str(output_file))
            
            print(f"提取了 {len(output_files)} 个音频片段")
            return output_files
            
        except Exception as e:
            print(f"提取音频片段失败: {e}")
            return []
    
    def get_model_info(self) -> Dict[str, Any]:
        """获取当前模型信息"""
        if not self.current_model:
            return {
                "loaded": False,
                "model_name": None,
                "device": self.device
            }
        
        return {
            "loaded": True,
            "model_name": self.model_name,
            "device": self.device,
            "compute_type": self.compute_type
        }
    
    def get_supported_languages(self) -> Dict[str, str]:
        """获取支持的语言列表"""
        return {
            "auto": "自动检测",
            "zh": "中文",
            "en": "English", 
            "ja": "日本語",
            "ko": "한국어",
            "es": "Español",
            "fr": "Français",
            "de": "Deutsch",
            "it": "Italiano",
            "pt": "Português",
            "ru": "Русский",
            "ar": "العربية",
            "hi": "हिन्दी",
            "th": "ไทย",
            "vi": "Tiếng Việt",
            "nl": "Nederlands",
            "tr": "Türkçe",
            "pl": "Polski",
            "sv": "Svenska",
            "da": "Dansk",
            "no": "Norsk",
            "fi": "Suomi"
        }
    
    def cleanup(self):
        """清理资源"""
        try:
            if self.current_model:
                del self.current_model
                self.current_model = None
                self.model_name = None
            
            # 清理GPU缓存
            if self.device == "cuda":
                import torch
                torch.cuda.empty_cache()
                
        except Exception as e:
            print(f"清理资源时出错: {e}")
    
    def __del__(self):
        """析构函数"""
        self.cleanup()

    def change_device(self, device: str) -> bool:
        """
        动态切换计算设备
        
        Args:
            device: 新的设备类型 ("auto", "cuda", "cpu")
            
        Returns:
            bool: 切换是否成功
        """
        try:
            old_device = self.device
            new_device = self._determine_device(device)
            
            if old_device == new_device:
                rprint(f"[yellow]设备已经是 {new_device}，无需切换[/yellow]")
                return True
            
            rprint(f"[cyan]正在从 {old_device} 切换到 {new_device}...[/cyan]")
            
            # 清理当前模型
            if self.current_model:
                del self.current_model
                self.current_model = None
                self.model_name = None
            
            # 更新设备配置
            self.device = new_device
            
            # 重新配置参数
            if self.device == "cuda":
                try:
                    import torch
                    gpu_mem = torch.cuda.get_device_properties(0).total_memory / (1024**3)
                    self.batch_size = 4 if gpu_mem <= 8 else 16
                    self.compute_type = "float16"
                    rprint(f"[cyan]GPU内存:[/cyan] {gpu_mem:.2f} GB, [cyan]批处理大小:[/cyan] {self.batch_size}")
                except ImportError:
                    self.batch_size = 4
                    self.compute_type = "int8"
            else:
                self.batch_size = 4
                self.compute_type = "int8"
            
            rprint(f"[green]设备切换成功: {old_device} → {new_device}[/green]")
            return True
            
        except Exception as e:
            rprint(f"[red]设备切换失败: {e}[/red]")
            return False 