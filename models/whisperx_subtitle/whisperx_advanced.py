#!/usr/bin/env python3
"""
高级 WhisperX 语音识别实现
基于 VideoLingo 项目中经过验证的可靠方法
支持本地模型和API两种模式
"""

import os
import sys
import json
import time
import base64
import subprocess
from typing import Dict, List, Tuple, Optional, Callable, Any
from pathlib import Path
import pandas as pd
import torch
import librosa
import soundfile as sf
from moviepy.editor import AudioFileClip
from rich import print as rprint
from rich.progress import Progress, SpinnerColumn, TextColumn, TimeElapsedColumn


class WhisperXAdvanced:
    """高级 WhisperX 语音识别器"""
    
    def __init__(self, models_dir: str = None, use_api: bool = False, api_token: str = None):
        """
        初始化高级语音识别器
        
        Args:
            models_dir: 模型目录路径
            use_api: 是否使用API模式
            api_token: API令牌（API模式必需）
        """
        self.models_dir = Path(models_dir) if models_dir else Path("models/whisperx_subtitle/weights")
        self.use_api = use_api
        self.api_token = api_token
        
        # 设备配置
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        if self.device == "cuda":
            gpu_mem = torch.cuda.get_device_properties(0).total_memory / (1024**3)
            self.batch_size = 4 if gpu_mem <= 8 else 16
            self.compute_type = "float16"
            rprint(f"[cyan]GPU内存:[/cyan] {gpu_mem:.2f} GB, [cyan]批处理大小:[/cyan] {self.batch_size}")
        else:
            self.batch_size = 4
            self.compute_type = "int8"
        
        rprint(f"[green]WhisperX高级识别器初始化完成[/green] - 设备: {self.device}, 模式: {'API' if use_api else '本地'}")
        
        # API模式配置
        if self.use_api:
            if not api_token:
                raise ValueError("API模式需要提供api_token")
            os.environ["REPLICATE_API_TOKEN"] = api_token
    
    def convert_video_to_audio(self, input_file: str, output_dir: str = "output/audio") -> str:
        """
        视频转音频 - 使用librosa优化版本
        
        Args:
            input_file: 输入视频文件
            output_dir: 输出目录
            
        Returns:
            str: 音频文件路径
        """
        os.makedirs(output_dir, exist_ok=True)
        audio_file = os.path.join(output_dir, 'raw_full_audio.wav')

        if not os.path.exists(audio_file):
            try:
                rprint(f"[green]🎬➡️🎵 使用librosa转换音频...[/green]")
                # 使用librosa加载音频，采样率16kHz
                y, sr = librosa.load(input_file, sr=16000)
                
                # 保存为WAV文件
                sf.write(audio_file, y, sr, subtype='PCM_16')
                
                rprint(f"[green]🎬➡️🎵 转换完成: {input_file} → {audio_file}[/green]")
            except Exception as e:
                rprint(f"[red]❌ 音频转换失败: {e}[/red]")
                raise

        return audio_file
    
    def split_audio_intelligent(self, audio_file: str, target_duration: int = 20*60, window: int = 60) -> List[Tuple[float, float]]:
        """
        智能音频分割 - 基于静音检测
        
        Args:
            audio_file: 音频文件路径
            target_duration: 目标分割时长（秒）
            window: 检测窗口（秒）
            
        Returns:
            List[Tuple[float, float]]: 分割点列表 (开始时间, 结束时间)
        """
        rprint("[green]🔪 开始智能音频分割...[/green]")
        
        # 获取音频时长
        with AudioFileClip(audio_file) as audio:
            duration = audio.duration
        
        segments = []
        start = 0
        
        while start < duration:
            end = min(start + target_duration + window, duration)
            if end - start < target_duration:
                segments.append((start, end))
                break
            
            # 分析窗口内的静音
            window_start = start + target_duration - window
            window_end = min(window_start + 2 * window, duration)
            
            ffmpeg_cmd = [
                'ffmpeg',
                '-y',
                '-i', audio_file,
                '-ss', str(window_start),
                '-to', str(window_end),
                '-af', 'silencedetect=n=-30dB:d=0.5',
                '-f', 'null',
                '-'
            ]
            
            try:
                output = subprocess.run(ffmpeg_cmd, capture_output=True, text=True, timeout=60).stderr
                
                # 解析静音检测结果
                silence_end_times = [
                    float(line.split('silence_end: ')[1].split(' ')[0]) 
                    for line in output.split('\n') 
                    if 'silence_end' in line
                ]
                
                if silence_end_times:
                    # 找到目标时长后的第一个静音点
                    split_point = next((t for t in silence_end_times if t > target_duration), None)
                    if split_point:
                        segments.append((start, start + split_point))
                        start += split_point
                        continue
            except subprocess.TimeoutExpired:
                rprint(f"[yellow]⚠️ 静音检测超时，使用固定分割[/yellow]")
            
            # 如果没有找到合适的分割点，使用固定时长
            segments.append((start, start + target_duration))
            start += target_duration
        
        rprint(f"[green]🔪 音频分割完成，共 {len(segments)} 个片段[/green]")
        return segments
    
    def transcribe_segment_local(self, audio_file: str, start: float, end: float, 
                               model_name: str = "large-v3", language: str = None) -> Dict:
        """
        本地模型转录单个音频段
        
        Args:
            audio_file: 音频文件路径
            start: 开始时间
            end: 结束时间
            model_name: 模型名称
            language: 目标语言
            
        Returns:
            Dict: 转录结果
        """
        try:
            import whisperx
            
            rprint(f"[green]🎙️ 转录片段: {start:.2f}s - {end:.2f}s[/green]")
            
            # 中文特殊模型处理
            if language == 'zh':
                model_name = "BELLE-2/Belle-whisper-large-v3-zh-punct"
            
            rprint(f"[cyan]使用模型:[/cyan] {model_name}")
            
            # 加载模型
            try:
                model = whisperx.load_model(
                    model_name, 
                    self.device, 
                    compute_type=self.compute_type,
                    download_root=str(self.models_dir)
                )
            except Exception as e:
                rprint(f"[red]模型加载失败:[/red] {e}")
                raise
            
            # 加载音频片段
            audio = whisperx.load_audio(audio_file)
            audio_segment = audio[int(start * 16000):int(end * 16000)]
            
            # 执行转录
            with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                TimeElapsedColumn(),
                transient=True
            ) as progress:
                task = progress.add_task("[cyan]转录中...", total=None)
                result = model.transcribe(
                    audio_segment, 
                    batch_size=self.batch_size,
                    language=(None if language == 'auto' else language)
                )
                progress.update(task, completed=True)
            
            # 释放GPU内存
            del model
            torch.cuda.empty_cache()
            
            # 语言检查
            detected_language = result.get('language', 'unknown')
            if detected_language == 'zh' and language != 'zh':
                rprint("[yellow]⚠️ 检测到中文，建议使用中文专用模型[/yellow]")
            
            # 对齐时间戳
            try:
                model_a, metadata = whisperx.load_align_model(
                    language_code=detected_language, 
                    device=self.device
                )
                result = whisperx.align(
                    result["segments"], 
                    model_a, 
                    metadata, 
                    audio_segment, 
                    self.device,
                    return_char_alignments=False
                )
                del model_a
                torch.cuda.empty_cache()
            except Exception as e:
                rprint(f"[yellow]⚠️ 时间戳对齐失败: {e}[/yellow]")
            
            # 调整时间戳
            for segment in result['segments']:
                segment['start'] += start
                segment['end'] += start
                for word in segment.get('words', []):
                    if 'start' in word:
                        word['start'] += start
                    if 'end' in word:
                        word['end'] += start
            
            return result
            
        except Exception as e:
            rprint(f"[red]转录失败:[/red] {e}")
            raise
    
    def transcribe_segment_api(self, audio_file: str, start: float, end: float, 
                             language: str = None) -> Dict:
        """
        API模式转录单个音频段
        
        Args:
            audio_file: 音频文件路径
            start: 开始时间
            end: 结束时间
            language: 目标语言
            
        Returns:
            Dict: 转录结果
        """
        if language == 'zh':
            raise Exception("WhisperX API 不支持中文，请使用本地模式进行中文转录")
        
        try:
            import replicate
            
            rprint(f"[green]🎙️ API转录片段: {start:.2f}s - {end:.2f}s[/green]")
            
            # 创建音频片段
            segment_file = f'temp_segment_{start:.2f}_{end:.2f}.wav'
            ffmpeg_cmd = [
                'ffmpeg', '-y', '-i', audio_file,
                '-ss', str(start), '-to', str(end),
                '-c', 'copy', segment_file
            ]
            
            try:
                subprocess.run(ffmpeg_cmd, check=True, stderr=subprocess.PIPE, timeout=300)
            except subprocess.TimeoutExpired:
                rprint("[yellow]⚠️ ffmpeg超时，重试...[/yellow]")
                ffmpeg_cmd[6] = 'pcm_s16le'
                subprocess.run(ffmpeg_cmd, check=True, stderr=subprocess.PIPE)
            
            time.sleep(1)  # 等待文件写入完成
            
            # 编码为base64
            with open(segment_file, 'rb') as file:
                audio_base64 = base64.b64encode(file.read()).decode('utf-8')
            
            segment_size = len(audio_base64) / (1024 * 1024)
            rprint(f"[cyan]片段大小:[/cyan] {segment_size:.2f} MB")
            
            # API转录
            rprint("[green]🚀 调用WhisperX API...[/green]")
            input_params = {
                "debug": False,
                "vad_onset": 0.5,
                "audio_file": f"data:audio/wav;base64,{audio_base64}",
                "batch_size": 64,
                "vad_offset": 0.363,
                "diarization": False,
                "temperature": 0,
                "align_output": True,
                "language_detection_min_prob": 0,
                "language_detection_max_tries": 5
            }
            
            if language and language != 'auto':
                input_params["language"] = language
            
            result = replicate.run(
                "victor-upmeet/whisperx:84d2ad2d6194fe98a17d2b60bef1c7f910c46b2f6fd38996ca457afd9c8abfcb",
                input=input_params
            )
            
            # 删除临时文件
            os.remove(segment_file)
            
            # 调整时间戳
            for segment in result['segments']:
                segment['start'] += start
                segment['end'] += start
                for word in segment.get('words', []):
                    if 'start' in word:
                        word['start'] += start
                    if 'end' in word:
                        word['end'] += start
            
            return result
            
        except Exception as e:
            if os.path.exists(segment_file):
                os.remove(segment_file)
            rprint(f"[red]API转录失败:[/red] {e}")
            raise
    
    def process_transcription_result(self, result: Dict) -> pd.DataFrame:
        """
        处理转录结果为DataFrame格式
        
        Args:
            result: 转录结果
            
        Returns:
            pd.DataFrame: 处理后的结果
        """
        all_words = []
        
        for segment in result['segments']:
            for word in segment.get('words', []):
                # 处理特殊字符（法语等）
                word_text = word.get("word", "").replace('»', '').replace('«', '')
                
                if 'start' not in word and 'end' not in word:
                    if all_words:
                        # 使用前一个词的结束时间
                        word_dict = {
                            'text': word_text,
                            'start': all_words[-1]['end'],
                            'end': all_words[-1]['end'],
                        }
                        all_words.append(word_dict)
                    else:
                        # 查找下一个有时间戳的词
                        next_word = next(
                            (w for w in segment['words'] if 'start' in w and 'end' in w), 
                            None
                        )
                        if next_word:
                            word_dict = {
                                'text': word_text,
                                'start': next_word["start"],
                                'end': next_word["end"],
                            }
                            all_words.append(word_dict)
                        else:
                            raise Exception(f"无法为词语找到时间戳: {word_text}")
                else:
                    # 正常情况，有开始和结束时间
                    word_dict = {
                        'text': word_text,
                        'start': word.get('start', all_words[-1]['end'] if all_words else 0),
                        'end': word['end'],
                    }
                    all_words.append(word_dict)
        
        return pd.DataFrame(all_words)
    
    def save_results(self, df: pd.DataFrame, output_dir: str = 'output/log'):
        """
        保存转录结果
        
        Args:
            df: 转录结果DataFrame
            output_dir: 输出目录
        """
        os.makedirs(output_dir, exist_ok=True)
        excel_path = os.path.join(output_dir, "cleaned_chunks.xlsx")
        
        # 格式化文本
        df['text'] = df['text'].apply(lambda x: f'"{x}"')
        df.to_excel(excel_path, index=False)
        
        rprint(f"[green]📊 结果已保存到: {excel_path}[/green]")
    
    def save_language(self, language: str, output_dir: str = 'output/log'):
        """
        保存检测到的语言信息
        
        Args:
            language: 检测到的语言
            output_dir: 输出目录
        """
        os.makedirs(output_dir, exist_ok=True)
        with open(os.path.join(output_dir, 'transcript_language.json'), 'w', encoding='utf-8') as f:
            json.dump({"language": language}, f, ensure_ascii=False, indent=4)
    
    def transcribe_complete(self, video_file: str, model_name: str = "large-v3", 
                          language: str = "auto", enable_uvr5: bool = True,
                          progress_callback: Callable = None) -> Dict[str, Any]:
        """
        完整的视频转录流程
        
        Args:
            video_file: 输入视频文件
            model_name: 使用的模型名称
            language: 目标语言
            enable_uvr5: 是否启用UVR5人声分离
            progress_callback: 进度回调函数
            
        Returns:
            Dict: 转录结果和统计信息
        """
        start_time = time.time()
        
        if progress_callback:
            progress_callback("开始转录流程...", 5)
        
        # 检查是否已有结果
        if os.path.exists("output/log/cleaned_chunks.xlsx"):
            rprint("[yellow]📊 发现已有转录结果，跳过转录步骤[/yellow]")
            return {"status": "skipped", "message": "已存在转录结果"}
        
        # Step 1: 视频转音频
        if progress_callback:
            progress_callback("正在转换视频为音频...", 10)
        audio_file = self.convert_video_to_audio(video_file)
        
        # Step 2: UVR5人声分离（可选）
        if enable_uvr5:
            if progress_callback:
                progress_callback("正在进行人声分离...", 20)
            
            background_path = 'output/audio/background.wav'
            vocal_path = 'output/audio/original_vocal.wav'
            
            if not os.path.exists(background_path):
                try:
                    # 这里需要导入UVR5模块
                    from third_party.uvr5.uvr5_for_videolingo import uvr5_for_videolingo
                    uvr5_for_videolingo(
                        audio_file,
                        'output/audio',
                        background_path,
                        vocal_path
                    )
                    rprint("[green]✅ UVR5人声分离完成[/green]")
                    audio_file = vocal_path  # 使用分离后的人声
                except ImportError:
                    rprint("[yellow]⚠️ UVR5模块未找到，跳过人声分离[/yellow]")
                except Exception as e:
                    rprint(f"[yellow]⚠️ UVR5处理失败，使用原始音频: {e}[/yellow]")
            else:
                rprint("[green]✅ 发现已有人声分离结果[/green]")
                audio_file = vocal_path
        
        # Step 3: 音频分割
        if progress_callback:
            progress_callback("正在分割音频...", 30)
        segments = self.split_audio_intelligent(audio_file)
        
        # Step 4: 逐段转录
        all_results = []
        total_segments = len(segments)
        
        for i, (start, end) in enumerate(segments):
            if progress_callback:
                progress = 40 + (i / total_segments) * 50
                progress_callback(f"转录进度: {i+1}/{total_segments}", progress)
            
            try:
                if self.use_api:
                    result = self.transcribe_segment_api(audio_file, start, end, language)
                else:
                    result = self.transcribe_segment_local(audio_file, start, end, model_name, language)
                
                result['time_offset'] = start
                all_results.append(result)
                
            except Exception as e:
                rprint(f"[red]片段 {i+1} 转录失败: {e}[/red]")
                # 可以选择跳过失败的片段或重试
                continue
        
        # Step 5: 合并结果
        if progress_callback:
            progress_callback("正在合并转录结果...", 90)
        
        combined_result = {
            'segments': [],
            'detected_language': all_results[0].get('language', 'unknown') if all_results else 'unknown'
        }
        
        for result in all_results:
            combined_result['segments'].extend(result.get('segments', []))
        
        # Step 6: 保存结果
        if progress_callback:
            progress_callback("正在保存结果...", 95)
        
        self.save_language(combined_result['detected_language'])
        df = self.process_transcription_result(combined_result)
        self.save_results(df)
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        if progress_callback:
            progress_callback("转录完成！", 100)
        
        rprint(f"[green]🎉 转录完成！总耗时: {processing_time:.2f}秒[/green]")
        
        return {
            "status": "success",
            "segments_count": len(combined_result['segments']),
            "detected_language": combined_result['detected_language'],
            "processing_time": processing_time,
            "output_files": {
                "excel": "output/log/cleaned_chunks.xlsx",
                "language": "output/log/transcript_language.json"
            }
        }


def main():
    """命令行测试入口"""
    import argparse
    
    parser = argparse.ArgumentParser(description="高级WhisperX语音识别")
    parser.add_argument("video_file", help="输入视频文件")
    parser.add_argument("--model", default="large-v3", help="模型名称")
    parser.add_argument("--language", default="auto", help="目标语言")
    parser.add_argument("--api", action="store_true", help="使用API模式")
    parser.add_argument("--api-token", help="API令牌")
    parser.add_argument("--no-uvr5", action="store_true", help="禁用UVR5人声分离")
    
    args = parser.parse_args()
    
    # 初始化识别器
    extractor = WhisperXAdvanced(
        use_api=args.api,
        api_token=args.api_token
    )
    
    # 执行转录
    result = extractor.transcribe_complete(
        video_file=args.video_file,
        model_name=args.model,
        language=args.language,
        enable_uvr5=not args.no_uvr5
    )
    
    rprint(f"[green]转录完成:[/green] {result}")


if __name__ == "__main__":
    main() 