#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WhisperX 字幕提取使用示例
演示如何使用WhisperX进行音频转字幕
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到路径
current_dir = Path(__file__).parent
project_root = current_dir.parent.parent
sys.path.append(str(project_root))

from models.whisperx_subtitle import WhisperXExtractor, WhisperXModelManager


def main():
    """主函数 - 演示WhisperX的基本使用"""
    
    print("=== WhisperX 字幕提取示例 ===\n")
    
    # 初始化模型管理器
    model_manager = WhisperXModelManager()
    
    print("1. 可用模型列表:")
    models = model_manager.get_available_models()
    for name, info in models.items():
        status = "已下载" if model_manager.is_model_downloaded(name) else "未下载"
        print(f"   {name}: {info['size']} - {info['description']} ({status})")
    
    print("\n2. 推荐模型:")
    print(f"   中文: {model_manager.get_recommended_model('zh')}")
    print(f"   英文: {model_manager.get_recommended_model('en')}")
    print(f"   自动: {model_manager.get_recommended_model('auto')}")
    
    # 初始化提取器
    print("\n3. 初始化提取器...")
    extractor = WhisperXExtractor(device="auto")
    
    print(f"   设备: {extractor.device}")
    print(f"   计算类型: {extractor.compute_type}")
    
    # 检查音频文件路径
    audio_file = input("\n4. 请输入音频文件路径 (按回车跳过): ").strip()
    
    if not audio_file:
        print("   未提供音频文件，演示结束。")
        return
    
    if not os.path.exists(audio_file):
        print(f"   错误: 音频文件不存在 - {audio_file}")
        return
    
    # 选择模型
    print("\n5. 选择模型 (默认: small):")
    model_name = input("   输入模型名称: ").strip() or "small"
    
    if model_name not in models:
        print(f"   警告: 未知模型 {model_name}，使用默认模型 small")
        model_name = "small"
    
    # 检查模型是否已下载
    if not model_manager.is_model_downloaded(model_name):
        print(f"\n   模型 {model_name} 未下载，正在下载...")
        success = model_manager.download_model(model_name)
        if not success:
            print(f"   错误: 模型下载失败")
            return
    
    # 设置输出目录
    output_dir = Path(audio_file).parent / "subtitles"
    output_dir.mkdir(exist_ok=True)
    
    print(f"\n6. 开始提取字幕...")
    print(f"   音频文件: {audio_file}")
    print(f"   使用模型: {model_name}")
    print(f"   输出目录: {output_dir}")
    
    try:
        # 进度回调函数
        def progress_callback(message, percentage):
            print(f"   进度: {percentage:3d}% - {message}")
        
        # 执行字幕提取
        result = extractor.extract_subtitle(
            audio_path=audio_file,
            model_name=model_name,
            language=None,  # 自动检测
            progress_callback=progress_callback
        )
        
        print(f"\n7. 提取完成!")
        print(f"   检测语言: {result['language']}")
        print(f"   字幕段落: {len(result['segments'])} 个")
        
        # 生成字幕文件
        base_name = Path(audio_file).stem
        
        # SRT格式
        srt_file = output_dir / f"{base_name}.srt"
        srt_content = extractor.generate_srt(result['segments'], str(srt_file))
        print(f"   SRT文件: {srt_file}")
        
        # VTT格式
        vtt_file = output_dir / f"{base_name}.vtt"
        vtt_content = extractor.generate_vtt(result['segments'], str(vtt_file))
        print(f"   VTT文件: {vtt_file}")
        
        # 显示前几个字幕段落
        print(f"\n8. 字幕预览 (前5个段落):")
        for i, segment in enumerate(result['segments'][:5]):
            start = segment.get('start', 0)
            end = segment.get('end', 0)
            text = segment.get('text', '').strip()
            print(f"   {i+1:2d}. [{start:6.2f}s - {end:6.2f}s] {text}")
        
        if len(result['segments']) > 5:
            print(f"   ... 还有 {len(result['segments']) - 5} 个段落")
        
        print(f"\n✅ 字幕提取成功完成！")
        
    except Exception as e:
        print(f"\n❌ 字幕提取失败: {e}")
    
    finally:
        # 清理资源
        del extractor


def test_model_management():
    """测试模型管理功能"""
    print("=== 模型管理测试 ===\n")
    
    model_manager = WhisperXModelManager()
    
    # 测试小模型下载
    test_model = "tiny"
    
    print(f"测试模型: {test_model}")
    
    # 检查是否已下载
    if model_manager.is_model_downloaded(test_model):
        print(f"模型 {test_model} 已下载")
    else:
        print(f"模型 {test_model} 未下载，开始下载...")
        success = model_manager.download_model(test_model)
        
        if success:
            print(f"模型 {test_model} 下载成功！")
        else:
            print(f"模型 {test_model} 下载失败")
    
    # 显示已下载的模型
    downloaded = model_manager.get_downloaded_models()
    print(f"\n已下载的模型: {downloaded}")


def test_audio_validation():
    """测试音频文件验证功能"""
    print("=== 音频文件验证测试 ===\n")
    
    # 假设项目根目录下有一个测试音频文件
    test_files = [
        "test.wav",
        "test.mp3", 
        "nonexistent.wav"
    ]
    
    # 初始化插件
    sys.path.append(str(project_root / "plugins"))
    from subtitle_extractor import WhisperXSubtitleExtractor
    
    plugin = WhisperXSubtitleExtractor()
    plugin.initialize({})
    
    for test_file in test_files:
        print(f"验证文件: {test_file}")
        result = plugin.validate_audio_file(test_file)
        
        if result["valid"]:
            print(f"  ✅ 文件有效")
            print(f"     时长: {result.get('duration', 0):.2f}s")
            print(f"     采样率: {result.get('sample_rate', 0)}Hz")
            print(f"     声道数: {result.get('channels', 0)}")
        else:
            print(f"  ❌ 文件无效: {result['error']}")
        print()


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="WhisperX 字幕提取示例")
    parser.add_argument("--test", choices=["models", "validation"], 
                       help="运行特定测试")
    parser.add_argument("--audio", help="指定音频文件路径")
    
    args = parser.parse_args()
    
    if args.test == "models":
        test_model_management()
    elif args.test == "validation":
        test_audio_validation()
    else:
        main() 