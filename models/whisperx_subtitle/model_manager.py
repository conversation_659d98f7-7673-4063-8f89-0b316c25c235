"""
WhisperX 模型管理器
负责下载、缓存和管理WhisperX预训练模型
"""

import os
import json
from typing import Dict, List, Optional
from pathlib import Path


class WhisperXModelManager:
    """WhisperX模型管理器"""
    
    # 可用的模型配置
    AVAILABLE_MODELS = {
        "tiny": {
            "size": "39MB",
            "multilingual": False,
            "languages": ["en"],
            "description": "最小模型，英语专用，速度最快"
        },
        "tiny.en": {
            "size": "39MB", 
            "multilingual": False,
            "languages": ["en"],
            "description": "英语专用小模型"
        },
        "base": {
            "size": "74MB",
            "multilingual": False,
            "languages": ["en"],
            "description": "基础模型，英语专用"
        },
        "base.en": {
            "size": "74MB",
            "multilingual": False,
            "languages": ["en"],
            "description": "英语专用基础模型"
        },
        "small": {
            "size": "244MB",
            "multilingual": True,
            "languages": ["多语言"],
            "description": "小型多语言模型，支持中英文"
        },
        "small.en": {
            "size": "244MB",
            "multilingual": False,
            "languages": ["en"],
            "description": "英语专用小型模型"
        },
        "medium": {
            "size": "769MB",
            "multilingual": True,
            "languages": ["多语言"],
            "description": "中型多语言模型，准确度较高"
        },
        "medium.en": {
            "size": "769MB",
            "multilingual": False,
            "languages": ["en"],
            "description": "英语专用中型模型"
        },
        "large": {
            "size": "1550MB",
            "multilingual": True,
            "languages": ["多语言"],
            "description": "大型多语言模型，最高准确度"
        },
        "large-v2": {
            "size": "1550MB",
            "multilingual": True,
            "languages": ["多语言"],
            "description": "大型多语言模型V2版本"
        },
        "large-v3": {
            "size": "1550MB",
            "multilingual": True,
            "languages": ["多语言"],
            "description": "大型多语言模型V3版本（推荐）"
        }
    }
    
    def __init__(self, models_dir: str = None):
        """
        初始化模型管理器
        
        Args:
            models_dir: 模型存储目录，默认为 models/whisperx_subtitle/weights
        """
        if models_dir is None:
            current_dir = Path(__file__).parent
            models_dir = current_dir / "weights"
        
        self.models_dir = Path(models_dir)
        self.models_dir.mkdir(parents=True, exist_ok=True)
        
        # 模型信息缓存文件
        self.cache_file = self.models_dir / "models_cache.json"
        self.load_cache()
    
    def load_cache(self):
        """加载模型缓存信息"""
        try:
            if self.cache_file.exists():
                with open(self.cache_file, 'r', encoding='utf-8') as f:
                    self.cache = json.load(f)
            else:
                self.cache = {}
        except Exception as e:
            print(f"加载模型缓存失败: {e}")
            self.cache = {}
    
    def save_cache(self):
        """保存模型缓存信息"""
        try:
            with open(self.cache_file, 'w', encoding='utf-8') as f:
                json.dump(self.cache, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"保存模型缓存失败: {e}")
    
    def get_available_models(self) -> Dict:
        """获取可用模型列表"""
        return self.AVAILABLE_MODELS.copy()
    
    def is_model_downloaded(self, model_name: str) -> bool:
        """检查模型是否已下载到本地weights目录"""
        if model_name not in self.AVAILABLE_MODELS:
            return False
        
        # 首先检查缓存记录
        if model_name in self.cache:
            cached_path = self.cache[model_name].get("path", "")
            if cached_path and Path(cached_path).exists():
                # 验证路径下确实有模型文件
                model_files = list(Path(cached_path).rglob("*"))
                if any(f.is_file() for f in model_files):
                    return True
                else:
                    # 缓存过期，清理无效记录
                    del self.cache[model_name]
                    self.save_cache()
        
        # 优先检查原始格式的模型目录（如 models--Systran--faster-whisper-tiny）
        for item in self.models_dir.iterdir():
            if item.is_dir() and f"faster-whisper-{model_name}" in item.name:
                model_files = list(item.rglob("*"))
                if any(f.is_file() and f.suffix in ['.bin', '.pt', '.json', '.txt'] for f in model_files):
                    # 找到原始格式的模型
                    file_count = len([f for f in model_files if f.is_file()])
                    self.cache[model_name] = {
                        "path": str(item),
                        "downloaded": True,
                        "detected_time": str(os.path.getctime(str(item))),
                        "file_count": file_count,
                        "storage_format": "original"
                    }
                    self.save_cache()
                    print(f"检测到原始格式模型: {model_name} 位于 {item}")
                    return True
        
        # 如果原始格式不存在，检查简化格式目录：weights/{model_name}/
        simple_model_dir = self.models_dir / model_name
        if simple_model_dir.exists() and simple_model_dir.is_dir():
            model_files = list(simple_model_dir.glob("*"))
            if any(f.is_file() and f.suffix in ['.bin', '.pt', '.json', '.txt'] for f in model_files):
                # 找到简化格式的模型
                self.cache[model_name] = {
                    "path": str(simple_model_dir),
                    "downloaded": True,
                    "detected_time": str(os.path.getctime(str(simple_model_dir))),
                    "storage_format": "simple"
                }
                self.save_cache()
                print(f"检测到简化格式模型: {model_name} 位于 {simple_model_dir}")
                return True
        
        # 最后检查其他可能的路径格式
        possible_paths = [
            # 单个模型文件
            self.models_dir / f"{model_name}.pt",
            self.models_dir / f"{model_name}.bin",
            # huggingface格式
            self.models_dir / model_name / "pytorch_model.bin",
            self.models_dir / model_name / "model.bin",
            # whisper原版格式
            self.models_dir / f"{model_name}.ggml"
        ]
        
        for path in possible_paths:
            if path.exists():
                if path.is_file():
                    # 单个模型文件
                    model_path = path.parent if path.name in [f"{model_name}.pt", f"{model_name}.bin", f"{model_name}.ggml"] else path
                else:
                    # 模型目录
                    model_files = list(path.rglob("*"))
                    if not any(f.is_file() for f in model_files):
                        continue
                    model_path = path
                
                # 更新缓存记录
                self.cache[model_name] = {
                    "path": str(model_path),
                    "downloaded": True,
                    "detected_time": str(os.path.getctime(str(model_path)))
                }
                self.save_cache()
                print(f"检测到本地模型: {model_name} 位于 {model_path}")
                return True
        
        # 检查是否有类似名称的目录（处理版本号等情况）
        for item in self.models_dir.iterdir():
            if item.is_dir() and model_name in item.name:
                model_files = list(item.rglob("*"))
                if any(f.is_file() for f in model_files):
                    self.cache[model_name] = {
                        "path": str(item),
                        "downloaded": True,
                        "detected_time": str(os.path.getctime(str(item)))
                    }
                    self.save_cache()
                    print(f"检测到本地模型: {model_name} 位于 {item}")
                    return True
        
        return False
    
    def get_model_path(self, model_name: str) -> Optional[str]:
        """获取模型路径"""
        if not self.is_model_downloaded(model_name):
            return None
        
        if model_name in self.cache:
            return self.cache[model_name].get("path")
        
        return None
    
    def download_model(self, model_name: str, progress_callback=None) -> bool:
        """
        下载指定模型到项目的weights目录
        
        Args:
            model_name: 模型名称
            progress_callback: 进度回调函数
            
        Returns:
            bool: 下载是否成功
        """
        if model_name not in self.AVAILABLE_MODELS:
            raise ValueError(f"不支持的模型: {model_name}")
        
        if self.is_model_downloaded(model_name):
            print(f"模型 {model_name} 已存在于本地")
            return True
        
        try:
            print(f"开始下载模型: {model_name} 到 {self.models_dir}")
            
            # 使用faster-whisper的内置下载功能
            try:
                from faster_whisper import WhisperModel
                import torch
                import shutil
                import tempfile
            except ImportError:
                print("错误：请安装 faster-whisper 和 torch")
                return False
            
            device = "cuda" if torch.cuda.is_available() else "cpu"
            compute_type = "float16" if device == "cuda" else "int8"
            
            print(f"正在下载模型到原始格式目录: {self.models_dir}")
            
            # 直接下载到weights目录，保持原始目录结构
            model = WhisperModel(
                model_name, 
                device=device,
                compute_type=compute_type,
                download_root=str(self.models_dir)  # 下载到weights目录，保持原始命名
            )
            
            # 查找实际的模型目录（faster-whisper会创建格式如 models--Systran--faster-whisper-tiny）
            model_dirs = []
            for item in self.models_dir.iterdir():
                if item.is_dir() and f"faster-whisper-{model_name}" in item.name:
                    model_dirs.append(item)
            
            if model_dirs:
                # 使用找到的模型目录（保持原始命名）
                actual_model_dir = model_dirs[0]  # 通常只有一个
                model_files = list(actual_model_dir.rglob("*"))
                file_count = len([f for f in model_files if f.is_file()])
                
                print(f"模型已下载到原始格式目录: {actual_model_dir}")
                print(f"包含 {file_count} 个文件")
                
                # 记录实际的模型路径（保持原始目录名）
                self.cache[model_name] = {
                    "path": str(actual_model_dir),
                    "downloaded": True,
                    "download_time": str(os.path.getctime(str(actual_model_dir))),
                    "file_count": file_count,
                    "storage_format": "original"  # 标记为原始格式
                }
                self.save_cache()
                print(f"模型 {model_name} 下载完成，保持原始目录格式: {actual_model_dir.name}")
                return True
            else:
                print(f"警告：未找到下载的模型目录")
                return False
            
        except Exception as e:
            print(f"下载模型 {model_name} 失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def remove_model(self, model_name: str) -> bool:
        """删除指定模型"""
        try:
            # 从缓存中删除
            if model_name in self.cache:
                del self.cache[model_name]
                self.save_cache()
            
            print(f"模型 {model_name} 已从缓存删除")
            return True
            
        except Exception as e:
            print(f"删除模型 {model_name} 失败: {e}")
            return False
    
    def get_downloaded_models(self) -> List[str]:
        """获取已下载的模型列表"""
        downloaded = []
        for model_name in self.AVAILABLE_MODELS:
            if self.is_model_downloaded(model_name):
                downloaded.append(model_name)
        return downloaded
    
    def get_model_info(self, model_name: str) -> Dict:
        """获取模型详细信息"""
        if model_name not in self.AVAILABLE_MODELS:
            return {}
        
        info = self.AVAILABLE_MODELS[model_name].copy()
        info["downloaded"] = self.is_model_downloaded(model_name)
        info["path"] = self.get_model_path(model_name)
        
        return info
    
    def get_recommended_model(self, language: str = "zh") -> str:
        """
        根据语言获取推荐模型
        
        Args:
            language: 目标语言 ("zh", "en", "auto")
            
        Returns:
            str: 推荐的模型名称
        """
        if language == "en":
            return "medium.en"  # 英语专用中型模型
        elif language == "zh":
            return "large-v3"   # 中文推荐大型模型
        else:
            return "medium"     # 通用推荐中型多语言模型
    
    def cleanup_cache(self):
        """清理无效的缓存条目"""
        to_remove = []
        for model_name, info in self.cache.items():
            if "path" in info:
                model_path = Path(info["path"])
                if not model_path.exists():
                    to_remove.append(model_name)
                elif model_path.is_dir():
                    # 检查目录是否包含实际的模型文件
                    model_files = list(model_path.rglob("*"))
                    if not any(f.is_file() for f in model_files):
                        to_remove.append(model_name)
        
        for model_name in to_remove:
            del self.cache[model_name]
            print(f"清理无效缓存: {model_name}")
        
        if to_remove:
            self.save_cache()
            print(f"清理了 {len(to_remove)} 个无效缓存条目")
        
        return len(to_remove)
    
    def load_model(self, model_name: str, device: str = None, compute_type: str = None, force_cpu: bool = False):
        """
        从统一目录结构加载模型
        
        Args:
            model_name: 模型名称
            device: 设备 ('cuda' 或 'cpu')
            compute_type: 计算类型 ('float16', 'int8' 等)
            
        Returns:
            WhisperModel: 加载的模型对象
        """
        if not self.is_model_downloaded(model_name):
            raise ValueError(f"模型 {model_name} 未下载，请先下载模型")
        
        # 获取统一格式的模型路径
        model_path = self.get_model_path(model_name)
        if not model_path:
            raise ValueError(f"无法找到模型 {model_name} 的路径")
        
        try:
            from faster_whisper import WhisperModel
            import torch
            
            # 设置默认参数
            if force_cpu:
                device = "cpu"
                compute_type = "int8"
                print("⚠️ 强制使用CPU模式")
            else:
                if device is None:
                    device = "cuda" if torch.cuda.is_available() else "cpu"
                if compute_type is None:
                    compute_type = "float16" if device == "cuda" else "int8"
            
            # 根据存储格式处理模型路径
            storage_format = self.cache[model_name].get('storage_format', 'unknown')
            print(f"从{storage_format}格式目录加载模型: {model_name}")
            print(f"模型路径: {model_path}")
            print(f"设备: {device}, 计算类型: {compute_type}")
            
            # 获取实际的模型文件路径
            actual_model_path = model_path
            
            if storage_format == "original":
                # 原始 HuggingFace 格式：需要查找 snapshots 目录下的实际模型文件
                model_path_obj = Path(model_path)
                snapshots_dir = model_path_obj / "snapshots"
                
                if snapshots_dir.exists():
                    # 查找第一个 commit hash 目录
                    commit_dirs = [d for d in snapshots_dir.iterdir() if d.is_dir()]
                    if commit_dirs:
                        actual_model_path = str(commit_dirs[0])  # 使用第一个 commit
                        print(f"使用实际模型路径: {actual_model_path}")
                    else:
                        raise FileNotFoundError(f"在 {snapshots_dir} 中未找到模型文件")
                else:
                    raise FileNotFoundError(f"未找到 snapshots 目录: {snapshots_dir}")
            
            # 从本地路径加载模型
            model = WhisperModel(
                actual_model_path,  # 使用实际模型路径
                device=device,
                compute_type=compute_type,
                local_files_only=True  # 强制使用本地文件
            )
            
            print(f"✅ 模型 {model_name} 加载成功")
            return model
            
        except Exception as e:
            print(f"❌ 加载模型 {model_name} 失败: {e}")
            raise
    
    def list_local_models(self):
        """列出所有本地已下载的模型"""
        print(f"\n模型存储目录: {self.models_dir}")
        print(f"缓存文件: {self.cache_file}")
        
        # 清理无效缓存
        removed = self.cleanup_cache()
        if removed > 0:
            print(f"已清理 {removed} 个无效缓存")
        
        print("\n=== 本地模型状态 ===")
        
        # 检查每个可用模型的状态
        for model_name in self.AVAILABLE_MODELS:
            is_downloaded = self.is_model_downloaded(model_name)
            if is_downloaded:
                path = self.get_model_path(model_name)
                size_info = self.AVAILABLE_MODELS[model_name]["size"]
                print(f"✅ {model_name:<12} - {size_info:<8} - {path}")
            else:
                size_info = self.AVAILABLE_MODELS[model_name]["size"]
                print(f"❌ {model_name:<12} - {size_info:<8} - 未下载")
        
        # 显示weights目录中的所有内容
        print(f"\n=== {self.models_dir} 目录内容 ===")
        if self.models_dir.exists():
            for item in self.models_dir.iterdir():
                if item.is_dir():
                    file_count = len(list(item.rglob("*")))
                    print(f"📁 {item.name} ({file_count} 个文件)")
                else:
                    file_size = item.stat().st_size / (1024*1024)  # MB
                    print(f"📄 {item.name} ({file_size:.1f} MB)")
        else:
            print("目录不存在")
        
        print(f"\n=== 缓存信息 ===")
        if self.cache:
            for model_name, info in self.cache.items():
                print(f"{model_name}: {info}")
        else:
            print("缓存为空") 