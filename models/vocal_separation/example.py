#!/usr/bin/env python3
"""
人声分离使用示例

这个脚本展示了如何使用移植的人声分离算法。
"""

import os
import sys
import argparse
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent.parent))

from models.vocal_separation import VocalSeparator, create_separator, get_model_info


def single_file_example(input_path, model_path, output_dir="output"):
    """单文件分离示例"""
    print("=== 单文件人声分离示例 ===")
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 创建分离器
    print("正在初始化人声分离器...")
    separator = create_separator(
        model_path=model_path,
        device="auto",  # 自动选择设备
        batchsize=4,    # 可根据显存调整
        postprocess=True  # 启用后处理提高质量
    )
    
    # 显示模型信息
    print("\n模型信息:")
    info = separator.get_model_info()
    for key, value in info.items():
        print(f"  {key}: {value}")
    
    # 生成输出文件路径
    base_name = os.path.splitext(os.path.basename(input_path))[0]
    vocal_path = os.path.join(output_dir, f"{base_name}_vocal.wav")
    instrumental_path = os.path.join(output_dir, f"{base_name}_instrumental.wav")
    
    # 执行分离
    print(f"\n正在分离音频: {input_path}")
    vocal_audio, instrumental_audio = separator.separate_audio_file(
        input_path=input_path,
        output_vocal_path=vocal_path,
        output_instrumental_path=instrumental_path,
        use_tta=True,  # 使用测试时增强，质量更高但速度稍慢
        sr=44100       # 采样率
    )
    
    print(f"\n分离完成!")
    print(f"人声文件: {vocal_path}")
    print(f"伴奏文件: {instrumental_path}")
    
    return vocal_audio, instrumental_audio


def batch_processing_example(input_dir, model_path, output_dir="batch_output"):
    """批量处理示例"""
    print("=== 批量人声分离示例 ===")
    
    # 查找音频文件
    audio_extensions = ['.wav', '.mp3', '.flac', '.ogg', '.m4a']
    input_files = []
    
    for ext in audio_extensions:
        input_files.extend(Path(input_dir).glob(f"*{ext}"))
        input_files.extend(Path(input_dir).glob(f"*{ext.upper()}"))
    
    if not input_files:
        print(f"在目录 {input_dir} 中没有找到支持的音频文件")
        return
    
    print(f"找到 {len(input_files)} 个音频文件")
    
    # 创建分离器
    separator = create_separator(
        model_path=model_path,
        device="auto",
        batchsize=2,  # 批量处理时适当减小批大小
        postprocess=True
    )
    
    # 批量处理
    separator.batch_separate(
        input_files=[str(f) for f in input_files],
        output_dir=output_dir,
        use_tta=True,
        sr=44100
    )


def quality_comparison_example(input_path, model_path, output_dir="comparison"):
    """质量对比示例：比较不同设置的分离效果"""
    print("=== 质量对比示例 ===")
    
    os.makedirs(output_dir, exist_ok=True)
    base_name = os.path.splitext(os.path.basename(input_path))[0]
    
    # 配置不同的处理参数
    configs = [
        {"name": "fast", "use_tta": False, "postprocess": False, "batchsize": 8},
        {"name": "balanced", "use_tta": True, "postprocess": False, "batchsize": 4},
        {"name": "high_quality", "use_tta": True, "postprocess": True, "batchsize": 2}
    ]
    
    for config in configs:
        print(f"\n正在处理: {config['name']} 模式")
        
        # 创建分离器
        separator = create_separator(
            model_path=model_path,
            device="auto",
            batchsize=config['batchsize'],
            postprocess=config['postprocess']
        )
        
        # 生成输出路径
        vocal_path = os.path.join(output_dir, f"{base_name}_{config['name']}_vocal.wav")
        instrumental_path = os.path.join(output_dir, f"{base_name}_{config['name']}_instrumental.wav")
        
        # 执行分离
        separator.separate_audio_file(
            input_path=input_path,
            output_vocal_path=vocal_path,
            output_instrumental_path=instrumental_path,
            use_tta=config['use_tta'],
            sr=44100
        )
        
        print(f"  人声: {vocal_path}")
        print(f"  伴奏: {instrumental_path}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="人声分离示例脚本")
    parser.add_argument("--mode", choices=["single", "batch", "comparison"], 
                       default="single", help="运行模式")
    parser.add_argument("--input", required=True, 
                       help="输入文件路径（single/comparison模式）或目录路径（batch模式）")
    parser.add_argument("--model", required=True, 
                       help="模型文件路径")
    parser.add_argument("--output", default="output", 
                       help="输出目录")
    
    args = parser.parse_args()
    
    # 检查输入文件/目录
    if not os.path.exists(args.input):
        print(f"错误: 输入路径 {args.input} 不存在")
        return
    
    # 检查模型文件
    if not os.path.exists(args.model):
        print(f"错误: 模型文件 {args.model} 不存在")
        print("请下载预训练模型或提供正确的模型路径")
        return
    
    # 显示算法信息
    print("算法信息:")
    info = get_model_info()
    for key, value in info.items():
        print(f"  {key}: {value}")
    print()
    
    # 根据模式执行相应功能
    try:
        if args.mode == "single":
            single_file_example(args.input, args.model, args.output)
        elif args.mode == "batch":
            batch_processing_example(args.input, args.model, args.output)
        elif args.mode == "comparison":
            quality_comparison_example(args.input, args.model, args.output)
    except Exception as e:
        print(f"处理过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    # 如果没有命令行参数，显示帮助信息
    if len(sys.argv) == 1:
        print("人声分离算法使用示例\n")
        print("使用方法:")
        print("  单文件处理:")
        print("    python example.py --mode single --input audio.wav --model baseline.pth")
        print("")
        print("  批量处理:")
        print("    python example.py --mode batch --input ./audio_folder --model baseline.pth")
        print("")
        print("  质量对比:")
        print("    python example.py --mode comparison --input audio.wav --model baseline.pth")
        print("")
        print("参数说明:")
        print("  --mode: 运行模式 (single/batch/comparison)")
        print("  --input: 输入文件或目录路径")
        print("  --model: 预训练模型文件路径")
        print("  --output: 输出目录 (可选)")
        print("")
        print("注意: 需要先下载或复制预训练模型文件到 models/vocal_separation/weights/ 目录")
    else:
        main() 