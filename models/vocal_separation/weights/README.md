# 人声分离预训练模型

这个目录用于存放人声分离算法的预训练模型权重文件。

## 模型文件

模型文件需要从原始的 pytvzhen-1.3.0 项目中复制过来：

### 复制现有模型
如果您已经有 pytvzhen-1.3.0 项目，可以直接复制模型文件：

```bash
# 复制预训练模型
cp "J:\Python\PycharmProjects\Example\01-easyvideotrans\pytvzhen-1.3.0\models\baseline.pth" ./baseline.pth
```

### 模型说明

- **baseline.pth**: 基础人声分离模型
  - 模型架构: CascadedNet
  - FFT大小: 2048
  - 跳跃长度: 1024
  - 参数量: 约32M
  - 用途: 高质量人声和伴奏分离

## 使用方法

```python
from models.vocal_separation import create_separator

# 创建分离器，指定模型路径
separator = create_separator(
    model_path="models/vocal_separation/weights/baseline.pth",
    device="auto"
)

# 分离音频
vocal, instrumental = separator.separate_audio_file(
    "input.wav",
    "output_vocal.wav",
    "output_instrumental.wav"
)
```

## 性能说明

- **处理速度**: GPU模式下约为实时的3-5倍
- **分离质量**: 高质量人声分离，适合大多数音乐类型
- **内存需求**: GPU模式约需要2-4GB显存
- **支持格式**: WAV, MP3, FLAC, OGG, M4A 等

## 注意事项

1. 模型文件较大（约250MB），请确保有足够的存储空间
2. 首次使用时会自动检测可用的计算设备（CPU/GPU）
3. GPU模式可显著提升处理速度
4. 建议使用44.1kHz采样率的音频获得最佳效果

## 模型来源

这些模型来自 pytvzhen-1.3.0 项目，基于级联神经网络架构训练，
专门针对音乐中的人声分离任务进行了优化。 

import os
model_path = "models/vocal_separation/weights/baseline.pth"
print(f"模型文件存在: {os.path.exists(model_path)}")
if os.path.exists(model_path):
    print(f"文件大小: {os.path.getsize(model_path)} bytes") 