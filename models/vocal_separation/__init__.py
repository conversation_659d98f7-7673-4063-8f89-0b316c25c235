"""
人声分离模块

这是一个基于深度学习的快速人声分离算法，移植自 pytvzhen-1.3.0 项目。
可以将音频中的人声和伴奏高质量分离。

主要功能:
- 快速人声分离处理
- 支持GPU加速
- 支持批量处理
- 多种音频格式支持

使用示例:
    from models.vocal_separation import VocalSeparator, create_separator
    
    # 创建分离器
    separator = create_separator(
        model_path="models/vocal_separation/weights/baseline.pth",
        device="auto"
    )
    
    # 分离音频
    vocal, instrumental = separator.separate_audio_file(
        "input.wav",
        "output_vocal.wav", 
        "output_instrumental.wav"
    )
"""

from .separator import VocalSeparator, create_separator
try:
    from .lib import nets, spec_utils, utils, dataset
except ImportError:
    # 如果lib模块导入失败，设置为None
    nets = spec_utils = utils = dataset = None

__version__ = '1.0.0'
__author__ = 'FlipTalk AI Team'

# 导出主要类和函数
__all__ = [
    'VocalSeparator',
    'create_separator',
    'get_model_info',
    'nets',
    'spec_utils', 
    'utils',
    'dataset'
]

# 模型配置
DEFAULT_MODEL_CONFIG = {
    'fft_size': 2048,
    'hop_size': 1024,
    'batchsize': 4,
    'cropsize': 256,
    'postprocess': False
}

# 支持的音频格式
SUPPORTED_FORMATS = ['.wav', '.mp3', '.flac', '.ogg', '.m4a', '.aac']

def get_version():
    """获取版本信息"""
    return __version__

def get_model_info():
    """获取模型架构信息"""
    return {
        "算法名称": "CascadedNet 人声分离",
        "来源": "pytvzhen-1.3.0",
        "架构": "级联卷积神经网络",
        "特点": ["快速处理", "高质量分离", "GPU加速", "测试时增强"],
        "支持格式": SUPPORTED_FORMATS,
        "默认配置": DEFAULT_MODEL_CONFIG
    } 