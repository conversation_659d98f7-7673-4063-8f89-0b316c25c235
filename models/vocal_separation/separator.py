import os

import librosa
import numpy as np
import soundfile as sf
import torch
from tqdm import tqdm

from .lib import dataset
from .lib import nets
from .lib import spec_utils
from .lib import utils

AUDIO_REMOVE_DEVICE = "gpu"
AUDIO_REMOVE_FFT_SIZE = 2048
AUDIO_REMOVE_HOP_SIZE = 1024


class Separator(object):
    """原始的Separator类，用于内部处理"""

    def __init__(self, model, device=None, batchsize=1, cropsize=256, postprocess=False):
        self.model = model
        self.offset = model.offset
        self.device = device
        self.batchsize = batchsize
        self.cropsize = cropsize
        self.postprocess = postprocess

    def _postprocess(self, X_spec, mask):
        if self.postprocess:
            mask_mag = np.abs(mask)
            mask_mag = spec_utils.merge_artifacts(mask_mag)
            mask = mask_mag * np.exp(1.j * np.angle(mask))

        X_mag = np.abs(X_spec)
        X_phase = np.angle(X_spec)

        y_spec = mask * X_mag * np.exp(1.j * X_phase)
        v_spec = (1 - mask) * X_mag * np.exp(1.j * X_phase)
        # y_spec = X_spec * mask
        # v_spec = X_spec - y_spec

        return y_spec, v_spec

    def _separate(self, X_spec_pad, roi_size):
        X_dataset = []
        patches = (X_spec_pad.shape[2] - 2 * self.offset) // roi_size
        for i in range(patches):
            start = i * roi_size
            X_spec_crop = X_spec_pad[:, :, start:start + self.cropsize]
            X_dataset.append(X_spec_crop)

        X_dataset = np.asarray(X_dataset)

        self.model.eval()
        with torch.no_grad():
            mask_list = []
            # To reduce the overhead, dataloader is not used.
            for i in tqdm(range(0, patches, self.batchsize)):
                X_batch = X_dataset[i: i + self.batchsize]
                X_batch = torch.from_numpy(X_batch).to(self.device)

                mask = self.model.predict_mask(torch.abs(X_batch))

                mask = mask.detach().cpu().numpy()
                mask = np.concatenate(mask, axis=2)
                mask_list.append(mask)

            mask = np.concatenate(mask_list, axis=2)

        return mask

    def separate(self, X_spec):
        n_frame = X_spec.shape[2]
        pad_l, pad_r, roi_size = dataset.make_padding(n_frame, self.cropsize, self.offset)
        X_spec_pad = np.pad(X_spec, ((0, 0), (0, 0), (pad_l, pad_r)), mode='constant')
        X_spec_pad /= np.abs(X_spec).max()

        mask = self._separate(X_spec_pad, roi_size)
        mask = mask[:, :, :n_frame]

        y_spec, v_spec = self._postprocess(X_spec, mask)

        return y_spec, v_spec

    def separate_tta(self, X_spec):
        n_frame = X_spec.shape[2]
        pad_l, pad_r, roi_size = dataset.make_padding(n_frame, self.cropsize, self.offset)
        X_spec_pad = np.pad(X_spec, ((0, 0), (0, 0), (pad_l, pad_r)), mode='constant')
        X_spec_pad /= X_spec_pad.max()

        mask = self._separate(X_spec_pad, roi_size)

        pad_l += roi_size // 2
        pad_r += roi_size // 2
        X_spec_pad = np.pad(X_spec, ((0, 0), (0, 0), (pad_l, pad_r)), mode='constant')
        X_spec_pad /= X_spec_pad.max()

        mask_tta = self._separate(X_spec_pad, roi_size)
        mask_tta = mask_tta[:, :, roi_size // 2:]
        mask = (mask[:, :, :n_frame] + mask_tta[:, :, :n_frame]) * 0.5

        y_spec, v_spec = self._postprocess(X_spec, mask)

        return y_spec, v_spec


class VocalSeparator:
    """
    高级人声分离器类，提供简单易用的接口
    
    这个类封装了CascadedNet算法，提供了更友好的API接口，
    适用于GUI界面和批量处理需求。
    """
    
    def __init__(self, model_path, device="auto", batchsize=4, cropsize=256, postprocess=False):
        """
        初始化人声分离器
        
        Args:
            model_path: 模型文件路径
            device: 设备设置 ("auto", "cpu", "cuda", "gpu")
            batchsize: 批处理大小
            cropsize: 裁剪大小
            postprocess: 是否启用后处理
        """
        self.model_path = model_path
        self.batchsize = batchsize
        self.cropsize = cropsize
        self.postprocess = postprocess
        
        # 设备检测
        self.device = self._get_device(device)
        print(f"VocalSeparator 使用设备: {self.device}")
        
        # 加载模型
        self._load_model()
        
    def _get_device(self, device_setting):
        """设备检测和配置"""
        if device_setting in ["auto", "gpu"]:
            if torch.cuda.is_available():
                return torch.device('cuda:0')
            else:
                print("CUDA不可用，切换到CPU模式")
                return torch.device('cpu')
        elif device_setting == "cpu":
            return torch.device('cpu')
        elif device_setting == "cuda":
            if torch.cuda.is_available():
                return torch.device('cuda:0')
            else:
                raise ValueError("CUDA不可用，但指定了cuda设备")
        else:
            raise ValueError(f"不支持的设备设置: {device_setting}")
    
    def _load_model(self):
        """加载CascadedNet模型"""
        if not os.path.exists(self.model_path):
            raise FileNotFoundError(f"模型文件不存在: {self.model_path}")
        
        print(f"正在加载模型: {self.model_path}")
        self.model = nets.CascadedNet(AUDIO_REMOVE_FFT_SIZE, AUDIO_REMOVE_HOP_SIZE, 32, 128)
        self.model.load_state_dict(torch.load(self.model_path, map_location='cpu'))
        self.model.to(self.device)
        self.model.eval()
        print("模型加载完成")
        
        # 创建内部分离器
        self.separator = Separator(
            model=self.model,
            device=self.device,
            batchsize=self.batchsize,
            cropsize=self.cropsize,
            postprocess=self.postprocess
        )
    
    def separate_audio_file(self, input_path, output_vocal_path, output_instrumental_path, 
                          use_tta=True, sr=44100):
        """
        分离音频文件
        
        Args:
            input_path: 输入音频文件路径
            output_vocal_path: 输出人声文件路径
            output_instrumental_path: 输出伴奏文件路径
            use_tta: 是否使用测试时增强 (Test Time Augmentation)
            sr: 采样率
            
        Returns:
            tuple: (人声文件路径, 伴奏文件路径)
        """
        if not os.path.exists(input_path):
            raise FileNotFoundError(f"输入文件不存在: {input_path}")
        
        # 确保输出目录存在
        os.makedirs(os.path.dirname(output_vocal_path), exist_ok=True)
        os.makedirs(os.path.dirname(output_instrumental_path), exist_ok=True)
        
        print(f'正在加载音频文件: {input_path}')
        X, sample_rate = librosa.load(
            input_path, sr=sr, mono=False, dtype=np.float32, res_type='kaiser_fast'
        )
        
        if X.ndim == 1:
            # 单声道转立体声
            X = np.asarray([X, X])
        
        print('正在进行频谱转换...')
        X_spec = spec_utils.wave_to_spectrogram(X, AUDIO_REMOVE_HOP_SIZE, AUDIO_REMOVE_FFT_SIZE)
        
        print('正在进行人声分离...')
        if use_tta:
            y_spec, v_spec = self.separator.separate_tta(X_spec)
        else:
            y_spec, v_spec = self.separator.separate(X_spec)
        
        print('正在生成伴奏音频...')
        instrumental_wave = spec_utils.spectrogram_to_wave(y_spec, AUDIO_REMOVE_HOP_SIZE)

        # 音量归一化和增益补偿 - 背景音乐
        instrumental_wave = self._normalize_and_boost_audio(instrumental_wave, target_db=-12, boost_db=6)
        sf.write(output_instrumental_path, instrumental_wave.T, sample_rate)

        print('正在生成人声音频...')
        vocal_wave = spec_utils.spectrogram_to_wave(v_spec, AUDIO_REMOVE_HOP_SIZE)

        # 音量归一化 - 人声
        vocal_wave = self._normalize_and_boost_audio(vocal_wave, target_db=-12, boost_db=3)
        sf.write(output_vocal_path, vocal_wave.T, sample_rate)
        
        print(f'分离完成！')
        print(f'人声文件: {output_vocal_path}')
        print(f'伴奏文件: {output_instrumental_path}')
        
        return output_vocal_path, output_instrumental_path
    
    def separate_audio_data(self, audio_data, sr=44100, use_tta=True):
        """
        分离音频数据 (numpy数组)
        
        Args:
            audio_data: 音频数据 (numpy数组)
            sr: 采样率
            use_tta: 是否使用测试时增强
            
        Returns:
            tuple: (人声数据, 伴奏数据)
        """
        if audio_data.ndim == 1:
            # 单声道转立体声
            audio_data = np.asarray([audio_data, audio_data])
        
        # 频谱转换
        X_spec = spec_utils.wave_to_spectrogram(audio_data, AUDIO_REMOVE_HOP_SIZE, AUDIO_REMOVE_FFT_SIZE)
        
        # 人声分离
        if use_tta:
            y_spec, v_spec = self.separator.separate_tta(X_spec)
        else:
            y_spec, v_spec = self.separator.separate(X_spec)
        
        # 转回音频
        instrumental_wave = spec_utils.spectrogram_to_wave(y_spec, AUDIO_REMOVE_HOP_SIZE)
        vocal_wave = spec_utils.spectrogram_to_wave(v_spec, AUDIO_REMOVE_HOP_SIZE)
        
        return vocal_wave, instrumental_wave

    def _normalize_and_boost_audio(self, audio_data, target_db=-12, boost_db=0):
        """
        音频归一化和增益补偿

        Args:
            audio_data: 音频数据 (numpy数组)
            target_db: 目标音量 (dB)
            boost_db: 额外增益 (dB)

        Returns:
            numpy.ndarray: 处理后的音频数据
        """
        try:
            # 确保音频数据不为空
            if audio_data.size == 0:
                return audio_data

            # 计算当前音频的RMS值
            if audio_data.ndim == 1:
                rms = np.sqrt(np.mean(audio_data ** 2))
            else:
                rms = np.sqrt(np.mean(audio_data ** 2, axis=1, keepdims=True))

            # 避免除零错误
            if np.any(rms == 0):
                print("⚠️ 检测到静音音频，跳过归一化")
                return audio_data

            # 计算目标RMS值 (从dB转换)
            target_rms = 10 ** (target_db / 20)

            # 计算增益系数
            gain = target_rms / rms

            # 应用额外增益
            if boost_db != 0:
                boost_gain = 10 ** (boost_db / 20)
                gain *= boost_gain

            # 应用增益
            normalized_audio = audio_data * gain

            # 防止削波 - 如果峰值超过0.95，进行限制
            max_val = np.max(np.abs(normalized_audio))
            if max_val > 0.95:
                limiter_gain = 0.95 / max_val
                normalized_audio *= limiter_gain
                print(f"🔧 应用限制器，增益系数: {limiter_gain:.3f}")

            # 计算最终音量
            if normalized_audio.ndim == 1:
                final_rms = np.sqrt(np.mean(normalized_audio ** 2))
            else:
                final_rms = np.sqrt(np.mean(normalized_audio ** 2))

            final_db = 20 * np.log10(final_rms + 1e-10)
            print(f"🔊 音频归一化完成: 目标={target_db}dB, 增益={boost_db}dB, 实际={final_db:.1f}dB")

            return normalized_audio

        except Exception as e:
            print(f"❌ 音频归一化失败: {e}")
            return audio_data


def create_separator(model_path, device="auto", batchsize=4, cropsize=256, postprocess=False):
    """
    创建人声分离器的便捷函数
    
    Args:
        model_path: 模型文件路径
        device: 设备设置
        batchsize: 批处理大小
        cropsize: 裁剪大小
        postprocess: 是否启用后处理
        
    Returns:
        VocalSeparator: 配置好的人声分离器实例
    """
    return VocalSeparator(
        model_path=model_path,
        device=device,
        batchsize=batchsize,
        cropsize=cropsize,
        postprocess=postprocess
    )


def audio_remove(audioFileNameAndPath, voiceFileNameAndPath, instrumentFileNameAndPath, modelNameAndPath):
    """原始的audio_remove函数，保持向后兼容"""
    # 修改: 检查 CUDA 是否可用，若不可用则使用 CPU
    if AUDIO_REMOVE_DEVICE == "cpu" or not torch.cuda.is_available():
        device = torch.device('cpu')
    elif AUDIO_REMOVE_DEVICE == "gpu":
        device = torch.device('cuda:0')
    else:
        raise ValueError("Invalid device: {}".format(AUDIO_REMOVE_DEVICE))

    print("Loading model on device:", device)
    model = nets.CascadedNet(AUDIO_REMOVE_FFT_SIZE, AUDIO_REMOVE_HOP_SIZE, 32, 128)  # 模型参数
    model.load_state_dict(torch.load(modelNameAndPath, map_location='cpu'))
    model.to(device)
    print("Model loaded")

    print('loading wave source ' + audioFileNameAndPath)
    X, sr = librosa.load(
        audioFileNameAndPath, sr=44100, mono=False, dtype=np.float32, res_type='kaiser_fast'
    )
    print("Wave source loaded")

    if X.ndim == 1:
        # mono to stereo
        X = np.asarray([X, X])

    print('stft of wave source...', end=' ')
    X_spec = spec_utils.wave_to_spectrogram(X, AUDIO_REMOVE_HOP_SIZE, AUDIO_REMOVE_FFT_SIZE)
    print('done')

    sp = Separator(
        model=model,
        device=device,
        batchsize=4,
        cropsize=256,
        postprocess=False
    )

    y_spec, v_spec = sp.separate_tta(X_spec)
    print('inverse stft of instruments...', end=' ')
    wave = spec_utils.spectrogram_to_wave(y_spec, AUDIO_REMOVE_HOP_SIZE)
    print('done')
    sf.write(instrumentFileNameAndPath, wave.T, sr)

    print('inverse stft of vocals...', end=' ')
    wave = spec_utils.spectrogram_to_wave(v_spec, hop_length=AUDIO_REMOVE_HOP_SIZE)
    print('done')
    sf.write(voiceFileNameAndPath, wave.T, sr)


if __name__ == '__main__':
    audio_remove("J:\\MyAi\\03 FlipTalk Ai\\Downloads\\Tutorial_Vectorworks_Plugin_for_crowdit1080p_audio.wav",
                 "J:\\MyAi\\03 FlipTalk Ai\\output\\voice_separation\\RXXRguaHZs0_voice.wav",
                 "J:\\MyAi\\03 FlipTalk Ai\\output\\voice_separation\\RXXRguaHZs0_instrument.wav",
                 "J:\\MyAi\\03 FlipTalk Ai\\models\\vocal_separation\\weights\\baseline.pth")