#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试苹果风格弹窗设计
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_apple_message_boxes():
    """测试苹果风格弹窗"""
    try:
        from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton
        from PySide6.QtCore import Qt
        from ui.fliptalk_ui import AppleMessageBox
        
        # 创建应用程序
        app = QApplication(sys.argv)
        
        # 创建测试窗口
        window = QMainWindow()
        window.setWindowTitle("苹果风格弹窗测试")
        window.setFixedSize(400, 300)
        window.setStyleSheet("""
            QMainWindow {
                background-color: #1C1C1E;
            }
            QPushButton {
                background-color: #007AFF;
                color: white;
                border: none;
                border-radius: 8px;
                font-size: 14px;
                font-weight: 600;
                padding: 12px 24px;
                margin: 8px;
                min-height: 44px;
            }
            QPushButton:hover {
                background-color: #0056CC;
            }
        """)
        
        # 创建中央部件
        central_widget = QWidget()
        window.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        layout.setSpacing(16)
        layout.setContentsMargins(40, 40, 40, 40)
        
        # 创建测试按钮
        info_btn = QPushButton("📘 信息提示")
        info_btn.clicked.connect(lambda: AppleMessageBox.show_info(
            window, "信息提示", 
            "这是一个苹果风格的信息提示框。\n\n"
            "采用了现代化的设计语言，包括：\n"
            "• 圆角边框和阴影效果\n"
            "• 高对比度的文字颜色\n"
            "• 苹果系统字体\n"
            "• 优雅的按钮设计"
        ))
        
        warning_btn = QPushButton("⚠️ 警告提示")
        warning_btn.clicked.connect(lambda: AppleMessageBox.show_warning(
            window, "警告提示",
            "这是一个苹果风格的警告提示框。\n\n"
            "使用橙色主题来表示警告信息，\n"
            "确保用户能够清楚地识别重要提示。"
        ))
        
        error_btn = QPushButton("❌ 错误提示")
        error_btn.clicked.connect(lambda: AppleMessageBox.show_error(
            window, "错误提示",
            "这是一个苹果风格的错误提示框。\n\n"
            "使用红色主题来表示错误信息，\n"
            "帮助用户快速识别和处理问题。"
        ))
        
        success_btn = QPushButton("✅ 成功提示")
        success_btn.clicked.connect(lambda: AppleMessageBox.show_success(
            window, "成功提示",
            "这是一个苹果风格的成功提示框。\n\n"
            "使用绿色主题来表示成功信息，\n"
            "给用户积极的反馈体验。"
        ))
        
        question_btn = QPushButton("❓ 确认对话框")
        question_btn.clicked.connect(lambda: test_question_dialog(window))
        
        # 添加按钮到布局
        layout.addWidget(info_btn)
        layout.addWidget(warning_btn)
        layout.addWidget(error_btn)
        layout.addWidget(success_btn)
        layout.addWidget(question_btn)
        layout.addStretch()
        
        # 显示窗口
        window.show()
        
        print("🎨 苹果风格弹窗测试程序已启动")
        print("点击不同的按钮来测试各种弹窗样式")
        
        return app.exec()
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return 1

def test_question_dialog(parent):
    """测试确认对话框"""
    try:
        from ui.fliptalk_ui import AppleMessageBox
        
        result = AppleMessageBox.show_question(
            parent, "确认操作",
            "这是一个苹果风格的确认对话框。\n\n"
            "您确定要执行此操作吗？\n"
            "此操作可能会影响您的数据。",
            "确定", "取消"
        )
        
        if result:
            AppleMessageBox.show_success(parent, "操作确认", "您选择了确定！")
        else:
            AppleMessageBox.show_info(parent, "操作取消", "您选择了取消。")
            
    except Exception as e:
        print(f"❌ 确认对话框测试失败: {e}")

def test_contrast_and_readability():
    """测试对比度和可读性"""
    print("🔍 测试颜色对比度...")
    
    # 苹果风格颜色配置
    colors = {
        'background': '#1C1C1E',
        'text_primary': '#FFFFFF',
        'text_secondary': '#EBEBF5',
        'primary': '#007AFF',
        'error': '#FF3B30',
        'warning': '#FF9500',
        'success': '#34C759'
    }
    
    def hex_to_rgb(hex_color):
        """将十六进制颜色转换为RGB"""
        hex_color = hex_color.lstrip('#')
        return tuple(int(hex_color[i:i+2], 16) for i in (0, 2, 4))
    
    def calculate_luminance(rgb):
        """计算颜色的亮度"""
        r, g, b = [x/255.0 for x in rgb]
        
        def gamma_correct(c):
            if c <= 0.03928:
                return c / 12.92
            else:
                return pow((c + 0.055) / 1.055, 2.4)
        
        r = gamma_correct(r)
        g = gamma_correct(g)
        b = gamma_correct(b)
        
        return 0.2126 * r + 0.7152 * g + 0.0722 * b
    
    def calculate_contrast_ratio(color1, color2):
        """计算两种颜色的对比度比例"""
        lum1 = calculate_luminance(hex_to_rgb(color1))
        lum2 = calculate_luminance(hex_to_rgb(color2))
        
        lighter = max(lum1, lum2)
        darker = min(lum1, lum2)
        
        return (lighter + 0.05) / (darker + 0.05)
    
    # 测试对比度
    background = colors['background']
    
    print(f"📊 颜色对比度测试结果:")
    print(f"背景色: {background}")
    print("-" * 40)
    
    for name, color in colors.items():
        if name != 'background':
            ratio = calculate_contrast_ratio(background, color)
            status = "✅ 优秀" if ratio >= 7 else "✅ 良好" if ratio >= 4.5 else "❌ 不足"
            print(f"{name:15} {color:8} 对比度: {ratio:.2f}:1 {status}")
    
    print("-" * 40)
    print("📋 对比度标准:")
    print("• 7:1 或更高 - AAA级别（优秀）")
    print("• 4.5:1 或更高 - AA级别（良好）")
    print("• 低于4.5:1 - 不符合无障碍标准")

def main():
    """主函数"""
    print("🚀 开始苹果风格弹窗测试")
    print("=" * 50)
    
    # 测试对比度
    test_contrast_and_readability()
    
    print("\n" + "=" * 50)
    print("🎨 启动弹窗界面测试...")
    
    # 测试弹窗界面
    result = test_apple_message_boxes()
    
    print("\n" + "=" * 50)
    if result == 0:
        print("✅ 苹果风格弹窗测试完成")
        print("\n💡 设计特点:")
        print("• 采用苹果官方设计语言")
        print("• 高对比度确保文字清晰可读")
        print("• 圆角和阴影提供现代感")
        print("• 一致的颜色主题和字体")
        print("• 符合无障碍设计标准")
    else:
        print("❌ 测试过程中出现问题")
    
    return result

if __name__ == "__main__":
    sys.exit(main())
