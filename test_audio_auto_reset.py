#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试音频播放完成后自动重置播放按钮功能
"""

import sys
import os
import time
from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QLabel
from PySide6.QtCore import QTimer, QUrl
from PySide6.QtMultimedia import QMediaPlayer, QAudioOutput

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

class AudioTestWindow(QMainWindow):
    """音频播放测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("音频播放自动重置测试")
        self.setFixedSize(400, 300)
        
        # 创建媒体播放器
        self.media_player = QMediaPlayer()
        self.audio_output = QAudioOutput()
        self.audio_output.setVolume(0.8)
        self.media_player.setAudioOutput(self.audio_output)
        
        # 连接信号
        self.media_player.mediaStatusChanged.connect(self.on_media_status_changed)
        self.media_player.playbackStateChanged.connect(self.on_playback_state_changed)
        self.media_player.errorOccurred.connect(self.on_error_occurred)
        
        # 播放状态
        self.is_playing = False
        
        self.setup_ui()
        
    def setup_ui(self):
        """设置UI"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        layout.setSpacing(20)
        
        # 标题
        title_label = QLabel("音频播放自动重置功能测试")
        title_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #333;")
        layout.addWidget(title_label)
        
        # 状态显示
        self.status_label = QLabel("状态: 准备就绪")
        self.status_label.setStyleSheet("font-size: 14px; color: #666;")
        layout.addWidget(self.status_label)
        
        # 播放按钮
        self.play_button = QPushButton("🔊 播放测试音频")
        self.play_button.setFixedHeight(50)
        self.play_button.setStyleSheet("""
            QPushButton {
                background-color: #3B82F6;
                color: white;
                border: none;
                border-radius: 25px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #60A5FA;
            }
            QPushButton:pressed {
                background-color: #1D4ED8;
            }
        """)
        self.play_button.clicked.connect(self.toggle_playback)
        layout.addWidget(self.play_button)
        
        # 说明文本
        info_label = QLabel(
            "点击播放按钮开始播放测试音频。\n"
            "音频播放完成后，按钮应该自动恢复到默认状态。\n"
            "如果没有音频文件，将播放5秒钟的静音测试。"
        )
        info_label.setStyleSheet("font-size: 12px; color: #888; line-height: 1.5;")
        info_label.setWordWrap(True)
        layout.addWidget(info_label)
        
        layout.addStretch()
        
    def toggle_playback(self):
        """切换播放状态"""
        if not self.is_playing:
            self.start_playback()
        else:
            self.stop_playback()
            
    def start_playback(self):
        """开始播放"""
        try:
            # 尝试查找测试音频文件
            test_audio_paths = [
                "test_audio.mp3",
                "test_audio.wav",
                os.path.join("assets", "test_audio.mp3"),
                os.path.join("assets", "test_audio.wav"),
            ]
            
            audio_file = None
            for path in test_audio_paths:
                if os.path.exists(path):
                    audio_file = path
                    break
                    
            if audio_file:
                print(f"播放音频文件: {audio_file}")
                self.media_player.setSource(QUrl.fromLocalFile(os.path.abspath(audio_file)))
                self.status_label.setText(f"状态: 播放音频文件 - {os.path.basename(audio_file)}")
            else:
                # 如果没有音频文件，创建一个短暂的静音测试
                print("未找到测试音频文件，使用定时器模拟播放完成")
                self.status_label.setText("状态: 模拟播放测试（5秒后自动停止）")
                
                # 使用定时器模拟播放完成
                QTimer.singleShot(5000, self.simulate_playback_finished)
                
            # 更新按钮状态
            self.update_button_to_playing()
            self.is_playing = True
            
            if audio_file:
                self.media_player.play()
                
        except Exception as e:
            print(f"播放失败: {e}")
            self.status_label.setText(f"状态: 播放失败 - {e}")
            
    def stop_playback(self):
        """停止播放"""
        try:
            self.media_player.stop()
            self.reset_button_state()
            self.is_playing = False
            self.status_label.setText("状态: 播放已停止")
            print("手动停止播放")
        except Exception as e:
            print(f"停止播放失败: {e}")
            
    def simulate_playback_finished(self):
        """模拟播放完成"""
        print("模拟播放完成 - 自动重置按钮状态")
        self.reset_button_state()
        self.is_playing = False
        self.status_label.setText("状态: 模拟播放完成 - 按钮已自动重置")
        
    def update_button_to_playing(self):
        """更新按钮为播放状态"""
        self.play_button.setText("🟥 停止播放")
        self.play_button.setStyleSheet("""
            QPushButton {
                background-color: #EF4444;
                color: white;
                border: none;
                border-radius: 25px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #F87171;
            }
            QPushButton:pressed {
                background-color: #DC2626;
            }
        """)
        
    def reset_button_state(self):
        """重置按钮状态"""
        print("🔄 重置播放按钮状态")
        self.play_button.setText("🔊 播放测试音频")
        self.play_button.setStyleSheet("""
            QPushButton {
                background-color: #3B82F6;
                color: white;
                border: none;
                border-radius: 25px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #60A5FA;
            }
            QPushButton:pressed {
                background-color: #1D4ED8;
            }
        """)
        
    def on_media_status_changed(self, status):
        """处理媒体状态变化"""
        try:
            print(f"媒体状态变化: {status}")
            
            if hasattr(QMediaPlayer, 'MediaStatus'):
                if status == QMediaPlayer.MediaStatus.EndOfMedia:
                    print("🎵 音频播放完成 - 自动重置按钮状态")
                    self.reset_button_state()
                    self.is_playing = False
                    self.status_label.setText("状态: 播放完成 - 按钮已自动重置")
                elif status == QMediaPlayer.MediaStatus.InvalidMedia:
                    print("❌ 音频媒体无效")
                    self.reset_button_state()
                    self.is_playing = False
                    self.status_label.setText("状态: 媒体无效 - 按钮已重置")
                elif status == QMediaPlayer.MediaStatus.LoadedMedia:
                    print("✅ 音频媒体已加载")
                elif status == QMediaPlayer.MediaStatus.LoadingMedia:
                    print("⏳ 音频媒体加载中...")
                    
        except Exception as e:
            print(f"处理媒体状态变化失败: {e}")
            
    def on_playback_state_changed(self, state):
        """处理播放状态变化"""
        try:
            print(f"播放状态变化: {state}")
            
            if hasattr(QMediaPlayer, 'PlaybackState'):
                if state == QMediaPlayer.PlaybackState.StoppedState:
                    print("⏹ 播放已停止")
                    if self.is_playing:  # 只有在播放状态时才自动重置
                        self.reset_button_state()
                        self.is_playing = False
                        self.status_label.setText("状态: 播放停止 - 按钮已自动重置")
                elif state == QMediaPlayer.PlaybackState.PlayingState:
                    print("▶️ 正在播放")
                elif state == QMediaPlayer.PlaybackState.PausedState:
                    print("⏸️ 播放已暂停")
                    
        except Exception as e:
            print(f"处理播放状态变化失败: {e}")
            
    def on_error_occurred(self, error, error_string):
        """处理播放错误"""
        try:
            print(f"播放错误: {error_string}")
            self.reset_button_state()
            self.is_playing = False
            self.status_label.setText(f"状态: 播放错误 - {error_string}")
        except Exception as e:
            print(f"处理播放错误失败: {e}")


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    window = AudioTestWindow()
    window.show()
    
    print("音频播放自动重置测试程序已启动")
    print("请点击播放按钮测试自动重置功能")
    
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
