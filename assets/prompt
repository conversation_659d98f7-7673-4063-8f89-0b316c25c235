我想开发一款桌面端视频翻译软件，主要包含以下功能：
1、用户可以拖拽或者点击上传视频文件
2、视频提取音频：对视频进行音频提取，提取的音频保存为mp3、wav等常见音频文件，并去除里面的背景音乐保留纯人声
3、音频转字幕：使用成熟的语音识别模型对音频文件进行语音识别，识别后的字幕保存为srt文件
4、字幕翻译：使用成熟的翻译模型对字幕进行翻译，翻译后的字幕保存为srt文件
5、分段处理字幕：提取字幕中的时间戳，根据时间戳对字幕进行分段处理，提取出每个时间段的字幕
6、字幕配音：使用成熟的语音合成模型为每个时间段的字幕配音，配音后的音频保存为mp3、wav等常见音频文件
7、去除头尾静音：去除配音音频文件的头尾静音，保留有声音的部分
8、初始化一个空音频：初始化一个空音频文件，音频长度与完整字幕持续时间一致
9、时间轴冲突检测与加速模块：检测本段字幕时间戳开始时间加上本段纯声音的音频时长，如果大于下一段字幕时间戳开始时间，则认为本段字幕与下一段字幕时间轴冲突，需要进行加速处理，加速的速率为（本段音频持续时间加上0.1秒的最小时间间隔）除以（下段字幕时间戳的开始时间减去本段字幕时间戳的开始时间），如果速率大于1.2，则对该段，进入下一阶段人工处理
10、字幕编辑：将上一步中标记的字幕进行人工编辑，并重新生成去除头尾静音的音频文件，然后重复9-10步，直到所有字幕不再被标记
11、将第10步中编辑后的字幕与音频文件替换掉原来的字幕和音频文件
12、将处理后音频文件进行合并，生成最终的完整音频文件
13、将最终的完整音频文件与原视频进行合并，生成最终的视频文件
14、将最终的视频文件保存到本地

现在需要你帮我输出高保真的UI界面，根据我提供给你的图片进行设计，请仔细总结和分析以下要求，并确保生成的界面可以直接用于后续的开发：
1、功能分析：先分析软件的各项功能，确定核心交互逻辑
2、产品界面规划：作为产品经理，定义关键界面，确保信息架构合理。
3、UI配色：软件界面主要使用5种颜色，分别是：
#00DD65：用于标题、按钮背景色等重要元素
#000000：用于界面模块的背景以及按钮文字
#1F1F1F：用于背景和输入框等的底色
#FFFFFF：用于文字
#FF0000：用于需要重点标记的字幕
4、UI设计：
界面的尺寸为1440*960，所有模块（包括导航栏、按钮，各功能模块）采用圆角矩形，圆角大小为30px，背景色为#1F1F1F；
最左侧为导航栏：
1、导航栏最上面为logo
2、尺寸为172*936，距离上边缘12px，距离左边缘12px，距离下边缘12px，背景色为#000000；
3、导航栏的按钮尺寸为163*44，按钮之间的间距为15px，按钮距离导航栏上边缘133px，按钮字体大小为20px,按钮背景色为#1F1F1F，按钮文字颜色为#FFFFFF，选中时按钮背景色为#00DD65；
中间为视频处理区，分为三个板块，采用垂直布局，分别是：
1、最上面为软件名称及参数设置区：尺寸为612*90，距离上边缘12px，距离左侧导航栏边缘13px，软件名称字体大小为45px，软件名称文字颜色为#FFFFFF，软件名称文字靠左对齐，右侧是参数设置按钮，按钮大小为125*38，按钮上文字大小为20px，按钮背景色为#00DD65
2、中间为视频上传区：尺寸为612*310，背景色为#000000，距离软件名称及参数设置区下边缘10px，内部使用虚线标识出视频上传区域，支持批量上传、支持拖拽和点击上传
3、最下面为视频处理列表区：尺寸为612*516，背景色为#000000，距离视频上传区下边缘10px;最上面一行是板块标题"视频处理列表"，字体大小为20px，文字颜色为#FFFFFF，"视频处理列表"下面提示总处理的文件数量，字体大小为15px，文字靠左对齐，右边显示处理中的数量和已完成的数量，字体大小为30px；下面显示列表标题，分别为"文件名"、"处理状态"、"当前进度"，字体大小为15px；列表标题下面采用滚动框显示处理中的文件列表（包含文件名、处理状态，以及进度条），滚动框的尺寸为595*316px，背景色为#1F1F1F;滚动框的下方为"开始处理"按钮，按钮背景色#00DD65，大小为125*51px，圆角30px
最右侧为字幕编辑板块：
1、板块最上方为GPU状态栏及头像区：尺寸为612*68px，背景色为#000000，距离中间视频处理板块右边缘27px，距离上边缘12px；内部最左侧是显示GPU是否可用的区域，该区域大小为245*39px,背景色为#1F1F1F，状态显示文字大小为15px，当检测到GPU可用时显示#00DD65，否则显示#FF0000;右侧为用户头像及用户名，头像区域的为64*64px的圆形，背景色为#1F1F1F
2、板块下方为字幕编辑区：尺寸为612*857px，背景色为#000000，距离上方GPU状态栏及头像区下边缘11px，该区域最上方为字幕编辑区的标题，分别为"时间戳"、"操作"、"字幕文本"，字体大小为18px，文字颜色为#FFFFFF；下方为字幕编辑列表，以滚动框显示每条字幕，滚动框尺寸为612*710px，背景色为#000000；滚动框内部每条字幕区尺寸为581*92px，背景色为#1F1F1F，被标记的字幕条背景色将变成#FF0000，以提示用户需要对其进行修改，每个字幕条包含三个部分，最左侧以上下布局的可编辑的文本框分别显示字幕的本条字幕的开始和结束时间，文本框大小为134*34px，背景色为#000000，内部文字大小为14px，居中对齐;中间为操作按钮，按钮大小为96*34px，圆角30px，按钮背景色为#00DD65，按钮文字大小为15px，按钮文字颜色为#000000；右侧为可编辑字幕文本框，尺寸为300*92px，背景色为#000000，内部文字大小为15px，居中对齐；
3、滚动框下方靠右侧为"保存字幕"及"合并音频"按钮，按钮大小为115*39px，圆角30px，按钮背景色为#00DD65，按钮文字大小为20px，按钮文字颜色为#000000；靠左侧为"是否启用精修"的复选框，复选框大小为18*18px，复选框背景色为#1F1F1F，选中时背景色为#00DD65，复选框选中时，复选框内部为对勾图标，未选中时为空心图标

请按以上要求基于python+pyside6生成完整的UI界面代码，并确保代码可以用于实际开发














