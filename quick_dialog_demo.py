#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速弹窗演示 - 展示优化后的精致效果
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def quick_demo():
    """快速演示优化后的弹窗"""
    try:
        from PySide6.QtWidgets import QApplication
        from ui.fliptalk_ui import AppleMessageBox, FlipTalkMainWindow
        
        # 创建应用程序
        app = QApplication(sys.argv)
        
        # 创建主窗口作为父窗口
        main_window = FlipTalkMainWindow()
        
        print("🎨 展示优化后的精致弹窗效果")
        print("=" * 40)
        
        # 演示信息提示框
        print("1. 信息提示框 (精致版)")
        AppleMessageBox.show_info(
            main_window, "FlipTalk AI 优化完成", 
            "弹窗设计已成功优化！\n\n"
            "现在弹窗更加精致美观：\n"
            "• 合适的尺寸比例\n"
            "• 精致的按钮设计\n"
            "• 优雅的间距布局"
        )
        
        # 演示确认对话框
        print("2. 确认对话框 (精致版)")
        result = AppleMessageBox.show_question(
            main_window, "应用优化",
            "您觉得新的弹窗设计如何？\n\n"
            "相比之前的版本，现在的弹窗：\n"
            "• 尺寸更合理 (280-360px)\n"
            "• 按钮更精致 (32px高度)\n"
            "• 布局更紧凑",
            "很好", "还行"
        )
        
        if result:
            # 演示成功提示框
            print("3. 成功提示框 (精致版)")
            AppleMessageBox.show_success(
                main_window, "优化成功",
                "感谢您的反馈！\n\n"
                "FlipTalk AI 的弹窗设计\n"
                "已达到精致美观的效果。"
            )
        else:
            # 演示警告提示框
            print("3. 反馈提示框 (精致版)")
            AppleMessageBox.show_info(
                main_window, "感谢反馈",
                "感谢您的反馈！\n\n"
                "我们会继续优化用户体验。"
            )
        
        print("✅ 弹窗演示完成")
        return 0
        
    except Exception as e:
        print(f"❌ 演示失败: {e}")
        return 1

def main():
    """主函数"""
    print("🚀 FlipTalk AI 精致弹窗快速演示")
    print("=" * 50)
    
    print("📏 优化对比:")
    print("弹窗宽度: 320-480px → 280-360px (减少25%)")
    print("按钮高度: 44px → 32px (减少27%)")
    print("圆角半径: 14px → 12px (更精致)")
    print("整体效果: 更紧凑、更美观、更现代")
    
    print("\n" + "=" * 50)
    
    # 运行演示
    result = quick_demo()
    
    print("\n" + "=" * 50)
    print("📋 优化总结:")
    print("✅ 弹窗尺寸更加合理，不会显得过大")
    print("✅ 按钮高度适中，符合现代UI设计")
    print("✅ 间距布局更加紧凑精致")
    print("✅ 字体大小层次分明，易于阅读")
    print("✅ 整体视觉协调美观，用户体验佳")
    
    return result

if __name__ == "__main__":
    sys.exit(main())
