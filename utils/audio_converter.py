#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
音频格式转换工具
将各种音频格式转换为标准WAV格式，确保播放兼容性
"""

import os
import subprocess
import tempfile
from pathlib import Path

def check_file_format(file_path):
    """检查音频文件的实际格式"""
    if not os.path.exists(file_path):
        return None, "文件不存在"
    
    try:
        with open(file_path, 'rb') as f:
            header = f.read(12)
            
        if len(header) < 4:
            return None, "文件头部不完整"
        
        # 检查常见音频格式
        if header[0:4] == b'RIFF' and len(header) >= 12 and header[8:12] == b'WAVE':
            return "wav", "标准WAV格式"
        elif header[0:2] == b'\xff\xfb' or header[0:2] == b'\xff\xf3' or header[0:2] == b'\xff\xf2':
            return "mp3", "MP3格式"
        elif header[0:4] == b'OggS':
            return "ogg", "OGG格式"
        elif header[0:4] == b'\x1a\x45\xdf\xa3':
            return "webm", "WebM格式"
        else:
            return "unknown", f"未知格式，头部: {header[:8].hex()}"
            
    except Exception as e:
        return None, f"检查失败: {e}"

def convert_to_wav(input_path, output_path=None, force=False):
    """
    将音频文件转换为WAV格式
    
    参数:
        input_path: 输入文件路径
        output_path: 输出文件路径（默认为输入文件路径，扩展名改为.wav）
        force: 是否强制转换（即使已经是WAV格式）
    
    返回:
        (bool, str): (是否成功, 消息)
    """
    if not os.path.exists(input_path):
        return False, "输入文件不存在"
    
    if output_path is None:
        output_path = str(Path(input_path).with_suffix('.wav'))
    
    # 检查输入文件格式
    format_type, format_info = check_file_format(input_path)
    print(f"🔍 检测到格式: {format_type} - {format_info}")
    
    if format_type == "wav" and not force:
        if input_path != output_path:
            # 复制文件
            import shutil
            shutil.copy2(input_path, output_path)
            return True, "已是WAV格式，已复制到目标位置"
        else:
            return True, "已是WAV格式，无需转换"
    
    # 尝试使用ffmpeg转换
    success, message = _convert_with_ffmpeg(input_path, output_path)
    if success:
        return True, message
    
    # 如果ffmpeg失败，尝试其他方法
    print("⚠️ ffmpeg转换失败，尝试其他方法...")
    
    # 尝试使用pydub（如果可用）
    try:
        from pydub import AudioSegment
        audio = AudioSegment.from_file(input_path)
        audio.export(output_path, format="wav")
        return True, "使用pydub转换成功"
    except ImportError:
        print("⚠️ pydub未安装")
    except Exception as e:
        print(f"⚠️ pydub转换失败: {e}")
    
    return False, "所有转换方法都失败了"

def _convert_with_ffmpeg(input_path, output_path):
    """使用ffmpeg进行转换"""
    try:
        cmd = [
            'ffmpeg', '-y',  # -y 覆盖输出文件
            '-i', input_path,  # 输入文件
            '-acodec', 'pcm_s16le',  # 音频编码为16位PCM
            '-ar', '22050',  # 采样率22050Hz
            '-ac', '1',  # 单声道
            output_path  # 输出WAV文件
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            # 验证输出文件
            if os.path.exists(output_path) and os.path.getsize(output_path) > 1000:
                return True, "ffmpeg转换成功"
            else:
                return False, "转换后文件无效"
        else:
            return False, f"ffmpeg转换失败: {result.stderr}"
            
    except FileNotFoundError:
        return False, "未找到ffmpeg程序"
    except Exception as e:
        return False, f"ffmpeg转换出错: {e}"

def fix_audio_file(file_path):
    """
    修复音频文件格式问题
    
    参数:
        file_path: 音频文件路径
    
    返回:
        (bool, str): (是否成功, 消息)
    """
    if not file_path.endswith('.wav'):
        return False, "只能修复.wav扩展名的文件"
    
    # 检查实际格式
    format_type, format_info = check_file_format(file_path)
    
    if format_type == "wav":
        return True, "文件格式正常，无需修复"
    
    print(f"🔧 检测到格式不匹配: {format_info}")
    print(f"🔄 开始修复文件: {os.path.basename(file_path)}")
    
    # 创建备份
    backup_path = file_path + ".backup"
    try:
        import shutil
        shutil.copy2(file_path, backup_path)
        print(f"📦 已创建备份: {os.path.basename(backup_path)}")
    except Exception as e:
        print(f"⚠️ 创建备份失败: {e}")
    
    # 转换文件
    success, message = convert_to_wav(file_path, file_path, force=True)
    
    if success:
        print(f"✅ 修复成功: {message}")
        # 删除备份
        try:
            if os.path.exists(backup_path):
                os.remove(backup_path)
                print("🗑️ 已删除备份文件")
        except:
            pass
        return True, message
    else:
        print(f"❌ 修复失败: {message}")
        # 恢复备份
        try:
            if os.path.exists(backup_path):
                shutil.copy2(backup_path, file_path)
                os.remove(backup_path)
                print("🔄 已恢复原文件")
        except Exception as e:
            print(f"⚠️ 恢复备份失败: {e}")
        return False, message

if __name__ == "__main__":
    # 测试代码
    import sys
    if len(sys.argv) > 1:
        file_path = sys.argv[1]
        print(f"🔧 修复音频文件: {file_path}")
        success, message = fix_audio_file(file_path)
        print(f"结果: {'✅ 成功' if success else '❌ 失败'} - {message}")
    else:
        print("用法: python audio_converter.py <音频文件路径>") 