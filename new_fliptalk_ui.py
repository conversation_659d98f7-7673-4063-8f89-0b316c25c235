#!/usr/bin/env python
# -*- coding: utf-8 -*-

# 修复reset_right_area_layout方法

def reset_right_area_layout(self):
    """重置右侧区域布局"""
    try:
        # 清空右侧区域布局中的所有组件
        for i in reversed(range(self.right_area_layout.count())): 
            widget = self.right_area_layout.itemAt(i).widget()
            if widget:
                widget.hide()
                self.right_area_layout.removeWidget(widget)
        
        # 添加主内容区域到右侧布局（包含了左侧容器和字幕编辑区域）
        if hasattr(self, 'main_content') and self.main_content:
            self.right_area_layout.addWidget(self.main_content)
            self.main_content.show()
            
            # 确保内部组件也显示
            if hasattr(self, 'upload_area') and self.upload_area:
                self.upload_area.show()
            if hasattr(self, 'parameter_panel') and self.parameter_panel:
                self.parameter_panel.show()
            if hasattr(self, 'subtitle_area') and self.subtitle_area:
                self.subtitle_area.show()
            
            print("首页布局已重置")
        else:
            print("错误：主内容区域不存在")
    except Exception as e:
        print(f"重置右侧区域布局出错: {e}") 