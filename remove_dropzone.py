import re

def remove_dropzone_class():
    # 读取原始文件
    with open('ui/fliptalk_ui.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 找到DropZone类的开始和结束位置
    class_start_pattern = r'class DropZone\(QWidget\):'
    class_start_match = re.search(class_start_pattern, content)
    
    if class_start_match:
        class_start_pos = class_start_match.start()
        
        # 找到下一个类定义位置
        next_class_pattern = r'class \w+\('
        next_class_matches = list(re.finditer(next_class_pattern, content[class_start_pos+1:]))
        
        if next_class_matches:
            class_end_pos = class_start_pos + 1 + next_class_matches[0].start()
            
            # 从内容中删除DropZone类
            content = content[:class_start_pos] + content[class_end_pos:]
            print("成功删除DropZone类")
            
            # 检查并修改closeEvent方法中的引用
            close_event_pattern = r'def closeEvent\(self, event\):.*?super\(\)\.closeEvent\(event\)'
            close_event_match = re.search(close_event_pattern, content, re.DOTALL)
            
            if close_event_match:
                close_event_text = close_event_match.group(0)
                
                # 如果closeEvent方法中有DropZone相关代码，删除它
                if 'dropzone' in close_event_text.lower():
                    new_close_event = '''def closeEvent(self, event):
        """窗口关闭事件处理，确保所有资源被释放"""
        print("窗口正在关闭，清理资源...")
        
        # 调用父类的关闭事件处理
        super().closeEvent(event)'''
                    
                    content = content.replace(close_event_match.group(0), new_close_event)
                    print("修改了closeEvent方法，移除了DropZone相关代码")
        else:
            print("找不到DropZone类的结束位置")
    else:
        print("找不到DropZone类的定义，可能已经被删除")
    
    # 保存修改后的内容
    with open('ui/fliptalk_ui.py', 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("文件保存成功")

if __name__ == "__main__":
    remove_dropzone_class() 