#!/usr/bin/env python
# -*- coding: utf-8 -*-

# 读取正确的方法实现
with open('new_fliptalk_ui.py', 'r', encoding='utf-8') as f:
    new_method = f.read().split('def reset_right_area_layout(self):')[1]

# 读取原文件内容
with open('ui/fliptalk_ui.py', 'r', encoding='utf-8') as f:
    content = f.read()

# 查找方法的起始位置
start_idx = content.find('def reset_right_area_layout(self):')
if start_idx == -1:
    print("没有找到reset_right_area_layout方法！")
    exit(1)

# 查找方法的结束位置（下一个def或class的位置）
end_idx = content.find('def ', start_idx + 10)
if end_idx == -1:
    end_idx = content.find('class ', start_idx + 10)
    if end_idx == -1:
        print("无法确定方法的结束位置！")
        exit(1)

# 替换方法
new_content = content[:start_idx] + 'def reset_right_area_layout(self):' + new_method + content[end_idx:]

# 写入修改后的内容
with open('ui/fliptalk_ui.py', 'w', encoding='utf-8') as f:
    f.write(new_content)

print("reset_right_area_layout方法替换完成！") 