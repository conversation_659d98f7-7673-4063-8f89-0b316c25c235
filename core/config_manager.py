#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置管理器
负责保存和加载应用程序的配置信息，包括API密钥、TTS设置等
"""

import os
import json
import tempfile
from typing import Dict, Any, Optional
from pathlib import Path


class ConfigManager:
    """配置管理器类"""
    
    def __init__(self, config_file: str = None):
        """初始化配置管理器"""
        if config_file is None:
            # 默认配置文件路径
            app_data_dir = self._get_app_data_dir()
            self.config_file = os.path.join(app_data_dir, "fliptalk_config.json")
        else:
            self.config_file = config_file
        
        # 确保配置目录存在
        os.makedirs(os.path.dirname(self.config_file), exist_ok=True)
        
        # 默认配置
        self.default_config = {
            "api_keys": {
                "openai_api_key": "",
                "openai_base_url": "https://api.openai.com",
                "azure_tts_key": "",
                "azure_tts_region": "eastus",
                "deepl_api_key": "",
                "google_api_key": ""
            },
            "tts_settings": {
                "default_engine": "edge_tts",
                "default_language": "zh-CN",
                "default_voice": "zh-CN-XiaoxiaoNeural",
                "default_speed": 1.0,
                "cache_enabled": True,
                "output_format": "wav"
            },
            "ui_settings": {
                "theme": "dark",
                "language": "zh-CN",
                "auto_save": True
            },
            "app_settings": {
                "output_directory": "",
                "temp_directory": "",
                "max_concurrent_tasks": 4
            }
        }
        
        # 加载配置
        self.config = self.load_config()
    
    def _get_app_data_dir(self) -> str:
        """获取应用程序数据目录"""
        if os.name == 'nt':  # Windows
            app_data = os.environ.get('APPDATA', os.path.expanduser('~'))
            return os.path.join(app_data, 'FlipTalk AI')
        else:  # macOS/Linux
            return os.path.expanduser('~/.fliptalk')
    
    def load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    loaded_config = json.load(f)
                
                # 合并默认配置和加载的配置
                config = self.default_config.copy()
                self._deep_update(config, loaded_config)
                return config
            else:
                # 如果配置文件不存在，返回默认配置
                return self.default_config.copy()
        
        except Exception as e:
            print(f"加载配置文件失败: {e}")
            return self.default_config.copy()
    
    def save_config(self) -> bool:
        """保存配置到文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=2, ensure_ascii=False)
            return True
        except Exception as e:
            print(f"保存配置文件失败: {e}")
            return False
    
    def get(self, key_path: str, default: Any = None) -> Any:
        """
        获取配置值
        key_path: 用点分隔的键路径，如 "api_keys.azure_tts_key"
        """
        try:
            keys = key_path.split('.')
            value = self.config
            for key in keys:
                value = value[key]
            return value
        except (KeyError, TypeError):
            return default
    
    def set(self, key_path: str, value: Any) -> None:
        """
        设置配置值
        key_path: 用点分隔的键路径，如 "api_keys.azure_tts_key"
        """
        keys = key_path.split('.')
        config = self.config
        
        # 导航到最后一级的父级
        for key in keys[:-1]:
            if key not in config:
                config[key] = {}
            config = config[key]
        
        # 设置最后一级的值
        config[keys[-1]] = value
    
    def get_api_config(self) -> Dict[str, str]:
        """获取API配置，用于TTS引擎初始化"""
        return {
            "azure_api_key": self.get("api_keys.azure_tts_key", ""),
            "azure_region": self.get("api_keys.azure_tts_region", "eastus"),
            "openai_api_key": self.get("api_keys.openai_api_key", ""),
            "openai_base_url": self.get("api_keys.openai_base_url", "https://api.openai.com"),
        }
    
    def get_tts_config(self) -> Dict[str, Any]:
        """获取TTS配置"""
        api_config = self.get_api_config()
        tts_settings = self.get("tts_settings", {})
        
        return {
            "azure_api_key": api_config.get("azure_api_key", ""),
            "azure_region": api_config.get("azure_region", "eastus"),
            "default_engine": tts_settings.get("default_engine", "edge_tts"),
            "default_language": tts_settings.get("default_language", "zh-CN"),
            "default_voice": tts_settings.get("default_voice", "zh-CN-XiaoxiaoNeural"),
            "default_speed": tts_settings.get("default_speed", 1.0),
            "cache_enabled": tts_settings.get("cache_enabled", True),
            "output_format": tts_settings.get("output_format", "wav"),
            "fast_mode": False,  # 禁用快速模式以确保加载声音列表
            "use_cache": True,
            "skip_voice_list": False  # 确保加载声音列表
        }
    
    def update_api_key(self, service: str, key: str, value: str = None) -> bool:
        """
        更新API密钥
        service: 服务名称 (azure_tts, openai, deepl, google)
        key: 密钥类型 (key, region, base_url等)
        value: 密钥值
        """
        if service == "azure_tts":
            if key == "key":
                self.set("api_keys.azure_tts_key", value or "")
            elif key == "region":
                self.set("api_keys.azure_tts_region", value or "eastus")
        elif service == "openai":
            if key == "key":
                self.set("api_keys.openai_api_key", value or "")
            elif key == "base_url":
                self.set("api_keys.openai_base_url", value or "https://api.openai.com")
        elif service == "deepl":
            self.set("api_keys.deepl_api_key", value or "")
        elif service == "google":
            self.set("api_keys.google_api_key", value or "")
        
        return self.save_config()
    
    def _deep_update(self, base_dict: Dict, update_dict: Dict) -> None:
        """深度更新字典"""
        for key, value in update_dict.items():
            if key in base_dict and isinstance(base_dict[key], dict) and isinstance(value, dict):
                self._deep_update(base_dict[key], value)
            else:
                base_dict[key] = value
    
    def reset_to_defaults(self) -> bool:
        """重置为默认配置"""
        self.config = self.default_config.copy()
        return self.save_config()
    
    def export_config(self, export_path: str) -> bool:
        """导出配置到指定路径"""
        try:
            with open(export_path, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=2, ensure_ascii=False)
            return True
        except Exception as e:
            print(f"导出配置失败: {e}")
            return False
    
    def import_config(self, import_path: str) -> bool:
        """从指定路径导入配置"""
        try:
            with open(import_path, 'r', encoding='utf-8') as f:
                imported_config = json.load(f)
            
            # 验证配置格式
            if self._validate_config(imported_config):
                self.config = imported_config
                return self.save_config()
            else:
                print("导入的配置文件格式无效")
                return False
                
        except Exception as e:
            print(f"导入配置失败: {e}")
            return False
    
    def _validate_config(self, config: Dict) -> bool:
        """验证配置格式"""
        required_sections = ["api_keys", "tts_settings", "ui_settings", "app_settings"]
        return all(section in config for section in required_sections)


# 全局配置管理器实例
_config_manager = None

def get_config_manager() -> ConfigManager:
    """获取全局配置管理器实例"""
    global _config_manager
    if _config_manager is None:
        _config_manager = ConfigManager()
    return _config_manager 