#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FlipTalk AI - 插件管理器
负责插件的注册、发现和生命周期管理
"""

import os
import sys
import importlib
import importlib.util
import inspect
from typing import Dict, Type, List, Any
from pathlib import Path

from .interfaces import IPlugin, IAudioExtractor, IVoiceSeparator, IAsrEngine, ITranslator, ITtsEngine, IAudioProcessor, IVideoSynthesizer


class PluginManager:
    """
    插件管理器
    负责插件的注册、发现、加载和管理
    """
    
    def __init__(self):
        self._plugins: Dict[Type, Dict[str, IPlugin]] = {
            IAudioExtractor: {},
            IVoiceSeparator: {},
            IAsrEngine: {},
            ITranslator: {},
            ITtsEngine: {},
            IAudioProcessor: {},
            IVideoSynthesizer: {}
        }
        self._plugin_configs: Dict[str, Dict[str, Any]] = {}
    
    def register_plugin(self, plugin_type: Type[IPlugin], plugin_instance: IPlugin) -> bool:
        """
        注册插件实例
        
        Args:
            plugin_type: 插件类型接口
            plugin_instance: 插件实例
            
        Returns:
            bool: 注册是否成功
        """
        try:
            if plugin_type not in self._plugins:
                print(f"警告：未知的插件类型 {plugin_type}")
                return False
            
            plugin_name = plugin_instance.get_name()
            if plugin_name in self._plugins[plugin_type]:
                print(f"警告：插件 {plugin_name} 已存在，将覆盖原插件")
            
            self._plugins[plugin_type][plugin_name] = plugin_instance
            print(f"插件注册成功：{plugin_name} ({plugin_type.__name__})")
            return True
            
        except Exception as e:
            print(f"插件注册失败：{e}")
            return False
    
    def get_plugin(self, plugin_type: Type[IPlugin], plugin_name: str = None) -> IPlugin:
        """
        获取插件实例
        
        Args:
            plugin_type: 插件类型接口
            plugin_name: 插件名称，如果为None则返回第一个可用插件
            
        Returns:
            IPlugin: 插件实例，如果未找到则返回None
        """
        if plugin_type not in self._plugins:
            return None
        
        plugins = self._plugins[plugin_type]
        if not plugins:
            return None
        
        if plugin_name is None:
            # 返回第一个可用插件
            return next(iter(plugins.values()))
        
        return plugins.get(plugin_name)
    
    def get_available_plugins(self, plugin_type: Type[IPlugin]) -> List[str]:
        """
        获取指定类型的可用插件列表
        
        Args:
            plugin_type: 插件类型接口
            
        Returns:
            List[str]: 插件名称列表
        """
        if plugin_type not in self._plugins:
            return []
        
        return list(self._plugins[plugin_type].keys())
    
    def discover_plugins(self, plugins_dir: str = "plugins") -> int:
        """
        自动发现并加载插件
        
        Args:
            plugins_dir: 插件目录路径
            
        Returns:
            int: 成功加载的插件数量
        """
        plugins_path = Path(plugins_dir)
        if not plugins_path.exists():
            print(f"插件目录不存在：{plugins_path}")
            return 0
        
        # 将插件目录添加到Python路径
        if str(plugins_path.parent) not in sys.path:
            sys.path.insert(0, str(plugins_path.parent))
        
        loaded_count = 0
        
        # 遍历插件目录
        for plugin_dir in plugins_path.iterdir():
            if plugin_dir.is_dir() and not plugin_dir.name.startswith('.'):
                loaded_count += self._load_plugin_from_directory(plugin_dir)
        
        print(f"插件发现完成，共加载 {loaded_count} 个插件")
        return loaded_count
    
    def _load_plugin_from_directory(self, plugin_dir: Path) -> int:
        """
        从指定目录加载插件
        
        Args:
            plugin_dir: 插件目录路径
            
        Returns:
            int: 加载的插件数量
        """
        try:
            # 查找插件主文件
            main_file = plugin_dir / "plugin.py"
            if not main_file.exists():
                # 如果没有plugin.py，尝试查找与目录同名的.py文件
                main_file = plugin_dir / f"{plugin_dir.name}.py"
                if not main_file.exists():
                    print(f"在 {plugin_dir} 中未找到插件主文件")
                    return 0
            
            # 动态导入插件模块
            module_name = f"plugins.{plugin_dir.name}.{main_file.stem}"
            spec = importlib.util.spec_from_file_location(module_name, main_file)
            module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(module)
            
            # 查找插件类
            loaded_count = 0
            for name, obj in inspect.getmembers(module, inspect.isclass):
                if self._is_plugin_class(obj):
                    plugin_instance = obj()
                    plugin_type = self._get_plugin_type(obj)
                    if plugin_type and self.register_plugin(plugin_type, plugin_instance):
                        loaded_count += 1
            
            return loaded_count
            
        except Exception as e:
            print(f"加载插件失败 {plugin_dir}: {e}")
            return 0
    
    def _is_plugin_class(self, cls) -> bool:
        """
        判断是否为插件类
        
        Args:
            cls: 要检查的类
            
        Returns:
            bool: 是否为插件类
        """
        if not inspect.isclass(cls):
            return False
        
        # 检查是否继承自IPlugin
        if not issubclass(cls, IPlugin):
            return False
        
        # 排除接口类本身
        if cls in [IPlugin, IAudioExtractor, IVoiceSeparator, IAsrEngine, 
                   ITranslator, ITtsEngine, IAudioProcessor, IVideoSynthesizer]:
            return False
        
        # 检查是否为抽象类
        if inspect.isabstract(cls):
            return False
        
        return True
    
    def _get_plugin_type(self, cls) -> Type[IPlugin]:
        """
        获取插件的类型接口
        
        Args:
            cls: 插件类
            
        Returns:
            Type[IPlugin]: 插件类型接口
        """
        for plugin_type in self._plugins.keys():
            if issubclass(cls, plugin_type):
                return plugin_type
        return None
    
    def initialize_plugins(self, config: Dict[str, Any] = None) -> bool:
        """
        初始化所有已注册的插件
        
        Args:
            config: 配置参数字典
            
        Returns:
            bool: 是否全部初始化成功
        """
        if config is None:
            config = {}
        
        success = True
        for plugin_type, plugins in self._plugins.items():
            for plugin_name, plugin in plugins.items():
                try:
                    plugin_config = config.get(plugin_name, {})
                    if not plugin.initialize(plugin_config):
                        print(f"插件初始化失败：{plugin_name}")
                        success = False
                    else:
                        print(f"插件初始化成功：{plugin_name}")
                except Exception as e:
                    print(f"插件初始化异常：{plugin_name} - {e}")
                    success = False
        
        return success
    
    def cleanup_plugins(self) -> None:
        """
        清理所有插件资源
        """
        for plugin_type, plugins in self._plugins.items():
            for plugin_name, plugin in plugins.items():
                try:
                    plugin.cleanup()
                    print(f"插件清理完成：{plugin_name}")
                except Exception as e:
                    print(f"插件清理失败：{plugin_name} - {e}")
    
    def get_plugin_info(self) -> Dict[str, List[Dict[str, str]]]:
        """
        获取所有插件的信息
        
        Returns:
            Dict[str, List[Dict[str, str]]]: 按类型分组的插件信息
        """
        info = {}
        for plugin_type, plugins in self._plugins.items():
            type_name = plugin_type.__name__
            info[type_name] = []
            for plugin_name, plugin in plugins.items():
                plugin_info = {
                    'name': plugin.get_name(),
                    'version': plugin.get_version(),
                    'description': plugin.get_description()
                }
                info[type_name].append(plugin_info)
        
        return info


# 全局插件管理器实例
plugin_manager = PluginManager() 