#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FlipTalk AI - 插件接口定义
定义各功能模块的抽象接口，确保插件实现的一致性
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional
from pathlib import Path


class IPlugin(ABC):
    """
    插件基础接口
    所有插件都必须继承此接口
    """
    
    @abstractmethod
    def get_name(self) -> str:
        """获取插件名称"""
        pass
    
    @abstractmethod
    def get_version(self) -> str:
        """获取插件版本"""
        pass
    
    @abstractmethod
    def get_description(self) -> str:
        """获取插件描述"""
        pass
    
    @abstractmethod
    def initialize(self, config: Dict[str, Any]) -> bool:
        """
        初始化插件
        
        Args:
            config: 配置参数字典
            
        Returns:
            bool: 初始化是否成功
        """
        pass
    
    @abstractmethod
    def cleanup(self) -> None:
        """清理插件资源"""
        pass


class IAudioExtractor(IPlugin):
    """
    音频提取插件接口
    用于从视频文件中提取音频流
    """
    
    @abstractmethod
    def extract(self, video_path: str, output_dir: str = None) -> str:
        """
        从视频文件提取音频
        
        Args:
            video_path (str): 输入视频文件路径
            output_dir (str, optional): 输出目录，如果为None则使用默认目录
            
        Returns:
            str: 提取的音频文件路径
            
        Raises:
            FileNotFoundError: 视频文件不存在
            Exception: 提取过程中的其他错误
        """
        pass
    
    @abstractmethod
    def get_supported_formats(self) -> list:
        """
        获取支持的视频格式列表
        
        Returns:
            list: 支持的视频格式扩展名列表，如 ['.mp4', '.avi', '.mkv']
        """
        pass
    
    @abstractmethod
    def get_audio_info(self, video_path: str) -> Dict[str, Any]:
        """
        获取视频中音频流的信息
        
        Args:
            video_path (str): 视频文件路径
            
        Returns:
            Dict[str, Any]: 音频信息，包含采样率、声道数、时长等
        """
        pass


class IVoiceSeparator(IPlugin):
    """
    人声分离插件接口
    用于分离音频中的人声和背景音乐
    """
    
    @abstractmethod
    def separate(self, audio_path: str, output_dir: str = None, output_options: Dict[str, bool] = None) -> Dict[str, str]:
        """
        分离音频中的人声和背景音乐
        
        Args:
            audio_path (str): 输入音频文件路径
            output_dir (str, optional): 输出目录，如果为None则使用默认目录
            
        Returns:
            Dict[str, str]: 分离结果，包含 'vocals'（人声）和 'background'（背景音乐）文件路径
            
        Raises:
            FileNotFoundError: 音频文件不存在
            Exception: 分离过程中的其他错误
        """
        pass
    
    @abstractmethod
    def get_supported_formats(self) -> list:
        """
        获取支持的音频格式列表
        
        Returns:
            list: 支持的音频格式扩展名列表，如 ['.wav', '.mp3', '.flac']
        """
        pass
    
    @abstractmethod
    def get_quality_settings(self) -> Dict[str, Any]:
        """
        获取质量设置选项
        
        Returns:
            Dict[str, Any]: 包含可用的质量设置和参数
        """
        pass


class IAsrEngine(IPlugin):
    """
    语音识别引擎接口
    用于将音频转录为带时间戳的字幕
    """
    
    @abstractmethod
    def transcribe(self, audio_path: str, source_language: str = "auto") -> str:
        """
        将音频转录为字幕
        
        Args:
            audio_path (str): 音频文件路径
            source_language (str): 源语言，默认为"auto"自动识别
            
        Returns:
            str: SRT字幕文件路径
        """
        pass


class ITranslator(IPlugin):
    """
    翻译插件接口
    用于翻译字幕内容
    """
    
    @abstractmethod
    def translate(self, source_srt: str, target_language: str, output_path: str = None) -> str:
        """
        翻译字幕文件
        
        Args:
            source_srt (str): 源字幕文件路径
            target_language (str): 目标语言
            output_path (str, optional): 输出文件路径
            
        Returns:
            str: 翻译后的字幕文件路径
        """
        pass


class ITtsEngine(IPlugin):
    """
    文本转语音引擎接口
    用于生成配音音频
    """
    
    @abstractmethod
    def synthesize(self, text: str, voice: str, speed: float = 1.0, volume: float = 1.0, pitch: float = 0.0, output_path: str = None) -> str:
        """
        合成语音（增强版）
        
        Args:
            text: 要合成的文本
            voice: 声音ID
            speed: 语速倍数 (0.5-2.0)
            volume: 音量倍数 (0.1-2.0)
            pitch: 音调偏移 (-50 到 +50 Hz)
            output_path: 输出文件路径
            
        Returns:
            str: 生成的音频文件路径
            
        Raises:
            Exception: 合成失败时抛出异常
        """
        pass
    
    @abstractmethod
    def get_available_voices(self, language: str = None) -> Dict[str, str]:
        """
        获取可用声音列表
        
        Args:
            language: 语言代码，如果为None则返回所有语言的声音
            
        Returns:
            Dict[str, str]: 声音ID到显示名称的映射
        """
        pass
    
    @abstractmethod
    def get_supported_languages(self) -> Dict[str, str]:
        """
        获取支持的语言列表
        
        Returns:
            Dict[str, str]: 语言代码到语言名称的映射
        """
        pass
    
    @abstractmethod
    def preview_voice(self, text: str, voice: str, speed: float = 1.0, volume: float = 1.0, pitch: float = 0.0) -> str:
        """
        生成语音预览（增强版）
        
        Args:
            text: 预览文本
            voice: 声音ID
            speed: 语速倍数 (0.5-2.0)
            volume: 音量倍数 (0.1-2.0)
            pitch: 音调偏移 (-50 到 +50 Hz)
            
        Returns:
            str: 预览音频文件路径
            
        Raises:
            Exception: 预览生成失败时抛出异常
        """
        pass
    
    @abstractmethod
    def synthesize_batch(self, texts: list, voice: str, speed: float = 1.0, volume: float = 1.0, pitch: float = 0.0, output_dir: str = None) -> list:
        """
        批量语音合成（增强版）
        
        Args:
            texts: 文本列表
            voice: 声音ID
            speed: 语速倍数 (0.5-2.0)
            volume: 音量倍数 (0.1-2.0)
            pitch: 音调偏移 (-50 到 +50 Hz)
            output_dir: 输出目录
            
        Returns:
            list: 生成的音频文件路径列表（失败的项为None）
        """
        pass


class IAudioProcessor(IPlugin):
    """
    音频处理插件接口
    用于音频剪辑、合并等操作
    """
    
    @abstractmethod
    def trim_silence(self, audio_path: str, output_path: str = None) -> tuple:
        """
        去除音频首尾静音
        
        Args:
            audio_path (str): 输入音频文件路径
            output_path (str, optional): 输出文件路径
            
        Returns:
            tuple: (处理后的音频文件路径, 音频时长)
        """
        pass
    
    @abstractmethod
    def merge_audio_segments(self, segments: list, output_path: str) -> str:
        """
        合并音频段
        
        Args:
            segments (list): 音频段列表，每个元素包含文件路径和时间信息
            output_path (str): 输出文件路径
            
        Returns:
            str: 合并后的音频文件路径
        """
        pass


class IVideoSynthesizer(IPlugin):
    """
    视频合成插件接口
    用于将新的音频轨道与视频合成
    """
    
    @abstractmethod
    def combine(self, video_path: str, audio_path: str, output_path: str) -> str:
        """
        合成视频和音频
        
        Args:
            video_path (str): 视频文件路径
            audio_path (str): 音频文件路径
            output_path (str): 输出文件路径
            
        Returns:
            str: 合成后的视频文件路径
        """
        pass 