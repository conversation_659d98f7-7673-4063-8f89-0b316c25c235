#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
GPU加速配置指南模块
提供完整的GPU配置帮助内容，与CUDA_INSTALLATION_GUIDE.md保持一致
"""

class GPUHelpGuide:
    """GPU配置指南类"""
    
    @staticmethod
    def get_help_content_html(is_available: bool, gpu_name: str):
        """获取GPU帮助内容HTML格式
        
        Args:
            is_available: GPU是否可用
            gpu_name: GPU名称
            
        Returns:
            str: 格式化的HTML内容
        """
        if is_available:
            return GPUHelpGuide.get_gpu_available_guide()
        else:
            if "AMD" in gpu_name:
                return GPUHelpGuide.get_amd_gpu_guide()
            elif "Intel" in gpu_name or "集成" in gpu_name:
                return GPUHelpGuide.get_integrated_gpu_guide()
            elif "NVIDIA" in gpu_name:
                return GPUHelpGuide.get_cuda_configuration_guide()
            else:
                return GPUHelpGuide.get_no_gpu_guide()
    
    @staticmethod
    def get_cuda_configuration_guide():
        """获取CUDA配置指南 - GPU硬件检测到但AI加速不可用"""
        return """
        <h3 style="color: #F39C12; font-size: 20px; font-weight: bold; margin: 15px 0;">🔧 GPU硬件检测到，需要配置CUDA环境</h3>
        <p><strong>如果您的GPU状态栏显示"⚠️ GPU硬件可用，但AI加速不可用"，说明您需要配置CUDA环境才能在Demucs人声分离等AI功能中使用GPU加速。</strong></p>
        
        <h4 style="color: #2B9D7C; font-size: 20px; font-weight: bold; margin: 20px 0 12px 0;">🚨 重要说明</h4>
        <ul style="font-size: 13px; margin-left: 20px; line-height: 1.6;">
            <li style="margin-bottom: 8px; ">GPU加速可以将人声分离速度提升5-10倍</li>
            <li style="margin-bottom: 8px; ">没有GPU加速时，AI功能将使用CPU，速度较慢但仍可正常工作</li>
            <li style="margin-bottom: 8px; ">NVIDIA显卡支持最佳，AMD和Intel显卡支持有限</li>
        </ul>
        
        <h4 style="color: #2B9D7C; font-size: 18px; font-weight: bold; margin: 25px 0 12px 0;">📋 系统要求</h4>
        <h5 style="color: #F39C12; font-size: 17px; font-weight: bold; margin: 15px 0 8px 0;">🎯 NVIDIA GPU要求</h5>
        <ul style="font-size: 13px; margin-left: 20px; line-height: 1.6;">
            <li style="margin-bottom: 6px; ">NVIDIA GeForce GTX 1060 或更高性能显卡</li>
            <li style="margin-bottom: 6px; ">计算能力 3.5 或更高</li>
            <li style="margin-bottom: 6px; ">显存 4GB 或更大（推荐 8GB+）</li>
        </ul>
        
        <h5 style="color: #F39C12; font-size: 17px; font-weight: bold; margin: 15px 0 8px 0;">💻 软件要求</h5>
        <ul style="font-size: 13px; margin-left: 20px; line-height: 1.6;">
            <li style="margin-bottom: 6px; ">Windows 10/11 (64位) 或 Linux</li>
            <li style="margin-bottom: 6px; ">最新的NVIDIA显卡驱动程序</li>
            <li style="margin-bottom: 6px; ">Visual Studio 2019 或更新版本的 C++ 运行库</li>
        </ul>
        
        <h4 style="color: #2B9D7C; font-size: 18px; font-weight: bold; margin: 25px 0 12px 0;">🔧 安装步骤</h4>
        
        <h5 style="color: #34A085; font-size: 17px; font-weight: bold; margin: 18px 0 10px 0; background: rgba(52, 160, 133, 0.1); padding: 8px 12px; border-radius: 6px;">📥 步骤1: 更新NVIDIA驱动程序</h5>
        <ol style="font-size: 13px; margin-left: 20px; line-height: 1.7;">
            <li style="margin-bottom: 8px; ">访问 <a href="https://www.nvidia.com/drivers/" style="color: #2B9D7C; font-weight: bold;">NVIDIA官方驱动下载页面</a></li>
            <li style="margin-bottom: 8px; ">选择您的显卡型号并下载最新驱动</li>
            <li style="margin-bottom: 8px; ">安装并重启计算机</li>
        </ol>
        
        <h5 style="color: #34A085; font-size: 17px; font-weight: bold; margin: 18px 0 10px 0; background: rgba(52, 160, 133, 0.1); padding: 8px 12px; border-radius: 6px;">🛠️ 步骤2: 安装CUDA Toolkit</h5>
        <ol style="font-size: 13px; margin-left: 20px; line-height: 1.7;">
            <li style="margin-bottom: 8px; ">访问 <a href="https://developer.nvidia.com/cuda-downloads" style="color: #2B9D7C; font-weight: bold;">CUDA Toolkit下载页面</a></li>
            <li style="margin-bottom: 8px; ">推荐版本: <strong style="color: #F39C12; font-size: 15px;">CUDA 11.8</strong> (与PyTorch 2.0兼容)</li>
            <li style="margin-bottom: 8px; ">选择操作系统和安装类型</li>
            <li style="margin-bottom: 8px; ">下载并运行安装程序</li>
            <li style="margin-bottom: 8px; ">按默认设置安装即可</li>
        </ol>
        
        <h5 style="color: #34A085; font-size: 17px; font-weight: bold; margin: 18px 0 10px 0; background: rgba(52, 160, 133, 0.1); padding: 8px 12px; border-radius: 6px;">📦 步骤3: 安装cuDNN</h5>
        <ol style="font-size: 13px; margin-left: 20px; line-height: 1.7;">
            <li style="margin-bottom: 8px; ">访问 <a href="https://developer.nvidia.com/cudnn" style="color: #2B9D7C; font-weight: bold;">cuDNN下载页面</a> (需要注册NVIDIA账户)</li>
            <li style="margin-bottom: 8px; ">推荐版本: <strong style="color: #F39C12; font-size: 15px;">cuDNN 8.6</strong> (对应CUDA 11.8)</li>
            <li style="margin-bottom: 8px; ">下载ZIP文件</li>
            <li style="margin-bottom: 12px; ">解压并将文件复制到CUDA安装目录:<br/>
               将 <code style="background: #14161A; padding: 3px 6px; border-radius: 4px; font-size: 12px; color: #34A085;">bin</code>, 
               <code style="background: #14161A; padding: 3px 6px; border-radius: 4px; font-size: 12px; color: #34A085;">include</code>, 
               <code style="background: #14161A; padding: 3px 6px; border-radius: 4px; font-size: 12px; color: #34A085;">lib</code> 文件夹内容复制到<br/>
               <code style="background: #14161A; padding: 8px 10px; border-radius: 6px; color: #2B9D7C; font-size: 12px; display: inline-block; margin-top: 6px;">C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\</code></li>
        </ol>
        
        <h5 style="color: #34A085; font-size: 17px; font-weight: bold; margin: 18px 0 10px 0; background: rgba(52, 160, 133, 0.1); padding: 8px 12px; border-radius: 6px;">🐍 步骤4: 安装GPU版本的PyTorch</h5>
        <ol style="font-size: 13px; margin-left: 20px; line-height: 1.7;">
            <li style="margin-bottom: 8px; ">打开命令提示符或PowerShell</li>
            <li style="margin-bottom: 12px; ">卸载现有的PyTorch:<br/>
               <code style="background: #2C1810; padding: 10px 12px; border-radius: 6px; color: #F39C12; display: block; margin: 8px 0; font-size: 12px; border-left: 3px solid #F39C12;">pip uninstall torch torchaudio</code></li>
            <li style="margin-bottom: 12px; ">安装GPU版本的PyTorch:<br/>
               <code style="background: #0F1A15; padding: 10px 12px; border-radius: 6px; color: #2B9D7C; display: block; margin: 8px 0; font-size: 12px; border-left: 3px solid #2B9D7C;">pip install torch torchaudio --index-url https://download.pytorch.org/whl/cu118</code></li>
        </ol>
        
        <h5 style="color: #34A085; font-size: 17px; font-weight: bold; margin: 18px 0 10px 0; background: rgba(52, 160, 133, 0.1); padding: 8px 12px; border-radius: 6px;">✅ 步骤5: 验证安装</h5>
        <ol style="font-size: 13px; margin-left: 20px; line-height: 1.7;">
            <li style="margin-bottom: 8px; ">重启FlipTalk AI程序</li>
            <li style="margin-bottom: 8px; ">查看GPU状态栏，应该显示绿色的"GPU可用，显卡名称"</li>
            <li style="margin-bottom: 12px; ">或者运行以下Python代码验证:<br/>
               <code style="background: #0F1A15; padding: 12px; border-radius: 6px; color: #2B9D7C; display: block; margin: 8px 0; font-size: 12px; border-left: 3px solid #2B9D7C; line-height: 1.5;">
               import torch<br/>
               print("PyTorch版本:", torch.__version__)<br/>
               print("CUDA可用:", torch.cuda.is_available())<br/>
               print("GPU数量:", torch.cuda.device_count())
               </code></li>
        </ol>
        
        <h4 style="color: #2B9D7C; font-size: 18px; font-weight: bold; margin: 25px 0 12px 0;">📈 性能对比</h4>
        
        <h5 style="color: #F39C12; font-size: 17px; font-weight: bold; margin: 15px 0 8px 0;">⚡ 人声分离性能（5分钟音频文件）</h5>
        <ul style="font-size: 13px; margin-left: 20px; line-height: 1.6;">
            <li style="margin-bottom: 6px; "><strong style="color: #E74C3C;">CPU模式</strong>: 15-25分钟</li>
            <li style="margin-bottom: 6px; "><strong style="color: #2B9D7C;">GPU加速</strong>: 2-4分钟</li>
            <li style="margin-bottom: 6px; "><strong style="color: #F39C12;">提升倍数</strong>: 5-10倍</li>
        </ul>
        
        <h5 style="color: #F39C12; font-size: 17px; font-weight: bold; margin: 15px 0 8px 0;">🎮 不同显卡性能预期</h5>
        <ul style="font-size: 13px; margin-left: 20px; line-height: 1.6;">
            <li style="margin-bottom: 6px; "><strong style="color: #9B59B6;">RTX 4090</strong>: 最快，2分钟内完成</li>
            <li style="margin-bottom: 6px; "><strong style="color: #2B9D7C;">RTX 3080/3080Ti</strong>: 2-3分钟</li>
            <li style="margin-bottom: 6px; "><strong style="color: #3498DB;">RTX 3060/3070</strong>: 3-4分钟</li>
            <li style="margin-bottom: 6px; "><strong style="color: #95A5A6;">GTX 1660/2060</strong>: 4-6分钟</li>
        </ul>
        
        <h4 style="color: #E74C3C; font-size: 18px; font-weight: bold; margin: 25px 0 12px 0;">🔧 故障排除</h4>
        
        <h5 style="color: #F39C12; font-size: 17px; font-weight: bold; margin: 15px 0 8px 0;">⚠️ 常见错误及解决方案</h5>
        <ul style="font-size: 13px; margin-left: 20px; line-height: 1.7;">
            <li style="margin-bottom: 12px; "><strong style="color: #E74C3C; font-size: 14px;">"Could not load dynamic library 'cudart64_110.dll'"</strong><br/>
                <span style="color: #95A5A6;">原因:</span> CUDA版本不匹配 | <span style="color: #2B9D7C;">解决:</span> 确保安装CUDA 11.8版本</li>
            <li style="margin-bottom: 12px; "><strong style="color: #E74C3C; font-size: 14px;">"Could not load dynamic library 'cudnn64_8.dll'"</strong><br/>
                <span style="color: #95A5A6;">原因:</span> cuDNN未正确安装 | <span style="color: #2B9D7C;">解决:</span> 重新下载cuDNN 8.6并正确复制到CUDA目录</li>
            <li style="margin-bottom: 12px; "><strong style="color: #E74C3C; font-size: 14px;">GPU检测到但仍显示CPU模式</strong><br/>
                <span style="color: #95A5A6;">原因:</span> PyTorch没有使用GPU版本 | <span style="color: #2B9D7C;">解决:</span> 重新安装 PyTorch GPU版本</li>
            <li style="margin-bottom: 12px; "><strong style="color: #E74C3C; font-size: 14px;">内存不足错误</strong><br/>
                <span style="color: #95A5A6;">原因:</span> GPU显存不足 | <span style="color: #2B9D7C;">解决:</span> 关闭其他占用GPU的程序，或使用较小的音频文件</li>
        </ul>
        
        <p style="color: #9CA3AF; font-size: 12px; margin-top: 15px; padding: 10px; background: rgba(43, 157, 124, 0.1); border-radius: 6px;">
        💡 <strong>提示</strong>：如果配置过程中遇到问题，可以继续使用CPU模式，功能完全正常，只是速度较慢。建议按步骤重新配置或寻求技术支持。
        </p>
        """
    
    @staticmethod 
    def get_amd_gpu_guide():
        """获取AMD显卡指南"""
        return """
        <h3 style="color: #F39C12; font-size: 20px; font-weight: bold; margin: 15px 0;">⚠️ AMD显卡支持有限</h3>
        <p style="font-size: 14px; line-height: 1.6; margin-bottom: 20px;">检测到AMD显卡，但PyTorch/CUDA对AMD显卡支持有限。</p>
        
        <h4 style="color: #2B9D7C; font-size: 18px; font-weight: bold; margin: 20px 0 12px 0;">💡 建议方案：</h4>
        <ul style="font-size: 13px; margin-left: 20px; line-height: 1.7;">
            <li style="margin-bottom: 10px; "><strong style="color: #E74C3C;">继续使用CPU模式</strong> - 功能完整，速度较慢</li>
            <li style="margin-bottom: 10px; "><strong style="color: #2B9D7C;">升级至NVIDIA显卡</strong> - 获得最佳GPU加速</li>
            <li style="margin-bottom: 10px; "><strong style="color: #9B59B6;">使用云GPU服务</strong> - 临时解决方案</li>
        </ul>
        
        <h4 style="color: #2B9D7C; font-size: 18px; font-weight: bold; margin: 20px 0 12px 0;">🎯 推荐配置：</h4>
        <ul style="font-size: 13px; margin-left: 20px; line-height: 1.7;">
            <li style="margin-bottom: 8px; ">NVIDIA GeForce RTX 3060及以上型号</li>
            <li style="margin-bottom: 8px; ">显存 8GB 或更大</li>
            <li style="margin-bottom: 8px; ">支持CUDA 11.8的型号</li>
        </ul>
        
        <p style="color: #9CA3AF; font-size: 13px; margin-top: 20px; padding: 12px; background: rgba(249, 156, 18, 0.1); border-radius: 6px; line-height: 1.5;">
        💡 <strong>注意</strong>：AMD显卡可以尝试使用ROCm平台，但配置复杂且兼容性有限，建议优先考虑NVIDIA方案。
        </p>
        """
    
    @staticmethod
    def get_integrated_gpu_guide():
        """获取集成显卡指南"""
        return """
        <h3 style="color: #F39C12; font-size: 20px; font-weight: bold; margin: 15px 0;">💻 检测到集成显卡</h3>
        <p style="font-size: 14px; line-height: 1.6; margin-bottom: 20px;">当前系统使用集成显卡，性能不足以进行GPU加速。</p>
        
        <h4 style="color: #2B9D7C; font-size: 18px; font-weight: bold; margin: 20px 0 12px 0;">⬆️ 升级建议：</h4>
        <ul style="font-size: 13px; margin-left: 20px; line-height: 1.7;">
            <li style="margin-bottom: 10px; "><strong style="color: #2B9D7C;">添加独立显卡</strong> - NVIDIA GeForce RTX 3060或更高</li>
            <li style="margin-bottom: 10px; "><strong style="color: #9B59B6;">使用外接GPU</strong> - 通过Thunderbolt连接</li>
            <li style="margin-bottom: 10px; "><strong style="color: #E67E22;">云GPU服务</strong> - Google Colab, AWS等</li>
            <li style="margin-bottom: 10px; "><strong style="color: #34495E;">升级整机</strong> - 选择带独立显卡的配置</li>
        </ul>
        
        <h4 style="color: #2B9D7C; font-size: 18px; font-weight: bold; margin: 20px 0 12px 0;">📊 性能预期：</h4>
        <ul style="font-size: 13px; margin-left: 20px; line-height: 1.7;">
            <li style="margin-bottom: 8px; "><strong style="color: #E74C3C;">当前CPU模式</strong>: 15-30分钟（5分钟音频）</li>
            <li style="margin-bottom: 8px; "><strong style="color: #2B9D7C;">升级后GPU模式</strong>: 2-4分钟（提升5-10倍）</li>
        </ul>
        
        <p style="color: #9CA3AF; font-size: 13px; margin-top: 20px; padding: 12px; background: rgba(249, 156, 18, 0.1); border-radius: 6px; line-height: 1.5;">
        💡 <strong>提示</strong>：CPU模式下所有功能正常，处理大文件时耐心等待即可。如需频繁使用，建议考虑硬件升级。
        </p>
        """
    
    @staticmethod
    def get_gpu_available_guide():
        """获取GPU可用状态指南"""
        return """
        <h3 style="color: #2B9D7C; font-size: 20px; font-weight: bold; margin: 15px 0;">✅ GPU加速已启用</h3>
        <p style="font-size: 14px; line-height: 1.6; margin-bottom: 20px;"><strong>恭喜！您的GPU配置正确，AI加速功能已启用。</strong></p>
        
        <h4 style="color: #2B9D7C; font-size: 18px; font-weight: bold; margin: 20px 0 12px 0;">🚀 性能优化建议：</h4>
        <ul style="font-size: 13px; margin-left: 20px; line-height: 1.7;">
            <li style="margin-bottom: 10px; "><strong style="color: #3498DB;">监控GPU使用率</strong> - 任务管理器中查看GPU利用率</li>
            <li style="margin-bottom: 10px; "><strong style="color: #E67E22;">确保显存充足</strong> - 关闭其他占用GPU的程序</li>
            <li style="margin-bottom: 10px; "><strong style="color: #9B59B6;">处理大文件时</strong> - 分段处理避免显存不足</li>
            <li style="margin-bottom: 10px; "><strong style="color: #27AE60;">保持驱动更新</strong> - 定期更新NVIDIA驱动程序</li>
        </ul>
        
        <h4 style="color: #2B9D7C; font-size: 18px; font-weight: bold; margin: 20px 0 12px 0;">📊 当前性能预期：</h4>
        <ul style="font-size: 13px; margin-left: 20px; line-height: 1.7;">
            <li style="margin-bottom: 8px; ">人声分离: <strong style="color: #2B9D7C;">2-4分钟</strong> (5分钟音频)</li>
            <li style="margin-bottom: 8px; ">GPU使用率: <strong style="color: #27AE60;">80%+</strong> (正常工作时)</li>
            <li style="margin-bottom: 8px; ">显存使用: <strong style="color: #F39C12;">根据文件大小变化</strong></li>
        </ul>
        
        <h4 style="color: #2B9D7C; font-size: 18px; font-weight: bold; margin: 20px 0 12px 0;">🔧 监控工具：</h4>
        <ul style="font-size: 13px; margin-left: 20px; line-height: 1.7;">
            <li style="margin-bottom: 8px; "><strong style="color: #3498DB;">任务管理器</strong> - 查看GPU使用率和显存</li>
            <li style="margin-bottom: 8px; "><strong style="color: #27AE60;">nvidia-smi</strong> - 命令行监控工具</li>
            <li style="margin-bottom: 8px; "><strong style="color: #E67E22;">GPU-Z</strong> - 详细的GPU信息</li>
        </ul>
        
        <p style="color: #9CA3AF; font-size: 13px; margin-top: 20px; padding: 12px; background: rgba(43, 157, 124, 0.1); border-radius: 6px; line-height: 1.5;">
        💡 <strong>提示</strong>：如果处理过程中遇到显存不足错误，可以尝试处理较小的音频片段，或关闭其他GPU程序。
        </p>
        """
    
    @staticmethod
    def get_no_gpu_guide():
        """获取无GPU指南"""
        return """
        <h3 style="color: #F39C12; font-size: 20px; font-weight: bold; margin: 15px 0;">🔍 未检测到GPU</h3>
        <p style="font-size: 14px; line-height: 1.6; margin-bottom: 20px;">系统中未检测到可用的GPU硬件。</p>
        
        <h4 style="color: #2B9D7C; font-size: 18px; font-weight: bold; margin: 20px 0 12px 0;">🔧 排查步骤：</h4>
        <ol style="font-size: 13px; margin-left: 20px; line-height: 1.7;">
            <li style="margin-bottom: 10px; "><strong style="color: #3498DB;">检查硬件连接</strong> - 确保显卡正确安装到主板</li>
            <li style="margin-bottom: 10px; "><strong style="color: #E67E22;">检查电源连接</strong> - 独立显卡需要额外供电</li>
            <li style="margin-bottom: 10px; "><strong style="color: #27AE60;">更新驱动程序</strong> - 安装最新NVIDIA驱动</li>
            <li style="margin-bottom: 10px; "><strong style="color: #9B59B6;">检查BIOS设置</strong> - 启用PCIe插槽和独立显卡</li>
            <li style="margin-bottom: 10px; "><strong style="color: #E74C3C;">重启计算机</strong> - 完成硬件识别</li>
        </ol>
        
        <h4 style="color: #2B9D7C; font-size: 18px; font-weight: bold; margin: 20px 0 12px 0;">💡 解决方案：</h4>
        <ul style="font-size: 13px; margin-left: 20px; line-height: 1.7;">
            <li style="margin-bottom: 10px; "><strong style="color: #2B9D7C;">安装独立显卡</strong> - 推荐NVIDIA RTX系列</li>
            <li style="margin-bottom: 10px; "><strong style="color: #3498DB;">检查兼容性</strong> - 确保主板支持显卡</li>
            <li style="margin-bottom: 10px; "><strong style="color: #E67E22;">电源功率</strong> - 确保电源功率足够</li>
            <li style="margin-bottom: 10px; "><strong style="color: #95A5A6;">使用CPU模式</strong> - 所有功能正常，处理时间较长</li>
        </ul>
        
        <h4 style="color: #2B9D7C; font-size: 18px; font-weight: bold; margin: 20px 0 12px 0;">🎯 推荐配置：</h4>
        <ul style="font-size: 13px; margin-left: 20px; line-height: 1.7;">
            <li style="margin-bottom: 8px; "><strong style="color: #27AE60;">入门级</strong>: RTX 3060 (8GB显存)</li>
            <li style="margin-bottom: 8px; "><strong style="color: #3498DB;">性能级</strong>: RTX 3070/3080 (10-12GB显存)</li>
            <li style="margin-bottom: 8px; "><strong style="color: #9B59B6;">专业级</strong>: RTX 4080/4090 (16-24GB显存)</li>
        </ul>
        
        <p style="color: #9CA3AF; font-size: 13px; margin-top: 20px; padding: 12px; background: rgba(249, 156, 18, 0.1); border-radius: 6px; line-height: 1.5;">
        💡 <strong>提示</strong>：使用CPU模式时所有功能正常，只是处理时间较长。如需频繁使用AI功能，建议添加独立显卡。
        </p>
        """ 