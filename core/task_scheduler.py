#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FlipTalk AI - 任务调度器
负责管理和调度各种处理任务，支持多线程和进度报告
"""

import uuid
import time
from enum import Enum
from typing import Dict, Any, Callable, Optional
from dataclasses import dataclass, field
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, Future
from PySide6.QtCore import QObject, Signal, QThread, QRunnable, QThreadPool


class TaskStatus(Enum):
    """任务状态枚举"""
    PENDING = "待处理"      # 任务已创建，等待执行
    RUNNING = "处理中"      # 任务正在执行
    COMPLETED = "已完成"    # 任务成功完成
    FAILED = "失败"         # 任务执行失败
    CANCELLED = "已取消"    # 任务被用户取消


@dataclass
class Task:
    """任务数据结构"""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    name: str = ""
    description: str = ""
    status: TaskStatus = TaskStatus.PENDING
    progress: int = 0  # 进度百分比 0-100
    result: Any = None
    error: str = ""
    created_time: datetime = field(default_factory=datetime.now)
    started_time: Optional[datetime] = None
    completed_time: Optional[datetime] = None
    metadata: Dict[str, Any] = field(default_factory=dict)


class TaskWorker(QRunnable):
    """
    任务工作线程
    继承QRunnable以便在QThreadPool中执行
    """
    
    def __init__(self, task: Task, func: Callable, args: tuple = (), kwargs: dict = None):
        super().__init__()
        self.task = task
        self.func = func
        self.args = args
        self.kwargs = kwargs or {}
        self.signals = WorkerSignals()
        
        # 将进度回调函数添加到kwargs中
        if 'progress_callback' not in self.kwargs:
            self.kwargs['progress_callback'] = self.signals.progress.emit
    
    def run(self):
        """执行任务"""
        try:
            self.task.status = TaskStatus.RUNNING
            self.task.started_time = datetime.now()
            self.signals.started.emit(self.task.id)
            
            # 执行实际任务
            result = self.func(*self.args, **self.kwargs)
            
            # 任务成功完成
            self.task.status = TaskStatus.COMPLETED
            self.task.result = result
            self.task.progress = 100
            self.task.completed_time = datetime.now()
            self.signals.completed.emit(self.task.id, result)
            
        except Exception as e:
            # 任务执行失败
            self.task.status = TaskStatus.FAILED
            self.task.error = str(e)
            self.task.completed_time = datetime.now()
            self.signals.failed.emit(self.task.id, str(e))


class WorkerSignals(QObject):
    """工作线程信号"""
    started = Signal(str)  # 任务开始信号，参数：task_id
    progress = Signal(str, int)  # 进度更新信号，参数：task_id, progress
    completed = Signal(str, object)  # 任务完成信号，参数：task_id, result
    failed = Signal(str, str)  # 任务失败信号，参数：task_id, error


class TaskScheduler(QObject):
    """
    任务调度器
    管理任务队列，支持多线程执行和进度监控
    """
    
    # 定义信号
    task_added = Signal(str)  # 任务添加信号
    task_started = Signal(str)  # 任务开始信号
    task_progress = Signal(str, int)  # 任务进度更新信号
    task_completed = Signal(str, object)  # 任务完成信号
    task_failed = Signal(str, str)  # 任务失败信号
    
    def __init__(self, max_workers: int = 4):
        super().__init__()
        self.max_workers = max_workers
        self.thread_pool = QThreadPool()
        self.thread_pool.setMaxThreadCount(max_workers)
        
        # 任务存储
        self.tasks: Dict[str, Task] = {}
        self.active_workers: Dict[str, TaskWorker] = {}
        
        print(f"任务调度器初始化完成，最大并发数: {max_workers}")
    
    def add_task(self, name: str, func: Callable, args: tuple = (), 
                 kwargs: dict = None, description: str = "", metadata: dict = None) -> str:
        """
        添加新任务到队列
        
        Args:
            name: 任务名称
            func: 要执行的函数
            args: 函数参数元组
            kwargs: 函数关键字参数字典
            description: 任务描述
            metadata: 任务元数据
            
        Returns:
            str: 任务ID
        """
        task = Task(
            name=name,
            description=description,
            metadata=metadata or {}
        )
        
        self.tasks[task.id] = task
        
        # 创建工作线程
        worker = TaskWorker(task, func, args, kwargs)
        worker.signals.started.connect(self._on_task_started)
        worker.signals.progress.connect(self._on_task_progress)
        worker.signals.completed.connect(self._on_task_completed)
        worker.signals.failed.connect(self._on_task_failed)
        
        self.active_workers[task.id] = worker
        
        # 启动任务
        self.thread_pool.start(worker)
        
        # 发出任务添加信号
        self.task_added.emit(task.id)
        
        print(f"任务已添加到队列: {task.name} (ID: {task.id})")
        return task.id
    
    def get_task(self, task_id: str) -> Optional[Task]:
        """
        获取任务信息
        
        Args:
            task_id: 任务ID
            
        Returns:
            Task: 任务对象，如果不存在则返回None
        """
        return self.tasks.get(task_id)
    
    def get_all_tasks(self) -> Dict[str, Task]:
        """
        获取所有任务
        
        Returns:
            Dict[str, Task]: 所有任务字典
        """
        return self.tasks.copy()
    
    def get_tasks_by_status(self, status: TaskStatus) -> Dict[str, Task]:
        """
        根据状态获取任务
        
        Args:
            status: 任务状态
            
        Returns:
            Dict[str, Task]: 符合状态的任务字典
        """
        return {tid: task for tid, task in self.tasks.items() if task.status == status}
    
    def cancel_task(self, task_id: str) -> bool:
        """
        取消任务（尽力而为，不保证一定能取消）
        
        Args:
            task_id: 任务ID
            
        Returns:
            bool: 是否成功标记为取消
        """
        task = self.tasks.get(task_id)
        if task and task.status in [TaskStatus.PENDING, TaskStatus.RUNNING]:
            task.status = TaskStatus.CANCELLED
            task.completed_time = datetime.now()
            print(f"任务已标记为取消: {task.name} (ID: {task_id})")
            return True
        return False
    
    def clear_completed_tasks(self) -> int:
        """
        清理已完成的任务
        
        Returns:
            int: 清理的任务数量
        """
        completed_ids = [
            tid for tid, task in self.tasks.items() 
            if task.status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED]
        ]
        
        for task_id in completed_ids:
            del self.tasks[task_id]
            if task_id in self.active_workers:
                del self.active_workers[task_id]
        
        print(f"已清理 {len(completed_ids)} 个已完成任务")
        return len(completed_ids)
    
    def get_statistics(self) -> Dict[str, int]:
        """
        获取任务统计信息
        
        Returns:
            Dict[str, int]: 各状态任务数量统计
        """
        stats = {status.value: 0 for status in TaskStatus}
        for task in self.tasks.values():
            stats[task.status.value] += 1
        
        stats['总数'] = len(self.tasks)
        return stats
    
    def shutdown(self, wait: bool = True):
        """
        关闭任务调度器
        
        Args:
            wait: 是否等待所有任务完成
        """
        if wait:
            self.thread_pool.waitForDone()
        else:
            self.thread_pool.clear()
        
        print("任务调度器已关闭")
    
    def _on_task_started(self, task_id: str):
        """任务开始回调"""
        task = self.tasks.get(task_id)
        if task:
            print(f"任务开始执行: {task.name}")
            self.task_started.emit(task_id)
    
    def _on_task_progress(self, task_id: str, progress: int):
        """任务进度更新回调"""
        task = self.tasks.get(task_id)
        if task:
            task.progress = progress
            self.task_progress.emit(task_id, progress)
    
    def _on_task_completed(self, task_id: str, result: Any):
        """任务完成回调"""
        task = self.tasks.get(task_id)
        if task:
            print(f"任务完成: {task.name}")
            self.task_completed.emit(task_id, result)
            
            # 清理工作线程引用
            if task_id in self.active_workers:
                del self.active_workers[task_id]
    
    def _on_task_failed(self, task_id: str, error: str):
        """任务失败回调"""
        task = self.tasks.get(task_id)
        if task:
            print(f"任务失败: {task.name} - {error}")
            self.task_failed.emit(task_id, error)
            
            # 清理工作线程引用
            if task_id in self.active_workers:
                del self.active_workers[task_id]


# 全局任务调度器实例
task_scheduler = TaskScheduler() 