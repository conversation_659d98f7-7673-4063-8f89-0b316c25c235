#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FlipTalk AI - Typing兼容性模块
解决Python不同版本之间的类型注解兼容性问题
"""

import sys

# Self类型的兼容性处理
try:
    # Python 3.11+ 中，Self在typing模块中
    if sys.version_info >= (3, 11):
        from typing import Self
    else:
        # Python 3.11之前，从typing_extensions导入
        from typing_extensions import Self
except ImportError:
    # 如果都导入失败，创建一个占位符类
    class Self:
        """Self类型占位符，用于向前兼容"""
        def __class_getitem__(cls, item):
            return item

# 导出所有需要的类型
__all__ = ['Self'] 