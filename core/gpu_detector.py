#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GPU检测模块
用于检测系统中GPU的可用性和详细信息
"""

import sys
import platform
import subprocess
import threading
import time
from typing import Dict, Optional, Tuple
from PySide6.QtCore import QObject, Signal


class GPUDetector(QObject):
    """GPU检测器类"""
    
    # 信号定义
    gpu_status_updated = Signal(bool, str, str)  # (is_available, gpu_name, status_text)
    
    def __init__(self):
        super().__init__()
        self.is_checking = False
        self.last_check_time = 0
        self.check_interval = 30  # 检查间隔30秒
        
    def check_gpu_async(self):
        """异步检查GPU状态"""
        if self.is_checking:
            return
            
        # 如果距离上次检查时间太短，直接返回
        current_time = time.time()
        if current_time - self.last_check_time < self.check_interval:
            return
            
        self.is_checking = True
        thread = threading.Thread(target=self._check_gpu_thread, daemon=True)
        thread.start()
        
    def _check_gpu_thread(self):
        """GPU检查线程函数"""
        try:
            is_available, gpu_name, status_text = self._detect_gpu()
            self.gpu_status_updated.emit(is_available, gpu_name, status_text)
        except Exception as e:
            print(f"GPU检测出错: {e}")
            self.gpu_status_updated.emit(False, "未知", "GPU检测失败")
        finally:
            self.is_checking = False
            self.last_check_time = time.time()
    
    def _detect_gpu(self) -> Tuple[bool, str, str]:
        """
        检测GPU信息
        返回: (是否可用, GPU名称, 状态文本)
        """
        try:
            # 首先快速检测NVIDIA GPU
            nvidia_info = self._detect_nvidia_gpu()
            if nvidia_info[0] or "NVIDIA" in nvidia_info[1]:
                return nvidia_info
            
            # 检测AMD GPU
            amd_info = self._detect_amd_gpu()
            if amd_info[0]:
                return amd_info
            
            # 检测Intel集成显卡
            intel_info = self._detect_intel_gpu()
            if intel_info[0]:
                return intel_info
            
            # 没有检测到任何GPU
            return False, "未检测到", "GPU不可用"
            
        except Exception as e:
            print(f"GPU检测异常: {e}")
            return False, "检测失败", "GPU检测出错"
    
    def _detect_nvidia_gpu(self) -> Tuple[bool, str, str]:
        """检测NVIDIA GPU"""
        try:
            # 先快速检查nvidia-smi是否可用
            result = subprocess.run(
                ['nvidia-smi', '--query-gpu=name', '--format=csv,noheader,nounits'],
                capture_output=True,
                text=True,
                timeout=5
            )
            
            if result.returncode == 0 and result.stdout.strip():
                gpu_name = result.stdout.strip().split('\n')[0].strip()
                if gpu_name:
                    # 检查AI框架的GPU支持情况
                    ai_gpu_available, ai_detail = self._check_ai_framework_gpu_support()
                    
                    if ai_gpu_available:
                        # AI框架支持GPU，真正可用
                        status_text = f"GPU可用，{gpu_name}"
                        return True, gpu_name, status_text
                    else:
                        # 硬件存在但AI框架不支持
                        status_text = f"GPU硬件可用，但AI加速不可用 - {gpu_name}"
                        return False, gpu_name, status_text
                        
        except (subprocess.TimeoutExpired, subprocess.CalledProcessError, FileNotFoundError):
            pass
        except Exception as e:
            print(f"NVIDIA GPU检测失败: {e}")
        
        return False, "未检测到NVIDIA GPU", "NVIDIA GPU不可用"
    
    def _detect_amd_gpu(self) -> Tuple[bool, str, str]:
        """检测AMD GPU"""
        try:
            # Windows系统检测AMD GPU
            if platform.system() == "Windows":
                # 使用wmic命令查询显卡信息
                result = subprocess.run(
                    ['wmic', 'path', 'win32_VideoController', 'get', 'name', '/format:list'],
                    capture_output=True,
                    text=True,
                    timeout=10
                )
                
                if result.returncode == 0:
                    lines = result.stdout.strip().split('\n')
                    for line in lines:
                        if 'Name=' in line and 'AMD' in line.upper():
                            gpu_name = line.split('=')[1].strip()
                            # AMD GPU通常对AI框架支持有限，标记为不推荐
                            status_text = f"AMD GPU检测到，AI加速支持有限 - {gpu_name}"
                            return False, gpu_name, status_text
            
            # Linux系统检测AMD GPU
            elif platform.system() == "Linux":
                # 使用lspci命令
                result = subprocess.run(
                    ['lspci', '-nn'],
                    capture_output=True,
                    text=True,
                    timeout=10
                )
                
                if result.returncode == 0:
                    lines = result.stdout.split('\n')
                    for line in lines:
                        if 'VGA' in line and 'AMD' in line.upper():
                            # 提取GPU名称
                            parts = line.split(': ')
                            if len(parts) > 1:
                                gpu_name = parts[1].split('[')[0].strip()
                                # AMD GPU通常对AI框架支持有限，标记为不推荐
                                status_text = f"AMD GPU检测到，AI加速支持有限 - {gpu_name}"
                                return False, gpu_name, status_text
                                
        except (subprocess.TimeoutExpired, subprocess.CalledProcessError, FileNotFoundError):
            pass
        except Exception as e:
            print(f"AMD GPU检测失败: {e}")
        
        return False, "未检测到AMD GPU", "AMD GPU不可用"
    
    def _detect_intel_gpu(self) -> Tuple[bool, str, str]:
        """检测Intel集成显卡"""
        try:
            # Windows系统检测Intel GPU
            if platform.system() == "Windows":
                result = subprocess.run(
                    ['wmic', 'path', 'win32_VideoController', 'get', 'name', '/format:list'],
                    capture_output=True,
                    text=True,
                    timeout=10
                )
                
                if result.returncode == 0:
                    lines = result.stdout.strip().split('\n')
                    for line in lines:
                        if 'Name=' in line and 'Intel' in line:
                            gpu_name = line.split('=')[1].strip()
                            # Intel集成显卡通常性能较低，标记为黄色
                            status_text = f"集成显卡，{gpu_name}"
                            return False, gpu_name, status_text
            
            # Linux系统检测Intel GPU
            elif platform.system() == "Linux":
                result = subprocess.run(
                    ['lspci', '-nn'],
                    capture_output=True,
                    text=True,
                    timeout=10
                )
                
                if result.returncode == 0:
                    lines = result.stdout.split('\n')
                    for line in lines:
                        if 'VGA' in line and 'Intel' in line:
                            parts = line.split(': ')
                            if len(parts) > 1:
                                gpu_name = parts[1].split('[')[0].strip()
                                status_text = f"集成显卡，{gpu_name}"
                                return False, gpu_name, status_text
                                
        except (subprocess.TimeoutExpired, subprocess.CalledProcessError, FileNotFoundError):
            pass
        except Exception as e:
            print(f"Intel GPU检测失败: {e}")
        
        return False, "未检测到Intel GPU", "Intel GPU不可用"
    
    def _check_ai_framework_gpu_support(self) -> Tuple[bool, str]:
        """
        检查AI框架的GPU支持情况
        返回: (是否支持GPU, 详细信息)
        """
        frameworks_status = []
        gpu_available = False
        
        # TensorFlow检查已移除：项目使用PyTorch，不需要TensorFlow
        
        # 检查PyTorch GPU支持
        try:
            import torch
            if torch.cuda.is_available():
                frameworks_status.append("PyTorch GPU")
                gpu_available = True
            else:
                frameworks_status.append("PyTorch (仅CPU)")
        except ImportError:
            pass  # PyTorch是可选的
        except Exception:
            pass
        
        # 检查CUDA运行时
        try:
            import pynvml
            pynvml.nvmlInit()
            device_count = pynvml.nvmlDeviceGetCount()
            if device_count > 0:
                frameworks_status.append("CUDA运行时")
                # 注意：这里不设置gpu_available=True，因为仅有CUDA不足以保证AI框架能用
        except ImportError:
            pass
        except Exception:
            pass
        
        detail = " + ".join(frameworks_status) if frameworks_status else "无AI框架GPU支持"
        return gpu_available, detail
    
    def get_ai_framework_status(self) -> str:
        """获取AI框架状态的详细信息"""
        gpu_available, detail = self._check_ai_framework_gpu_support()
        
        status_lines = []
        if gpu_available:
            status_lines.append("✅ AI GPU加速：可用")
        else:
            status_lines.append("❌ AI GPU加速：不可用")
        
        status_lines.append(f"📋 框架状态：{detail}")
        
        # 添加配置建议
        if not gpu_available:
            status_lines.append("💡 建议：")
            status_lines.append("   1. 确保安装了CUDA 11.8+")
            status_lines.append("   2. 确保安装了cuDNN 8.6+")
            status_lines.append("   3. 重新安装 torch 和 torchaudio")
            status_lines.append("   4. 重启应用程序")
        
        return "\n".join(status_lines)
    
    def get_detailed_gpu_info(self) -> Dict:
        """获取详细的GPU信息"""
        try:
            # 检测所有GPU
            gpu_info = {
                'nvidia': [],
                'amd': [],
                'intel': [],
                'cuda_available': False,
                'total_memory': 0
            }
            
            # NVIDIA GPU详细信息
            try:
                result = subprocess.run(
                    ['nvidia-smi', '--query-gpu=index,name,driver_version,memory.total,memory.used,memory.free,temperature.gpu,power.draw', '--format=csv,noheader,nounits'],
                    capture_output=True,
                    text=True,
                    timeout=10
                )
                
                if result.returncode == 0:
                    lines = result.stdout.strip().split('\n')
                    for line in lines:
                        if line.strip():
                            parts = [p.strip() for p in line.split(',')]
                            if len(parts) >= 8:
                                gpu_detail = {
                                    'index': int(parts[0]),
                                    'name': parts[1],
                                    'driver_version': parts[2],
                                    'memory_total': int(parts[3]),
                                    'memory_used': int(parts[4]),
                                    'memory_free': int(parts[5]),
                                    'temperature': float(parts[6]) if parts[6] != '[Not Supported]' else None,
                                    'power_draw': float(parts[7]) if parts[7] != '[Not Supported]' else None
                                }
                                gpu_info['nvidia'].append(gpu_detail)
                                gpu_info['total_memory'] += gpu_detail['memory_total']
                    
                    # 检查CUDA可用性
                    cuda_available, _ = self._check_ai_framework_gpu_support()
                    gpu_info['cuda_available'] = cuda_available
                    
            except (subprocess.TimeoutExpired, subprocess.CalledProcessError, FileNotFoundError):
                pass
            
            return gpu_info
            
        except Exception as e:
            print(f"获取GPU详细信息失败: {e}")
            return {} 