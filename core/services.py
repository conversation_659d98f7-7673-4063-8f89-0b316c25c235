#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FlipTalk AI - 核心服务
提供高级API接口，封装插件调用和任务管理
"""

import os
from typing import Dict, Any, List, Optional, Callable
from pathlib import Path

from .plugin_manager import plugin_manager
from .task_scheduler import task_scheduler, TaskStatus
from .interfaces import IAudioExtractor, IVoiceSeparator, IAsrEngine, ITranslator, ITtsEngine, IAudioProcessor, IVideoSynthesizer
from PySide6.QtCore import QObject, Signal


class AudioExtractionService(QObject):
    """
    音频提取服务
    提供视频音频提取的高级接口
    """
    
    # 信号定义
    extraction_started = Signal(str, str)  # task_id, filename
    extraction_progress = Signal(str, int)  # task_id, progress
    extraction_completed = Signal(str, str, str)  # task_id, input_path, output_path
    extraction_failed = Signal(str, str, str)  # task_id, input_path, error
    
    def __init__(self):
        super().__init__()
        self.extractor: Optional[IAudioExtractor] = None
        self._initialize_extractor()
        
        # 连接任务调度器信号
        task_scheduler.task_started.connect(self._on_task_started)
        task_scheduler.task_progress.connect(self._on_task_progress)
        task_scheduler.task_completed.connect(self._on_task_completed)
        task_scheduler.task_failed.connect(self._on_task_failed)
        
        # 存储任务类型映射
        self._audio_extraction_tasks: Dict[str, str] = {}  # task_id -> input_path
    
    def _initialize_extractor(self):
        """初始化音频提取器"""
        self.extractor = plugin_manager.get_plugin(IAudioExtractor)
        if not self.extractor:
            print("警告：未找到音频提取插件")
    
    def extract_audio_async(self, video_path: str, output_dir: str = None) -> str:
        """
        异步提取音频
        
        Args:
            video_path: 视频文件路径
            output_dir: 输出目录，如果为None则使用视频文件同目录
            
        Returns:
            str: 任务ID
            
        Raises:
            RuntimeError: 音频提取器不可用
            FileNotFoundError: 视频文件不存在
        """
        if not self.extractor:
            raise RuntimeError("音频提取器不可用，请检查插件是否正确加载")
        
        video_path = Path(video_path)
        if not video_path.exists():
            raise FileNotFoundError(f"视频文件不存在: {video_path}")
        
        # 创建任务
        task_name = f"提取音频 - {video_path.name}"
        task_id = task_scheduler.add_task(
            name=task_name,
            func=self._extract_audio_worker,
            args=(str(video_path), output_dir),
            description=f"从视频 {video_path.name} 中提取音频",
            metadata={
                'type': 'audio_extraction',
                'input_path': str(video_path),
                'output_dir': output_dir
            }
        )
        
        # 记录任务映射
        self._audio_extraction_tasks[task_id] = str(video_path)
        
        return task_id
    
    def extract_audio_sync(self, video_path: str, output_dir: str = None) -> str:
        """
        同步提取音频
        
        Args:
            video_path: 视频文件路径
            output_dir: 输出目录
            
        Returns:
            str: 提取的音频文件路径
        """
        if not self.extractor:
            raise RuntimeError("音频提取器不可用")
        
        return self.extractor.extract(video_path, output_dir)
    
    def get_supported_formats(self) -> List[str]:
        """
        获取支持的视频格式
        
        Returns:
            List[str]: 支持的视频格式列表
        """
        if self.extractor:
            return self.extractor.get_supported_formats()
        return []
    
    def get_audio_info(self, video_path: str) -> Dict[str, Any]:
        """
        获取视频音频信息
        
        Args:
            video_path: 视频文件路径
            
        Returns:
            Dict[str, Any]: 音频信息
        """
        if self.extractor:
            return self.extractor.get_audio_info(video_path)
        return {'error': '音频提取器不可用'}
    
    def _extract_audio_worker(self, video_path: str, output_dir: str = None, 
                             progress_callback: Callable[[str, int], None] = None) -> str:
        """
        音频提取工作函数
        
        Args:
            video_path: 视频文件路径
            output_dir: 输出目录
            progress_callback: 进度回调函数
            
        Returns:
            str: 提取的音频文件路径
        """
        # 模拟进度报告
        if progress_callback:
            # 获取当前任务ID（通过任务调度器的映射）
            task_id = None
            for tid, path in self._audio_extraction_tasks.items():
                if path == video_path:
                    task_id = tid
                    break
            
            if task_id:
                progress_callback(task_id, 10)  # 开始处理
        
        # 执行实际提取
        result = self.extractor.extract(video_path, output_dir)
        
        if progress_callback and task_id:
            progress_callback(task_id, 100)  # 完成
        
        return result
    
    def _on_task_started(self, task_id: str):
        """处理任务开始信号"""
        if task_id in self._audio_extraction_tasks:
            input_path = self._audio_extraction_tasks[task_id]
            filename = Path(input_path).name
            self.extraction_started.emit(task_id, filename)
    
    def _on_task_progress(self, task_id: str, progress: int):
        """处理任务进度信号"""
        if task_id in self._audio_extraction_tasks:
            self.extraction_progress.emit(task_id, progress)
    
    def _on_task_completed(self, task_id: str, result: Any):
        """处理任务完成信号"""
        if task_id in self._audio_extraction_tasks:
            input_path = self._audio_extraction_tasks[task_id]
            output_path = str(result) if result else ""
            self.extraction_completed.emit(task_id, input_path, output_path)
            
            # 清理任务映射
            del self._audio_extraction_tasks[task_id]
    
    def _on_task_failed(self, task_id: str, error: str):
        """处理任务失败信号"""
        if task_id in self._audio_extraction_tasks:
            input_path = self._audio_extraction_tasks[task_id]
            self.extraction_failed.emit(task_id, input_path, error)
            
            # 清理任务映射
            del self._audio_extraction_tasks[task_id]


class VoiceSeparationService(QObject):
    """
    人声分离服务
    提供音频人声分离的高级接口
    """
    
    # 信号定义
    separation_started = Signal(str, str)  # task_id, filename
    separation_progress = Signal(str, int)  # task_id, progress
    separation_completed = Signal(str, str, dict)  # task_id, input_path, output_paths
    separation_failed = Signal(str, str, str)  # task_id, input_path, error
    
    def __init__(self):
        super().__init__()
        self.separator: Optional[IVoiceSeparator] = None
        self._initialize_separator()
        
        # 连接任务调度器信号
        task_scheduler.task_started.connect(self._on_task_started)
        task_scheduler.task_progress.connect(self._on_task_progress)
        task_scheduler.task_completed.connect(self._on_task_completed)
        task_scheduler.task_failed.connect(self._on_task_failed)
        
        # 存储任务类型映射
        self._voice_separation_tasks: Dict[str, str] = {}  # task_id -> input_path
    
    def _initialize_separator(self):
        """初始化人声分离器"""
        self.separator = plugin_manager.get_plugin(IVoiceSeparator)
        if not self.separator:
            print("警告：未找到人声分离插件")
    
    def separate_voice_async(self, audio_path: str, output_dir: str = None) -> str:
        """
        异步分离人声
        
        Args:
            audio_path: 音频文件路径
            output_dir: 输出目录，如果为None则使用音频文件同目录
            
        Returns:
            str: 任务ID
            
        Raises:
            RuntimeError: 人声分离器不可用
            FileNotFoundError: 音频文件不存在
        """
        if not self.separator:
            raise RuntimeError("人声分离器不可用，请检查插件是否正确加载")
        
        audio_path = Path(audio_path)
        if not audio_path.exists():
            raise FileNotFoundError(f"音频文件不存在: {audio_path}")
        
        # 创建任务
        task_name = f"人声分离 - {audio_path.name}"
        task_id = task_scheduler.add_task(
            name=task_name,
            func=self._separate_voice_worker,
            args=(str(audio_path), output_dir),
            description=f"从音频 {audio_path.name} 中分离人声和背景音乐",
            metadata={
                'type': 'voice_separation',
                'input_path': str(audio_path),
                'output_dir': output_dir
            }
        )
        
        # 记录任务映射
        self._voice_separation_tasks[task_id] = str(audio_path)
        
        return task_id
    
    def separate_voice_sync(self, audio_path: str, output_dir: str = None) -> Dict[str, str]:
        """
        同步分离人声
        
        Args:
            audio_path: 音频文件路径
            output_dir: 输出目录
            
        Returns:
            Dict[str, str]: 分离结果，包含人声和背景音乐文件路径
        """
        if not self.separator:
            raise RuntimeError("人声分离器不可用")
        
        return self.separator.separate(audio_path, output_dir, None)
    
    def get_supported_formats(self) -> List[str]:
        """
        获取支持的音频格式
        
        Returns:
            List[str]: 支持的音频格式列表
        """
        if self.separator:
            return self.separator.get_supported_formats()
        return []
    
    def get_quality_settings(self) -> Dict[str, Any]:
        """
        获取质量设置选项
        
        Returns:
            Dict[str, Any]: 质量设置选项
        """
        if self.separator:
            return self.separator.get_quality_settings()
        return {}
    
    def _separate_voice_worker(self, audio_path: str, output_dir: str = None, 
                              progress_callback: Callable[[str, int], None] = None) -> Dict[str, str]:
        """
        人声分离工作函数
        
        Args:
            audio_path: 音频文件路径
            output_dir: 输出目录
            progress_callback: 进度回调函数
            
        Returns:
            Dict[str, str]: 分离结果
        """
        # 模拟进度报告
        if progress_callback:
            # 获取当前任务ID
            task_id = None
            for tid, path in self._voice_separation_tasks.items():
                if path == audio_path:
                    task_id = tid
                    break
            
            if task_id:
                progress_callback(task_id, 10)  # 开始处理
        
        # 执行实际分离
        result = self.separator.separate(audio_path, output_dir, None)
        
        if progress_callback and task_id:
            progress_callback(task_id, 100)  # 完成
        
        return result
    
    def _on_task_started(self, task_id: str):
        """处理任务开始信号"""
        if task_id in self._voice_separation_tasks:
            input_path = self._voice_separation_tasks[task_id]
            filename = Path(input_path).name
            self.separation_started.emit(task_id, filename)
    
    def _on_task_progress(self, task_id: str, progress: int):
        """处理任务进度信号"""
        if task_id in self._voice_separation_tasks:
            self.separation_progress.emit(task_id, progress)
    
    def _on_task_completed(self, task_id: str, result: Any):
        """处理任务完成信号"""
        if task_id in self._voice_separation_tasks:
            input_path = self._voice_separation_tasks[task_id]
            output_paths = result if isinstance(result, dict) else {}
            self.separation_completed.emit(task_id, input_path, output_paths)
            
            # 清理任务映射
            del self._voice_separation_tasks[task_id]
    
    def _on_task_failed(self, task_id: str, error: str):
        """处理任务失败信号"""
        if task_id in self._voice_separation_tasks:
            input_path = self._voice_separation_tasks[task_id]
            self.separation_failed.emit(task_id, input_path, error)
            
            # 清理任务映射
            del self._voice_separation_tasks[task_id]


class FlipTalkCoreService(QObject):
    """
    FlipTalk 核心服务
    整合所有功能服务，提供统一的API接口
    """
    
    def __init__(self):
        super().__init__()
        
        # 初始化各个服务
        self.audio_extraction = AudioExtractionService()
        self.voice_separation = VoiceSeparationService()
        
        # 初始化插件管理器
        self._initialize_plugins()
    
    def _initialize_plugins(self):
        """初始化插件系统"""
        print("正在初始化插件系统...")
        
        # 发现并加载插件
        loaded_count = plugin_manager.discover_plugins()
        
        # 初始化插件
        if loaded_count > 0:
            success = plugin_manager.initialize_plugins()
            if success:
                print("插件系统初始化成功")
                # 重新初始化各个服务中的插件
                self.audio_extraction._initialize_extractor()
                self.voice_separation._initialize_separator()
            else:
                print("部分插件初始化失败")
        else:
            print("未发现任何插件")
    
    def get_plugin_info(self) -> Dict[str, List[Dict[str, str]]]:
        """获取插件信息"""
        return plugin_manager.get_plugin_info()
    
    def get_task_statistics(self) -> Dict[str, int]:
        """获取任务统计信息"""
        return task_scheduler.get_statistics()
    
    def shutdown(self):
        """关闭服务"""
        print("正在关闭核心服务...")
        
        # 关闭任务调度器
        task_scheduler.shutdown(wait=False)
        
        # 清理插件资源
        plugin_manager.cleanup_plugins()
        
        print("核心服务已关闭")


# 全局核心服务实例
core_service = FlipTalkCoreService() 