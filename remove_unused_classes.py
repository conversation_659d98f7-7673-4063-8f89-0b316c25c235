import re

def remove_unused_classes():
    # 读取原始文件
    with open('ui/fliptalk_ui.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 找到VideoUploadPreviewPanel类的开始和结束位置
    panel_start_pattern = r'class VideoUploadPreviewPanel\(RoundedWidget\):'
    panel_start_match = re.search(panel_start_pattern, content)
    
    if panel_start_match:
        panel_start_pos = panel_start_match.start()
        
        # 找到下一个类定义位置
        next_class_pattern = r'class ClickableAvatar\(QLabel\):'
        next_class_match = re.search(next_class_pattern, content)
        
        if next_class_match:
            panel_end_pos = next_class_match.start()
            
            # 从内容中删除VideoUploadPreviewPanel类
            content = content[:panel_start_pos] + content[panel_end_pos:]
            print("成功删除VideoUploadPreviewPanel类")
        else:
            print("找不到ClickableAvatar类，无法确定VideoUploadPreviewPanel类的结束位置。")
    else:
        print("找不到VideoUploadPreviewPanel类的定义。")
    
    # 找到VideoProcessingArea类的开始和结束位置
    # 先找注释行
    comment_pattern = r'# VideoProcessingArea类已移至ui/widgets/video_processing.py，这里的定义不再使用'
    comment_match = re.search(comment_pattern, content)
    
    if comment_match:
        comment_pos = comment_match.start()
        
        # 从注释行开始找类定义
        area_start_pattern = r'class VideoProcessingArea\(RoundedWidget\):'
        area_start_match = re.search(area_start_pattern, content[comment_pos:])
        
        if area_start_match:
            area_start_pos = comment_pos + area_start_match.start()
            
            # 找到下一个类定义或注释行
            next_class_pattern = r'class SubtitleEditArea\(QWidget\)|# SubtitleEditArea类已移至'
            next_class_match = re.search(next_class_pattern, content[area_start_pos:])
            
            if next_class_match:
                area_end_pos = area_start_pos + next_class_match.start()
                
                # 从内容中删除VideoProcessingArea类及其注释
                content = content[:comment_pos] + content[area_end_pos:]
                print("成功删除VideoProcessingArea类")
            else:
                print("找不到SubtitleEditArea类，无法确定VideoProcessingArea类的结束位置。")
        else:
            print("找不到VideoProcessingArea类的定义。")
    else:
        print("找不到VideoProcessingArea类的注释行。")
    
    # 保存修改后的内容
    with open('ui/fliptalk_ui.py', 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("文件保存成功")

if __name__ == "__main__":
    remove_unused_classes() 