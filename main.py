#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FlipTalk AI - 主入口文件
"""

import sys
import os
import traceback
from PySide6.QtWidgets import QApplication, QVBoxLayout, QLabel, QMessageBox
from ui.fliptalk_ui import FlipTalkMainWindow
from PySide6.QtMultimediaWidgets import QVideoWidget
from PySide6.QtMultimedia import QMediaPlayer, QAudioOutput, QMediaFormat
from PySide6.QtCore import Qt, QUrl

# 创建QApplication实例，必须在导入任何Qt组件之前创建
app = QApplication(sys.argv)

# 添加详细调试信息
print("===== 正在启动FlipTalk AI =====")
print("Python版本:", sys.version)
print(f"运行目录: {os.getcwd()}")
print(f"PySide6路径: {PySide6.__file__ if 'PySide6' in globals() else '未导入'}")

# 检查多媒体模块
try:
    print("\n===== 检查Qt多媒体模块 =====")
    from PySide6.QtMultimedia import QMediaFormat

    print("检查QVideoWidget可用性...")
    try:
        test_widget = QVideoWidget()
        print("QVideoWidget创建成功")
    except Exception as e:
        print(f"QVideoWidget创建失败: {e}")
        traceback.print_exc()

    print("检查QMediaPlayer可用性...")
    try:
        test_player = QMediaPlayer()
        print("QMediaPlayer创建成功")
    except Exception as e:
        print(f"QMediaPlayer创建失败: {e}")
        traceback.print_exc()

    # 修复API调用方式
    try:
        media_format = QMediaFormat()
        print("支持的视频格式:", [fmt.name for fmt in media_format.supportedVideoCodecs(QMediaFormat.ConversionMode.Decode)])
        print("支持的音频格式:", [fmt.name for fmt in media_format.supportedAudioCodecs(QMediaFormat.ConversionMode.Decode)])
    except Exception as api_error:
        print(f"获取支持格式时出错: {api_error}")
        print("跳过格式检查，继续初始化...")
    print("Qt多媒体模块加载成功")
except Exception as e:
    print(f"Qt多媒体模块加载失败: {e}")
    traceback.print_exc()

# 检查OpenCV
try:
    print("\n===== 检查OpenCV =====")
    import cv2
    print("OpenCV版本:", cv2.__version__)

    # 测试OpenCV视频功能
    print("测试OpenCV视频读取功能...")
    try:
        # 列出视频文件
        video_files = []
        for root, dirs, files in os.walk(".", topdown=True):
            for file in files:
                if file.lower().endswith(('.mp4', '.avi', '.mov', '.mkv')):
                    video_files.append(os.path.join(root, file))

        if video_files:
            print(f"找到{len(video_files)}个视频文件，尝试使用第一个测试...")
            test_video = video_files[0]
            cap = cv2.VideoCapture(test_video)
            if cap.isOpened():
                ret, frame = cap.read()
                if ret:
                    print(f"成功读取视频帧，大小: {frame.shape}")
                else:
                    print("无法读取视频帧")
                cap.release()
            else:
                print(f"OpenCV无法打开视频文件: {test_video}")
        else:
            print("未找到测试用的视频文件")
    except Exception as e:
        print(f"OpenCV视频测试失败: {e}")
        traceback.print_exc()
except ImportError:
    print("OpenCV未安装")

def register_video_formats():
    """注册支持的视频格式"""
    try:
        print("\n===== 注册视频格式 =====")
        # 注册常见视频格式
        media_format = QMediaFormat()

        # 打印支持的容器格式
        try:
            print("支持的容器格式:", [fmt.name for fmt in media_format.supportedFileFormats(QMediaFormat.ConversionMode.Decode)])
        except Exception as fmt_error:
            print(f"获取容器格式时出错: {fmt_error}")
            print("跳过容器格式检查...")

        # 注册MP4格式
        mp4_format = QMediaFormat()
        mp4_format.setFileFormat(QMediaFormat.FileFormat.MPEG4)
        print("已注册MP4格式")

        # 注册AVI格式
        avi_format = QMediaFormat()
        avi_format.setFileFormat(QMediaFormat.FileFormat.AVI)
        print("已注册AVI格式")

        # 注册MOV格式
        mov_format = QMediaFormat()
        mov_format.setFileFormat(QMediaFormat.FileFormat.QuickTime)
        print("已注册MOV格式")

        print("视频格式注册成功")
    except Exception as e:
        print(f"注册视频格式时出错: {e}")
        traceback.print_exc()

def create_test_player():
    """创建测试视频播放器，检查播放功能"""
    try:
        print("\n===== 测试视频播放功能 =====")
        player = QMediaPlayer()
        video_widget = QVideoWidget()
        player.setVideoOutput(video_widget)

        # 检查视频处理后端
        try:
            # 修复metaData API调用
            metadata = player.metaData()
            print(f"媒体播放器后端: {type(metadata).__name__}")
        except Exception as meta_error:
            print(f"获取媒体播放器信息时出错: {meta_error}")
            print("媒体播放器后端: 无法获取")

        print("视频播放测试创建成功")
        return True
    except Exception as e:
        print(f"视频播放测试失败: {e}")
        traceback.print_exc()
        return False

def main():
    # 注册视频格式
    register_video_formats()

    # 测试视频播放器
    playback_works = create_test_player()

    print(f"\n===== 应用程序初始化 =====")
    print(f"Qt版本: {app.applicationVersion()}")
    
    # 创建主窗口
    print("\n===== 创建主窗口 =====")
    try:
        window = FlipTalkMainWindow()
        print("主窗口创建成功")
        window.show()
        print("主窗口显示成功")
        
        # 如果测试失败，在控制台输出信息但不显示弹窗
        if not playback_works:
            print("警告：视频播放功能测试失败，可能无法正常显示视频。请检查是否安装了必要的视频编解码器。")
        
        # 不再使用sys.exit包装app.exec()，这样可以避免在退出时报错
        return app.exec()
    except Exception as e:
        print(f"主窗口创建失败: {e}")
        traceback.print_exc()
        QMessageBox.critical(None, "应用程序错误", f"启动FlipTalk AI时遇到问题:\n{str(e)}")
        return 1

if __name__ == "__main__":
    sys.exit(main()) 